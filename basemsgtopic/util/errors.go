package util

// 错误码
const (
	RetOK                    = 0
	RetInvalidParam          = 180200 // 参数错误
	RetCacheReadError        = 180201 // 缓存读取错误
	RetUserConnEmpty         = 180202 // 用户长连接为空
	RetSubscribeError        = 180203 // 订阅错误
	RetUnSubscribeError      = 180204 // 取消订阅错误
	RetConnStatusReportError = 180205 //长连接状态上报错误
)

var ErrorMsg = map[int32]string{
	RetOK:                    "success",
	RetInvalidParam:          "parameter error",
	RetCacheReadError:        "cache read error",
	RetUserConnEmpty:         "user long connection is empty",
	RetSubscribeError:        "subscription error",
	RetUnSubscribeError:      "cancel subscription error",
	RetConnStatusReportError: "long connection status escalation error",
}
