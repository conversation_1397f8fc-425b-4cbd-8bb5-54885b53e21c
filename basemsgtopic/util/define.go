package util

/**
常量和结构体定义
*/

const (
	TIMESTART    = "2006-01-02 15:04:05"
	DATESTART    = "2006-01-02"
	TIME_ONE_DAY = 86400

	PlatformConnRule_OneUserConn       = 1 //同平台同用户只有一个长连接
	PlatformConnRule_OneUserDeviceConn = 2 //同平台同用户同设备只有一个长连接

	UserConnKickReason_AnotherClientLogin     = "another_client_login"
	UserConnKickReason_AnotherClientLoginJson = `{"kick_reason": "another_client_login"}`
)

//平台用户长连接信息
type PlatformUserConnInfo struct {
	PlatformId    uint64
	UserId        string
	DeviceId      string
	ClientId      uint32
	ClientVersion string
	BrokerIp      string
	ConnId        uint64
	ConnTime      int64
}
