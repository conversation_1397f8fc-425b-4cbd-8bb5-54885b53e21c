# 服务监听地址
addr: 0.0.0.0:8080

# 日志配置文件
logger: deploy/logger.xml

# 缓存配置
redis:
  #实例列表
  instances:
    master:
      url: redis:6379
      password: 123456
    lock:
      url: redis:6379
      password: 123456

#broker代理配置
broker:
  in_k8s: false
  kube_config: ~/.kube/config
  namespace: xllivemp
  # map[string]string
  pod_label:
    xlmesh-app: xllivemp-basemsgbroker-s
  srv_name: xllivemp-basemsgbroker-s