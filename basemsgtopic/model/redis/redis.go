package redis

import (
	cache "xim/common/cache_redis"
)

const (
	cacheLock   = "lock"
	cacheMaster = "master"
)

var redisHelper *Redis

// Redis redis模型
type Redis struct {
	config cache.RedisConfig
	dbs    *cache.Caches
}

// NewRedis 构造函数
func NewRedis(conf cache.RedisConfig) error {
	caches, err := cache.GetCaches(&conf)
	if err != nil {
		return nil
	}
	redisHelper = &Redis{config: conf, dbs: caches}
	return nil
}

func getCache(dbName string) *cache.Cache {
	return redisHelper.dbs.GetCache(dbName)
}
