package redis

import (
	"errors"
	"time"

	"github.com/go-redis/redis"
	"fmt"
)

// lock错误
var (
	ErrLockFailed = errors.New("lock failed")
)

const (
	USER_CONN_LOCK_TIME = 3
	USER_CONN_LOCK_TRY_TIMES = 50
)

func GetPlatformUserLockKey(platformId uint64, userId string) (string) {
	return fmt.Sprintf("basemsgtopic:s:user:lock:%d:%s", platformId, userId)
}

func GetPlatformUserConnLockKey(userConnUniqueId string) (string) {
	return fmt.Sprintf("basemsgtopic:s:user:conn:lock:%s", userConnUniqueId)
}

// GetLock 获取锁
func GetLock(key string, expiration time.Duration) error {
	v := time.Now().UnixNano()
	ok, err := getCache(cacheLock).SetNX(key, v, expiration).Result()
	if err != nil { //缓存操作失败
		return err
	}
	if !ok { //拿锁失败
		return ErrLockFailed
	}
	return nil
}

// DelLock 删除锁
func DelLock(key string) error {
	err := getCache(cacheLock).Del(key).Err()
	if err != nil && err != redis.Nil { //删除缓存失败
		return err
	}
	return nil
}

// 获取锁，如果获取不到，重试获取锁，总次数可定义，每次重试间隔20ms
func SpinLock(key string, expiration time.Duration, maxTimes uint32) (error) {
	var tryTimes uint32
	for {
		tryTimes++
		err := GetLock(key, expiration)
		//如果获取成功，直接返回
		if err == nil {
			return nil
		}
		//如果获取失败，重试获取锁，直到次数重试完
		if tryTimes >= maxTimes {
			return err
		}
		time.Sleep(20 * time.Millisecond)
	}
}
