package redis

import (
	"fmt"
	"time"

	log "xim/baselib/logger"

	"github.com/go-redis/redis"
)

// 平台用户长连接订阅的所有topic，区分用户的不同长连接
// type: set（有效期三天）
// key：后缀为平台用户长连接唯一标识，platformId_userId_brokerIp_connId
// members：topic名称
func GetPlatformUserConnAllTopicsKey(userConnUniqueId string) string {
	return fmt.Sprintf("basemsgtopic:set:userconn:topic:%s", userConnUniqueId)
}

// 平台topic下的所有订阅用户长连接
// type: zset(有效期三天)
// key：后缀为平台id和topic名称
// member-score：member为平台用户长连接唯一标识，platformId_userId_brokerIp_connId，score为订阅时间戳
func GetTopicAllPlatformUserConnskey(platformId uint64, topic string) string {
	return fmt.Sprintf("basemsgtopic:zset:topic:userconn:%d:%s", platformId, topic)
}

// 用户长连接订阅topic
func PlatformUserConnSubscribeTopic(platformId uint64, userConnUniqueId string, topics []string) error {
	//如果topic列表为空，不需要继续执行订阅
	if len(topics) <= 0 {
		return nil
	}
	//先更新用户订阅的topic列表的缓存
	userTopicSetKey := GetPlatformUserConnAllTopicsKey(userConnUniqueId)
	values := make([]interface{}, 0)
	for _, topic := range topics {
		values = append(values, topic)
	}
	err := getCache(cacheMaster).SAdd(userTopicSetKey, values...).Err()
	if err != nil {
		log.Errorf("AddPlatformUserConnAllTopics useruniqueid %s topics %+v error %v", userConnUniqueId, topics, err)
		return err
	}
	getCache(cacheMaster).Expire(userTopicSetKey, time.Duration(3*86400)*time.Second)
	log.Infof("AddPlatformUserConnAllTopics useruniqueid %s topics %+v success", userConnUniqueId, topics)
	//再更新每个topic上订阅的用户记录
	nowtime := float64(time.Now().Unix())
	for _, topic := range topics {
		topicUserZsetKey := GetTopicAllPlatformUserConnskey(platformId, topic)
		member := redis.Z{
			Member: userConnUniqueId,
			Score:  nowtime,
		}
		err = getCache(cacheMaster).ZAdd(topicUserZsetKey, member).Err()
		if err != nil {
			log.Errorf("SetTopicAllPlatformUserConns topic %s member %s error %v", topic, userConnUniqueId, err)
			continue
		}
		getCache(cacheMaster).Expire(topicUserZsetKey, time.Duration(3*86400)*time.Second)
		log.Infof("AddTopicAllPlatformUserConns topic %s member %s success", topic, userConnUniqueId)
	}
	return nil
}

// 用户长连接取消订阅topic
func PlatformUserConnUnSubscribeTopic(platformId uint64, userConnUniqueId string, topics []string) error {
	//如果topic列表为空，不需要取消订阅
	if len(topics) <= 0 {
		return nil
	}
	//先更新用户订阅的topic列表缓存，删除对应topic
	userTopicSetKey := GetPlatformUserConnAllTopicsKey(userConnUniqueId)
	values := make([]interface{}, 0)
	for _, topic := range topics {
		values = append(values, topic)
	}
	err := getCache(cacheMaster).SRem(userTopicSetKey, values...).Err()
	if err != nil {
		log.Errorf("RemPlatformUserConnAllTopics useruniqueid %s topics %+v error %v", userConnUniqueId, topics, err)
		return err
	}
	log.Infof("RemPlatformUserConnAllTopics useruniqueid %s topics %+v success", userConnUniqueId, topics)
	//再更新每个topic上订阅的用户记录，删除
	for _, topic := range topics {
		topicUserZsetKey := GetTopicAllPlatformUserConnskey(platformId, topic)
		err = getCache(cacheMaster).ZRem(topicUserZsetKey, userConnUniqueId).Err()
		if err != nil {
			log.Errorf("RemTopicAllPlatformUserConns topic %s member %s error %v", topic, userConnUniqueId, err)
			continue
		}
		log.Infof("RemTopicAllPlatformUserConns topic %s member %s success", topic, userConnUniqueId)
	}
	return nil
}

// 获取单个用户长连接的topic列表
func GetPlatformUserConnAllTopics(userConnUniqueId string) ([]string, error) {
	setkey := GetPlatformUserConnAllTopicsKey(userConnUniqueId)
	topics, err := getCache(cacheMaster).SMembers(setkey).Result()
	if err != nil {
		log.Errorf("GetUserConnAllTopics setkey %s error %v", setkey, err)
		return nil, err
	}
	return topics, nil
}

// 获取多个用户长连接的topic列表
func GetMultiPlatformUserConnsAllTopics(userConnUniqueIds []string) (map[string][]string, error) {
	results := make(map[string][]string, 0)
	var cmders []*redis.StringSliceCmd
	_, err := getCache(cacheMaster).Pipelined(func(pipeliner redis.Pipeliner) error {
		for _, userConnUniqueId := range userConnUniqueIds {
			userTopicSetKey := GetPlatformUserConnAllTopicsKey(userConnUniqueId)
			cmders = append(cmders, pipeliner.SMembers(userTopicSetKey))
		}
		return nil
	})
	if err != nil {
		log.Errorf("GetMultiUserConnsAllTopics userConnUniqueIds %+v error %v", userConnUniqueIds, err)
		return nil, err
	}
	for idx, cmder := range cmders {
		userConnUniqueId := userConnUniqueIds[idx]
		values, err := cmder.Result()
		if err != nil {
			log.Errorf("GetMultiUserConnsAllTopics oneUserConnUniqueId %s error %v", userConnUniqueId, err)
			continue
		}
		results[userConnUniqueId] = values
	}
	return results, nil
}

// 获取topic下订阅的部分用户长连接
func GetTopicLimitNumUserConns(platformId uint64, topic string, start, stop int64) ([]string, error) {
	zsetkey := GetTopicAllPlatformUserConnskey(platformId, topic)
	result, err := getCache(cacheMaster).ZRange(zsetkey, start, stop).Result()
	if err != nil {
		log.Errorf("GetTopicLimitNumUserConns zsetkey %s start %d stop %d error %v", zsetkey, start, stop, err)
		return nil, err
	}
	return result, nil
}

// 获取topic下订阅的所有用户长连接，也是分批获取
func GetTopicAllUserConns(platformId uint64, topic string) ([]string, error) {
	var result = make([]string, 0)
	var start int64 = 0
	var limit int64 = 2000
	var maxErrTime = 10
	for {
		stop := start + limit - 1
		values, err := GetTopicLimitNumUserConns(platformId, topic, start, stop)
		if err != nil {
			log.Errorf("GetTopicAllUserConns platformid %d topic %s start %d stop %s error %v",
				platformId, topic, start, stop, err)
			maxErrTime--
			if maxErrTime <= 0 {
				return nil, err
			}
			start = start + limit
			continue
		}
		num := len(values)
		if num > 0 {
			result = append(result, values...)
		}
		start = start + limit
		if num < int(limit) {
			break
		}
	}
	return result, nil
}

// 删除用户长连接的所有订阅topic
func DelPlatformUserConnAllTopics(platformId uint64, userConnUniqueId string) error {
	//先获取所有订阅topic
	topics, err := GetPlatformUserConnAllTopics(userConnUniqueId)
	if err != nil {
		log.Errorf("DelUserConnAllTopics GetPlatformUserConnAllTopics userconn %s error %v", userConnUniqueId, err)
		return err
	}
	//然后用户取消订阅这些topic
	err = PlatformUserConnUnSubscribeTopic(platformId, userConnUniqueId, topics)
	if err != nil {
		log.Errorf("DelUserConnAllTopics PlatformUserConnUnSubscribeTopic userconn %s error %v", userConnUniqueId, err)
		return err
	}
	return nil
}
