package redis

import (
	"fmt"
	"time"

	log "xim/baselib/logger"
	"xim/basemsgtopic/util"

	"github.com/go-redis/redis"
)

// 平台用户的conn信息
// type：hash（会定时清理，最多保留3天）
// key：后缀为平台id和用户userid
// field-value：field为用户长连接唯一标识，platformId_userId_brokerIp_connId；value为conn信息，包括platformId，userId，deviceId，brokerIp，connId，connTime
func GetPlatformUserConnInfoKey(platformId uint64, userId string) string {
	return fmt.Sprintf("basemsgtopic:h:user:conn:%d:%s", platformId, userId)
}

// 平台用户conn的ping心跳时间
// type：zset（无过期，会定时清理）
// key：后缀为平台id，每个平台单独一个zset
// member-score：member是platformId_userId_brokerIp_connId，score是ping心跳时间
func GetPlatformUserConnPingTimeKey(platformId uint64) string {
	return fmt.Sprintf("basemsgtopic:zset:user:ping:%d", platformId)
}

// 更新平台用户的长连接信息
func SetPlatformUserConnInfo(platformId uint64, userId string, conns []*util.PlatformUserConnInfo, seconds int64) error {
	hashkey := GetPlatformUserConnInfoKey(platformId, userId)
	fields := make(map[string]interface{}, 0)
	for _, conn := range conns {
		field := util.FormatPlatformUserConnUniqueId(conn)
		value, _ := util.Json.MarshalToString(conn)
		fields[field] = value
	}
	err := getCache(cacheMaster).HMSet(hashkey, fields).Err()
	if err != nil {
		log.Errorf("SetPlatformUserConnInfo hashkey %s conns %+v error %v", hashkey, conns, err)
		return err
	}
	getCache(cacheMaster).Expire(hashkey, time.Duration(seconds)*time.Second)
	return nil
}

func GetPlatformUserConnInfo(platformId uint64, userId string, userConnUniqueId string) (*util.PlatformUserConnInfo, error) {
	hashkey := GetPlatformUserConnInfoKey(platformId, userId)
	field := userConnUniqueId
	value, err := getCache(cacheMaster).HGet(hashkey, field).Result()
	if err == redis.Nil {
		log.Infof("GetPlatformUserConnInfo hashkey %s field %s missing", hashkey, field)
		return nil, nil
	}
	if err != nil {
		log.Errorf("GetPlatformUserConnInfo hashkey %s field %s error %v", hashkey, field, err)
		return nil, err
	}
	if value == "" {
		log.Infof("GetPlatformUserConnInfo hashkey %s field %s empty", hashkey, field)
		return nil, nil
	}
	connInfo := &util.PlatformUserConnInfo{}
	err = util.Json.UnmarshalFromString(value, connInfo)
	if err != nil {
		log.Errorf("GetPlatformUserConnInfo hashkey %s field %s value %s unmarsalerror %v", hashkey, field, value, err)
		return nil, err
	}
	return connInfo, nil
}

// 获取平台用户的所有长连接信息
func GetPlatformUserAllConnInfo(platformId uint64, userId string) ([]*util.PlatformUserConnInfo, error) {
	hashkey := GetPlatformUserConnInfoKey(platformId, userId)
	values, err := getCache(cacheMaster).HVals(hashkey).Result()
	if err != nil {
		log.Errorf("GetPlatformUserAllConnInfo platformid %d userid %s error %v", platformId, userId, err)
		return nil, err
	}
	result := make([]*util.PlatformUserConnInfo, 0)
	for _, value := range values {
		item := &util.PlatformUserConnInfo{}
		err = util.Json.UnmarshalFromString(value, item)
		if err != nil {
			log.Errorf("GetPlatformUserAllConnInfo platformid %d userid %s value %s jsonunmarshal error %v", platformId, userId, value, err)
			return nil, err
		}
		result = append(result, item)
	}
	return result, nil
}

// 删除平台用户所有的长连接信息
func DelPlatformUserAllConnInfo(platformId uint64, userId string) error {
	hashkey := GetPlatformUserConnInfoKey(platformId, userId)
	err := getCache(cacheMaster).Del(hashkey).Err()
	if err != nil {
		log.Errorf("DelPlatformUserAllConnInfo platformid %d userid %s error %v", platformId, userId, err)
		return err
	}
	return nil
}

// 删除平台用户的特定长连接信息
func DelPlatformUserConnInfo(platformId uint64, userId string, conns []*util.PlatformUserConnInfo) error {
	hashkey := GetPlatformUserConnInfoKey(platformId, userId)
	fields := make([]string, 0)
	for _, conn := range conns {
		field := util.FormatPlatformUserConnUniqueId(conn)
		fields = append(fields, field)
	}
	err := getCache(cacheMaster).HDel(hashkey, fields...).Err()
	if err != nil {
		log.Errorf("DelPlatformUserConnInfo hashkey %s fields %+v error %v", hashkey, fields, err)
		return err
	}
	return nil
}

// 更新记录用户长连接的ping心跳时间
func SetPlatformUserConnPingTime(platformId uint64, userConnUniqueId string, pingTime int64) error {
	zsetKey := GetPlatformUserConnPingTimeKey(platformId)
	member := redis.Z{
		Member: userConnUniqueId,
		Score:  float64(pingTime),
	}
	err := getCache(cacheMaster).ZAdd(zsetKey, member).Err()
	if err != nil {
		log.Errorf("SetPlatformUserConnPingTime zset %s member %s pingtime %d error %v",
			zsetKey, userConnUniqueId, pingTime, err)
		return err
	}
	return nil
}

// 获取平台内心跳时间小于指定时间戳的用户长连接，每次获取数量有限制，防止获取数量过大，影响其他操作
func GetPlatformUserConnBeforePingTime(platformId uint64, pingTime, offset, count int64) ([]string, error) {
	zsetkey := GetPlatformUserConnPingTimeKey(platformId)
	queryOpt := redis.ZRangeBy{
		Min:    "0",
		Max:    util.FormatInt64(pingTime),
		Offset: offset,
		Count:  count,
	}
	result, err := getCache(cacheMaster).ZRangeByScore(zsetkey, queryOpt).Result()
	if err != nil {
		log.Errorf("GetPlatformUserConnBeforePingTime zsetkey %s pingtime %d error %v", zsetkey, pingTime, err)
		return nil, err
	}
	return result, nil
}

// 删除用户长连接的ping心跳时间记录
func DelPlatformUserConnPingTime(platformId uint64, userConnUniqueId string) error {
	zsetKey := GetPlatformUserConnPingTimeKey(platformId)
	member := userConnUniqueId
	err := getCache(cacheMaster).ZRem(zsetKey, member).Err()
	if err != nil {
		log.Errorf("DelPlatformUserConnPingTime zset %s member %s error %v", zsetKey, member, err)
		return err
	}
	return nil
}
