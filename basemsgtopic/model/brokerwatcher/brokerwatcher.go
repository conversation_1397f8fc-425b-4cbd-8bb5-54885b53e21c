package brokerwatcher

import (
	"context"
	"fmt"
	"net"
	"path/filepath"
	"strings"
	"xim/baselib/server/env"

	log "xim/baselib/logger"
	"xim/basemsgtopic/config"
	"xim/proto/api/basemsgbroker"

	"google.golang.org/grpc"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

var brokerWatcher *BrokerWatcher

type BrokerWatcher struct {
	brokerConf config.BrokerConf
	stopCh     chan struct{}
	brokers    map[string]basemsgbroker.SClient
}

func NewBrokerWatcher(brokerConf config.BrokerConf) error {
	brokerWatcher = &BrokerWatcher{
		brokerConf: brokerConf,
		stopCh:     make(chan struct{}),
		brokers:    make(map[string]basemsgbroker.SClient, 0),
	}
	if !env.IsDev() {
		return brokerWatcher.runWatch()
	}
	return nil
}

func GetBroker(brokerIp string) basemsgbroker.SClient {
	if env.IsDev() {
		rpcClient, err := createRpcClient("basemsgbroker")
		if err != nil {
			log.Errorf("addBroker createRpcClient basemsgbroker error %v", err)
			return nil
		}
		return rpcClient

	}
	return brokerWatcher.brokers[brokerIp]
}

func (bw *BrokerWatcher) runWatch() error {
	log.Infof("broker watcher start")

	k8sConfig, err := bw.createConfig()
	log.Infof("createConfig %v error %v", k8sConfig, err)
	if err != nil {
		return err
	}
	client, err := kubernetes.NewForConfig(k8sConfig)
	log.Infof("NewForConfig error %v", err)
	if err != nil {
		return err
	}

	sharedInformerFactory := informers.NewSharedInformerFactoryWithOptions(
		client,
		0,
		informers.WithTweakListOptions(func(opt *metav1.ListOptions) {
			labels := make([]string, 0)
			for k, v := range bw.brokerConf.PodLabel {
				labels = append(labels, fmt.Sprintf("%s=%s", k, v))
			}
			opt.LabelSelector = strings.Join(labels, ",")
		}),
		informers.WithNamespace(bw.brokerConf.NameSpace),
	)
	podInformer := sharedInformerFactory.Core().V1().Pods().Informer()
	podInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    bw.addBroker,
		UpdateFunc: bw.updateBroker,
		DeleteFunc: bw.delBroker,
	})
	sharedInformerFactory.Start(bw.stopCh)
	if !cache.WaitForCacheSync(bw.stopCh, podInformer.HasSynced) {
		panic("informer cache sync failed")
	}

	log.Infof("broker watcher success")
	return nil
}

func (bw *BrokerWatcher) createConfig() (*rest.Config, error) {
	if bw.brokerConf.InK8s {
		k8sConfig, err := rest.InClusterConfig()
		if err != nil {
			log.Errorf("createConfig k8s InClusterConfig error %v", err)
			return nil, err
		}
		return k8sConfig, nil
	} else {
		if home := homedir.HomeDir(); home != "" && bw.brokerConf.KubeConfig == "" {
			bw.brokerConf.KubeConfig = filepath.Join(home, ".kube", "config")
		}

		k8sConfig, err := clientcmd.BuildConfigFromFlags("", bw.brokerConf.KubeConfig)
		if err != nil {
			log.Errorf("createConfig k8s BuildConfigFromFlags error %v", err)
			return nil, err
		}
		return k8sConfig, nil
	}
}

func (bw *BrokerWatcher) addBroker(obj interface{}) {
	broker, ok := obj.(*v1.Pod)
	if !ok {
		log.Errorf("addBroker type %+v assertion failed", obj)
		return
	}
	log.Infof("addBroker podip %s phase %s begin", broker.Status.PodIP, broker.Status.Phase)
	if broker.Status.Phase == v1.PodRunning {
		rpcClient, err := createRpcClient(broker.Status.PodIP)
		if err != nil {
			log.Errorf("addBroker createRpcClient podip %s error %v", broker.Status.PodIP, err)
			return
		}
		bw.brokers[broker.Status.PodIP] = rpcClient
		log.Infof("addBroker podip %s success", broker.Status.PodIP)
	}
}

func (bw *BrokerWatcher) updateBroker(oldObj, newObj interface{}) {
	oldBroker, ok := oldObj.(*v1.Pod)
	if !ok {
		log.Errorf("updateBroker old type %+v assertion failed", oldObj)
		return
	}
	newBroker, ok := newObj.(*v1.Pod)
	if !ok {
		log.Errorf("updateBroker new type %+v assertion failed", newObj)
		return
	}

	log.Infof("updateBroker [oldPodIP: %s, Phase: %s] => [newPodIP: %s, Phase: %s] begin",
		oldBroker.Status.PodIP, oldBroker.Status.Phase, newBroker.Status.PodIP, newBroker.Status.Phase)

	//Pod IP发生变更需要删除旧的pod
	if oldBroker.Status.PodIP != newBroker.Status.PodIP {
		delete(bw.brokers, oldBroker.Status.PodIP)
		log.Infof("updateBroker delete oldpod %s msg %s", oldBroker.Status.PodIP, oldBroker.Status.Message)
	}

	//pod状态为running才能加入
	if newBroker.Status.Phase == v1.PodRunning {
		rpcClient, err := createRpcClient(newBroker.Status.PodIP)
		if err != nil {
			log.Errorf("updateBroker createRpcClient podip %s error %v", newBroker.Status.PodIP, err)
			return
		}
		bw.brokers[newBroker.Status.PodIP] = rpcClient
		log.Infof("updateBroker podip %s success", newBroker.Status.PodIP)
	}
}

func (bw *BrokerWatcher) delBroker(obj interface{}) {
	broker, ok := obj.(*v1.Pod)
	if !ok {
		log.Errorf("delBroker type %+v assertion failed", obj)
		return
	}
	delete(bw.brokers, broker.Status.PodIP)
	log.Infof("delBroker podip %s del msg %s", broker.Status.PodIP, broker.Status.Message)
}

func createRpcClient(targetIp string) (basemsgbroker.SClient, error) {
	target := fmt.Sprintf("%s:%d", targetIp, 9000)
	//ctx := service.NewClientContext(env, "")
	conn, err := grpc.Dial(
		target,
		grpc.WithInsecure(),
		grpc.WithContextDialer(func(context.Context, string) (net.Conn, error) {
			log.Infof("Dial target %s", target)
			return net.Dial("tcp", target)
		}),
		//grpc.WithUnaryInterceptor(ctx.GRPCUnaryClientInterceptor()),
		//grpc.WithStreamInterceptor(ctx.GRPCStreamClientInterceptor()),
	)
	if err != nil {
		log.Errorf("createRpcClient Dial target %s error %v", target, err)
		return nil, err
	}
	brokerClient := basemsgbroker.NewSClient(conn)
	return brokerClient, nil
}
