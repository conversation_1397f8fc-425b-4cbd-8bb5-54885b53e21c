package model

import (
	"xim/basemsgtopic/config"
	"xim/basemsgtopic/model/brokerwatcher"
	"xim/basemsgtopic/model/redis"
	"xim/basemsgtopic/model/timetask"
)

// BasemsgtopicModel 数据模型层
// 封装与业务逻辑相关的数据以及对数据的处理方法，包括内部数据模型、数据库、缓存、消息通道、grpc服务、PHP服务等等
// 按来源、按业务将 Model 分割成不同的文件以利维护
type BasemsgtopicModel struct {
}

// NewBasemsgtopicModel 创建basemsgtopic model
func NewBasemsgtopicModel(conf *config.Config) error {
	if err := redis.NewRedis(conf.Redis); err != nil {
		return err
	}
	if err := brokerwatcher.NewBrokerWatcher(conf.Broker); err != nil {
		return err
	}
	//开启定时任务，放在最后开启，需初始化上面的组件
	timetask.NewTimeTask()
	return nil
}
