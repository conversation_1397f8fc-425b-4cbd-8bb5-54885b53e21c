package timetask

import (
	"time"

	log "xim/baselib/logger"
	"xim/basemsgtopic/model/redis"
	"xim/basemsgtopic/util"
)

const (
	CLEAN_INTERVAL      = 300
	CLEAN_LOCK          = "basemsgtopic:s:lock:clean"
	CLEAN_LOCK_TIME     = 500
	RECYCLE_TIME        = 6 * 3600
	CLEAN_NUM_EACH_TIME = 3000
)

var (
	PLATFORM_IDS = []uint64{1, 2, 3, 4, 5, 6, 7, 1004} //todo 这里平台id列表写死了，后续改成配置
)

func CleanOldConnTimeTask() {
	ticker := time.NewTicker(CLEAN_INTERVAL * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			log.Infof("CleanOldConn")
			cleanOldConn()
		}
	}
}

func cleanOldConn() {
	err := redis.GetLock(CLEAN_LOCK, time.Duration(CLEAN_LOCK_TIME)*time.Second)
	if err != nil {
		log.Errorf("cleanOldConn get lock error %v", err)
		return
	}
	defer redis.DelLock(CLEAN_LOCK)

	delLastPingTime := time.Now().Unix() - RECYCLE_TIME
	log.Infof("clean old conn before ping time %d", delLastPingTime)
	for _, platformId := range PLATFORM_IDS {
		userConns, err := redis.GetPlatformUserConnBeforePingTime(platformId, delLastPingTime, 0, CLEAN_NUM_EACH_TIME)
		if err != nil {
			log.Errorf("cleanOldConn GetPlatformUserConnBeforePingTime platformid %d error %v", platformId, err)
			continue
		}
		log.Infof("clean old conn platformid %d begin", platformId)
		for _, userConn := range userConns {
			//删除用户指定长连接
			connInfo, _ := util.ParsePlatformUserConnUniqueId(userConn)
			if connInfo != nil {
				redis.DelPlatformUserConnInfo(platformId, connInfo.UserId, []*util.PlatformUserConnInfo{connInfo})
			}
			//删除用户长连接的所有订阅信息
			redis.DelPlatformUserConnAllTopics(platformId, userConn)
			//删除用户长连接的心跳记录
			redis.DelPlatformUserConnPingTime(platformId, userConn)
		}
	}

}
