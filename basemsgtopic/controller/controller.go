package controller

import (
	"context"
	"sync"

	"fmt"
	"time"

	log "xim/baselib/logger"
	baselibUtil "xim/baselib/util"
	"xim/basemsgtopic/config"
	"xim/basemsgtopic/model"
	"xim/basemsgtopic/model/brokerwatcher"
	"xim/basemsgtopic/model/redis"
	"xim/basemsgtopic/util"
	"xim/common/msg_ticker"
	"xim/proto/api/basemsgbroker"
	pb_basemsgtopic "xim/proto/api/basemsgtopic"
)

// BasemsgtopicController 服务控制层
// 用于控制业务流程、处理事件并作出响应。“事件”包括 gRPC 接口请求和数据 Model 上的改变（例如nsq事件）
// 按业务将 Controller 分割成不同的文件以利维护
type BasemsgtopicController struct {
	conf                *config.Config
	platformConnRuleMap map[uint64]int
	platformConnKickMap map[uint64]string
}

// NewBasemsgtopicController 创建basemsgtopic controller
func NewBasemsgtopicController(conf *config.Config) (*BasemsgtopicController, error) {
	c := &BasemsgtopicController{conf: conf}
	//todo 这里写死了平台各自的长连接互踢规则
	c.platformConnRuleMap = map[uint64]int{
		1:    util.PlatformConnRule_OneUserConn,
		2:    util.PlatformConnRule_OneUserDeviceConn,
		3:    util.PlatformConnRule_OneUserDeviceConn,
		4:    util.PlatformConnRule_OneUserDeviceConn,
		5:    util.PlatformConnRule_OneUserDeviceConn,
		6:    util.PlatformConnRule_OneUserDeviceConn,
		7:    util.PlatformConnRule_OneUserDeviceConn,
		1004: util.PlatformConnRule_OneUserDeviceConn,
	}
	//todo 这里写死了平台各自的踢长连接原因数据
	c.platformConnKickMap = map[uint64]string{
		1:    util.UserConnKickReason_AnotherClientLogin,
		2:    util.UserConnKickReason_AnotherClientLogin,
		3:    util.UserConnKickReason_AnotherClientLoginJson,
		4:    util.UserConnKickReason_AnotherClientLogin,
		5:    util.UserConnKickReason_AnotherClientLogin,
		6:    util.UserConnKickReason_AnotherClientLogin,
		7:    util.UserConnKickReason_AnotherClientLogin,
		1004: util.UserConnKickReason_AnotherClientLogin,
	}

	err := model.NewBasemsgtopicModel(conf)
	if err != nil {
		return nil, err
	}

	return c, nil
}

// Test 实现Test接口
func (c *BasemsgtopicController) Test(ctx context.Context, req *pb_basemsgtopic.TestReq) (*pb_basemsgtopic.TestRsp, error) {
	message := "basemsgtopic"
	return &pb_basemsgtopic.TestRsp{Result: util.RetOK, Message: message}, nil
}

func (c *BasemsgtopicController) ConnStatusReport(ctx context.Context, req *pb_basemsgtopic.ConnStatusReportReq) (*pb_basemsgtopic.ConnStatusReportRsp, error) {
	res := &pb_basemsgtopic.ConnStatusReportRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	ticker := msg_ticker.Ticket{}
	ticker.Decode([]byte(req.UserToken))
	connInfo := &util.PlatformUserConnInfo{
		PlatformId:    ticker.PlatformId,
		UserId:        ticker.UserId,
		DeviceId:      ticker.DeviceId,
		ClientId:      ticker.ExtendData.ClientId,
		ClientVersion: ticker.ExtendData.ClientVersion,
		BrokerIp:      req.BrokerIP,
		ConnId:        req.ConnId,
		ConnTime:      req.OpTime,
	}
	var err error

	switch req.Type {
	case pb_basemsgtopic.ConnStatusType_ConnectType:
		//建立连接状态上报处理
		err = c.dealConnStatus(connInfo)
	case pb_basemsgtopic.ConnStatusType_CloseType:
		//断开连接状态上报处理
		err = c.dealDisconnStatus(connInfo)
	case pb_basemsgtopic.ConnStatusType_PingType:
		//处理心跳状态上报，更新缓存的长连接心跳时间
		userConnUniqueId := util.FormatPlatformUserConnUniqueId(connInfo)
		err = redis.SetPlatformUserConnPingTime(connInfo.PlatformId, userConnUniqueId, req.OpTime)
	default:
		err = fmt.Errorf("unknown connStatusType %d", req.Type)
	}

	if err != nil {
		res.Result = util.RetConnStatusReportError
		res.Message = util.ErrorMsg[util.RetConnStatusReportError]
		return res, err
	}
	return res, nil
}

func (c *BasemsgtopicController) dealConnStatus(connInfo *util.PlatformUserConnInfo) error {
	log.Infof("dedealConnStatus connInfo=%+v", baselibUtil.JsonStr(connInfo))
	//获取用户锁（所有长连接的）
	lockkey := redis.GetPlatformUserLockKey(connInfo.PlatformId, connInfo.UserId)
	err := redis.SpinLock(lockkey, time.Duration(redis.USER_CONN_LOCK_TIME)*time.Second, redis.USER_CONN_LOCK_TRY_TIMES)
	if err != nil {
		log.Errorf("dealConnStatus SpinLock key %s error %v", lockkey, err)
		return err
	}
	defer redis.DelLock(lockkey)

	//获取用户的所有长连接
	allConnInfo, err := redis.GetPlatformUserAllConnInfo(connInfo.PlatformId, connInfo.UserId)
	if err != nil {
		log.Errorf("dealConnStatus GetPlatformUserAllConnInfo platformid %d userid %s error",
			connInfo.PlatformId, connInfo.UserId, err)
		return err
	}
	//在旧连接中，获取同设备或同平台的长连接
	redundancyConnList := make([]*util.PlatformUserConnInfo, 0)
	platformConnRule := c.platformConnRuleMap[connInfo.PlatformId]
	switch platformConnRule {
	case util.PlatformConnRule_OneUserConn:
		//同平台同用户只有一个长连接
		redundancyConnList = allConnInfo
	case util.PlatformConnRule_OneUserDeviceConn:
		//同平台同用户同设备只有一个长连接
		for _, item := range allConnInfo {
			if item.DeviceId == connInfo.DeviceId {
				redundancyConnList = append(redundancyConnList, item)
			}
		}
	}
	//检测同平台或同设备的冗余长连接
	isValidConn := true
	if len(redundancyConnList) > 0 {
		redundancyConnListstr, _ := util.Json.MarshalToString(redundancyConnList)
		log.Infof("dealConnStatus oldconn %s newconn %+v", redundancyConnListstr, connInfo)
		//检测并清除冗余长连接
		//检测当前长连接是否有效，是新的长连接
		for _, oldConnInfo := range redundancyConnList {
			if oldConnInfo.BrokerIp != connInfo.BrokerIp ||
				oldConnInfo.ConnId != connInfo.ConnId {
				if oldConnInfo.ConnTime > connInfo.ConnTime {
					isValidConn = false
					continue
				}
				brokerClient := brokerwatcher.GetBroker(oldConnInfo.BrokerIp)
				if brokerClient != nil {
					log.Infof("dealConnStatus kick invalid conn podip %s connid %d", oldConnInfo.BrokerIp, oldConnInfo.ConnId)
					// go brokerClient.Kick(context.Background(), &basemsgbroker.KickReq{
					// 	ConnId:  oldConnInfo.ConnId,
					// 	Topic:   "sessioninvalid",
					// 	Message: c.platformConnKickMap[connInfo.PlatformId],
					// })
				} else {
					log.Infof("dealConnStatus GetBroker %s is nil, del conn %+v redis data directly", oldConnInfo.BrokerIp, oldConnInfo)
					//如果长连接对应的broker不在了，直接删除这个长连接的缓存信息
					go c.dealDisconnStatus(oldConnInfo)
				}
			}
		}
	}
	//根据当前长连接是否有效，判断是否要更新到用户长连接信息
	if !isValidConn {
		//不是有效的长连接，需要断开连接
		brokerClient := brokerwatcher.GetBroker(connInfo.BrokerIp)
		if brokerClient != nil {
			log.Infof("dealConnStatus kick invalid conn podip %s connid %d", connInfo.BrokerIp, connInfo.ConnId)
			go brokerClient.Kick(context.Background(), &basemsgbroker.KickReq{
				ConnId:  connInfo.ConnId,
				Topic:   "sessioninvalid",
				Message: c.platformConnKickMap[connInfo.PlatformId],
			})
		} else {
			log.Infof("dealConnStatus GetBroker %s is nil", connInfo.BrokerIp)
		}
		return nil
	}
	//更新用户长连接信息
	err = redis.SetPlatformUserConnInfo(connInfo.PlatformId, connInfo.UserId, []*util.PlatformUserConnInfo{connInfo}, 3*util.TIME_ONE_DAY)
	if err != nil {
		log.Errorf("dealConnStatus SetPlatformUserConnInfo conninfo %+v error %v", connInfo, err)
		return err
	}
	//记录当前长连接的时间到ping心跳缓存
	userConnUniqueId := util.FormatPlatformUserConnUniqueId(connInfo)
	err = redis.SetPlatformUserConnPingTime(connInfo.PlatformId, userConnUniqueId, connInfo.ConnTime)
	if err != nil {
		log.Errorf("dealConnStatus SetPlatformUserConnPingTime userconnid %s error %v", userConnUniqueId, err)
		return err
	}
	return nil
}

func (c *BasemsgtopicController) dealDisconnStatus(connInfo *util.PlatformUserConnInfo) error {
	userConnUniqueId := util.FormatPlatformUserConnUniqueId(connInfo)

	//获取用户长连接锁
	lockkey := redis.GetPlatformUserConnLockKey(userConnUniqueId)
	err := redis.SpinLock(lockkey, time.Duration(redis.USER_CONN_LOCK_TIME)*time.Second, redis.USER_CONN_LOCK_TRY_TIMES)
	if err != nil {
		log.Errorf("dealDisconnStatus SpinLock key %s error %v", lockkey, err)
		return err
	}
	defer redis.DelLock(lockkey)

	//删除长连接信息，topic信息，和ping心跳信息
	redis.DelPlatformUserConnInfo(connInfo.PlatformId, connInfo.UserId, []*util.PlatformUserConnInfo{connInfo})
	redis.DelPlatformUserConnAllTopics(connInfo.PlatformId, userConnUniqueId)
	redis.DelPlatformUserConnPingTime(connInfo.PlatformId, userConnUniqueId)
	return nil
}

func (c *BasemsgtopicController) Subscribe(ctx context.Context, req *pb_basemsgtopic.SubscribeReq) (*pb_basemsgtopic.SubscribeRsp, error) {
	res := &pb_basemsgtopic.SubscribeRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	//检查topic参数
	if len(req.Topics) <= 0 {
		res.Result = util.RetInvalidParam
		res.Message = util.ErrorMsg[util.RetInvalidParam]
		return res, nil
	}

	//获取平台用户的长连接列表，如果有指定设备，会做筛选
	connsInfo, err := redis.GetPlatformUserAllConnInfo(req.PlatformId, req.UserId)
	if err != nil {
		res.Result = util.RetCacheReadError
		res.Message = util.ErrorMsg[util.RetCacheReadError]
		return res, err
	}
	if req.DeviceId != "" {
		spConnsInfo := make([]*util.PlatformUserConnInfo, 0)
		for _, conn := range connsInfo {
			if conn.DeviceId == req.DeviceId {
				spConnsInfo = append(spConnsInfo, conn)
			}
		}
		connsInfo = spConnsInfo
	}
	if len(connsInfo) <= 0 {
		res.Result = util.RetUserConnEmpty
		res.Message = util.ErrorMsg[util.RetUserConnEmpty]
		return res, nil
	}

	//用户长连接订阅topic
	errList := make([]error, 0)
	for _, conn := range connsInfo {
		userConnUniqueId := util.FormatPlatformUserConnUniqueId(conn)
		err = redis.PlatformUserConnSubscribeTopic(req.PlatformId, userConnUniqueId, req.Topics)
		if err != nil {
			errList = append(errList, err)
		}
	}
	if len(errList) > 0 {
		res.Result = util.RetSubscribeError
		res.Message = util.ErrorMsg[util.RetSubscribeError] + fmt.Sprintf(" error:%+v", errList)
		return res, errList[0]
	}
	return res, nil
}

func (c *BasemsgtopicController) UnSubscribe(ctx context.Context, req *pb_basemsgtopic.UnSubscribeReq) (*pb_basemsgtopic.UnSubscribeRsp, error) {
	res := &pb_basemsgtopic.UnSubscribeRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	//检查topic参数
	if len(req.Topics) <= 0 {
		res.Result = util.RetInvalidParam
		res.Message = util.ErrorMsg[util.RetInvalidParam]
		return res, nil
	}

	//获取平台用户的长连接列表，如果有指定设备，会做筛选
	connsInfo, err := redis.GetPlatformUserAllConnInfo(req.PlatformId, req.UserId)
	if err != nil {
		res.Result = util.RetCacheReadError
		res.Message = util.ErrorMsg[util.RetCacheReadError]
		return res, err
	}
	if req.DeviceId != "" {
		spConnsInfo := make([]*util.PlatformUserConnInfo, 0)
		for _, conn := range connsInfo {
			if conn.DeviceId == req.DeviceId {
				spConnsInfo = append(spConnsInfo, conn)
			}
		}
		connsInfo = spConnsInfo
	}
	if len(connsInfo) <= 0 {
		res.Result = util.RetUserConnEmpty
		res.Message = util.ErrorMsg[util.RetUserConnEmpty]
		return res, nil
	}

	//用户长连接取消订阅topic
	errList := make([]error, 0)
	for _, conn := range connsInfo {
		userConnUniqueId := util.FormatPlatformUserConnUniqueId(conn)
		err = redis.PlatformUserConnUnSubscribeTopic(req.PlatformId, userConnUniqueId, req.Topics)
		if err != nil {
			errList = append(errList, err)
		}
	}
	if len(errList) > 0 {
		res.Result = util.RetUnSubscribeError
		res.Message = util.ErrorMsg[util.RetUnSubscribeError] + fmt.Sprintf(" error:%+v", errList)
		return res, errList[0]
	}
	return res, nil
}

func (c *BasemsgtopicController) TopicUsers(ctx context.Context, req *pb_basemsgtopic.TopicUsersReq) (*pb_basemsgtopic.TopicUsersRsp, error) {
	res := &pb_basemsgtopic.TopicUsersRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}

	userConnUniqueIds, err := redis.GetTopicAllUserConns(req.PlatformId, req.Topic)
	if err != nil {
		res.Result = util.RetCacheReadError
		res.Message = util.ErrorMsg[util.RetCacheReadError]
		return res, err
	}
	res.Data = &pb_basemsgtopic.TopicUsersData{UserIds: userConnUniqueIds}
	return res, nil
}

func (c *BasemsgtopicController) StreamTopicUsers(req *pb_basemsgtopic.TopicUsersReq, stream pb_basemsgtopic.S_StreamTopicUsersServer) error {
	res := &pb_basemsgtopic.TopicUsersRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	var start int64 = 0
	var limit int64 = 100
	for {
		stop := start + limit - 1
		userConnUniqueIds, err := redis.GetTopicLimitNumUserConns(req.PlatformId, req.Topic, start, stop)
		if err != nil {
			log.Errorf("StreamTopicUsers req %+v cacheUsers [%d,%d] error %v", req, start, stop, err)
			start = start + limit
			continue
		}
		if len(userConnUniqueIds) > 0 {
			res.Data = &pb_basemsgtopic.TopicUsersData{UserIds: userConnUniqueIds}
			err = stream.Send(res)
			if err != nil {
				log.Errorf("StreamTopicUsers req %+v stream [%d,%d] send error %v", req, start, stop, err)
			}
		}
		start = start + limit
		if len(userConnUniqueIds) < int(limit) {
			break
		}
	}
	return nil
}

func (c *BasemsgtopicController) StreamTopicUsersWithConnInfo(req *pb_basemsgtopic.TopicUsersReq, stream pb_basemsgtopic.S_StreamTopicUsersWithConnInfoServer) error {
	res := &pb_basemsgtopic.TopicUsersWithConnInfoRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	var start int64 = 0
	var limit int64 = 100
	for {
		stop := start + limit - 1
		userConnUniqueIds, err := redis.GetTopicLimitNumUserConns(req.PlatformId, req.Topic, start, stop)
		if err != nil {
			log.Errorf("StreamTopicUsersWithConnInfo req %+v cacheUsers [%d,%d] error %v", req, start, stop, err)
			start = start + limit
			continue
		}
		if len(userConnUniqueIds) > 0 {
			conns := batchGetConnInfoByConnUniqueId(userConnUniqueIds)
			if len(conns) > 0 {
				res.Data = &pb_basemsgtopic.TopicUsersWithConnInfoData{Conns: conns}
				err = stream.Send(res)
				if err != nil {
					log.Errorf("StreamTopicUsersWithConnInfo req %+v stream [%d,%d] send error %v", req, start, stop, err)
				}
			}
		}
		start = start + limit
		if len(userConnUniqueIds) < int(limit) {
			break
		}
	}
	return nil
}

func (c *BasemsgtopicController) GetPlatformUserConn(ctx context.Context, req *pb_basemsgtopic.GetPlatformUserConnReq) (*pb_basemsgtopic.GetPlatformUserConnRsp, error) {
	res := &pb_basemsgtopic.GetPlatformUserConnRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	//检查参数
	if req.PlatformId <= 0 || req.UserId == "" {
		res.Result = util.RetInvalidParam
		res.Message = util.ErrorMsg[util.RetInvalidParam]
		return res, nil
	}

	//获取平台用户的长连接列表
	userConnInfoList, err := redis.GetPlatformUserAllConnInfo(req.PlatformId, req.UserId)
	if err != nil {
		res.Result = util.RetCacheReadError
		res.Message = util.ErrorMsg[util.RetCacheReadError]
		return res, err
	}
	//筛选目标长连接
	targetConnList := make([]*pb_basemsgtopic.PlatformUserConn, 0)
	for _, conn := range userConnInfoList {
		isTarget := false
		//请求设备为空，或长连接的设备和请求设备相同，才是目标长连接
		if req.DeviceId == "" || req.DeviceId == conn.DeviceId {
			isTarget = true
		}
		if isTarget {
			targetConnList = append(targetConnList, &pb_basemsgtopic.PlatformUserConn{
				PlatformId:    conn.PlatformId,
				UserId:        conn.UserId,
				DeviceId:      conn.DeviceId,
				ClientId:      conn.ClientId,
				ClientVersion: conn.ClientVersion,
				BrokerIp:      conn.BrokerIp,
				ConnId:        conn.ConnId,
				ConnTime:      conn.ConnTime,
			})
		}
	}

	res.Data = &pb_basemsgtopic.PlatformUserConnData{Conns: targetConnList}
	return res, nil
}

func (c *BasemsgtopicController) UserTopics(ctx context.Context, req *pb_basemsgtopic.UserTopicsReq) (*pb_basemsgtopic.UserTopicsRsp, error) {
	res := &pb_basemsgtopic.UserTopicsRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	//检查参数
	if req.PlatformId <= 0 || req.UserId == "" {
		res.Result = util.RetInvalidParam
		res.Message = util.ErrorMsg[util.RetInvalidParam]
		return res, nil
	}

	//获取平台用户的长连接列表，如果有指定设备，会做筛选
	connsInfo, err := redis.GetPlatformUserAllConnInfo(req.PlatformId, req.UserId)
	if err != nil {
		res.Result = util.RetCacheReadError
		res.Message = util.ErrorMsg[util.RetCacheReadError]
		return res, err
	}
	if req.DeviceId != "" {
		spConnsInfo := make([]*util.PlatformUserConnInfo, 0)
		for _, conn := range connsInfo {
			if conn.DeviceId == req.DeviceId {
				spConnsInfo = append(spConnsInfo, conn)
			}
		}
		connsInfo = spConnsInfo
	}
	if len(connsInfo) <= 0 {
		res.Result = util.RetUserConnEmpty
		res.Message = util.ErrorMsg[util.RetUserConnEmpty]
		return res, nil
	}

	userConnUniqueIds := make([]string, 0)
	userConnUniqueIdMap := make(map[string]*util.PlatformUserConnInfo, 0)
	for _, connInfo := range connsInfo {
		userConnUniqueId := util.FormatPlatformUserConnUniqueId(connInfo)
		userConnUniqueIds = append(userConnUniqueIds, userConnUniqueId)
		userConnUniqueIdMap[userConnUniqueId] = connInfo
	}
	//批量查询订阅topic
	userConnTopicMap, err := redis.GetMultiPlatformUserConnsAllTopics(userConnUniqueIds)
	if err != nil {
		res.Result = util.RetCacheReadError
		res.Message = util.ErrorMsg[util.RetCacheReadError]
		return res, err
	}
	list := make([]*pb_basemsgtopic.UserDeviceTopicsData, 0)
	for userConnUniqueId, topics := range userConnTopicMap {
		if userConnUniqueIdMap[userConnUniqueId] != nil && len(topics) > 0 {
			userDeviceTopics := &pb_basemsgtopic.UserDeviceTopicsData{
				PlatformId: userConnUniqueIdMap[userConnUniqueId].PlatformId,
				UserId:     userConnUniqueIdMap[userConnUniqueId].UserId,
				DeviceId:   userConnUniqueIdMap[userConnUniqueId].DeviceId,
				Topics:     topics,
			}
			list = append(list, userDeviceTopics)
		}
	}
	res.Data = &pb_basemsgtopic.UserTopicsData{List: list}
	return res, nil
}

// 批量根据ConnUniqueId长连接唯一标识id查询长连接信息，异步并发查询
func batchGetConnInfoByConnUniqueId(userConnUniqueIds []string) []*pb_basemsgtopic.PlatformUserConn {
	wg := sync.WaitGroup{}
	syncConnsMap := sync.Map{}
	for _, connUniqueId := range userConnUniqueIds {
		if parseConn, _ := util.ParsePlatformUserConnUniqueId(connUniqueId); parseConn != nil {
			wg.Add(1)
			go func(_platformId uint64, _userId, _connUniqueId string) {
				defer wg.Done()
				tempConn, _ := redis.GetPlatformUserConnInfo(_platformId, _userId, _connUniqueId)
				if tempConn != nil {
					conn := &pb_basemsgtopic.PlatformUserConn{
						PlatformId:    tempConn.PlatformId,
						UserId:        tempConn.UserId,
						DeviceId:      tempConn.DeviceId,
						ClientId:      tempConn.ClientId,
						ClientVersion: tempConn.ClientVersion,
						BrokerIp:      tempConn.BrokerIp,
						ConnId:        tempConn.ConnId,
						ConnTime:      tempConn.ConnTime,
					}
					syncConnsMap.Store(_connUniqueId, conn)
				}
			}(parseConn.PlatformId, parseConn.UserId, connUniqueId)
		}
	}
	wg.Wait()

	conns := make([]*pb_basemsgtopic.PlatformUserConn, 0)
	syncConnsMap.Range(func(k, v interface{}) bool {
		conn := v.(*pb_basemsgtopic.PlatformUserConn)
		conns = append(conns, conn)
		return true
	})
	return conns
}
