package server

import (
	"time"

	log "xim/baselib/logger"
	"xim/basemsgtopic/config"
	"xim/basemsgtopic/controller"
	"xim/basemsgtopic/stat"
	pb_basemsgtopic "xim/proto/api/basemsgtopic"

	"golang.org/x/net/context"
)

// BasemsgtopicServer 服务接入层
// 负责实现proto协议中定义的grpc接口，检查请求参数，拦截非法请求，记录访问日志
type BasemsgtopicServer struct {
	controller *controller.BasemsgtopicController
}

// NewBasemsgtopicServer 创建basemsgtopic服务
// 初始化controller和业务自定义模块
func NewBasemsgtopicServer(conf *config.Config) (*BasemsgtopicServer, error) {
	s := &BasemsgtopicServer{}

	var err error
	s.controller, err = controller.NewBasemsgtopicController(conf)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Test 实现Test接口
func (s *BasemsgtopicServer) Test(ctx context.Context, req *pb_basemsgtopic.TestReq) (*pb_basemsgtopic.TestRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.TestRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("Test req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.Test(ctx, req)
	return res, nil
}

// 用户长连接状态上报接口
func (s *BasemsgtopicServer) ConnStatusReport(ctx context.Context, req *pb_basemsgtopic.ConnStatusReportReq) (*pb_basemsgtopic.ConnStatusReportRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.ConnStatusReportRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("ConnStatusReport req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.ConnStatusReport(ctx, req)
	return res, nil
}

// 用户订阅topic
func (s *BasemsgtopicServer) Subscribe(ctx context.Context, req *pb_basemsgtopic.SubscribeReq) (*pb_basemsgtopic.SubscribeRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.SubscribeRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("Subscribe req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.Subscribe(ctx, req)
	return res, nil
}

// 用户取消订阅topc
func (s *BasemsgtopicServer) UnSubscribe(ctx context.Context, req *pb_basemsgtopic.UnSubscribeReq) (*pb_basemsgtopic.UnSubscribeRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.UnSubscribeRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("UnSubscribe req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.UnSubscribe(ctx, req)
	return res, nil
}

// 获取topic下的所有用户
func (s *BasemsgtopicServer) TopicUsers(ctx context.Context, req *pb_basemsgtopic.TopicUsersReq) (*pb_basemsgtopic.TopicUsersRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.TopicUsersRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("TopicUsers req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.TopicUsers(ctx, req)
	return res, nil
}

// stream方式获取topic下的所有用户
func (s *BasemsgtopicServer) StreamTopicUsers(req *pb_basemsgtopic.TopicUsersReq, stream pb_basemsgtopic.S_StreamTopicUsersServer) error {
	log.Infof("StreamTopicUsers req %+v start", req)
	return s.controller.StreamTopicUsers(req, stream)
}

// stream方式获取topic下的所有用户，带有详细的长连接信息
func (s *BasemsgtopicServer) StreamTopicUsersWithConnInfo(req *pb_basemsgtopic.TopicUsersReq, stream pb_basemsgtopic.S_StreamTopicUsersWithConnInfoServer) error {
	log.Infof("StreamTopicUsersWithConnInfo req %+v start", req)
	return s.controller.StreamTopicUsersWithConnInfo(req, stream)
}

// 查询平台用户的长连接信息
func (s *BasemsgtopicServer) GetPlatformUserConn(ctx context.Context, req *pb_basemsgtopic.GetPlatformUserConnReq) (*pb_basemsgtopic.GetPlatformUserConnRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.GetPlatformUserConnRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("GetPlatformUserConn req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.GetPlatformUserConn(ctx, req)
	return res, nil
}

// 查询平台用户的订阅topic列表
func (s *BasemsgtopicServer) UserTopics(ctx context.Context, req *pb_basemsgtopic.UserTopicsReq) (*pb_basemsgtopic.UserTopicsRsp, error) {
	var (
		err       error
		res       *pb_basemsgtopic.UserTopicsRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("UserTopics req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.UserTopics(ctx, req)
	return res, nil
}
