package database

import (
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/server/env"

	"github.com/prometheus/client_golang/prometheus/collectors"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"moul.io/zapgorm2"
)

var (
	mysqlDBs = make(map[string]*gorm.DB)
)

func InitMysql(configs []*config.MysqlConfig) {
	if len(configs) == 0 {
		logger.Warnf("no mysql configs")
		return
	}
	for _, config := range configs {
		gormConfig := mysql.Config{
			DSN:                       config.Dsn(), // DSN data source name
			DefaultStringSize:         191,          // string 类型字段的默认长度
			DisableDatetimePrecision:  true,         // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
			DontSupportRenameIndex:    true,         // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
			DontSupportRenameColumn:   true,         // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
			SkipInitializeWithVersion: false,        // 根据版本自动配置
		}
		l := zapgorm2.New(logger.Logger().Named("gorm").With(zap.String("db", config.DbName)))
		c := gormOptions(l)
		if config.DbName == "new_stats" {
			c.PrepareStmt = false
		}
		if db, err := gorm.Open(mysql.New(gormConfig), c); err == nil {
			sqlDB, _ := db.DB()
			if config.MaxIdleConns > 0 {
				sqlDB.SetMaxIdleConns(config.MaxIdleConns)
			}
			if config.MaxOpenConns > 0 {
				sqlDB.SetMaxOpenConns(config.MaxOpenConns)
			}
			sqlDB.SetConnMaxIdleTime(time.Minute * 20)
			if config.Name == "" {
				config.Name = config.DbName
			}
			metric.MustRegister(collectors.NewDBStatsCollector(sqlDB, config.Name+"_"+config.Host))

			mysqlDBs[config.Name] = db
			logger.Info("Mysql connected sucess", zap.Any("db-name", config.DbName), zap.Any("dsn", config.Dsn()))
		} else {
			logger.Panicf("MySQL Connection Exception", zap.Any("err", err))
			return
		}
	}
}

func gormOptions(logger gormlogger.Interface) *gorm.Config {
	if !env.IsProd() {
		logger = gormlogger.Default.LogMode(gormlogger.Info)
	}
	config := &gorm.Config{
		SkipDefaultTransaction:                   false,
		NamingStrategy:                           nil,
		FullSaveAssociations:                     false,
		Logger:                                   logger,
		NowFunc:                                  nil,
		DryRun:                                   false,
		PrepareStmt:                              false,
		DisableAutomaticPing:                     false,
		DisableForeignKeyConstraintWhenMigrating: true,
		DisableNestedTransaction:                 false,
		AllowGlobalUpdate:                        false,
		QueryFields:                              false,
		CreateBatchSize:                          0,
		ClauseBuilders:                           nil,
		ConnPool:                                 nil,
		Dialector:                                nil,
		Plugins:                                  nil,
	}
	return config
}

func GetMysqlDB(name string) *gorm.DB {
	if db, ok := mysqlDBs[name]; ok {
		return db
	}
	return nil
}

type BaseDao interface {
	Ready() *gorm.DB
}

type LogicTransaction struct {
	Tx *gorm.DB
}

func (trans *LogicTransaction) Finish(err error) error {
	if err != nil {
		return trans.Tx.Rollback().Error
	}
	return trans.Tx.Commit().Error
}
