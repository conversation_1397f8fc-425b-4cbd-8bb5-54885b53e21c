package database

import (
	"context"
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
)

var (
	clickhouseDbs = make(map[string]driver.Conn)
)

func GetClickhouse(name string) driver.Conn {
	if db, ok := clickhouseDbs[name]; ok {
		return db
	}
	return nil
}

func InitClickhouse(configs []config.ClickhouseConfig) (err error) {
	for _, c := range configs {
		option := &clickhouse.Options{
			TLS:  nil,
			Addr: []string{c.Addr},
			Auth: clickhouse.Auth{
				Database: c.DbName,
				Username: c.User,
				Password: c.Password,
			},
			DialContext:      nil,
			Debug:            false,
			Debugf:           nil,
			Settings:         nil,
			Compression:      nil,
			DialTimeout:      3 * time.Second,
			MaxOpenConns:     c.<PERSON>,
			MaxIdleConns:     c.<PERSON>,
			ConnMaxLifetime:  time.Minute * 10,
			ConnOpenStrategy: 0,
		}
		conn, err := clickhouse.Open(option)
		if err != nil {
			logger.Panicf("error %v", err)
			return err
		}
		err = conn.Ping(context.Background())
		if err != nil {
			logger.Panicf("error %v", err)
			return err
		}
		clickhouseDbs[option.Auth.Database] = conn
	}
	return nil
}
