package mongo

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/util"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

const (
	DEFAULT_POOLSIZE       = 10
	DEFAULT_TIMEOUT        = 30 * time.Second
	DEFAULT_SOCKET_TIMEOUT = 10 * time.Second
)

// var (
// 	client *mongo.Client
// )

// func GetMongoDBClient() *mongo.Client {
// 	return client
// }

// func InitMongoDB(config *config.MongodbConfig) {
// 	if config == nil {
// 		logger.Warnf("no found mongodb config")
// 		return
// 	}

// 	var err error
// 	option := options.Client().
// 		SetHosts([]string{fmt.Sprintf("%s:%d", config.Host, config.Port)}).
// 		SetMinPoolSize(uint64(config.PoolSize)).
// 		SetMaxPoolSize(uint64(config.PoolSize)).
// 		SetConnectTimeout(time.Second * 10).
// 		SetSocketTimeout(time.Second * 10).
// 		SetMaxConnIdleTime(time.Hour)
// 	client, err = mongo.Connect(context.TODO(), option)
// 	if err != nil {
// 		logger.Panicf("error call mongo.Connect %v", err)
// 	}
// 	if err := client.Ping(context.TODO(), readpref.Primary()); err != nil {
// 		logger.Panicf("error call mongodb.client.ping %v", err)
// 	}
// 	client.Database(config.DB)
// }

func InitMongoDB(config *config.MongodbConfig, opts ...*options.ClientOptions) {
	if config == nil {
		logger.Warnf("no found mongodb config")
		return
	}

	logger.Infof("InitMongoDB %+v", *config)
	var err error
	pwd := config.Password
	if strings.HasPrefix(pwd, "crypt-") { //password
		pwd, err = util.Decrypt(pwd, mongoKey)
		if err != nil {
			logger.Panicf("decrypt pwd err:%v", err)
			return
		}
	}

	timeout := time.Duration(config.Timeout) * time.Second
	if timeout <= 0 {
		timeout = DEFAULT_SOCKET_TIMEOUT
	}
	maxPoolsize := config.MaxPoolSize
	if maxPoolsize <= 0 {
		maxPoolsize = DEFAULT_POOLSIZE
	}
	minPoolSize := config.MinPoolSize
	if minPoolSize <= 0 {
		minPoolSize = DEFAULT_POOLSIZE / 2
	}
	maxConnecting := config.MaxConnecting
	if maxConnecting <= 0 {
		maxConnecting = DEFAULT_POOLSIZE / 2
	}

	option := options.Client().ApplyURI(config.Host)
	option.SetConnectTimeout(timeout)
	option.SetSocketTimeout(timeout)
	option.SetMaxPoolSize(uint64(maxPoolsize))
	option.SetMinPoolSize(uint64(minPoolSize))
	option.SetMaxConnecting(uint64(maxConnecting))
	option.SetMaxConnIdleTime(time.Minute * 10)

	if config.User != "" {
		option.SetAuth(options.Credential{
			Username: config.User,
			Password: pwd,
		})
	}

	if config.SecondaryPreferred {
		option.SetReadPreference(readpref.SecondaryPreferred())
	}

	if len(opts) > 0 {
		var tmpOpts []*options.ClientOptions
		tmpOpts = append(tmpOpts, option)
		tmpOpts = append(tmpOpts, opts...)
		option = options.MergeClientOptions(tmpOpts...)
	}

	conn, err := mongo.NewClient(option)
	if err != nil {
		logger.Panicf("new client err:%v", err)
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	err = conn.Connect(ctx)
	if err != nil {
		logger.Panicf("new get connect err:%v", err)
		return
	}
	err = conn.Ping(context.Background(), nil)
	if err != nil {
		logger.Panicf("ping err:%v", err)
		return
	}

	pool := make(chan bool, maxPoolsize)
	for i := 0; i < maxPoolsize; i++ {
		pool <- true
	}

	_, file, line, _ := runtime.Caller(1)
	if subs := strings.Split(file, "/"); len(subs) > 2 {
		file = subs[len(subs)-2] + "/" + subs[len(subs)-1]
	}

	client = &Client{
		conn:         conn,
		pool:         pool,
		timeout:      timeout,
		metricTarget: fmt.Sprintf("%s:%d", file, line),
	}
}
