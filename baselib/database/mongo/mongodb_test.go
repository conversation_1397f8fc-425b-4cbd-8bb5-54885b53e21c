package mongo

import (
	"context"
	"log"
	"testing"
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/util"

	"go.mongodb.org/mongo-driver/bson"
)

// go test -v  -run TestMongoPwd
func TestMongoPwd(t *testing.T) {
	ci, err := util.Encrypt("uqDWqJl#yDiS", mongoKey)
	if err != nil {
		log.Fatalf("encrypt err:%v", err)
		return
	}
	log.Printf(ci)
	pt, err := util.Decrypt(ci, mongoKey)
	if err != nil {
		log.Fatalf("encrypt err:%v", err)
		return
	}
	log.Printf(pt)
}

// go test -v  -run TestMongo
func TestMongo(t *testing.T) {
	config.Conf = "../../../svcstats/deploy/dev/config.yml"
	config.Init()
	c := config.GetServerConfig()
	logger.InitLogger(c.<PERSON>)

	InitMongoDB(c.Mongodb)
	cl := GetMongoDBClient()
	conn := cl.NewCollectionWrapper("stats", "stats_test")

	_, err := conn.InsertOne(context.Background(), bson.M{"_id": 5, "ct": time.Now().Unix()})
	if err != nil {
		logger.Errorf("%v", err)
	}

}
