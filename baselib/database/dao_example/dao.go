package dao_example

import (
	"xim/baselib/database"

	"gorm.io/gorm"
)

func (b UserDBDao) Ready() *gorm.DB {
	if b.db == nil {
		b.db = database.GetMysqlDB(b.dbName())
	}
	return b.db
}

type UserDBDao struct {
	db *gorm.DB
}

func (UserDBDao) dbName() string {
	return "user_db"
}

type UserAddressDao struct {
	UserDBDao
}

func NewUserAddressDao(db *gorm.DB) *UserAddressDao {
	res := &UserAddressDao{
		UserDBDao: UserDBDao{
			db: db,
		},
	}
	return res
}

func (u UserAddressDao) Query() (list []int, err error) {
	err = u.Ready().Where("").Find(&list).Error
	return nil, nil
}
