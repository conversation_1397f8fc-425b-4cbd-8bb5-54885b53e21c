package server

import (
	"net"
	"net/http"
	"strings"

	_ "net/http/pprof"

	"xim/baselib/logger"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
)

func StartPprof() {
	go func() {
		err := http.ListenAndServe("0.0.0.0:6060", nil)
		if err != nil {
			logger.Errorf("Listen %v", err)
		}
	}()
}

func StartHttp(f func(conn *grpc.ClientConn, mux *runtime.ServeMux)) {
	l, err := net.Listen("tcp", ":8080")
	if err != nil {
		logger.Panicf("panic error %v", err)
		return
	}
	logger.Infof("http listen at %v", l.Addr())
	m := runtime.NewServeMux(runtime.WithIncomingHeaderMatcher(func(s string) (string, bool) {
		var header = map[string]bool{
			"x-token":   true,
			"admintest": true,
			"partner":   true,
			"userid":    true,
			"sex":       true,
		}
		if header[strings.ToLower(s)] {
			return s, true
		}
		return "", false
		//if s == "X-Token" {
		//	return "x-token", true
		//} else if s == "Admintest" {
		//	return s, true
		//}
		//return s, false
	}))
	conn, err := grpc.Dial("localhost:9000", grpc.WithBlock(), grpc.WithInsecure())
	if err != nil {
		logger.Panicf("%v", err)
	}
	//svcmember.RegisterSHandlerClient(context.Background(), m, svcmember.NewSClient(conn))
	f(conn, m)
	http.Serve(l, m)
}
