/*
 * @Author: genobili <EMAIL>
 * @Date: 2024-08-29 18:58:15
 * @LastEditors: genobili <EMAIL>
 * @LastEditTime: 2025-03-27 14:59:54
 * @FilePath: /xim/baselib/server/interceptor/log.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package interceptor

import (
	"context"
	"fmt"
	"time"
	"xim/baselib/trace"

	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/baselib/server/usermd"

	"github.com/pborman/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

var (
	protoJsonMarshalOption = protojson.MarshalOptions{
		Multiline:       false,
		Indent:          "",
		AllowPartial:    false,
		UseProtoNames:   true,
		UseEnumNumbers:  false,
		EmitUnpopulated: false,
		Resolver:        nil,
	}
	closeClientLog bool
)

func SetCloseServerLog() {
	closeClientLog = true
}

var (
	noCallLogMap = map[string]bool{
		"/vc.svcpush.s/PushToUser": true,
	}
)

func ServerLog(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	span := trace.SpanFromContext(ctx)
	defer span.End()
	traceId := trace.GetTraceID(span)
	goId := logger.GoID()
	if traceId != "" {
		traceId2 := logger.GetTrace(goId)
		if traceId2 != traceId {
			logger.SetTrace(goId, traceId)
		}
	} else {
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			ms, ok := md[logger.VcTraceKey]
			if ok && len(ms) > 0 {
				traceId = ms[0]
			}
		}
		if traceId == "" {
			traceId = uuid.NewUUID().String()
		}
		logger.SetTrace(goId, traceId)
	}
	st := time.Now()
	ctx = usermd.SetUserContext(ctx, &usermd.UserContext{Userid: 0})
	reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
	trace.AddSpanEvents(span, "message", map[string]string{
		"req": string(reqData),
	})

	resp, err := handler(ctx, req)

	respData, _ := protoJsonMarshalOption.Marshal(resp.(proto.Message))
	trace.AddSpanEvents(span, "message", map[string]string{
		"resp": string(respData),
	})

	userid := usermd.GetUserIdFromContext(ctx)
	trace.AddSpanTags(span, map[string]string{
		"userid": fmt.Sprintf("%d", userid),
	})

	if !closeClientLog && (!env.IsProd() || !noCallLogMap[info.FullMethod]) {
		timeCost := time.Since(st)
		logger.Infof("request-log fromUserid %d method:%s,timeCost %dms,error:%v,req:%s,resp:%s", userid, info.FullMethod, timeCost.Milliseconds(), err, string(reqData), string(respData))
		if timeCost >= time.Millisecond*500 {
			logger.Infof("long_time request-log fromUserid %d method:%s,timeCost %dms,error:%v,req:%s,resp:%s", userid, info.FullMethod, timeCost.Milliseconds(), err, string(reqData), string(respData))
		}
	}

	logger.DeleteTrace(goId)
	return resp, err
}
