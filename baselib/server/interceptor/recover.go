package interceptor

import (
	"context"
	"runtime"

	"xim/baselib/dingding"
	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
)

func Recover(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	defer func() {
		if r := recover(); r != nil {
			printStack()
			resp = &common.SvcCommonResp{
				Base: &common.SvcBaseResp{
					Code: errcode.ErrorInternal.Code,
					Msg:  errcode.ErrorInternal.Msg,
				},
			}
		}
	}()
	resp, err = handler(ctx, req)
	return
}

func RecoverWithResp(errorResp interface{}) func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				msg := printStack()
				notifyPanic(info.FullMethod, req, msg)
				resp = errorResp
			}
		}()
		resp, err = handler(ctx, req)
		return
	}
}

func RecoverWithRespFunc(f func(stack string) interface{}) func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				msg := printStack()
				resp = f(msg)
				notifyPanic(info.FullMethod, req, msg)
			}
		}()
		resp, err = handler(ctx, req)

		return
	}
}

func printStack() string {
	var buf [4096]byte
	n := runtime.Stack(buf[:], false)
	stackMsg := string(buf[:n])
	logger.Errorf(stackMsg)
	return stackMsg
}

func notifyPanic(method string, req interface{}, msg string) {
	reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
	go dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: "panic error \n\n" +
				"method " + method + "\n\n" +
				" host " + env.GetHostName() + "\n\n" +
				"env " + string(env.GetEnvironment()) + "\n\n" +
				"req: " + string(reqData) + "\n\n" + msg,
		},
	})
}
