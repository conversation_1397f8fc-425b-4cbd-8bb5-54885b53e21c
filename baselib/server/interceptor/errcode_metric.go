package interceptor

import (
	"context"
	"strconv"
	"strings"

	"xim/baselib/dingding"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/server/env"
	"xim/baselib/util"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
)

func filterDingdingMsg(msg string) (isReturn bool) {
	if msg == "rpc error: code = Canceled desc = context canceled" {
		return true
	}
	return
}

func SvcErrcodeMetric(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	resp, err := handler(ctx, req)
	var errcode int32 = -1
	svcResp, ok := resp.(interface {
		GetBase() *common.SvcBaseResp
	})
	msg := ""
	if ok && svcResp != nil && svcResp.GetBase() != nil {
		baseResp := svcResp.GetBase()
		errcode = baseResp.Code
		msg = baseResp.Msg
	}
	sp := strings.Split(info.FullMethod, "/")
	metric.CounterWithLabels("grpc_svc_server_errcode", map[string]string{
		"grpc_service": sp[1],
		"grpc_method":  info.FullMethod,
		"errcode":      strconv.FormatInt(int64(errcode), 10),
	}).Inc()

	notifyErrorCode(errcode, info.FullMethod, msg, req, resp)
	return resp, err
}

func BizErrcodeMetric(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	resp, err := handler(ctx, req)
	var errcode int32 = -1
	msg := ""
	svcResp, ok := resp.(interface {
		GetCode() int32
		GetMsg() string
	})
	if ok && svcResp != nil {
		errcode = svcResp.GetCode()
		msg = svcResp.GetMsg()
	} else {
		res, ok := resp.(interface {
			GetResult() int32
		})
		if ok && res != nil {
			errcode = res.GetResult()
		}
	}
	sp := strings.Split(info.FullMethod, "/")
	metric.CounterWithLabels("grpc_svc_server_errcode", map[string]string{
		"grpc_service": sp[1],
		"grpc_method":  info.FullMethod,
		"errcode":      strconv.FormatInt(int64(errcode), 10),
	}).Inc()
	notifyErrorCode(errcode, info.FullMethod, msg, req, resp)
	return resp, err
}

func notifyErrorCode(code int32, method, msg string, req, resp interface{}) {
	switch code {
	case errcode.ErrorInternal.Code:
	default:
		return
	}
	if filterDingdingMsg(msg) {
		return
	}
	trace := logger.GetCurrentTrace()
	go func() {
		reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
		dingding.RobotClient().Send(dingding.DingDingMsg{
			MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
			Text: dingding.Text{
				Content: "Internal Error \n\n" +
					"host " + env.GetHostName() + "\n\n" +
					"env " + string(env.GetEnvironment()) + "\n\n" +
					"method " + method + "\n\n" +
					"trace:" + trace + "\n\n" +
					"req: " + string(reqData) + "\n\n" +
					"resp:" + util.JsonStr(resp),
			},
		})

	}()
}
