package env

import (
	"os"
	"strings"
)

var (
	serviceName        = os.Getenv("XLMESH_ENVOY_SERVICE_NAME")
	hostName           = os.Getenv("HOSTNAME")
	serviceClusterName = os.Getenv("XLMESH_ENVOY_SERVICE_CLUSTER")
	env                string
)

func init() {
	env = strings.TrimSpace(os.Getenv("env"))
	if strings.TrimSpace(env) == "" {
		env = string(Test)
	}
}

func ResetEnv() {
	env = strings.TrimSpace(os.Getenv("env"))
	if strings.TrimSpace(env) == "" {
		env = string(Test)
	}
}

func GetServiceName() string {
	return serviceName
}

func GetHostName() string {
	return hostName
}

func GetServiceClusterName() string {
	return serviceClusterName
}

type environment string

const (
	Test  environment = "test"
	Dev               = "dev"
	Prod              = "prod"
	Local             = "local"
)

func GetEnvironment() environment {
	return environment(env)
}

func IsLocal() bool { return GetEnvironment() == Local }
func IsTest() bool  { return GetEnvironment() == Test }
func IsDev() bool   { return GetEnvironment() == Dev }
func IsProd() bool  { return GetEnvironment() == Prod }

func SetEnv(envInput string) {
	env = envInput
}
