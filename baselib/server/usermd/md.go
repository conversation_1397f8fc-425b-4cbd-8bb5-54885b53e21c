package usermd

import (
	"context"
	"google.golang.org/grpc/metadata"
	"strconv"
)

const (
	UserContextKey     = "user_key"
	GrpcUserContextKey = "from_user"
	LangContextKey     = "lang"
)

type UserContext struct {
	Userid int64
	Lang   string
}

func SetUserContext(ctx context.Context, userContext *UserContext) context.Context {
	return context.WithValue(ctx, UserContextKey, userContext)
}

func GetUserContext(ctx context.Context) *UserContext {
	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil {
		return u
	}
	return nil
}

func GetUserIdFromContext(ctx context.Context) int64 {
	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil {
		return u.Userid
	}
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		s, ok := md[GrpcUserContextKey]
		if ok && len(s) > 0 {
			userid, _ := strconv.ParseInt(s[0], 10, 64)
			return userid
		}
	}
	return 0
}

func GetLangFromContext(ctx context.Context) (lang string) {
	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil && u.Lang != "" {
		return u.Lang
	}
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		l, ok := md[LangContextKey]
		if ok && len(l) > 0 && l[0] != "" {
			return l[0]
		}
	}
	return
}
