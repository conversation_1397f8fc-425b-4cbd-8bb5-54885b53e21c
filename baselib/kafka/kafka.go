package kafka

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"strings"
	"xim/baselib/config"
	"xim/baselib/logger"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/proto"
)

// Producer represents a Kafka producer.
type Producer struct {
	addr     []string
	topic    string
	config   *sarama.Config
	producer sarama.SyncProducer
}

var producers map[string]*Producer

func Client(topic string) *Producer {
	return producers[topic]
}

func KafkaInit(kafkaConf *config.Kafka, topics []string) {
	if kafkaConf == nil {
		logger.Errorf("KafkaInit config nil")
		return
	}

	producers = make(map[string]*Producer)

	kconf := kafkaConf.Build()
	if kconf == nil {
		return
	}
	conf, err := BuildProducerConfig(*kconf)
	if err != nil {
		logger.Errorf("KafkaInit err %v", err)
		return
	}

	for _, topic := range topics {
		producers[topic], err = NewKafkaProducer(conf, kafkaConf.Address, topic)
		if err != nil {
			logger.Errorf("KafkaInit err %v", err)
			return
		}
	}

}

func NewKafkaProducer(config *sarama.Config, addr []string, topic string) (*Producer, error) {
	producer, err := NewProducer(config, addr)
	if err != nil {
		return nil, err
	}
	return &Producer{
		addr:     addr,
		topic:    topic,
		config:   config,
		producer: producer,
	}, nil
}

// SendMessage sends a message to the Kafka topic configured in the Producer.
func (p *Producer) SendMessage(ctx context.Context, key string, msg proto.Message) (int32, int64, error) {

	if p == nil {
		logger.Errorf("p ==nil ")
		return 0, 0, nil
	}

	// Marshal the protobuf message
	bMsg, err := proto.Marshal(msg)
	if err != nil {
		return 0, 0, fmt.Errorf(err.Error(), "kafka proto Marshal err")
	}
	if len(bMsg) == 0 {
		return 0, 0, fmt.Errorf("kafka proto Marshal err")
	}

	// Prepare Kafka message
	kMsg := &sarama.ProducerMessage{
		Topic: p.topic,
		Key:   sarama.StringEncoder(key),
		Value: sarama.ByteEncoder(bMsg),
	}

	// Validate message key and value
	if kMsg.Key.Length() == 0 || kMsg.Value.Length() == 0 {
		return 0, 0, fmt.Errorf("errEmptyMsg")
	}

	// Send the message
	partition, offset, err := p.producer.SendMessage(kMsg)
	if err != nil {
		return 0, 0, fmt.Errorf(err.Error(), "p.producer.SendMessage error")
	}

	return partition, offset, nil
}

type MConsumerGroup struct {
	sarama.ConsumerGroup
	groupID string
	topics  []string
}

func NewMConsumerGroup(conf *config.Config, groupID string, topics []string, autoCommitEnable bool) (*MConsumerGroup, error) {
	config, err := BuildConsumerGroupConfig(conf, sarama.OffsetNewest, autoCommitEnable)
	if err != nil {
		return nil, err
	}
	group, err := NewConsumerGroup(config, conf.Addr, groupID)
	if err != nil {
		return nil, err
	}
	return &MConsumerGroup{
		ConsumerGroup: group,
		groupID:       groupID,
		topics:        topics,
	}, nil
}

func (mc *MConsumerGroup) RegisterHandleAndConsumer(ctx context.Context, handler sarama.ConsumerGroupHandler) {
	for {
		err := mc.ConsumerGroup.Consume(ctx, mc.topics, handler)
		if errors.Is(err, sarama.ErrClosedConsumerGroup) {
			return
		}
		if errors.Is(err, context.Canceled) {
			return
		}
		if err != nil {
			logger.Warnf("consume err=%+v topic=%+v groupID=%s", err, mc.topics, mc.groupID)
		}
	}
}

func (mc *MConsumerGroup) Close() error {
	return mc.ConsumerGroup.Close()
}

func NewConsumerGroup(conf *sarama.Config, addr []string, groupID string) (sarama.ConsumerGroup, error) {
	cg, err := sarama.NewConsumerGroup(addr, groupID, conf)
	if err != nil {
		return nil, fmt.Errorf(err.Error(), "NewConsumerGroup failed", "addr", addr, "groupID", groupID, "conf", *conf)
	}
	return cg, nil
}

func NewProducer(conf *sarama.Config, addr []string) (sarama.SyncProducer, error) {
	producer, err := sarama.NewSyncProducer(addr, conf)
	if err != nil {
		return nil, fmt.Errorf(err.Error(), "NewSyncProducer failed", "addr", addr, "conf", *conf)
	}
	return producer, nil
}

func BuildProducerConfig(conf config.Config) (*sarama.Config, error) {
	kfk := sarama.NewConfig()
	kfk.Producer.Return.Successes = true
	kfk.Producer.Return.Errors = true
	kfk.Producer.Partitioner = sarama.NewHashPartitioner
	if conf.Username != "" || conf.Password != "" {
		kfk.Net.SASL.Enable = true
		kfk.Net.SASL.User = conf.Username
		kfk.Net.SASL.Password = conf.Password
	}
	switch strings.ToLower(conf.ProducerAck) {
	case "no_response":
		kfk.Producer.RequiredAcks = sarama.NoResponse
	case "wait_for_local":
		kfk.Producer.RequiredAcks = sarama.WaitForLocal
	case "wait_for_all":
		kfk.Producer.RequiredAcks = sarama.WaitForAll
	default:
		kfk.Producer.RequiredAcks = sarama.WaitForAll
	}
	if conf.CompressType == "" {
		kfk.Producer.Compression = sarama.CompressionNone
	} else {
		if err := kfk.Producer.Compression.UnmarshalText(bytes.ToLower([]byte(conf.CompressType))); err != nil {
			return nil, fmt.Errorf(err.Error(), "UnmarshalText failed", "compressType", conf.CompressType)
		}
	}
	// if conf.TLS.EnableTLS {
	// 	tls, err := newTLSConfig(conf.TLS.ClientCrt, conf.TLS.ClientKey, conf.TLS.CACrt, []byte(conf.TLS.ClientKeyPwd), conf.TLS.InsecureSkipVerify)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	kfk.Net.TLS.Config = tls
	// 	kfk.Net.TLS.Enable = true
	// }
	return kfk, nil
}

func BuildConsumerGroupConfig(conf *config.Config, initial int64, autoCommitEnable bool) (*sarama.Config, error) {
	kfk := sarama.NewConfig()
	kfk.Version = sarama.V2_0_0_0
	kfk.Consumer.Offsets.Initial = initial
	kfk.Consumer.Offsets.AutoCommit.Enable = autoCommitEnable
	kfk.Consumer.Return.Errors = false
	if conf.Username != "" || conf.Password != "" {
		kfk.Net.SASL.Enable = true
		kfk.Net.SASL.User = conf.Username
		kfk.Net.SASL.Password = conf.Password
	}
	// if conf.TLS.EnableTLS {
	// 	tls, err := newTLSConfig(conf.TLS.ClientCrt, conf.TLS.ClientKey, conf.TLS.CACrt, []byte(conf.TLS.ClientKeyPwd), conf.TLS.InsecureSkipVerify)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	kfk.Net.TLS.Config = tls
	// 	kfk.Net.TLS.Enable = true
	// }
	return kfk, nil
}
