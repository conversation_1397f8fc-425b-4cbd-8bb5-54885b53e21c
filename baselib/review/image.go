package review

import (
	"errors"
	"strings"

	"xim/baselib/server/env"
)

type ImageScanTask struct {
	Type   string  `json:"type"`
	Url    string  `json:"url"`
	DataId string  `json:"dataId"`
	Origin *Origin `json:"origin"`
}

type ImageScanRequest struct {
	BaseReq
	Tasks []ImageScanTask `json:"tasks"`
}

type ImageScanDataResult struct {
	Scene      string     `json:"scene"`
	Label      string     `json:"label"`
	Suggestion Suggestion `json:"suggestion"`
}

type ImageScanData struct {
	Code       int                   `json:"code"`
	Msg        string                `json:"msg"`
	Suggestion Suggestion            `json:"suggestion"`
	Label      string                `json:"label"`
	Url        string                `json:"url"`
	DataId     string                `json:"dataId"`
	TaskId     string                `json:"taskId"`
	Results    []ImageScanDataResult `json:"results"`
}

type ImageScanResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []ImageScanData `json:"data"`
}

func ImageScan(dataId, url, bizType string, origin *Origin) (result ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, nil
	}
	if strings.TrimSpace(url) == "" {
		result = ReviewResult{Suggestion: SuggestionReject}
		err = ErrReviewContentEmpty
		return
	}
	params := ImageScanRequest{
		BaseReq: GetBaseReq(bizType),
	}
	imageScanTask := ImageScanTask{Url: url, DataId: dataId}

	if origin != nil {
		imageScanTask.Origin = origin
		if len(origin.DataId) > 0 {
			imageScanTask.DataId = origin.DataId
		}
	}

	params.Tasks = append(params.Tasks, imageScanTask)
	result = ReviewResult{Suggestion: SuggestionReview}
	response := &ImageScanResponse{}
	err = reviewClient.Check(ContentImage, bizType, params, response)
	if err != nil {
		return
	}
	if response.Code == 200 {
		if len(response.Data) > 0 && response.Data[0].Code == 200 {
			result = ReviewResult{Suggestion: response.Data[0].Suggestion, Label: response.Data[0].Label}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}
	return
}

func ImagesScan(urls map[string]string, bizType string, origin *Origin) (result ReviewResult, details map[string]ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		details = make(map[string]ReviewResult)
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, details, nil
	}
	if len(urls) == 0 {
		err = ErrReviewContentEmpty
		return
	}
	tasks := make([]ImageScanTask, 0, len(urls))

	for dataId, url := range urls {
		task := ImageScanTask{
			Url:    url,
			DataId: dataId,
		}
		if origin != nil {
			task.Origin = origin
			if len(origin.DataId) > 0 {
				task.DataId = origin.DataId
			}
		}
		tasks = append(tasks, task)
	}
	params := ImageScanRequest{
		BaseReq: GetBaseReq(bizType),
		Tasks:   tasks,
	}

	response := &ImageScanResponse{}
	err = reviewClient.Check(ContentImage, bizType, params, response)
	if err != nil {
		return
	}
	details = make(map[string]ReviewResult)
	if response.Code == 200 && len(response.Data) > 0 {
		for _, item := range response.Data {
			if item.Code == 200 {
				r := ReviewResult{Suggestion: item.Suggestion, Label: item.Label}
				// if !item.Suggestion.IsPass() && r.Label == AuditLabelNormal {
				// 	for _, itemResult := range item.Results {
				// 		if !itemResult.Suggestion.IsPass() {
				// 			r.Label = itemResult.Scene
				// 			break
				// 		}
				// 	}
				// }
				details[item.DataId] = r
				if origin != nil && len(origin.DataId) > 0 {
					for id, url := range urls {
						if url == item.Url {
							details[id] = r
							break
						}
					}
				}
				if r.Suggestion.IsMoreWorse(result.Suggestion) {
					result = r
				}
			}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}

	if len(details) == len(urls) {
		return
	}
	for dataId, _ := range urls {
		if _, ok := details[dataId]; !ok {
			r := ReviewResult{Suggestion: SuggestionReview}
			details[dataId] = r
			if r.Suggestion.IsMoreWorse(result.Suggestion) {
				result = r
			}
		}
	}
	return
}
