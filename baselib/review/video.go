package review

import (
	"errors"
	"strings"

	"xim/baselib/server/env"
)

type VideoScanTask struct {
	Type   string  `json:"type"`
	Url    string  `json:"url"`
	Md5    string  `json:"md5"`
	DataId string  `json:"dataId"`
	Origin *Origin `json:"origin"` //{"owner": "uid99811"} 需要透传(回调或到人审)的内容. 字段需要约定好.
}

type VideoScanRequest struct {
	BaseReq
	Tasks []VideoScanTask `json:"tasks"`
}

type VideoScanDataResult struct {
	Scene      string `json:"scene"`
	Label      string `json:"label"`
	Suggestion string `json:"suggestion"`
}

type VideoScanData struct {
	Code       int                   `json:"code"`
	Msg        string                `json:"msg"`
	Suggestion Suggestion            `json:"suggestion"`
	Label      string                `json:"label"`
	Url        string                `json:"url"`
	DataId     string                `json:"dataId"`
	TaskId     string                `json:"taskId"`
	Results    []VideoScanDataResult `json:"results"`
}

type VideoScanResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []VideoScanData `json:"data"`
}

func VideoScan(url string, bizType string, origin *Origin) (result ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, nil
	}
	if strings.TrimSpace(url) == "" {
		result = ReviewResult{Suggestion: SuggestionReject}
		err = ErrReviewContentEmpty
		return
	}
	params := VideoScanRequest{
		BaseReq: GetBaseReq(bizType),
	}

	videoScanTask := VideoScanTask{Url: url}

	if origin != nil {
		videoScanTask.Origin = origin
		if len(origin.DataId) > 0 {
			videoScanTask.DataId = origin.DataId
		}
	}

	params.Tasks = append(params.Tasks, videoScanTask)

	response := &VideoScanResponse{}
	err = reviewClient.Check(ContentVideo, bizType, params, response)
	if err != nil {
		return
	}
	result = ReviewResult{Suggestion: SuggestionReview}
	if response.Code == 200 {
		if len(response.Data) > 0 && response.Data[0].Code == 200 {
			result = ReviewResult{Suggestion: response.Data[0].Suggestion, Label: response.Data[0].Label}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}
	return
}
