package shumei

import (
	"fmt"
	"testing"
	"time"

	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/baselib/review/shumei/smfpcrypto"
	"xim/baselib/util"
)

// go test -v  -run TestText
func TestText(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	st := time.Now()
	result, err := TextScan(5462307, 5462307, 1, 0, "可以的美女！怎么能找到你", review.BizTypeFamilyAnnouncement, "", nil, false)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))
}

// go test -v  -run TestImage
func TestImage(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	//url := util.FullAvatarUrl("1000000000004752")
	url := util.FullMomentImageUrl("4209") // 1006402

	st := time.Now()
	result, err := ImageScan(1, 1, 0, 0, url, review.BizTypeChatImage, "", nil)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))
}

// go test -v  -run TestAudio
func TestAudio(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	st := time.Now()
	id := "1000000000001149"
	bizType := review.BizTypeChatAudio
	btId := fmt.Sprintf("%s_%s", bizType, id)
	url := util.FulllChatAudioUrl(id)
	result, err := AudioScan(1, 2, 1, 0, btId, url, review.BizTypeChatAudio, "", nil)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))

	for i := 0; i < 4; i++ {
		time.Sleep(5 * time.Second)
		resp, err := AutoAudioResult(btId)
		if err != nil {
			t.Logf(err.Error())
			continue
		}
		if resp.Code == CodeSuccess {
			t.Logf("%v", util.JsonStr(resp))
			break
		}
	}

}

// go test -v  -run TestSmid
func TestSmid(t *testing.T) {
	smid, err := smfpcrypto.OutputBoxId("BNWlxgHG+V3j3jtA0Kv1s94pModhe/fErgS9eaKUA3dJLaKPSVhn7b/eEiJ65OMUWa0ZWUTs1+0IvEMJTEzNf+Q==", priKey)
	fmt.Println(smid, err)
}

func TestImageReciveTokenId(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	//url := util.FullAvatarUrl("1000000000004752")
	url := util.FullMomentImageUrl("4209") // 1006402

	st := time.Now()
	result, err := ImageScan(1, 2, 0, 0, url, review.BizTypeChatImage, "", nil)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))
}
