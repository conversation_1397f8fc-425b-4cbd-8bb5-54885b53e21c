package shumei

import (
	"fmt"

	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/baselib/util"
)

type SkynetStatus uint8

const (
	SkynetStatusPass SkynetStatus = iota + 1
	SkynetStatusReview
	SkynetStatusReject
	SkynetStatusVerify
)

func GetSkynetStatus(r RiskLevel) SkynetStatus {
	if r.IsPass() {
		return SkynetStatusPass
	} else if r.IsReject() {
		return SkynetStatusReject
	} else if r.IsReview() {
		return SkynetStatusReview
	} else if r.IsVerify() {
		return SkynetStatusVerify
	}
	return 0
}

const (
	EventIdRegister = "register" //注册事件
	EventIdLogin    = "login"    //登录事件
)

type RegisterType string

const (
	RegisterTypePhoneOnePass   RegisterType = "phoneOnePass"   //手机号码一键注册
	RegisterTypePhoneMessage   RegisterType = "phoneMessage"   //手机号验证码注册
	RegisterTypeSignupPlatform RegisterType = "signupPlatform" //第三方授权
	RegisterTypeUserPassword   RegisterType = "userPassword"   //用户名密码注册
)

type SkynetCommonData struct {
	TokenId     string `json:"tokenId"` //当前业务新注册的用户账号
	Ip          string `json:"ip"`
	Timestamp   int64  `json:"timestamp"` //单位为毫秒（ms）
	DeviceId    string `json:"deviceId"`  // 数美设备指纹标识，由数美SDK生成
	Role        string `json:"role"`
	Level       int    `json:"level"`
	Os          string `json:"os,omitempty"`
	AppVersion  string `json:"appVersion"`
	ActivityId  string `json:"activityId"`
	PhoneSha256 string `json:"phoneSha256"`
	PhoneMd5    string `json:"phoneMd5"`
}

type RegisterBody struct {
	BaseReq
	EventId string       `json:"eventId"`
	Data    RegisterData `json:"data"`
}

type RegisterData struct {
	SkynetCommonData
	Type        RegisterType `json:"type"`
	Phone       string       `json:"phone"`       // 新用户注册使用手机号的MD5字符串 MD5加密字符串格式为32位小写
	CountryCode string       `json:"countryCode"` // 手机用户的国家代码 中国大陆区手机号填写0086
	Sex         string       `json:"sex"`         // 可选值：male,female
}

type LoginType string

const (
	LoginTypeFastLogin      LoginType = "fastLogin"      // 快速登录
	LoginTypePhoneOneLogin  LoginType = "phoneOneLogin"  // 本机号码一键登录
	LoginTypePhonePassword  LoginType = "phonePassword"  // 手机号密码登录
	LoginTypePhoneMessage   LoginType = "phoneMessage"   // 手机号验证码登录
	LoginTypeSignupPlatform LoginType = "signupPlatform" // 第三方授权登录
	LoginTypeUserPassword   LoginType = "userPassword"   // 用户名密码登录
	LoginTypeBiometric      LoginType = "biometric"      //生物识别
)

type LoginBody struct {
	BaseReq
	EventId string    `json:"eventId"`
	Data    LoginData `json:"data"`
}

type LoginData struct {
	SkynetCommonData
	Type         LoginType `json:"type"`
	HashPassword string    `json:"hashPassword"`
	SubTokenId   string    `json:"subTokenId,omitempty"`
	Phone        string    `json:"phone"`
	CountryCode  string    `json:"countryCode"`
	Level        int       `json:"level"`
}

type SkynetResp struct {
	RequestId          string              `json:"requestId"`
	Code               Code                `json:"code"`
	Message            string              `json:"message"`
	RiskLevel          RiskLevel           `json:"riskLevel"`
	Detail             SkynetDetail        `json:"detail"`
	TokenProfileLabels []TokenProfileLabel `json:"tokenProfileLabels"`
	TokenRiskLabels    []TokenRiskLabel    `json:"tokenRiskLabels"`
}

type SkynetDetail struct {
	Description        string             `json:"description"`
	Model              string             `json:"model"`
	Hits               []Hits             `json:"hits"`
	MachineAccountRisk MachineAccountRisk `json:"machineAccountRisk"`
	FakePhoneRisk      FakePhoneRisk      `json:"fakePhoneRisk"`
	FakeDeviceRisk     FakeDeviceRisk     `json:"fakeDeviceRisk"`
	IpCountry          string             `json:"ip_country"`
	IpProvince         string             `json:"ip_province"`
	IpCity             string             `json:"ip_city"`
	VerifyType         string             `json:"verifyType"`
}

type Hits struct {
	Description string    `json:"description"`
	Model       string    `json:"model"`
	RiskLevel   RiskLevel `json:"riskLevel"`
	VerifyType  string    `json:"verifyType"`
}

type MachineAccountRisk struct {
	TokenSampleLastTs int64  `json:"tokenSampleLastTs"`
	TokenSampleDesc   string `json:"tokenSampleDesc"`
}

type FakePhoneRisk struct {
	PhoneSampleLastTs int64  `json:"phoneSampleLastTs"`
	PhoneSampleDesc   string `json:"phoneSampleDesc"`
}

type FakeDeviceRisk struct {
	SmidSampleLastTs int64  `json:"smidSampleLastTs"`
	SmidSampleDesc   string `json:"smidSampleDesc"`
}

type TokenProfileLabel struct {
	Label1      string `json:"label1"`
	Label2      string `json:"label2"`
	Label3      string `json:"label3"`
	Description string `json:"description"`
	Timestamp   int64  `json:"timestamp"`
}

type TokenRiskLabel struct {
	Label1      string `json:"label1"`
	Label2      string `json:"label2"`
	Label3      string `json:"label3"`
	Description string `json:"description"`
	Timestamp   int64  `json:"timestamp"`
}

func SkynetScanRegister(data RegisterData) (response *SkynetResp, err error) {
	params := RegisterBody{
		BaseReq: GetBaseReq(),
		EventId: EventIdRegister,
		Data:    data,
	}

	response = &SkynetResp{}
	err = auditClient.AuditCheck(review.ContentSkynet, 3, params, response)
	if err != nil {
		return
	}

	logger.Warnf("SkynetScan params:%v,response:%v", util.JsonStr(params), util.JsonStr(response))

	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei skynet err:%v", err)
		return
	}
	return response, nil
}

func SkynetScanLogin(data LoginData) (response *SkynetResp, err error) {
	params := LoginBody{
		BaseReq: GetBaseReq(),
		EventId: EventIdLogin,
		Data:    data,
	}

	response = &SkynetResp{}
	fmt.Println(util.JsonStr(params))
	err = auditClient.AuditCheck(review.ContentSkynet, 3, params, response)
	if err != nil {
		return
	}
	logger.Warnf("SkynetScan params:%v,response:%v", util.JsonStr(params), util.JsonStr(response))

	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei skynet err:%v", err)
		return
	}
	return response, nil
}
