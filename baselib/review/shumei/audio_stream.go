package shumei

import (
	"fmt"

	agoraio "xim/baselib/agora.io"
	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/baselib/server/env"
	"xim/proto/consts"
)

const (
	ChannelVoiceRoom = "voiceroom" //音频流频道
)

// https://help.ishumei.com/docs/tj/audioStream/newest/developDoc
type AudioStreamBody struct {
	BaseReq
	EventId      string          `json:"eventId"`
	Type         string          `json:"type"`
	BusinessType string          `json:"businessType"`
	Data         AudioStreamData `json:"data"`
	Callback     string          `json:"callback"`
	BtId         string          `json:"btId"`
}

type AudioStreamData struct {
	TokenId string `json:"tokenId"` //用户账号标识
	//BtId       string                              `json:"btId"`
	StreamType string                              `json:"streamType"`
	Url        string                              `json:"url"`
	Lang       string                              `json:"lang"`
	AgoraParam agoraio.AudioStreamAgoraParamShumei `json:"agoraParam"`
	Room       string                              `json:"room"` // N 可选
	Role       string                              `json:"role"` // N
	// ReturnAllText    int                                 `json:"returnAllText"`    // N 0：返回风险等级为非pass的音频片段  1：返回所有风险等级的音频片段
	// ReturnPreText    int                                 `json:"returnPreText"`    // N 是否返回违规音频流片段的前文文字信息
	// ReturnPreAudio   int                                 `json:"returnPreAudio"`   // N 是否返回违规音频流片段的前文音频链接
	// ReturnFinishInfo int                                 `json:"returnFinishInfo"` // N 音频流结束回调通知
	ReturnAllText    bool                  `json:"returnAllText"`    // N 0：返回风险等级为非pass的音频片段  1：返回所有风险等级的音频片段
	ReturnPreText    bool                  `json:"returnPreText"`    // N 是否返回违规音频流片段的前文文字信息
	ReturnPreAudio   bool                  `json:"returnPreAudio"`   // N 是否返回违规音频流片段的前文音频链接
	ReturnFinishInfo bool                  `json:"returnFinishInfo"` // N 音频流结束回调通知
	Extra            *AudioStreamExtraData `json:"extra"`            // N  透传参数
	LiveTitle        string                `json:"liveTitle"`        // N 标题
	AnchorName       string                `json:"anchorName"`       // N 昵称
	AudioDetectStep  int                   `json:"audioDetectStep"`  // N 音频每个步长只会检测一次,取值范围1-36的整数，默认每个片段都审核（备注）
	Channel          string                `json:"channel"`          // 渠道配置表
}

type AudioStreamExtraData struct {
	PassThrough AudioStreamPassThrough `json:"passThrough"`
}

type AudioStreamPassThrough struct {
	Room        int64  `json:"room"`
	Userid      int64  `json:"userid"`
	RoomSession string `json:"room_session"`
}

// 同步返回结果
type AudioStreamResp struct {
	RequestId string            `json:"requestId"`
	Code      Code              `json:"code"`
	Message   string            `json:"message"`
	Detail    AudioStreamDetail `json:"detail"`
}

type AudioStreamDetail struct {
	Errorcode    int    `json:"errorcode"`    //1001：重复推流
	DupRequestId string `json:"dupRequestId"` //
}

// 回调返回结果
type AudioStreamCallbackResp struct {
	RequestId string          `json:"requestId"`
	Score     int             `json:"score"`
	BtId      string          `json:"btId"`
	RiskLevel RiskLevel       `json:"riskLevel"`
	Code      Code            `json:"code"`
	Message   string          `json:"message"`
	StatCode  int             `json:"statCode"` // 审核状态 0 ：审核中  1 ：审核结束
	Detail    AudioDetailData `json:"detail"`
	AuxInfo   AuxInfo         `json:"auxInfo"`
}

// type AudioDetailData struct {
// 	AudioUrl           string              `json:"audioUrl"`
// 	RiskLevel          RiskLevel           `json:"riskLevel"`
// 	RiskLabel1         string              `json:"riskLabel1"`
// 	RiskLabel2         string              `json:"riskLabel2"`
// 	RiskLabel3         string              `json:"riskLabel3"`
// 	RiskDescription    string              `json:"riskDescription"`
// 	AudioText          string              `json:"audioText"`   // N
// 	PreAudioUrl        string              `json:"preAudioUrl"` // N
// 	RiskDetail         RiskDetailData      `json:"riskDetail"`  // N
// 	AuxInfo            AuxInfoIner         `json:"auxInfo"`
// 	BusinessLabels     []BusinessLabel     `json:"businessLabels"` // N
// 	AllLabels          []AllLabel          `json:"allLabels"`      // N
// 	RiskSource         int                 `json:"riskSource"`
// 	TokenProfileLabels []TokenProfileLabel `json:"tokenProfileLabels"` // N
// 	TokenRiskLabels    []TokenRiskLabel    `json:"tokenRiskLabels"`    // N
// 	Speakers           [][]Speaker         `json:"speakers"`           // N
// 	VadCode            int                 `json:"vadCode"`
// }

type AudioDetailData struct {
	BeginProcessTime   string              `json:"beginProcessTime"`
	FinishProcessTime  string              `json:"finishProcessTime"`
	AudioEndtime       string              `json:"audio_endtime"`
	AudioStarttime     string              `json:"audio_starttime"`
	AudioUrl           string              `json:"audioUrl"`
	Model              string              `json:"model"`
	Content            string              `json:"content"`
	DescriptionV2      string              `json:"descriptionV2"`
	MatchedItem        string              `json:"matchedItem"`
	Hits               []AudioStreamHits   `json:"hits"`
	AudioText          string              `json:"audioText"`      // N
	PreAudioUrl        string              `json:"preAudioUrl"`    // N
	BusinessLabels     []BusinessLabel     `json:"businessLabels"` // N
	RiskSource         int                 `json:"riskSource"`
	TokenProfileLabels []TokenProfileLabel `json:"tokenProfileLabels"` // N
	TokenRiskLabels    []TokenRiskLabel    `json:"tokenRiskLabels"`    // N
	Speakers           [][]Speaker         `json:"speakers"`           // N
	VadCode            int                 `json:"vadCode"`
	RequestParams      AudioStreamData     `json:"requestParams"`
	RiskType           int                 `json:"riskType"`
	RiskTypeDesc       string              `json:"riskTypeDesc"`
	Room               string              `json:"room"`
	UserId             int64               `json:"userId"`
}

type RiskDetailData struct {
	AudioText    string        `json:"audioText"`
	MatchedLists []RiskMatched `json:"matchedLists"`
	RiskSegments []RiskSegment `json:"riskSegments"`
}

type RiskMatched struct {
	Name  string  `json:"name"`
	Words []Words `json:"words"`
}

type Words struct {
	Word     string `json:"word"`
	Position []int  `json:"position"`
}

type RiskSegment struct {
	Segment  string `json:"segment"`
	Position []int  `json:"position"`
}

type AuxInfoIner struct {
	AudioStartTime    string `json:"audioStartTime"`
	AudioEndTime      string `json:"audioEndTime"`
	BeginProcessTime  int    `json:"beginProcessTime"`
	FinishProcessTime int    `json:"finishProcessTime"`
	UserId            int    `json:"userId"`
	Room              string `json:"room"`
}

type BusinessLabel struct {
	BusinessLabel1      string `json:"businessLabel1"`
	BusinessLabel2      string `json:"businessLabel2"`
	BusinessLabel3      string `json:"businessLabel3"`
	BusinessDescription string `json:"businessDescription"`
}

type AllLabel struct {
	RiskLabel1      string `json:"riskLabel1"`
	RiskLabel2      string `json:"riskLabel2"`
	RiskLabel3      string `json:"riskLabel3"`
	RiskDescription string `json:"riskDescription"`
}

type Speaker struct {
	Uid    int64 `json:"uid"`
	Volume int64 `json:"volume"`
}

type AuxInfo struct {
	ErrorCode  AuErrCode `json:"errorCode"`
	StreamTime int       `json:"streamTime"` // 流结束后最后一次返回，代表送审时长，如有间隔审核逻辑时，和流真实时长可能不一致
}

type AudioStreamHits struct {
	Description   string    `json:"description"`
	DescriptionV2 string    `json:"descriptionV2"`
	Model         string    `json:"model"`
	RiskLevel     RiskLevel `json:"riskLevel"`
	RiskType      int       `json:"riskType"`
	Score         int       `json:"score"`
}

type AuErrCode int

const (
	AuErrCodeStreamAcessFail   AuErrCode = 3001 // 3001：流地址访问失败，例如资源HTTP状态码404、403
	AuErrCodeStreamInvalid     AuErrCode = 3002 // 3002：流数据无效，例如“Invalid data found when processing input”
	AuErrCodeStreamNoExist     AuErrCode = 3003 // 3003：流不存在，例如zego返回197612错误码
	AuErrCodeStreamNoData      AuErrCode = 3004 // 3004：流未返回音频数据
	AuErrCodeStreamTokenExpire AuErrCode = 3005 // 3005：拉流token无效或过期，建议使用新token重新开启审核，例如声网token过期或者trtc usersig无效
)

// 音频流关闭通知接口
type FinishAudiostreamReq struct {
	AccessKey string `json:"accessKey"`
	RequestId string `json:"requestId"`
}

type FinishAudiostreamResp struct {
	RequestId string `json:"requestId"`
	Code      Code   `json:"code"`
	Message   string `json:"message"`
}

func AudioStreamEventIdAndType() (eventId string, typ string) {
	return ChannelVoiceRoom, "EROTIC_DIRTY_ADVERT_AUDIOPOLITICAL_POLITY_MOAN_ANTHEN_MINOR_BANEDAUDIO" // "v2接口"
	//return ChannelVoiceRoom, "POLITY_EROTIC_ADVERT_MOAN_AUDIOPOLITICAL_ANTHEN_DIRTY_SING_MINOR_BANEDAUDIO_VOICE" // "v4接口"
}

func AudioStreamAgoraScan(userid int64, agoraCname string, roomid int64, roomSession string) (requestId string, dupRequestId string, err error) {
	eventId, typ := AudioStreamEventIdAndType()
	cb := consts.GetApiUrl(audioStreamCallback)
	btId := fmt.Sprintf("%d-%s", roomid, roomSession)
	room := fmt.Sprintf("%d-%s", roomid, roomSession)
	if !env.IsProd() {
		btId = fmt.Sprintf("test-%d-%s", roomid, roomSession)
		room = fmt.Sprintf("test-%d-%s", roomid, roomSession)
	}

	params := AudioStreamBody{
		BaseReq: GetBaseReq(),
		// EventId:  eventId,
		Type:     typ,
		Callback: cb,
		BtId:     btId,
		Data: AudioStreamData{
			TokenId: GetTokenId(userid),
			// BtId:       btId,
			StreamType: "AGORA",
			Lang:       "zh",
			Role:       "USER",
			AgoraParam: agoraio.BuildAudioStreamAgoraParamShumei(agoraCname),
			Extra: &AudioStreamExtraData{
				PassThrough: AudioStreamPassThrough{
					Room:        roomid,
					Userid:      agoraio.VoiceRoomUseridToUid(userid),
					RoomSession: roomSession,
				},
			},
			Room:             room,
			ReturnAllText:    true,
			ReturnPreText:    false,
			ReturnPreAudio:   false,
			ReturnFinishInfo: true,
			AudioDetectStep:  1,
			Channel:          eventId,
		},
	}

	response := &AudioStreamResp{}
	err = auditClient.AuditCheck(review.ContentAudioStream, 3, params, response)
	if err != nil {
		return
	}
	if response.Code != CodeSuccess {
		err = fmt.Errorf("AudioStreamAgoraScan code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("AudioStreamAgoraScan shumei audio stream scan agoraCname %v roomid %v roomSession %v err:%v", agoraCname, roomid, roomSession, err)
		return
	} else if response.Code == CodeSuccess && response.Detail.Errorcode == 1001 {
		err = fmt.Errorf("requestid %v message %v detail %+v", requestId, response.Message, response.Detail)
		logger.Errorf("AudioStreamAgoraScan shumei audio stream scan dupRequestId agoraCname  %v roomid %v roomSession %v err:%v", agoraCname, roomid, roomSession, err)
		dupRequestId = response.Detail.DupRequestId
	}
	requestId = response.RequestId
	return
}

func FinishAudioStreamScan(requestId string, roomid string) (err error) {
	params := FinishAudiostreamReq{
		AccessKey: accessKey,
		RequestId: requestId,
	}

	response := &FinishAudiostreamResp{}
	err = auditClient.AuditCheck(review.ContentFinishAudioStream, 3, params, response)
	if err != nil {
		return
	}
	if response.Code != CodeSuccess {
		err = fmt.Errorf("FinishAudioStreamScan code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("finish shumei audio stream scan err:%v", err)
		return
	}
	return
}
