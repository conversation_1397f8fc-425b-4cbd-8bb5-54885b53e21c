package shumei

import (
	"fmt"

	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/proto/consts/enums"
)

const (
	TextEventIdIm  = "message"   // 女、用户等级0级
	TextEventIdIm2 = "message_2" // 男、用户等级0级
	TextEventIdIm3 = "message_3" // 女、用户等级1级
	TextEventIdIm4 = "message_4" // 女、用户等级1级

	TextEventIdTextSign = "profile"  // 交友宣言
	TextEventIdNickName = "nickname" // 昵称
	TextEventIdPost     = "post"     // 动态
	TextEventIdComment  = "comment"  // 评论

	TextEventIdRoomChat       = "chat"                // 房间公屏消息
	TextEventIdRoomTitle      = "room_title"          // 语音房房间title
	TextEventIdRoomNotice     = "room_notice"         // 语音房房间公告
	TextEventIdFamilySpeaking = "Family_speaking"     // 家族聊天
	TextEventIdFamilyTitle    = "Family_name"         // 家族title
	TextEventIdFamilyNotice   = "Family_Announcement" // 家族公告

)

// 请求 body
type TextBody struct {
	BaseReq
	Type    string   `json:"type"`
	EventId string   `json:"eventId"`
	Data    TextData `json:"data"`
}

type TextData struct {
	Text     string        `json:"text"`
	TokenId  string        `json:"tokenId"`
	Nickname string        `json:"nickname"`
	Ip       string        `json:"ip,omitempty"`
	DeviceId string        `json:"deviceId"`
	Extra    TextExtraData `json:"extra"`
}

// resp body
type RespText struct {
	Code               Code                     `json:"code"`
	Message            string                   `json:"message"`
	RequestId          string                   `json:"requestId"`
	RiskLevel          RiskLevel                `json:"riskLevel"`
	RiskLabel1         string                   `json:"riskLabel1"`
	RiskLabel2         string                   `json:"riskLabel2"`
	RiskLabel3         string                   `json:"riskLabel3"`
	RiskDescription    string                   `json:"riskDescription"`
	RiskDetail         TextRiskDetailData       `json:"riskDetail"`
	Auxlnfo            TextAuxlnfoData          `json:"auxlnfo"`
	TokenLabels        TextTokenLabelsData      `json:"tokenLabels"`
	AllLabels          []TextAllLabelsData      `json:"allLabels"`
	BusinessLabels     []TextBusinessLabelsData `json:"businessLabels"`
	TokenProfileLabels []TokenProfileLabelData  `json:"tokenProfileLabels"`
	TokenRiskLabels    []TokenRiskLabelData     `json:"tokenRiskLabels"`
	LangResult         []LangResultData         `json:"langResult"`
}

type TextRiskDetailData struct {
	MatchedLists []MatchedListData `json:"matchedLists"`
	RiskSegments []RiskSegmentData `json:"riskSegments"`
}

type TextAuxlnfoData struct {
	FilteredText     string              `json:"filteredText"`
	PassThrough      Origin              `json:"passThrough"`
	ContactResult    []ContactResultData `json:"contactResult"`
	UnauthorizedType string              `json:"unauthorizedType"`
}

type TextTokenLabelsData struct {
	UGCAccountRisk TextUGCAccountRiskData `json:"UGC_account_risk"`
}

type TextAllLabelsData struct {
	RiskLabel1      string             `json:"riskLabel1"`
	RiskLabel2      string             `json:"riskLabel2"`
	RiskLabel3      string             `json:"riskLabel3"`
	RiskDescription string             `json:"riskDescription"`
	Probability     float64            `json:"probability"`
	RiskDetail      TextRiskDetailData `json:"riskDetail"`
	RiskLevel       string             `json:"riskLevel"`
}

type TextBusinessLabelsData struct {
	BusinessLabel1      string  `json:"businessLabel1"`
	BusinessLabel2      string  `json:"businessLabel2"`
	BusinessLabel3      string  `json:"businessLabel3"`
	BusinessDescription float64 `json:"businessDescription"`
	Probability         float64 `json:"probability"`
}

type TokenProfileLabelData struct {
	Label1      string `json:"label1"`
	Label2      string `json:"label2"`
	Label3      string `json:"label3"`
	Description string `json:"description"`
	Timestamp   int    `json:"timestamp"`
}

type TokenRiskLabelData struct {
	Label1      string `json:"label1"`
	Label2      string `json:"label2"`
	Label3      string `json:"label3"`
	Description string `json:"description"`
	Timestamp   int    `json:"timestamp"`
}

type LangResultData struct {
	DetectedLang string `json:"detectedLang"`
}

type TextUGCAccountRiskData struct {
	SexyRiskTokenid float64 `json:"sexy_risk_tokenid"`
}

type ContactResultData struct {
	ContactType   int    `json:"contactType"`
	ContactString string `json:"contactString"`
}

func IsHitRiskLable1CountLow(bizType, riskLabel1 string, sex int32, level int32) bool {
	eventId, _, _ := GetTextEventIdAndType(bizType, sex, level)
	if eventId == TextEventIdIm && (riskLabel1 == "ad" || riskLabel1 == "porn") {
		return true
	}
	return false
}

func GetTextEventIdAndType(bizType string, sex int32, level int32) (eventId, typ string, err error) {
	switch bizType {
	case review.BizTypeChat:
		eventId, typ = getEventIdAndTypeImText(sex, level)
	case review.BizTypeRoomChat:
		eventId, typ = TextEventIdRoomChat, "TEXTRISK_FRUAD"
	case review.BizTypeNickname:
		eventId, typ = TextEventIdNickName, "TEXTRISK_FRUAD"
	case review.BizTypeLoveWords:
		eventId, typ = TextEventIdTextSign, "TEXTRISK_FRUAD"
	case review.BizTypeMoment:
		eventId, typ = TextEventIdPost, "TEXTRISK_FRUAD"
	case review.BizTypeCommont:
		eventId, typ = TextEventIdComment, "TEXTRISK_FRUAD"
	case review.BizTypeRoomTitle:
		eventId, typ = TextEventIdRoomTitle, "TEXTRISK_FRUAD"
	case review.BizTypeRoomNotice:
		eventId, typ = TextEventIdRoomNotice, "TEXTRISK_FRUAD"
	case review.BizTypeFamilyChat:
		eventId, typ = TextEventIdFamilySpeaking, "TEXTRISK_FRUAD"
	case review.BizTypeFamilyName:
		eventId, typ = TextEventIdFamilyTitle, "TEXTRISK_FRUAD"
	case review.BizTypeFamilyAnnouncement:
		eventId, typ = TextEventIdFamilyNotice, "TEXTRISK_FRUAD"
	default:
		err = review.ErrRevieTypeBad
		return
	}
	return
}

func getEventIdAndTypeImText(sex, level int32) (eventId, typ string) {
	eventId = TextEventIdIm
	if sex == enums.UserSexFemale.Int32() {
		if level == 0 {
			eventId = TextEventIdIm
		} else if level >= 1 {
			eventId = TextEventIdIm3
		}
	} else if sex == enums.UserSexMale.Int32() {
		if level == 0 {
			eventId = TextEventIdIm2
		} else if level >= 1 {
			eventId = TextEventIdIm4
		}
	}
	return eventId, "TEXTRISK_FRUAD"
}

func TextScan(userid, peerid int64, sex int32, level int32, text string, bizType, ip string, origin *Origin, isContext bool) (result AuditResultShumei, err error) {
	if len(text) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}

	eventId, typ, err := GetTextEventIdAndType(bizType, sex, level)
	if err != nil {
		return
	}
	params := TextBody{
		BaseReq: GetBaseReq(),
		EventId: eventId,
		Type:    typ,
	}

	if level > 4 {
		level = 4
	}

	tokenId := GetTokenId(userid)
	if isContext {
		tokenId += "-misc"
	}
	params.Data = TextData{
		Text:    text,
		TokenId: tokenId,
		Ip:      ip,
		Extra: TextExtraData{
			Sex:            GetSex(sex),
			Level:          level,
			PassThrough:    origin,
			ReceiveTokenId: GetTokenId(peerid),
		},
	}

	if eventId == TextEventIdNickName ||
		eventId == TextEventIdTextSign ||
		eventId == TextEventIdPost ||
		eventId == TextEventIdComment {
		params.Data.Extra.ReceiveTokenId = fmt.Sprintf("%d", peerid)
	}

	response := &RespText{}
	err = auditClient.AuditCheck(review.ContentText, 2, params, response)
	if err != nil {
		return
	}

	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei text err:%v", err)
		return
	}

	return AuditResultShumei{
		RiskLevel:       response.RiskLevel,
		RequestId:       response.RequestId,
		RiskLabel1:      response.RiskLabel1,
		RiskLabel2:      response.RiskLabel2,
		RiskLabel3:      response.RiskLabel3,
		RiskDescription: response.RiskDescription,
	}, nil
}
