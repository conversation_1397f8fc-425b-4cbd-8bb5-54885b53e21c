package shumei

import (
	"fmt"
	"strings"

	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/proto/consts/enums"
)

const (
	EventIdImageHeadImage     = "headImage"      //头像
	EventIdImageAlbum         = "album"          //相册
	EventIdImageDynamic       = "dynamic"        //动态
	EventIdImageArticle       = "article"        //帖⼦
	EventIdImageComment       = "comment"        //评论
	EventIdImageMessage       = "message"        //私聊图⽚
	EventIdImageFemaleMessage = "female_message" //私聊图片 (女用户亲密的映射等级<=1)
	EventIdImageIMAGE         = "IMAGE"          //头像
	EventIdImagePhotoAlbum    = "Photo_album"    //相册
	EventIdImagePost          = "post"           //动态

	EventIdImageRoomBg      = "room_bg"    //语音房房间背景
	EventIdImageRoomCover   = "room_cover" //语音房房间封面
	EventIdImageFamilyCover = "Family_Cover"
)

type RiskSourceType int

const (
	RiskSourceNo           RiskSourceType = 1000 // 无风险
	RiskSourceText         RiskSourceType = 1001 // 文字风险
	RiskSourceVisualPhotos RiskSourceType = 1002 // 视觉图⽚风险
)

type ErrorCodeType int

const (
	ErrorCodeTypeDataFormat  ErrorCodeType = 2001 // 输⼊入数据格式不对，不是合法的json数据
	ErrorCodeTypeField       ErrorCodeType = 2002 // 输⼊入的参数字段不合法（必填字段缺失、类型不对、值不合法等）
	ErrorCodeTypeDownload    ErrorCodeType = 2003 // 图片下载失败
	ErrorCodeTypeSize        ErrorCodeType = 2004 // 图片过大，超过了10M
	ErrorCodeTypeImageFormat ErrorCodeType = 2005 // 非法图片格式
	ErrorCodeTypeInvalid     ErrorCodeType = 2006 // 无效风险监控类型
)

// 请求body
type ImageBody struct {
	BaseReq
	Type         string    `json:"type"`
	EventId      string    `json:"eventId"`
	BusinessType string    `json:"businessType,omitempty"`
	Data         ImageData `json:"data"`
	Callback     string    `json:"callback,omitempty"`
}

type ImageData struct {
	TokenId        string    `json:"tokenId"`
	Img            string    `json:"img"`
	Ip             string    `json:"ip,omitempty"`
	ImgCompareBase string    `json:"imgCompareBase,omitempty"`
	Extra          ExtraData `json:"extra"`
	ReceiveTokenId string    `json:"receiveTokenId"`
}

// resp body
type RespImage struct {
	Code            Code                      `json:"code"`
	Message         string                    `json:"message"`
	RequestId       string                    `json:"requestId"`
	RiskLevel       RiskLevel                 `json:"riskLevel"`
	RiskLabel1      string                    `json:"riskLabel1"`
	RiskLabel2      string                    `json:"riskLabel2"`
	RiskLabel3      string                    `json:"riskLabel3"`
	RiskDescription string                    `json:"riskDescription"`
	RiskDetail      ImageRiskDetailData       `json:"riskDetail"`
	Auxlnfo         ImageAuxlnfoData          `json:"auxlnfo"`
	AllLabels       []ImageAllLabelsData      `json:"allLabels"`
	BusinessLabels  []ImageBusinessLabelsData `json:"businessLabels"`
	TokenLabels     ImageTokenLabelsData      `json:"tokenLabels"`
}

type ImageRiskDetailData struct {
	Faces      []FaceData     `json:"faces"`
	Objects    []ObjectData   `json:"objects"`
	OcrText    OcrTextData    `json:"ocrText"`
	RiskSource RiskSourceType `json:"riskSource"`
}

type ImageAuxlnfoData struct {
	Segments    int             `json:"segments"`
	TypeVersion TypeVersionData `json:"typeVersion"`
	ErrorCode   ErrorCodeType   `json:"errorCode"`
	PassThrough Origin          `json:"passThrough"`
	QrContent   string          `json:"qrContent"`
}

type ImageAllLabelsData struct {
	RiskLabel1      string              `json:"riskLabel1"`
	RiskLabel2      string              `json:"riskLabel2"`
	RiskLabel3      string              `json:"riskLabel3"`
	RiskDescription string              `json:"riskDescription"`
	Probability     float64             `json:"probability"`
	RiskDetail      ImageRiskDetailData `json:"riskDetail"`
}

type ImageBusinessLabelsData struct {
	BusinessLabel1      string             `json:"businessLabel1"`
	BusinessLabel2      string             `json:"businessLabel2"`
	BusinessLabel3      string             `json:"businessLabel3"`
	BusinessDescription string             `json:"businessDescription"`
	BusinessDetail      BusinessDetailData `json:"businessDetail"`
	Probability         float64            `json:"probability"`
	ConfidenceLevel     int                `json:"confidenceLevel"`
}

type ImageTokenLabelsData struct {
	MachineAccountRisk MachineAccountRiskData  `json:"machine_account_risk"`
	UGCAccountRisk     ImageUGCAccountRiskData `json:"UGC_account_risk"`
	SceneAccountRisk   SceneAccountRiskData    `json:"scene_account_risk"`
}

type FaceData struct {
	Name        string  `json:"name"`
	Location    []int   `json:"location"`
	Probability float64 `json:"probability"`
}

type ObjectData struct {
	Name        string  `json:"name"`
	Location    []int   `json:"location"`
	Probability float64 `json:"probability"`
}

type OcrTextData struct {
	Text         string            `json:"text"`
	MatchedLists []MatchedListData `json:"matchedLists"`
	RiskSegments []RiskSegmentData `json:"riskSegments"`
}

type TypeVersionData struct {
	POLITICS string `json:"POLITICS"`
	VIOLENCE string `json:"VIOLENCE"`
	BAN      string `json:"BAN"`
	PORN     string `json:"PORN"`
	MINOR    string `json:"MINOR"`
	AD       string `json:"AD"`
	SPAM     string `json:"SPAM"`
	LOGO     string `json:"LOGO"`
	STAR     string `json:"STAR"`
	OCR      string `json:"OCR"`
	IMGTEXT  string `json:"IMGTEXT"`
	SCREEN   string `json:"SCREEN"`
	SCENCE   string `json:"SCENCE"`
	QR       string `json:"QR"`
	FACE     string `json:"FACE"`
	QUALITY  string `json:"QUALITY"`
	PORTRAIT string `json:"PORTRAIT"`
	ANIMAL   string `json:"ANIMAL"`
	BEAUTY   string `json:"BEAUTY"`
}

type BusinessDetailData struct {
	Name           string  `json:"name"`
	Probability    float64 `json:"probability"`
	FaceRatio      float64 `json:"face_ratio"`
	FaceNum        int     `json:"face_num"`
	FaceCompareNum int     `json:"face_compare_num"`
	Location       []int   `json:"location"`
	PersonNum      int     `json:"person_num"`
	PersonRatio    float64 `json:"person_ratio"`
}

type MachineAccountRiskData struct {
	BMachineControlTokenid       int `json:"b_machine_control_tokenid"`
	BMachineControlTokenidLastTs int `json:"b_machine_control_tokenid_last_ts"`
	BOfferWallTokenid            int `json:"b_offer_wall_tokenid"`
	BOfferWallTokenidLastTs      int `json:"b_offer_wall_tokenid_last_ts"`
}

type ImageUGCAccountRiskData struct {
	BPoliticsRiskTokenid        int `json:"b_politics_risk_tokenid"`
	BPoliticsRiskTokenidLastTs  int `json:"b_politics_risk_tokenid_last_ts"`
	BSexyRiskTokenid            int `json:"b_sexy_risk_tokenid"`
	BSexyRiskTokenidLastTs      int `json:"b_sexy_risk_tokenid_last_ts"`
	BAdvertiseRiskTokenid       int `json:"b_advertise_risk_tokenid"`
	BAdvertiseRiskTokenidLastTs int `json:"b_advertise_risk_tokenid_last_ts"`
}

type SceneAccountRiskData struct {
	IToutRiskTokenid       int `json:"i_tout_risk_tokenid"`
	IToutRiskTokenidLastTs int `json:"i_tout_risk_tokenid_last_ts"`
}

func GetImageEventIdAndType(bizType string, sex int32, level int32) (eventId, typ string, err error) {
	switch bizType {
	case review.BizTypeChatImage:
		eventId, typ = getEventIdAndTypeImImage(sex, level)
	case review.BizTypeAvatar:
		eventId = EventIdImageIMAGE
		typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	case review.BizTypeAlbum:
		eventId = EventIdImagePhotoAlbum
		typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	case review.BizTypeMoment:
		eventId = EventIdImagePost
		typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	case review.BizTypeRoomBg:
		eventId = EventIdImageRoomBg
		typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	case review.BizTypeRoomCover:
		eventId = EventIdImageRoomCover
		typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	case review.BizTypeFamilyCover:
		eventId = EventIdImageFamilyCover
		typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	default:
		err = review.ErrRevieTypeBad
		return
	}
	return
}

func getEventIdAndTypeImImage(sex int32, level int32) (eventId, typ string) {
	typ = "POLITICS_VIOLENCE_BAN_PORN_AD_OCR"
	//typ = "POLITY_EROTIC_VIOLENT_QRCODE_ADVERT_IMGTEXTRISK"
	if sex == enums.UserSexFemale.Int32() && level <= 1 {
		typ += "_FASHION_SOCIALAPPSLOGO"
		return EventIdImageFemaleMessage, typ
	}
	return EventIdImageMessage, typ
}

func ImageScan(userid, peerid int64, sex int32, level int32, url, bizType, ip string, origin *Origin) (result AuditResultShumei, err error) {
	if strings.TrimSpace(url) == "" {
		err = review.ErrReviewContentEmpty
		return
	}

	eventId, typ, err := GetImageEventIdAndType(bizType, sex, level)
	if err != nil {
		return
	}
	params := ImageBody{
		BaseReq: GetBaseReq(),
		EventId: eventId,
		Type:    typ,
	}
	params.Data = ImageData{
		TokenId: GetTokenId(userid),
		Img:     url,
		Ip:      ip,
		Extra: ExtraData{
			PassThrough: origin,
		},
		ReceiveTokenId: GetTokenId(peerid),
	}

	if eventId == EventIdImageIMAGE ||
		eventId == EventIdImagePhotoAlbum ||
		eventId == EventIdImagePost {
		params.Data.ReceiveTokenId = fmt.Sprintf("%d", peerid)
	}

	response := &RespImage{}
	err = auditClient.AuditCheck(review.ContentImage, 5, params, response)
	if err != nil {
		return
	}
	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei image err:%v", err)
		return
	}

	return AuditResultShumei{
		RiskLevel:       response.RiskLevel,
		RequestId:       response.RequestId,
		RiskLabel1:      response.RiskLabel1,
		RiskLabel2:      response.RiskLabel2,
		RiskLabel3:      response.RiskLabel3,
		RiskDescription: response.RiskDescription,
	}, nil
}
