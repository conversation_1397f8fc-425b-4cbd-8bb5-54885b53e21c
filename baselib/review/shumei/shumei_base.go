package shumei

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"xim/baselib/metric"

	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/baselib/server/env"
	"xim/baselib/util"
	"xim/proto/api/common"
	"xim/proto/consts/enums"
)

const (
	accessKey           = "JzPsEsRf2BvNjH9VeqGI"
	callbackUri         = "/biznotify/shumei/audio_callback"        // 私信会话
	audioStreamCallback = "/biznotify/shumei/audio_stream_callback" // 语音房音频流
	priKey              = "MIIBPAIBAAJBALO0+GRdmC5yrg38mnGtQC6fadln70qc1ANga321/aLuuynASmRcbhJ6nEPuPstblkh2LRHZHuNIB3R3QzCjHlsCAwEAAQJBAJSCqgPHlUxNWaoVZ00qcDAXbdJNImP21v8zX6G0FLfgTf7uEF+TeNqvjF1BnnOwHt3Rld+7OF8ld6YR++d/d4ECIQDvinftke9iblgAS/rortD4LlrmSzQsNbV0t4YfJeTMPwIhAMAOBTVa/dUyp2DXvh1dlo4AxORgxL+7RXn0B3EP1RblAiEAo6XAuv/5QBDMdB90YiSURQk+NpjoIs/qkLuUmJEF13cCIQCew7hYiDToJuypKRA3tGM+VrJtXPWozkY775irRXxRZQIgGoKnXpZWevOPGOgEHyTMTzFRtw+9ZV1F6Gga5bGHHAI="
)

const (
	TokenIdOffset = int64(5687456)
)

func GetShumeiPriKey() string {
	return priKey
}

type RiskLevel string

const (
	RiskLevelPass   RiskLevel = "PASS"
	RiskLevelReview RiskLevel = "REVIEW"
	RiskLevelReject RiskLevel = "REJECT"
	RiskLevelVerify RiskLevel = "VERIFY"
)

func (s RiskLevel) IsPass() bool {
	return s == RiskLevelPass
}

func (s RiskLevel) IsReview() bool {
	return s == RiskLevelReview
}

func (s RiskLevel) IsReject() bool {
	return s == RiskLevelReject
}

func (s RiskLevel) IsVerify() bool {
	return s == RiskLevelVerify
}

type Origin struct {
	SeqId   int64  `json:"seqid"`
	Content string `json:"content"`
}

type Code int

const (
	CodeSuccess      Code = 1100 // 成功
	CodeQPSLimit     Code = 1901 // QPS超限
	CodeParamError   Code = 1902 // 参数不合法
	CodeServiceFail  Code = 1903 // 服务失败
	CodeDownloadFail Code = 1911 // 图⽚片下载失败
	CodeNobalance    Code = 9100 // 余额不足
	CodeNoPermission Code = 9101 // 无权限操作
)

type TextExtraData struct {
	Sex            int     `json:"sex"`
	Level          int32   `json:"level"`
	ReceiveTokenId string  `json:"receiveTokenId"`
	PassThrough    *Origin `json:"passThrough"`
}

type ExtraData struct {
	PassThrough *Origin `json:"passThrough"`
}

type MatchedListData struct {
	Name  string     `json:"name"`
	Words []WordData `json:"words"`
}

type RiskSegmentData struct {
	Segment  string `json:"segment"`
	Position []int  `json:"position"`
}

type WordData struct {
	Word     string `json:"word"`
	Position []int  `json:"position"`
}

type AuditResultShumei struct {
	RiskLevel       RiskLevel
	RequestId       string
	BtId            string
	RiskLabel1      string
	RiskLabel2      string
	RiskLabel3      string
	RiskDescription string
}

type AuditClient struct {
	AppId                string
	TextUrl              string
	ImageUrl             string
	VoiceUrl             string
	VoiceResultUrl       string
	PhonePortraitUrl     string
	SkynetUrl            string
	AudioStreamUrl       string
	FinishAudioStreamUrl string
}

func (ar AuditResultShumei) Result() (auditResult common.AuditResult) {
	if ar.RiskLevel.IsPass() {
		auditResult = common.AuditResult_pass
	} else if ar.RiskLevel.IsReject() {
		auditResult = common.AuditResult_reject
	} else if ar.RiskLevel.IsReview() {
		auditResult = common.AuditResult_review
	} else {
		auditResult = common.AuditResult_unknown
	}
	return
}

var auditClient = AuditClient{
	AppId:            "qianxun",
	TextUrl:          "http://api-text-sh.fengkongcloud.com/text/v4",
	ImageUrl:         "http://api-img-sh.fengkongcloud.com/image/v4",
	VoiceUrl:         "http://api-audio-sh.fengkongcloud.com/v2/saas/anti_fraud/audio",
	VoiceResultUrl:   "http://api-audio-sh.fengkongcloud.com/v2/saas/anti_fraud/query_audio",
	PhonePortraitUrl: "http://api-tianxiang-bj.fengkongcloud.com/tianxiang/v4", // 暂时不接
	SkynetUrl:        "http://api-skynet-bj.fengkongcloud.com/v4/event",

	// 音频流审核
	AudioStreamUrl:       "http://api-audiostream-sh.fengkongcloud.com/v2/saas/anti_fraud/audiostream",        //"http://api-audiostream-sh.fengkongcloud.com/audiostream/v4",
	FinishAudioStreamUrl: "http://api-audiostream-sh.fengkongcloud.com/v2/saas/anti_fraud/finish_audiostream", //"http://api-audiostream-sh.fengkongcloud.com/finish_audiostream/v4",
}

func GetAuditClient() AuditClient {
	return auditClient
}

func GetTokenId(userid int64) string {
	if !env.IsProd() {
		return fmt.Sprintf("test-%d", userid+TokenIdOffset)
	}
	return fmt.Sprintf("%d", userid+TokenIdOffset)
}

func GetSex(sex int32) int {
	switch sex {
	case enums.UserSexFemale.Int32():
		return 1 //女性
	case enums.UserSexMale.Int32():
		return 0 //男性
	}
	return 2 //性别不明
}

type BaseReq struct {
	AccessKey string `json:"accessKey"`
	AppId     string `json:"appId"`
}

func GetBaseReq() BaseReq {
	return BaseReq{
		AppId:     auditClient.AppId,
		AccessKey: accessKey}
}

func (client AuditClient) AuditCheck(typ review.ContentType, timeout int64, params interface{}, response interface{}) (err error) {
	apiUrl := client.getUrl(typ)
	if len(apiUrl) == 0 {
		err = review.ErrRevieTypeBad
		return
	}

	st := time.Now()
	resp, err := httpcli.PostJsonWithTimeout(apiUrl, nil, timeout, params)
	if err != nil {
		logger.Warnf("ShumeiCheck post err: %v,params:%v", err, util.JsonStr(params))
		return
	}
	cost := time.Since(st).Milliseconds()
	if cost > 500 {
		logger.Warnf("ShumeiCheck contentType:%v,cost:%v,resp:%v", typ, cost, resp)
	}
	metric.HistogramLabelsWithBuckets("shumei_time", map[string]string{
		"typ": strconv.FormatInt(int64(typ), 10),
		"url": apiUrl,
	}, []float64{10, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 3000, 4000}).Observe(float64(cost))
	err = json.Unmarshal([]byte(resp), &response)
	if err != nil {
		logger.Warnf("ShumeiCheck json err: %v, resp: %v", err, resp)
		return
	}
	return
}

func (client AuditClient) getUrl(typ review.ContentType) string {
	switch typ {
	case review.ContentText:
		return client.TextUrl
	case review.ContentImage:
		return client.ImageUrl
	case review.ContentVoice:
		return client.VoiceUrl
	case review.ContentVoiceResult:
		return client.VoiceResultUrl
	case review.PhonePortrait:
		return client.PhonePortraitUrl
	case review.ContentSkynet:
		return client.SkynetUrl
	case review.ContentAudioStream:
		return client.AudioStreamUrl
	case review.ContentFinishAudioStream:
		return client.FinishAudioStreamUrl
	}
	return ""
}
