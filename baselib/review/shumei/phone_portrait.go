package shumei

import (
	"fmt"

	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/baselib/util"
)

// 请求 body
type PhonePortraitBody struct {
	BaseReq
	Data PhonePortraitData `json:"data"`
}

type PhonePortraitData struct {
	Phone    string `json:"phone"`
	PhoneMd5 string `json:"phoneMd5"`
}

// resp body
type RespPhonePortrait struct {
	Code             Code                 `json:"code"`
	Message          string               `json:"message"`
	RequestId        string               `json:"requestId"`
	PhonePrimaryInfo PhonePrimaryInfo     `json:"phonePrimaryInfo"`
	PhoneRiskLabels  []PhoneRiskLabelData `json:"phoneRiskLabels"`
}

type PhonePrimaryInfo struct {
	PhoneProvince string `json:"phone_province"`
	PhoneCity     string `json:"phone_city"`
	PhoneOperator string `json:"phone_operator"`
}

type PhoneRiskLabelData struct {
	Label1      string `json:"label1"`      // 一级标签 展示手机号风险标签的一级标签。
	Label2      string `json:"label2"`      // 二级标签 展示手机号风险标签的二级标签。
	Label3      string `json:"label3"`      // 三级标签 展示手机号风险标签的三级标签。
	Description string `json:"description"` //风险描述  展示手机号风险标签的中文描述。
	Timestamp   int64  `json:"timestamp"`   //最近一次命中策略的时间
}

func PhonePortraitScan(phone string) (result *RespPhonePortrait, err error) {
	if len(phone) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}

	params := PhonePortraitBody{
		BaseReq: GetBaseReq(),
		Data: PhonePortraitData{
			Phone:    phone,
			PhoneMd5: util.Md5([]byte(phone)),
		},
	}

	response := &RespPhonePortrait{}
	err = auditClient.AuditCheck(review.PhonePortrait, 1, params, response)
	if err != nil {
		return
	}

	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei phone_portrait err:%v", err)
		return
	}
	return response, nil
}
