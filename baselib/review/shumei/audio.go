package shumei

import (
	"fmt"
	"strings"

	"xim/baselib/logger"
	"xim/baselib/review"
	"xim/proto/consts"
	"xim/proto/consts/enums"
)

const (
	ChannelPrivate       = "private"        //私密语音聊天
	FemaleChannelPrivate = "female_private" //
)

type AudioRiskType int

const (
	AudioRiskTypeNormal         AudioRiskType = 0   // 正常
	AudioRiskTypePolicy         AudioRiskType = 100 // 涉政
	AudioRiskTypeTerrorism      AudioRiskType = 110 // 暴恐
	AudioRiskTypeNationalAnthem AudioRiskType = 120 // 国歌
	AudioRiskTypePorn           AudioRiskType = 200 // ⾊情
	AudioRiskTypeAbuse          AudioRiskType = 210 // 辱骂
	AudioRiskTypeJiaochuan      AudioRiskType = 250 //娇喘
	AudioRiskTypeLeader         AudioRiskType = 260 //⼀号领导声纹
	AudioRiskTypeRenshengAttr   AudioRiskType = 270 //⼈声属性
	AudioRiskTypeForbidSong     AudioRiskType = 280 //违禁歌曲
	AudioRiskTypeAd             AudioRiskType = 300 //⼴告
	AudioRiskTypePour           AudioRiskType = 400 //灌⽔
	AudioRiskTypeNoMean         AudioRiskType = 500 //⽆意义
	AudioRiskTypeForbid         AudioRiskType = 600 //违禁
	AudioRiskTypeOther          AudioRiskType = 700 //其他
	AudioRiskTypeBlockAccount   AudioRiskType = 720 //⿊账号
	AudioRiskTypeBlockIp        AudioRiskType = 730 //⿊IP
	AudioRiskTypeDangerAccount  AudioRiskType = 800 //⾼危账号
	AudioRiskTypeDefine         AudioRiskType = 900 //⾃定义

)

type AudioBody struct {
	BaseReq
	Type          string    `json:"type"`
	BtId          string    `json:"btId"`
	Data          AudioData `json:"data"`
	Callback      string    `json:"callback,omitempty"`
	CallbackParam *Origin   `json:"callbackParam,omitempty"`
}

type AudioData struct {
	Url            string         `json:"url"`
	Lang           string         `json:"lang"`
	Content        string         `json:"content,omitempty"`
	FormatInfo     FormatInfoData `json:"formatInfo"`
	AudioName      string         `json:"audioName"`
	TokenId        string         `json:"tokenId"`
	Channel        string         `json:"channel"`
	ReturnAllText  bool           `json:"returnAllText"`
	Nickname       string         `json:"nickname"`
	Timestamp      int            `json:"timestamp"`
	Room           string         `json:"room"`
	ReceiveTokenId string         `json:"receiveTokenId"`
	Ip             string         `json:"ip"`
}

type CallbackParamData struct {
}

type FormatInfoData struct {
	Format string `json:"format"`
	Rate   int    `json:"rate"`
	Track  int    `json:"track"`
}

type RespAudio struct {
	Code      Code   `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	BtId      string `json:"btId"`
}

type RespAudioCallback struct {
	Code          Code              `json:"code"`
	Message       string            `json:"message"`
	RequestId     string            `json:"requestId"`
	BtId          string            `json:"btId"`
	AudioText     string            `json:"audioText"`
	AudioTime     int               `json:"audioTime"`
	Labels        string            `json:"labels"`
	RiskLevel     RiskLevel         `json:"riskLevel"`
	Detail        []ImageDetailData `json:"detail"`
	Gender        GenderData        `json:"gender"`
	Tags          []TagData         `json:"tags"`
	CallbackParam Origin            `json:"callbackParam"`
}

type ImageDetailData struct {
	AudioStarttime   int           `json:"audioStarttime"`
	AudioEndtime     int           `json:"audioEndtime"`
	AudioUrl         string        `json:"audioUrl"`
	AudioText        string        `json:"audioText"`
	RiskLevel        RiskLevel     `json:"riskLevel"`
	RiskType         AudioRiskType `json:"riskType"`
	AudioMatchedItem string        `json:"audioMatchedItem"`
	Description      string        `json:"description"`
}

type GenderData struct {
	Label      string `json:"label"`
	Confidence int    `json:"confidence"`
}

type TagData struct {
	Label      string `json:"label"`
	Confidence int    `json:"confidence"`
}

// 自动查询结果 req
type AutoAudioResultReq struct {
	AccessKey string `json:"accessKey"`
	BtId      string `json:"btId"`
}

// 自动查询结果 resp
type AutoAudioResultResp struct {
	Code           Code                  `json:"code"`
	Message        string                `json:"message"`
	RequestId      string                `json:"requestId"`
	BtId           string                `json:"btId"`
	AudioText      string                `json:"audioText"`
	AudioTime      int                   `json:"audioTime"`
	Labels         string                `json:"labels"`
	RiskLevel      RiskLevel             `json:"riskLevel"`
	Detail         []AutoImageDetailData `json:"detail"`
	Gender         GenderData            `json:"gender"`
	Language       []LanguageData        `json:"language"`
	IsSing         int                   `json:"isSing"`
	Tags           []TagData             `json:"tags"`
	BusinessLabels []BusinessLabelData   `json:"businessLabels"`
}

type AutoImageDetailData struct {
	RequestId string `json:"requestId"`
	ImageDetailData
}

type LanguageData struct {
	Label      int `json:"label"`
	Confidence int `json:"confidence"`
}

type BusinessLabelData struct {
	BusinessLabel1      string `json:"businessLabel1"`
	BusinessLabel2      string `json:"businessLabel2"`
	BusinessLabel3      string `json:"businessLabel3"`
	BusinessDescription string `json:"businessDescription"`
}

func GetAudioChannelAndType(bizType string, sex int32, level int32) (channel, typ string, err error) {
	switch bizType {
	case review.BizTypeChatAudio:
		channel, typ = getChannelAndTypeImAudio(sex, level)
	case review.BizTypeFamilyChat:
		return review.BizTypeFamilyChat, "POLITICS_PORN_AD_ABUSE_MOAN_ANTHEN", nil
	default:
		err = review.ErrRevieTypeBad
		return
	}
	return
}

func getChannelAndTypeImAudio(sex, level int32) (channel, typ string) {
	typ = "POLITICS_PORN_AD_ABUSE_MOAN_ANTHEN"
	if sex == enums.UserSexFemale.Int32() && level <= 1 {
		channel = FemaleChannelPrivate
		return
	}
	channel = ChannelPrivate
	return
}

func AudioScan(userid int64, peerid int64, sex int32, level int32, btId, audioUrl, bizType, ip string, origin *Origin) (result AuditResultShumei, err error) {
	if strings.TrimSpace(audioUrl) == "" || len(btId) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}

	channel, typ, err := GetAudioChannelAndType(bizType, sex, level)
	if err != nil {
		return
	}

	cb := consts.GetApiUrl(callbackUri)

	params := AudioBody{
		BaseReq:       GetBaseReq(),
		BtId:          btId,
		Type:          typ,
		Callback:      cb,
		CallbackParam: origin,
	}
	params.Data = AudioData{
		TokenId:        GetTokenId(userid),
		Url:            audioUrl,
		Lang:           "zh",
		Channel:        channel,
		ReceiveTokenId: GetTokenId(peerid),
		Ip:             ip,
	}

	response := &RespAudio{}
	err = auditClient.AuditCheck(review.ContentVoice, 3, params, response)
	if err != nil {
		return
	}
	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei audio err:%v", err)
		return
	}

	return AuditResultShumei{
		RiskLevel: RiskLevelReview,
		RequestId: response.RequestId,
		BtId:      response.BtId,
	}, nil
}

func AutoAudioResult(btId string) (response *AutoAudioResultResp, err error) {
	if len(btId) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}
	params := AutoAudioResultReq{
		AccessKey: accessKey,
		BtId:      btId,
	}

	response = &AutoAudioResultResp{}
	err = auditClient.AuditCheck(review.ContentVoiceResult, 1, params, response)
	if err != nil {
		return
	}

	return
}
