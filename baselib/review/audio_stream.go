package review

import (
	"errors"
	"fmt"
	"strings"
)

type AudioStreamScanRequest struct {
	RoomId         string `json:"room_id"`         // 房间 id
	AppId          string `json:"app_id"`          // app_id
	BizType        string `json:"biz_type"`        // biz_type
	RoomName       string `json:"room_name"`       // 房间名称
	RoomType       int8   `json:"room_type"`       // 房间类型 0-普通 1-工会
	IsSecret       bool   `json:"is_secret"`       // 房间是否上锁
	AuditReason    string `json:"audit_reason"`    // 进审原因
	Suggestion     string `json:"suggestion"`      // 进审建议
	TranslatedText string `json:"translated_text"` // 转译文本
	ContentType    string `json:"content_type"`    // case类型  text 文本 	image 图片  audio 语音 	context 复合
	SampleUrl      string `json:"sample_url"`      // 审查样本url
	AnchorId       string `json:"anchor_id"`       // 房主id
	UserId         string `json:"user_id"`         // 用户id
	DataId         string `json:"data_id"`         // 业务方ID
	CaseId         string `json:"case_id"`
}

type AudioStreamScanResp struct {
	Code int                 `json:"code"`
	Msg  string              `json:"msg"`
	Data AudioStreamScanData `json:"data"`
}

type AudioStreamScanData struct {
	EarthId string `json:"earth_id"`
}

func AudioStreamScan(roomid int64, sid, bizType, auditReason, suggestion, translatedText, sampleUrl, anchorId, userid, requestId string) (err error) {
	// if !env.IsProd() {
	// 	roomid = fmt.Sprintf("test-%s", roomid)
	// 	if time.Now().Unix() > 1695732324 { // 方便测试线上流程 商业化没有测试环境
	// 		return
	// 	}
	// }
	if strings.TrimSpace(translatedText) == "" || len(sampleUrl) == 0 {
		err = ErrReviewContentEmpty
		return
	}

	params := AudioStreamScanRequest{
		RoomId:         sid, // 送审对应场次id
		AppId:          reviewClient.AppId,
		BizType:        bizType,
		RoomName:       fmt.Sprintf("%sroom-%d", anchorId, roomid),
		RoomType:       1,
		AuditReason:    auditReason,
		Suggestion:     suggestion,
		TranslatedText: translatedText,
		ContentType:    "audio",
		SampleUrl:      sampleUrl,
		AnchorId:       anchorId,
		UserId:         userid,
		DataId:         userid,
		CaseId:         requestId,
	}

	response := &AudioStreamScanResp{}
	err = reviewClient.Check(ContentAudioStream, bizType, params, response)
	if err != nil {
		return
	}

	// code = 1 成功
	if response.Code == 1 {
		return
	} else if len(response.Msg) != 0 {
		err = errors.New(response.Msg)
		return
	}
	return
}
