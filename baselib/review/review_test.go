package review

import (
	"fmt"
	"testing"
	"time"

	circuitbreaker "xim/baselib/circuit_breaker"
	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/util"

	"github.com/afex/hystrix-go/hystrix"
)

// go test -v  -run TestImagesBatch
func TestImagesBatch(t *testing.T) {
	Images := []int64{1931, 1933, 1979, 1980, 1982, 1999, 2000, 2001, 2002, 2003, 2005, 2007, 2015, 2016, 2021, 2053,
		2056, 2057, 2058, 2059, 2093, 2094, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2123, 2124, 2125, 2126, 2127,
		2310, 2311, 2312, 2313, 2314, 2315, 2316, 2317, 2318, 2319, 2320, 2321, 2322, 2323, 2324, 2325, 2326, 2437, 2438, 2439, 2440, 2441, 2442, 2443, 2453, 2454, 2455,
		2456, 2457, 2458, 2459, 2461, 2463, 2479, 2480, 2481, 2501, 2502, 2503, 2504, 2505, 2506,
		2507, 2508, 2509, 2510, 2511, 2512, 2513, 2514, 2515, 2516, 2517, 2518, 2519, 2520, 2634, 2635, 2640, 2641, 2642, 2643, 2644, 2645, 2646, 2647, 2648, 2649, 2650, 2651,
		2652, 2653, 2654, 2655, 2656, 2657, 2658, 2659, 2660, 2661, 2662, 2663, 2664, 2665, 2666, 2667, 2668, 2669, 2670,
		2675, 2676, 2677, 2691, 2692, 2693, 2694, 2702, 2703, 2711, 2712, 2713, 2714, 2715, 2716, 2717, 2718, 2731, 2732, 2733, 2734, 2735, 2736, 2737, 2738, 2739, 2740, 2741, 2742, 2743, 2744, 2745, 2746, 2747, 2748, 2749,
		2846, 2847, 2848, 2849, 2850, 2851, 2852, 2853, 2854, 2855, 2856, 2857, 2858, 2859, 2860, 2861, 2862, 2863, 2864, 2865,
		2866, 2867, 2868, 2869, 2870, 2871, 2872, 2873, 2896, 2897, 2898, 2899, 2900, 2901, 2902, 2903, 2904, 2905, 2906, 2907,
		2908, 2909, 2910, 2911, 2912, 2913, 2914, 2915, 2916, 2917, 2918, 2919, 2920, 2921, 2922, 2923, 2924, 2925, 2926, 2927,
		3014, 3015, 3016, 3023, 3024, 3025, 3026, 3027, 3028, 3029, 3042, 3042, 3044, 3045, 3046, 3047, 3051, 3052, 3053, 3054,
		3055, 3056, 3070, 3073, 3093, 3094, 3095, 3097, 3099, 3125, 3129}

	logger.InitLogger("/dev/stdout")

	var rejects []int64
	for _, id := range Images {
		dataid := fmt.Sprintf("%d", id)
		imgUrl := util.FullAvatarUrl(dataid)
		result, err := ImageScan(dataid, imgUrl, BizTypeAvatar, nil)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		if !result.Suggestion.IsPass() {
			rejects = append(rejects, id)
		}
	}

	fmt.Println("========", rejects)

}

// go test -v  -run TestImage
func TestImage(t *testing.T) {
	logger.InitLogger("/dev/stdout")

	dataId := "1933"
	imgUrl := util.FullAvatarUrl(dataId)
	result, err := ImageScan(dataId, imgUrl, BizTypeAvatar, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("imgUrl:%v ,result %v ", imgUrl, result.Suggestion.String())

	dataId = "1085"
	imgUrl = util.FullAvatarUrl(dataId)
	result, err = ImageScan(dataId, imgUrl, BizTypeAlbum, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}

	dataId = "2661"
	imgUrl = "http://melon-image-test.oss-cn-shanghai.aliyuncs.com/avatar/2661"
	result, err = ImageScan(dataId, imgUrl, BizTypeAvatar, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("imgUrl:%v ,result %v ", imgUrl, result.Suggestion.String())
}

func TestImages(t *testing.T) {
	logger.InitLogger("/dev/stdout")

	urls := make(map[string]string)
	urls["1005"] = util.FullAvatarUrl("1005")
	result, details, err := ImagesScan(urls, BizTypeAvatar, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}

	urls["1085"] = util.FullAvatarUrl("1085")
	result, details, err = ImagesScan(urls, BizTypeAlbum, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("urls:%v result %v,details:%v", urls, result.Suggestion.String(), details)
}

// go test -v  -run TestText1
func TestText1(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	// nickname := "fuck you"
	// result, err := TextScan(nickname, BizTypeNickname, nil)
	// if err != nil {
	// 	logger.Errorf(err.Error())
	// 	return
	// }
	// logger.Infof("text:%v result %v", nickname, result.Suggestion.String())

	title := "你好哈哈"
	result, err := TextScan(title, BizTypeRoomTitle, nil)
	if err != nil {
		fmt.Println(err)
		logger.Errorf(err.Error())
		return
	}
	fmt.Println(result)

	// nickname = "好的"
	// result, err = TextScan(nickname, BizTypeLoveWords, nil)
	// if err != nil {
	// 	logger.Errorf(err.Error())
	// 	return
	// }
	// logger.Infof("text:%v result %v", nickname, result.Suggestion.String())

	// nickname = "本拉登"
	// result, err = TextScan(nickname, BizTypeChat, nil)
	// if err != nil {
	// 	logger.Errorf(err.Error())
	// 	return
	// }
	// logger.Infof("text:%v result %v", nickname, result.Suggestion.String())

	// nickname = "本拉登"
	// result, err = TextScan(nickname, BizTypeLoveWords, nil)
	// if err != nil {
	// 	logger.Errorf(err.Error())
	// 	return
	// }
	// logger.Infof("text:%v result %v", nickname, result.Suggestion.String())
}

func TestVideo(t *testing.T) {
	logger.InitLogger("/dev/stdout")

	url := "https://melon-video.oss-cn-shenzhen.aliyuncs.com/1000"
	result, err := VideoScan(url, "post", nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("url:%v result %v", url, result.Suggestion.String())

	url = "https://melon-video.oss-cn-shenzhen.aliyuncs.com/moment/2fc70c777b24519b217e6669aab6559b.mp4"
	result, err = VideoScan(url, "post", nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("url:%v result %v", url, result.Suggestion.String())
}

func TestVoice(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	url := "https://melon-voice.oss-cn-shenzhen.aliyuncs.com/1002"
	result, err := AudioScan("1002", url, BizTypeSignVoice, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("text:%v,urls:%v result %v", url, result.Suggestion.String())
}

func TestTextImage(t *testing.T) {
	logger.InitLogger("/dev/stdout")

	text := "看看吧"
	dataIds := []string{"1006", "1060", "1062"}
	urls := make(map[string]string)
	for _, dataId := range dataIds {
		urls[dataId] = util.FullAvatarUrl(dataId)
	}
	result, details, err := ContextScanTextImages(text, urls, BizTypeMoment, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("text:%v,urls:%v result %v,details %v", text, urls, result.Suggestion.String(), details)

	//===
	text = "本拉登"
	urls = make(map[string]string)
	for _, dataId := range dataIds {
		urls[dataId] = util.FullAvatarUrl(dataId)
	}
	result, details, err = ContextScanTextImages(text, urls, BizTypeCommont, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("text:%v,urls:%v result %v details:%v", text, urls, result.Suggestion.String(), details)

	//===
	dataIds = []string{"1006", "1060", "1062", "1085"}
	urls = make(map[string]string)
	for _, dataId := range dataIds {
		urls[dataId] = util.FullAvatarUrl(dataId)
	}
	result, _, err = ContextScanTextImages(text, urls, "post", nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("text:%v,urls:%v result %v", text, urls, result.Suggestion.String())

}

func TestTextVoice(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	text := "看看吧"
	url := "https://melon-voice.oss-cn-shenzhen.aliyuncs.com/1002"
	result, details, err := ContextScanTextVoice(text, "1002", "", BizTypeMoment, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("text:%v,urls:%v result %v,details %v", url, result.Suggestion.String(), details)
}

func TestTextVideo(t *testing.T) {
	logger.InitLogger("/dev/stdout")

	text := "看看吧"
	url := "https://melon-video.oss-cn-shenzhen.aliyuncs.com/1028"
	result, details, err := ContextScanTextVideo(text, url, BizTypeMoment, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	logger.Infof("text:%v,url:%v result %v,details:%v", text, url, result.Suggestion.String(), details)
}

// go test -v -run TestHystrix
func TestHystrix(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	entity := &circuitbreaker.EntityCircuitBreaker{
		Timeout:                1000,
		MaxConcurrentRequests:  100,
		RequestVolumeThreshold: 5,
		SleepWindow:            5 * 1000,
		ErrorPercentThreshold:  40,
	}
	circuitbreaker.SetCCBConf(map[string]*circuitbreaker.EntityCircuitBreaker{
		"im_chat": entity,
	})

	for i := 0; i < 10; i++ {
		nickname := "fuck you"
		_, err := TextScan(nickname, BizTypeChat, nil)
		if err != nil {
			fmt.Println(i, err)
			// logger.Errorf(err.Error())
		} else {
			fmt.Println(i, "sucess")
		}
	}

	time.Sleep(5 * time.Second)
	herr := hystrix.Do(BizTypeChat, func() error {
		params := TextScanRequest{
			BaseReq: GetBaseReq(BizTypeChat),
		}
		textScanTask := TextScanTask{Text: "fuck you"}
		params.Tasks = append(params.Tasks, textScanTask)
		_, err := httpcli.PostJsonWithTimeout("http://180.163.203.105:9090/v0.1/text/scan", nil, 1, params)
		if err != nil {
			return err
		}
		return nil
	}, func(e error) error {
		return e
	})
	if herr != nil {
		fmt.Println(herr)
	}

	time.Sleep(5 * time.Second)
	for i := 0; i < 20; i++ {
		nickname := "fuck you"
		_, err := TextScan(nickname, BizTypeChat, nil)
		if err != nil {
			fmt.Println(i, err)
			// logger.Errorf(err.Error())
		} else {
			fmt.Println(i, "sucess")
		}
	}

	select {}
}

func TestVoiceRoomText(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	title := "你好哈哈"
	result, err := TextScan(title, BizTypeRoomNotice, nil)
	if err != nil {
		fmt.Println(err)
		logger.Errorf(err.Error())
		return
	}

	fmt.Println(result)
}

func TestVoiceRoomImage(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	dataId := "1933"
	imgUrl := util.FullAvatarUrl(dataId)
	result, err := ImageScan(dataId, imgUrl, BizTypeRoomBg, nil)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}

	fmt.Println(result)
}
