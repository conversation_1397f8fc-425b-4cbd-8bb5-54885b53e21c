package review

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	circuitbreaker "xim/baselib/circuit_breaker"
	"xim/baselib/metric"
	"xim/baselib/util"

	"github.com/afex/hystrix-go/hystrix"

	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/server/env"
)

var (
	ErrReviewContentEmpty = errors.New("review content empty")
	ErrRevieTypeBad       = errors.New("review type is not support")
	ErrorHystrixLimit     = errors.New("hystrix limit")
	Secret                = "64xc1f224f7e508abb980503b46cacce"
	// "http://censorship-solar.srv.test.ixiaochuan.cn"
	// xl internal test "http://***************:9090"
	censorshipUrlTest = "http://***************:6880" //"http://***************:9090"
	//censorshipUrlOnline = "http://solar-api-audit.xunlei.com"
	censorshipUrlOnline = "http://solar-api.baichuanshuan.com"
)

type BizType = string

const (
	BizTypeNickname           BizType = "nickname"        //昵称
	BizTypeSignVoice          BizType = "sign_audio"      //语音签名
	BizTypeLoveWords          BizType = "sign"            //爱情宣言
	BizTypeAlbum              BizType = "album_image"     //相册
	BizTypeAvatar             BizType = "avatar"          //头像
	BizTypeMoment             BizType = "post"            //动态
	BizTypeCommont            BizType = "comment"         //动态评论
	BizTypeChat               BizType = "im_chat"         //IM即时聊天文本
	BizTypeChatImage          BizType = "im_image"        //IM即时聊天图片
	BizTypeChatAudio          BizType = "im_audio"        //IM即时聊天语音
	BizTypeChatVideo          BizType = "im_video"        //IM即时聊天语音 (目前没有相关业务)
	BizTypeVideoLive          BizType = "1v1_video_live"  //1v1视频聊天
	BizTypeAudioLive          BizType = "1v1_audio_live"  //1v1语音聊天
	BizTypeCommonMsg          BizType = "phrase"          //聊天常用语
	BizTypeRoomTitle          BizType = "room_title"      //房间名称
	BizTypeRoomNotice         BizType = "room_notice"     //房间公告
	BizTypeRoomCover          BizType = "room_cover"      //房间封面
	BizTypeRoomBg             BizType = "room_bg"         //房间背景
	BizTypeRoomChat           BizType = "room_chat"       //房间公屏
	BizTypeRoomAudio          BizType = "room_audio"      //房间语音流
	BizTypeFamilyChat         BizType = "family_speaking" //家族群聊
	BizTypeFamilyName         BizType = "family_name"     //家族名字
	BizTypeFamilyAnnouncement BizType = "family_notice"   //家族公告
	BizTypeFamilyCover        BizType = "family_cover"    //家族封面
)

type ContentType int

const (
	ContentText      ContentType = 1
	ContentImage     ContentType = 2
	ContentVoice     ContentType = 3
	ContentVideo     ContentType = 4
	ContentTextImage ContentType = 5
	ContentTextVideo ContentType = 6
	ContentTextVoice ContentType = 7

	ContentVoiceCall   ContentType = 8
	ContentVideoCall   ContentType = 9
	ContentVoiceResult ContentType = 10

	PhonePortrait            ContentType = 11 //风险手机号画像
	ContentAudioStream       ContentType = 12 // 音频流审核
	ContentFinishAudioStream ContentType = 13 // 结束音频流审核
	ContentSkynet            ContentType = 100
)

type Suggestion string

func (s Suggestion) IsPass() bool {
	return s == SuggestionPass
}

func (s Suggestion) IsReview() bool {
	return s == SuggestionReview
}

func (s Suggestion) IsReject() bool {
	return s == SuggestionReject
}

func (s Suggestion) String() string {
	return string(s)
}

func (s Suggestion) IsEmpty() bool {
	return s == ""
}

func (s Suggestion) IsMoreWorse(cur Suggestion) bool {
	if cur.IsEmpty() {
		return true
	} else if cur.IsPass() {
		if s.IsReject() || s.IsReview() {
			return true
		}
	} else if cur.IsReview() {
		if s.IsReject() {
			return true
		}
	}
	return false
}

type Origin struct {
	SeqId   int64  `json:"seqid"`
	Content string `json:"content"`
	DataId  string `json:"dataId"`
}

type ReviewResult struct {
	Suggestion Suggestion
	Label      string
}

const (
	SuggestionUnknown Suggestion = "unknown"
	SuggestionPass    Suggestion = "pass"
	SuggestionReview  Suggestion = "review"
	SuggestionReject  Suggestion = "reject"
)

const (
	AuditLabelNormal      = "normal"
	AuditLabelSex         = "sex"
	AuditLabelLowSex      = "low_sex"
	AuditLabelPolitics    = "politics"
	AuditLabelLowPolitics = "low_politics"
	AuditLabelCrime       = "crime"
	AuditLabelLowCrime    = "low_crime"
)

type ReviewClient struct {
	AppId           string
	TextUrl         string
	ImageUrl        string
	VoiceUrl        string
	VideoUrl        string
	ContextUrl      string
	AsyncContextUrl string
	StreamAudioUrl  string
	Callback        string
}

var reviewClient = ReviewClient{
	AppId:           "yuanliao",
	TextUrl:         "%s/v0.1/text/scan",
	ImageUrl:        "%s/v0.1/image/scan",
	VoiceUrl:        "%s/v0.1/audio/scan",
	VideoUrl:        "%s/v0.1/video/scan",
	ContextUrl:      "%s/v0.1/context/scan",
	AsyncContextUrl: "%s/v0.1/context/asyncscan",
	StreamAudioUrl:  "%s/api/earth/v1/live/case",
}

func (client ReviewClient) Check(typ ContentType, bizType string, params interface{}, response interface{}) (err error) {
	apiUrl := client.getUrl(typ)
	if len(apiUrl) == 0 {
		err = ErrRevieTypeBad
		return
	}
	if env.IsProd() {
		apiUrl = fmt.Sprintf(apiUrl, censorshipUrlOnline)
	} else {
		apiUrl = fmt.Sprintf(apiUrl, censorshipUrlTest)
	}

	timeount := int64(1)
	if typ != ContentText {
		timeount = 5
	}

	st := time.Now()

	var (
		resp string
	)
	if IsImBizType(bizType) {
		herr := hystrix.Do(bizType, func() error {
			if resp, err = httpcli.PostJsonWithTimeout(apiUrl, nil, timeount, params); err != nil {
				if err.Error() != hystrix.ErrCircuitOpen.Error() && err.Error() != hystrix.ErrTimeout.Error() {
					logger.Warnf("Check post err: %v", err)
				}
				return err
			}
			return nil
		}, func(e error) error {
			// 触发了熔断了 数美兜底（数美不可用消息也会暂时放开）
			return e
		})

		if herr != nil {
			return herr
		}
	} else {
		resp, err = httpcli.PostJsonWithTimeout(apiUrl, nil, timeount, params)
		if err != nil {
			logger.Warnf("Check post err: %v", err)
			return
		}
	}

	cost := time.Since(st).Milliseconds()
	if cost > 500 {
		logger.Warnf("cost Check contentType:%v, cost:%v,req:%v,resp:%v", typ, cost, util.JsonStr(params), resp)
	} else {
		logger.Warnf("Check contentType:%v, cost:%v,req:%v,resp:%v", typ, cost, util.JsonStr(params), resp)
	}
	metric.HistogramLabelsWithBuckets("review_time", map[string]string{
		"typ": strconv.FormatInt(int64(typ), 10),
		"url": apiUrl,
	}, []float64{10, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 3000, 4000}).Observe(float64(cost))
	err = json.Unmarshal([]byte(resp), &response)
	if err != nil {
		logger.Warnf("Check json err: %v, resp: %v", err, resp)
		return
	}
	return
}

func (client ReviewClient) getUrl(typ ContentType) string {
	switch typ {
	case ContentText:
		return client.TextUrl
	case ContentImage:
		return client.ImageUrl
	case ContentVoice:
		return client.VoiceUrl
	case ContentVideo:
		return client.VideoUrl
	case ContentTextImage:
		return client.ContextUrl
	case ContentTextVideo,
		ContentTextVoice:
		return client.AsyncContextUrl
	case ContentVoiceCall:
		return client.TextUrl
	case ContentVideoCall:
		return client.TextUrl
	case ContentAudioStream:
		return client.StreamAudioUrl
		// return "http://10.130.0.194/api/earth/v1/live/case" // 内部审核部门 木有配置域名 跟常规人审走的是两套服务 先写死吧
	}
	return ""
}

type BaseReq struct {
	AppId    string   `json:"appId"`   // 如：miaoban
	BizType  BizType  `json:"bizType"` // 业务线
	Scenes   []string `json:"scenes"`  // 一般不管就行
	Callback string   `json:"callback"`
}

func GetBaseReq(bizType BizType) BaseReq {
	return BaseReq{
		AppId:    reviewClient.AppId,
		BizType:  bizType,
		Scenes:   []string{},
		Callback: reviewClient.Callback}
}

func IsImBizType(bizType string) bool {
	switch bizType {
	case BizTypeChat,
		BizTypeChatImage,
		BizTypeChatAudio,
		BizTypeRoomChat:
		return true
	default:
		return false
	}
}

var (
	configs = make(map[string]*circuitbreaker.EntityCircuitBreaker)
)

func GetReviewHystrixExpire(bizType string) int64 {
	if d, ok := configs[bizType]; ok {
		return int64(d.SleepWindow)
	}
	return 0
}

func InitHystrix() {
	configs[BizTypeChat] = &circuitbreaker.EntityCircuitBreaker{
		Timeout:                int(time.Second * 1),
		MaxConcurrentRequests:  100000,
		RequestVolumeThreshold: 1000,
		SleepWindow:            int(time.Second * 300),
		ErrorPercentThreshold:  80,
	}
	configs[BizTypeChatImage] = &circuitbreaker.EntityCircuitBreaker{
		Timeout:                int(time.Second * 2),
		MaxConcurrentRequests:  100000,
		RequestVolumeThreshold: 800,
		SleepWindow:            int(time.Second * 300),
		ErrorPercentThreshold:  80,
	}
	configs[BizTypeChatAudio] = &circuitbreaker.EntityCircuitBreaker{
		Timeout:                int(time.Second * 3),
		MaxConcurrentRequests:  100000,
		RequestVolumeThreshold: 600,
		SleepWindow:            int(time.Second * 300),
		ErrorPercentThreshold:  80,
	}
	circuitbreaker.SetCCBConf(configs)
}
