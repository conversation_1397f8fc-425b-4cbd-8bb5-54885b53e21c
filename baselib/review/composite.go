package review

import (
	"errors"

	"xim/baselib/server/env"
)

type ContextScanRequest struct {
	BaseReq
	DataId     string          `json:"dataId"`
	Origin     *Origin         `json:"origin"` //接人审使用
	ImageTasks []ImageScanTask `json:"imageTasks,omitempty"`
	TextTasks  []TextScanTask  `json:"textTasks,omitempty"`
	AudioTasks []AudioScanTask `json:"audioTasks,omitempty"`
	VideoTasks []VideoScanTask `json:"videoTasks,omitempty"`
}

type ContextScanImageResult struct {
	Code       int        `json:"code"`
	Msg        string     `json:"msg"`
	Type       string     `json:"type"`
	Url        string     `json:"url"`
	Scene      string     `json:"scene"`
	Label      string     `json:"label"`
	Suggestion Suggestion `json:"suggestion"`
}

type ContextScanTextResult struct {
	Code       int        `json:"code"`
	Msg        string     `json:"msg"`
	Type       string     `json:"type"`
	Text       string     `json:"text"`
	Scene      string     `json:"scene"`
	Label      string     `json:"label"`
	Suggestion Suggestion `json:"suggestion"`
}

type ContextScanVideoResult struct {
	Code       int        `json:"code"`
	Msg        string     `json:"msg"`
	Type       string     `json:"type"`
	Orgin      *Origin    `json:"orgin"`
	Scene      string     `json:"scene"`
	Label      string     `json:"label"`
	Suggestion Suggestion `json:"suggestion"`
}

type ContextScanAudioResult struct {
	Code       int        `json:"code"`
	Msg        string     `json:"msg"`
	Type       string     `json:"type"`
	Url        string     `json:"url"`
	Scene      string     `json:"scene"`
	Label      string     `json:"label"`
	Suggestion Suggestion `json:"suggestion"`
}

type ContextScanData struct {
	Code       int        `json:"code"`
	Msg        string     `json:"msg"`
	Type       string     `json:"type"`
	DataId     string     `json:"dataId"`
	TaskId     string     `json:"taskId"`
	Text       string     `json:"text"` //文本内容(如果命中的结果是文本时)
	Url        string     `json:"url"`  //图片URL(如果命中的结果是图片时)
	Suggestion Suggestion `json:"suggestion"`
	Label      string     `json:"label"`

	ImageResults []ContextScanImageResult `json:"imageResults"`
	TextResults  []ContextScanTextResult  `json:"textResults"`
	VideoResults []ContextScanVideoResult `json:"videoResults"`
	AudioResults []ContextScanAudioResult `json:"audioResults"`
}

type ContextScanResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data ContextScanData `json:"data"`
}

func ContextScanTextImages(text string, urls map[string]string, bizType string, origin *Origin) (result ReviewResult, results map[string]map[string]ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		results = map[string]map[string]ReviewResult{}
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, results, nil
	}
	if len(text) == 0 && len(urls) == 0 {
		err = ErrReviewContentEmpty
		return
	}
	param := ContextScanRequest{
		BaseReq: GetBaseReq(bizType),
	}
	if len(text) != 0 {
		param.TextTasks = []TextScanTask{
			{
				Text: text,
			},
		}
	}

	for dataId, url := range urls {
		param.ImageTasks = append(param.ImageTasks, ImageScanTask{Url: url, DataId: dataId})
	}
	if origin != nil {
		param.DataId = origin.DataId
		param.Origin = origin
		if len(origin.DataId) > 0 {
			for _, t := range param.ImageTasks {
				t.DataId = origin.DataId
			}
			for _, t := range param.TextTasks {
				t.DataId = origin.DataId
			}
		}
	}
	return contextScan(param, ContentTextImage, bizType)
}

func ContextScanTextVideo(text, url, bizType string, origin *Origin) (result ReviewResult, results map[string]map[string]ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		results = map[string]map[string]ReviewResult{}
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, results, nil
	}
	if len(text) == 0 && len(url) == 0 {
		err = ErrReviewContentEmpty
		return
	}
	param := ContextScanRequest{
		BaseReq: GetBaseReq(bizType),
	}
	if len(text) != 0 {
		param.TextTasks = []TextScanTask{
			{
				Text: text,
			},
		}
	}
	if len(url) != 0 {
		param.VideoTasks = []VideoScanTask{
			{
				Url: url,
			},
		}
	}
	if origin != nil {
		param.DataId = origin.DataId
		param.Origin = origin
		if len(origin.DataId) > 0 {
			for _, t := range param.TextTasks {
				t.DataId = origin.DataId
			}
			for _, t := range param.VideoTasks {
				t.DataId = origin.DataId
			}
		}
	}
	return contextScan(param, ContentTextVideo, bizType)
}

func ContextScanTextVoice(text, dataId, url, bizType string, origin *Origin) (result ReviewResult, results map[string]map[string]ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		results = map[string]map[string]ReviewResult{}
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, results, nil
	}
	if len(text) == 0 && len(url) == 0 {
		err = ErrReviewContentEmpty
		return
	}
	param := ContextScanRequest{
		BaseReq: GetBaseReq(bizType),
	}

	if len(text) != 0 {
		param.TextTasks = []TextScanTask{
			{
				Text: text,
			},
		}
	}

	if len(url) != 0 {
		param.AudioTasks = []AudioScanTask{
			{
				DataId: dataId,
				Url:    url,
			},
		}
	}
	if origin != nil {
		param.DataId = origin.DataId
		param.Origin = origin
		if len(origin.DataId) > 0 {
			for _, t := range param.TextTasks {
				t.DataId = origin.DataId
			}
			for _, t := range param.AudioTasks {
				t.DataId = origin.DataId
			}
		}
	}
	return contextScan(param, ContentTextVoice, bizType)
}

func contextScan(param ContextScanRequest, contentType ContentType, bizType string) (result ReviewResult, results map[string]map[string]ReviewResult, err error) {
	for i := range param.TextTasks {
		if param.TextTasks[i].Type == "" {
			param.TextTasks[i].Type = "text"
		}
	}
	for i := range param.ImageTasks {
		if param.ImageTasks[i].Type == "" {
			param.ImageTasks[i].Type = "image"
		}
	}
	for i := range param.AudioTasks {
		if param.AudioTasks[i].Type == "" {
			param.AudioTasks[i].Type = "audio"
		}
	}
	for i := range param.VideoTasks {
		if param.VideoTasks[i].Type == "" {
			param.VideoTasks[i].Type = "video"
		}
	}
	response := &ContextScanResponse{}
	err = reviewClient.Check(contentType, bizType, param, response)
	if err != nil {
		return
	}
	result = ReviewResult{Suggestion: SuggestionReview}
	results = make(map[string]map[string]ReviewResult)
	if response.Code == 200 {
		if len(response.Data.Suggestion) > 0 {
			result = ReviewResult{Suggestion: response.Data.Suggestion, Label: response.Data.Label}
			for _, textResult := range response.Data.TextResults {
				textResults := getSpecialResults(results, "text")
				textResults[textResult.Text] = ReviewResult{Suggestion: SuggestionReview}
				if textResult.Code == 200 {
					textResults[textResult.Text] = ReviewResult{Suggestion: textResult.Suggestion, Label: textResult.Label}
				}
			}
			for _, audioResult := range response.Data.AudioResults {
				auditResults := getSpecialResults(results, "audio")
				auditResults[audioResult.Url] = ReviewResult{Suggestion: SuggestionReview}
				if audioResult.Code == 200 {
					auditResults[audioResult.Url] = ReviewResult{Suggestion: audioResult.Suggestion, Label: audioResult.Label}
				}
			}
			for _, imageResult := range response.Data.ImageResults {
				imageResults := getSpecialResults(results, "image")
				imageResults[imageResult.Url] = ReviewResult{Suggestion: SuggestionReview}
				if imageResult.Code == 200 {
					imageResults[imageResult.Url] = ReviewResult{Suggestion: imageResult.Suggestion, Label: imageResult.Label}
				}
			}
			//!!! 视频类的安全部门对应的接口传图片截图，看是否考虑用人审
			for _, videoTask := range param.VideoTasks {
				videoResults := getSpecialResults(results, "video")
				videoResults[videoTask.Url] = ReviewResult{Suggestion: SuggestionReview}
			}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}
	return
}

func getSpecialResults(results map[string]map[string]ReviewResult, typ string) map[string]ReviewResult {
	specialResults := results[typ]
	if specialResults == nil {
		specialResults = make(map[string]ReviewResult)
		results[typ] = specialResults
	}
	return specialResults
}
