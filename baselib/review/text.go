package review

import (
	"errors"
	"strings"

	"xim/baselib/server/env"
)

type TextScanTask struct {
	Type   string  `json:"type"`
	Text   string  `json:"text"`
	DataId string  `json:"dataId"`
	Origin *Origin `json:"origin"` //{"owner": "uid99811"}	需要透传(回调或到人审)的内容. 字段需要约定好.
}

type TextScanRequest struct {
	BaseReq
	Tasks []TextScanTask `json:"tasks"`
}

type TextScanData struct {
	Code       int        `json:"code"`
	Msg        string     `json:"msg"`
	DataId     string     `json:"dataId"`
	TaskId     string     `json:"taskId"`
	Text       string     `json:"text"`
	Suggestion Suggestion `json:"suggestion"`
	Label      string     `json:"label"`
}

type TextScanResponse struct {
	Code int            `json:"code"`
	Msg  string         `json:"msg"`
	Data []TextScanData `json:"data"`
}

func TextScan(text, bizType string, origin *Origin) (result ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, nil
	}
	if strings.TrimSpace(text) == "" {
		err = ErrReviewContentEmpty
		return
	}
	params := TextScanRequest{
		BaseReq: GetBaseReq(bizType),
	}
	textScanTask := TextScanTask{Text: text}

	if origin != nil {
		textScanTask.Origin = origin
		if len(origin.DataId) > 0 {
			textScanTask.DataId = origin.DataId
		}
	}
	params.Tasks = append(params.Tasks, textScanTask)

	response := &TextScanResponse{}
	result = ReviewResult{Suggestion: SuggestionReview}
	err = reviewClient.Check(ContentText, bizType, params, response)
	if err != nil {
		return
	}

	if response.Code == 200 {
		if len(response.Data) > 0 && response.Data[0].Code == 200 {
			result = ReviewResult{Suggestion: response.Data[0].Suggestion, Label: response.Data[0].Label}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}
	return
}

func TextsScan(texts []string, bizType string, origin *Origin) (result ReviewResult, details map[string]ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		details = map[string]ReviewResult{}
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, details, nil
	}
	if len(texts) == 0 {
		err = ErrReviewContentEmpty
		return
	}
	params := TextScanRequest{
		BaseReq: GetBaseReq(bizType),
	}

	for _, text := range texts {
		textScanTask := TextScanTask{Text: text}
		if origin != nil {
			textScanTask.Origin = origin
			if len(origin.DataId) > 0 {
				textScanTask.DataId = origin.DataId
			}
		}
		params.Tasks = append(params.Tasks, textScanTask)
	}
	response := &TextScanResponse{}

	err = reviewClient.Check(ContentText, bizType, params, response)
	if err != nil {
		return
	}
	details = make(map[string]ReviewResult)
	if response.Code == 200 {
		for _, data := range response.Data {
			if data.Code == 200 {
				r := ReviewResult{Suggestion: data.Suggestion, Label: data.Label}
				details[data.Text] = r
				if r.Suggestion.IsMoreWorse(result.Suggestion) {
					result = r
				}
			}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}
	if len(details) == len(texts) {
		return
	}

	for _, text := range texts {
		r := ReviewResult{Suggestion: SuggestionReview}
		details[text] = r
		if r.Suggestion.IsMoreWorse(result.Suggestion) {
			result = r
		}
	}
	return
}
