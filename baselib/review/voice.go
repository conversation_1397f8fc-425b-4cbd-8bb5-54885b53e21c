package review

import (
	"errors"
	"strings"

	"xim/baselib/server/env"
)

type AudioScanTask struct {
	Type   string  `json:"type"`
	Url    string  `json:"url"`
	Md5    string  `json:"md5"`
	DataId string  `json:"dataId"`
	Origin *Origin `json:"origin"` //{"owner": "uid99811"} 需要透传(回调或到人审)的内容. 字段需要约定好.
}

type AudioScanRequest struct {
	BaseReq
	Tasks []AudioScanTask `json:"tasks"`
}

type AudioScanDataResult struct {
	Scene      string `json:"scene"`
	Label      string `json:"label"`
	Suggestion string `json:"suggestion"`
}

type AudioScanData struct {
	Code       int                   `json:"code"`
	Msg        string                `json:"msg"`
	Suggestion Suggestion            `json:"suggestion"`
	Label      string                `json:"label"`
	Url        string                `json:"url"`
	DataId     string                `json:"dataId"`
	TaskId     string                `json:"taskId"`
	Results    []AudioScanDataResult `json:"results"`
}

type AudioScanResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []AudioScanData `json:"data"`
}

func AudioScan(dataId string, url string, bizType string, origin *Origin) (result ReviewResult, err error) {
	// 测试环境不在走内部审核了
	if !env.IsProd() {
		return ReviewResult{
			Suggestion: SuggestionPass,
		}, nil
	}
	if strings.TrimSpace(url) == "" {
		result = ReviewResult{Suggestion: SuggestionReject}
		err = ErrReviewContentEmpty
		return
	}
	params := AudioScanRequest{
		BaseReq: GetBaseReq(bizType),
	}
	audioScanTask := AudioScanTask{Url: url, DataId: dataId}

	if origin != nil {
		audioScanTask.Origin = origin
		if len(origin.DataId) > 0 {
			audioScanTask.DataId = origin.DataId
		}
	}
	params.Tasks = append(params.Tasks, audioScanTask)

	response := &AudioScanResponse{}
	err = reviewClient.Check(ContentVoice, bizType, params, response)
	if err != nil {
		return
	}
	result = ReviewResult{Suggestion: SuggestionReview}
	if response.Code == 200 {
		if len(response.Data) > 0 && response.Data[0].Code == 200 {
			result = ReviewResult{Suggestion: response.Data[0].Suggestion, Label: response.Data[0].Label}
		}
	} else {
		if len(response.Msg) != 0 {
			err = errors.New(response.Msg)
			return
		}
	}
	return
}
