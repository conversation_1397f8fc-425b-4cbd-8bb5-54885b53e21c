package httpserver

import (
	"fmt"

	"xim/baselib/dingding"
	"xim/baselib/server/env"
	"xim/baselib/util"

	"github.com/gin-gonic/gin"
)

type registerFunc = func(engine gin.IRouter)

func RunGin(f registerFunc) {
	g := gin.Default()
	g.<PERSON><PERSON>(noRouterHandler)
	g.NoMethod(noMethod)
	g.Use(Recover, GinLog)
	f(g)
	g.<PERSON>(":9000")
}

func noRouterHandler(c *gin.Context) {
	defer util.Recover()
	path := c.Request.URL.Path
	method := c.Request.Method
	dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: env.GetServiceName() + " " + fmt.Sprintf("method %s path %s not found", method, path),
		},
		Link:     dingding.Link{},
		Markdown: dingding.Markdown{},
		At:       dingding.At{},
	})
}

func noMethod(c *gin.Context) {
	defer util.Recover()
	path := c.Request.URL.Path
	method := c.Request.Method
	dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: env.GetServiceName() + " " + fmt.Sprintf("method %s path %s not found", method, path),
		},
		Link:     dingding.Link{},
		Markdown: dingding.Markdown{},
		At:       dingding.At{},
	})
}
