package msgchan

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/hashicorp/go-uuid"

	"xim/baselib/config"
	"xim/baselib/grpccli"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/util"
	"xim/proto/api/basemsgcallin"

	"golang.org/x/exp/rand"
)

const (
	RetOK           = 0      //成功
	RetInvalidParam = 180400 // 参数错误
	RetSignError    = 180401 // 签名错误

	devPlatFormId = "1004"
	devSecret     = "fsnq7vFSF9xffs"

	testPlatformId = "4"
	testSecret     = "fsnq7vFSF9xffs"

	onlinePlatformId = "4"
	onlineSecret     = "B8n37v4Xq95cfs"
)

const (
	PublishTypeSingleUser = "1"
	PublishTypeTopic      = "2"
)

var msgChan = &struct {
	config      *config.MsgChanConfig
	httpClient  basemsgcallin.SClient
	localClient basemsgcallin.SClient
	signature   *Signature
	useNew      bool
}{}

//func Init(config *config.MsgChanConfig) error {
//	logger.Infof("msgchan config %s", util.JsonStr(config))
//	if config != nil {
//		grpcConn, err := grpccli.NewConn("127.0.0.1:9002")
//		if err != nil {
//			logger.Infof("error %v", err)
//			return fmt.Errorf("init auth client, err:%v", err)
//		}
//
//		msgChan.config = config
//		msgChan.signature = NewSignature(config.Secret, config.Duration)
//		msgChan.client = bizmsgchan.NewSClient(grpcConn)
//
//		logger.Infof("msgchan init ok ")
//	}
//	return nil
//}

func InMsgChanWhitList(userid int64) bool {
	switch userid {
	case 623271, 623291, 623464:
		return true
	default:
		return false
	}
}

func Init() error {
	var conf *config.MsgChanConfig

	conf = &config.MsgChanConfig{
		PlatformId: onlinePlatformId,
		Secret:     onlineSecret,
		Duration:   600,
	}

	if conf == nil {
		logger.Panicf("no found msgchan config")
	}
	logger.Infof("msgchan conf %s", util.JsonStr(conf))

	msgChan.config = conf
	msgChan.signature = NewSignature(conf.Secret, time.Duration(conf.Duration)*time.Second)
	msgChan.signature.SetUserReflect(true)
	localConn, err := grpccli.NewLocalConn()
	if err != nil {
		logger.Errorf("error %v", err)
		return err
	}
	msgChan.localClient = basemsgcallin.NewSClient(localConn)
	msgChan.useNew = true // 强制使用新的
	logger.Infof("msgchan init local client success")

	return nil
}

func GetTokenData(ctx context.Context, userId int64, did string) (data *basemsgcallin.MsgTokenData, err error) {
	req, err := makeGetTokenSignReq(userId, did)
	if err != nil {
		logger.Errorf("GetToken error %v", err)
		return
	}
	var (
		resp *basemsgcallin.GetMsgConnTokenRsp
	)
	resp, err = msgChan.localClient.GetMsgConnToken(ctx, req)

	if err != nil {
		logger.Errorf("GetToken error %v", err)
		return
	}
	if resp.GetBase().GetCode() == RetOK {
		data = resp.Data
	} else {
		logger.Errorf("GetToken error %v", resp.GetBase().GetMsg())
		err = fmt.Errorf("error resp %s", resp.GetBase().GetMsg())
	}
	return
}

func PublishRawMsg(userid int64, excludeDevId string, data string, msgType string) error {
	return publishRawMsg(userid, "", excludeDevId, data, msgType)
}

func PublishRawMsgToDid(userid int64, devId string, data string, msgType string) error {
	return publishRawMsg(userid, devId, "", data, msgType)
}

func publishRawMsg(userid int64, devId, excludeDevId string, data string, msgType string) error {
	nonceStr, err := uuid.GenerateUUID()
	if err != nil {
		nonceStr = fmt.Sprintf("%d%d", time.Now().UnixNano(), rand.Int63n(100000000)+100000000)
	}
	target := &basemsgcallin.PublishTargetInfo{
		UserId:           strconv.FormatInt(userid, 10),
		DeviceId:         devId,
		Topic:            "",
		ExcludeDeviceIds: nil,
	}
	if excludeDevId != "" {
		target.ExcludeDeviceIds = append(target.ExcludeDeviceIds, excludeDevId)
	}
	req := &basemsgcallin.PublishMsgReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   nonceStr,
		Sign:       "",
		Payload:    data,
		Type:       PublishTypeSingleUser,
		TargetJson: util.JsonStr(target),
	}
	var resp *basemsgcallin.PublishMsgRsp
	defer func() {
		label := map[string]string{
			"result":       "-1",
			"error":        "",
			"msg_type":     msgType,
			"session_type": "",
		}
		if err != nil {
			label["result"] = err.Error()
		}
		if resp != nil {
			label["result"] = fmt.Sprintf("%d", resp.GetBase().GetCode())
		}
		metric.CounterWithLabels("msg_downstream", label).Inc()
	}()
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign
	if msgChan.useNew || InMsgChanWhitList(userid) {
		resp, err = msgChan.localClient.PublishMsg(context.Background(), req)
	} else {
		resp, err = msgChan.httpClient.PublishMsg(context.Background(), req)
	}
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}

func makeGetTokenSignReq(userId int64, did string) (req *basemsgcallin.GetMsgConnTokenReq, err error) {
	nonceStr, err := uuid.GenerateUUID()
	if err != nil {
		nonceStr = fmt.Sprintf("%d%d", time.Now().UnixNano(), rand.Int63n(100000000)+100000000)
	}
	req = &basemsgcallin.GetMsgConnTokenReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   nonceStr,
		UserId:     fmt.Sprintf("%d", userId),
		DeviceId:   did,
		ExtendData: "{\"scheme\":\"ws\"}",
	}

	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makeGetTokenSignReq error %v, %v", err, util.JsonStr(req))
		return
	}
	req.Sign = sign
	return
}

func Subscribe(userid int64, topics []string) (err error) {
	req := &basemsgcallin.SubscribeTopicReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		UserId:     strconv.FormatInt(userid, 10),
		DeviceId:   "",
		Topics:     util.JsonStr(topics),
	}
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign

	resp, err := msgChan.localClient.SubscribeTopic(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		if resp.GetBase().GetCode() == 180403 {
			// 长连接不存在
			return nil
		}
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}

func UnSubscribe(userid int64, topics []string) (err error) {
	req := &basemsgcallin.UnSubscribeTopicReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		UserId:     strconv.FormatInt(userid, 10),
		DeviceId:   "",
		Topics:     util.JsonStr(topics),
	}
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign

	resp, err := msgChan.localClient.UnSubscribeTopic(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}

func UserTopics(userid int64) (topics []string, err error) {
	req := &basemsgcallin.UsersTopicsReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		UserDevices: util.JsonStr([]basemsgcallin.UserDevice{
			{
				UserId:   strconv.FormatInt(userid, 10),
				DeviceId: "",
			},
		}),
	}
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return
	}
	req.Sign = sign

	resp, err := msgChan.localClient.UsersTopics(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		err = fmt.Errorf("error resp.Result %v", resp.GetBase().GetMsg())
		return
	}
	if resp.Data == nil {
		err = fmt.Errorf("error resp.Data nil")
		return
	}
	userTopic := resp.Data.UserTopics[strconv.FormatInt(userid, 10)]
	if userTopic == nil {
		return nil, nil
	}
	var filterMap = map[string]bool{}
	for _, data := range userTopic.List {
		for _, topic := range data.Topics {
			if !filterMap[topic] {
				topics = append(topics, topic)
				filterMap[topic] = true
			}
		}
	}
	return
}

func Broadcast(topic string, data string) (err error) {
	req := &basemsgcallin.PublishMsgReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		Payload:    data,
		Type:       PublishTypeTopic,
		TargetJson: util.JsonStr(basemsgcallin.PublishTargetInfo{
			UserId:           "",
			DeviceId:         "",
			ExcludeDeviceIds: nil,
			Topic:            topic,
			IncludeUserIds:   nil,
			ExcludeUserIds:   nil,
			BroadcastRate:    0,
			LimitList:        nil,
		}),
	}
	var resp *basemsgcallin.PublishMsgRsp
	defer func() {
		label := map[string]string{
			"result": "-1",
			"error":  "",
		}
		if err != nil {
			label["result"] = err.Error()
		}
		if resp != nil {
			label["result"] = fmt.Sprintf("%d", resp.GetBase().GetCode())
		}
		metric.CounterWithLabels("msgchan_broadcast", label).Inc()
	}()
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign
	resp, err = msgChan.localClient.PublishMsg(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}
