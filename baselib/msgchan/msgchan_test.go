package msgchan

import (
	"encoding/json"
	"fmt"
	"testing"

	"xim/proto/api/xllivempmsgcallin"

	"google.golang.org/protobuf/encoding/protojson"
)

func TestProtoJson(t *testing.T) {
	v := &xllivempmsgcallin.PublishTargetInfo{
		UserId:   "123",
		DeviceId: "3",
		Topic:    "4",
	}
	jsonData, _ := json.Marshal(v)
	protoJsonData, _ := protojson.Marshal(v)
	fmt.Println(string(jsonData))
	fmt.Println(string(protoJsonData))
}
