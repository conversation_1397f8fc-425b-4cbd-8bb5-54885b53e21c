package msgchan

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/proto/api/basemsgcallin"

	"google.golang.org/grpc"
)

func NewHttpClient(baseUrl string) *HttpClient {
	return &HttpClient{
		client: http.Client{
			Transport: &http.Transport{
				Proxy:                  nil,
				DialContext:            nil,
				Dial:                   nil,
				DialTLSContext:         nil,
				DialTLS:                nil,
				TLSClientConfig:        nil,
				TLSHandshakeTimeout:    0,
				DisableKeepAlives:      false,
				DisableCompression:     false,
				MaxIdleConns:           30,
				MaxIdleConnsPerHost:    30,
				MaxConnsPerHost:        30,
				IdleConnTimeout:        time.Minute * 10,
				ResponseHeaderTimeout:  0,
				ExpectContinueTimeout:  0,
				TLSNextProto:           nil,
				ProxyConnectHeader:     nil,
				GetProxyConnectHeader:  nil,
				MaxResponseHeaderBytes: 0,
				WriteBufferSize:        0,
				ReadBufferSize:         0,
				ForceAttemptHTTP2:      false,
			},
		},
		baseUrl: baseUrl,
	}
}

type HttpClient struct {
	client  http.Client
	baseUrl string
}

func (h *HttpClient) post(url string, req interface{}) (resp []byte, err error) {
	st := time.Now()
	reqBytes, err := json.Marshal(req)

	defer func() {
		logger.Infof("MSGCHAN http_client url %s req %s resp %v", url, string(reqBytes), string(resp))
		var res = "0"
		if err != nil {
			res = "1"
		}
		metric.HistogramLabelsWithBuckets("http_msg_chan", map[string]string{
			"res": res,
			"url": url,
		}, []float64{10, 20, 30, 40, 50, 100, 150, 200, 300, 400, 500, 1000}).Observe(time.Since(st).Seconds() * 1000)
	}()

	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	httpReq, err := http.NewRequest(http.MethodPost, h.baseUrl+url, bytes.NewBuffer(reqBytes))
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	httpReq.Header.Set("Content-Type", "application/json")
	httpResp, err := h.client.Do(httpReq)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error http %d", httpResp.StatusCode)
	}
	defer httpResp.Body.Close()
	resp, err = ioutil.ReadAll(httpResp.Body)
	logger.Infof("MSGCHAN http_client url %s req %s resp %v", url, string(reqBytes), string(resp))
	return
}

func (h *HttpClient) Test(ctx context.Context, in *basemsgcallin.TestReq, opts ...grpc.CallOption) (result *basemsgcallin.TestRsp, err error) {
	bytes, err := h.post("/xllivemp.basemsgcallin.s/v1/Test.json", in)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return
}

func (h *HttpClient) GetMsgConnToken(ctx context.Context, in *basemsgcallin.GetMsgConnTokenReq, opts ...grpc.CallOption) (result *basemsgcallin.GetMsgConnTokenRsp, err error) {
	bytes, err := h.post("/xllivemp.basemsgcallin.s/v1/GetMsgConnToken.json", in)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return
}

func (h *HttpClient) PublishMsg(ctx context.Context, in *basemsgcallin.PublishMsgReq, opts ...grpc.CallOption) (result *basemsgcallin.PublishMsgRsp, err error) {
	bytes, err := h.post("/xllivemp.basemsgcallin.s/v1/PublishMsg.json", in)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return
}

func (h *HttpClient) SubscribeTopic(ctx context.Context, in *basemsgcallin.SubscribeTopicReq, opts ...grpc.CallOption) (result *basemsgcallin.SubscribeTopicRsp, err error) {
	bytes, err := h.post("/xllivemp.basemsgcallin.s/v1/SubscribeTopic.json", in)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return
}

func (h *HttpClient) UnSubscribeTopic(ctx context.Context, in *basemsgcallin.UnSubscribeTopicReq, opts ...grpc.CallOption) (result *basemsgcallin.UnSubscribeTopicRsp, err error) {
	bytes, err := h.post("/xllivemp.basemsgcallin.s/v1/UnSubscribeTopic.json", in)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return
}

func (h *HttpClient) UsersTopics(ctx context.Context, in *basemsgcallin.UsersTopicsReq, opts ...grpc.CallOption) (result *basemsgcallin.UsersTopicsRsp, err error) {
	bytes, err := h.post("/xllivemp.basemsgcallin.s/v1/UsersTopics.json", in)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return
}

var _ basemsgcallin.SClient = new(HttpClient)
