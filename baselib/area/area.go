package area

import (
	_ "embed"
	"encoding/json"
	"strings"
)

//go:embed area-full.json
var areaJsonBytes []byte

type rawAreaInfo struct {
	Citycode  json.RawMessage `json:"citycode"`
	Adcode    string          `json:"adcode"`
	Name      string          `json:"name"`
	Center    string          `json:"center"`
	Level     string          `json:"level"`
	Districts []rawAreaInfo   `json:"districts"`
}

func init() {
	var list []rawAreaInfo
	err := json.Unmarshal(areaJsonBytes, &list)
	if err != nil {
		panic(err)
	}
	for _, info := range list {
		visit(info)
	}
}

func visit(info rawAreaInfo, list ...rawAreaInfo) {
	switch info.Level {
	case "province":
		adCodeAreaMap[info.Adcode] = AreaInfo{
			Citycode:     "",
			Adcode:       info.Adcode,
			Name:         info.Name,
			Level:        info.Level,
			CityName:     "",
			ProvinceName: info.Name,
		}
		for _, district := range info.Districts {
			visit(district, info)
		}
	case "city":
		if len(list) != 1 {
			panic("error")
		}
		adCodeAreaMap[info.Adcode] = AreaInfo{
			Citycode:     string(info.Citycode),
			Adcode:       info.Adcode,
			Name:         info.Name,
			Level:        info.Level,
			CityName:     info.Name,
			ProvinceName: list[0].Name,
		}
		for _, district := range info.Districts {
			visit(district, list[0], info)
		}
	case "district":
		if len(list) == 1 {
			adCodeAreaMap[info.Adcode] = AreaInfo{
				Citycode:     removeSign(string(info.Citycode)),
				Adcode:       info.Adcode,
				Name:         info.Name,
				Level:        info.Level,
				CityName:     info.Name,
				ProvinceName: list[0].Name,
			}
		} else if len(list) == 2 {
			adCodeAreaMap[info.Adcode] = AreaInfo{
				Citycode:     removeSign(string(info.Citycode)),
				Adcode:       info.Adcode,
				Name:         info.Name,
				Level:        info.Level,
				CityName:     list[1].Name,
				ProvinceName: list[0].Name,
			}
		} else {
			panic("error")
		}
	}
}

func removeSign(s string) string {
	return strings.Trim(s, "\"")
}

var (
	adCodeAreaMap = map[string]AreaInfo{}
)

func SearchByAdCode(adcode string) AreaInfo {
	return adCodeAreaMap[adcode]
}

type AreaInfo struct {
	Citycode     string `json:"citycode"`
	Adcode       string `json:"adcode"`
	Name         string `json:"name"`
	Level        string `json:"level"`
	CityName     string `json:"city_name"`
	ProvinceName string `json:"province_name"`
}
