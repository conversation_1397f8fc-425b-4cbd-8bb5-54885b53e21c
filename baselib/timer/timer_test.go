package timer

import (
	"testing"
	"time"
)

func TestTimer(t *testing.T) {
	wt := weeklyTimer{
		at: &AtWeeklyTime{
			Day:    time.Tuesday,
			Hour:   4,
			Minute: 1,
			Second: 0,
		},
		lastUpTime: time.Time{},
	}
	n := time.Date(2022, 6, 28, 4, 1, 0, 0, time.Local)
	if !wt.IsTimeUp(n.Unix()) {
		panic("")
	}
	if wt.IsTimeUp(n.Unix()) {
		panic("")
	}

	dt := dailyTimer{
		at: &AtDailyTime{
			Hour:   4,
			Minute: 1,
			Second: 0,
		},
		lastUpTime: time.Time{},
	}
	if !dt.IsTimeUp(n.Unix()) {
		panic("")
	}
	if dt.IsTimeUp(n.Unix()) {
		panic("")
	}

	mt := monthlyTimer{
		at: &AtMonthlyTime{
			Day:    28,
			Hour:   4,
			Minute: 1,
			Second: 0,
		},
		lastUpTime: time.Time{},
	}
	if !mt.IsTimeUp(n.Unix()) {
		panic("")
	}
	if mt.IsTimeUp(n.Unix()) {
		panic("")
	}

}
