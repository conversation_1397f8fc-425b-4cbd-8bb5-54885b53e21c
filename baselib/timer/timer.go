package timer

import (
	"time"

	"xim/baselib/logger"
)

type TimerInterface interface {
	IsTimeUp(now int64) bool
	Reset(ts int64)
	End() bool
}

var (
	_ TimerInterface = new(commonTimer)
	_ TimerInterface = new(dailyTimer)
	_ TimerInterface = new(monthlyTimer)
	_ TimerInterface = new(weeklyTimer)
)

type weeklyTimer struct {
	at *AtWeeklyTime

	lastUpTime time.Time
}

func (d *weeklyTimer) IsTimeUp(now int64) bool {
	nowTime := time.Unix(now, 0)
	// 判断本周是否已经执行过
	last := d.lastUpTime
	y1, w1 := last.ISOWeek()
	y2, w2 := nowTime.ISOWeek()
	if y1 == y2 && w1 == w2 {
		return false
	}
	// 判断是否到本周的执行时间
	at := d.at
	if nowTime.Weekday() >= at.Day && nowTime.Hour() >= at.Hour && nowTime.Minute() >= at.Minute && nowTime.Second() >= at.Second {
		d.lastUpTime = nowTime
		return true
	}
	return false
}

func (w *weeklyTimer) Reset(ts int64) {
	return
}

func (w *weeklyTimer) End() bool {
	return false
}

type monthlyTimer struct {
	at *AtMonthlyTime

	lastUpTime time.Time
}

func (d *monthlyTimer) IsTimeUp(now int64) bool {
	nowTime := time.Unix(now, 0)
	// 判断本月是否已经执行过
	last := d.lastUpTime
	if last.Year() == nowTime.Year() && last.Month() == nowTime.Month() {
		return false
	}
	// 判断是否到本月的执行时间
	n := time.Now()
	at := d.at
	atTime := time.Date(n.Year(), n.Month(), at.Day, at.Hour, at.Minute, at.Second, 0, n.Location())
	if nowTime.After(atTime) || nowTime.Equal(atTime) {
		d.lastUpTime = nowTime
		return true
	}
	return false
}

func (m *monthlyTimer) Reset(ts int64) {
	return
}

func (m *monthlyTimer) End() bool {
	return false
}

type dailyTimer struct {
	at *AtDailyTime

	lastUpTime time.Time
}

func (d *dailyTimer) IsTimeUp(now int64) bool {
	nowTime := time.Unix(now, 0)
	// 判断今天是否已经执行过
	last := d.lastUpTime
	if last.Year() == nowTime.Year() && last.Month() == nowTime.Month() && last.Day() == nowTime.Day() {
		return false
	}
	// 判断是否到今天的执行时间
	at := d.at
	atTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), at.Hour, at.Minute, at.Second, 0, nowTime.Location())
	if nowTime.After(atTime) || nowTime.Equal(atTime) {
		d.lastUpTime = nowTime
		return true
	}
	return false
}

func (d *dailyTimer) Reset(ts int64) {
}

func (d *dailyTimer) End() bool {
	return false
}

// 普通的timer
type commonTimer struct {
	timeType       TaskTimeType
	atTimeUp       int64
	afterTimeUp    int64
	internalTimeUp int64
	internalTime   int64
	begin          int64
	beginTimeUp    int64
	endTimeUp      int64
	end            bool
	first          bool
}

func (t *commonTimer) IsTimeUp(now int64) bool {
	if t.end {
		return false
	}
	if t.timeType == TaskTimeTypeAfter {
		return t.afterTimeUp <= now
	} else if t.timeType == TaskTimeTypeAt {
		return t.atTimeUp == now
	} else if t.timeType == TaskTimeTypeInternal {
		return t.internalTimeUp <= now
	} else if t.timeType == TaskTimeTypeAfterAndInternal {
		if !t.first && t.afterTimeUp <= now {
			t.first = true
			return true
		} else if t.first {
			return t.internalTimeUp <= now
		}
	} else if t.timeType == TaskTimeTypeBetween {
		if now >= t.endTimeUp {
			t.end = true
			return false
		}
		if !t.first && t.beginTimeUp <= now {
			t.first = true
			return true
		} else if t.internalTimeUp <= now {
			return true
		}
	}
	return false
}

func (t *commonTimer) Reset(ts int64) {
	if t.end {
		return
	}
	if t.timeType == TaskTimeTypeAt || t.timeType == TaskTimeTypeAfter {
		t.end = true
	}
	if t.timeType == TaskTimeTypeInternal || t.timeType == TaskTimeTypeAfterAndInternal || t.timeType == TaskTimeTypeBetween {
		t.internalTimeUp = ts + t.internalTime
	}
}

func (t *commonTimer) End() bool {
	return t.end
}

// 普通的timer
func createTimer(task Task, now int64, timeType TaskTimeType) TimerInterface {
	switch timeType {
	case TaskTimeTypeAt, TaskTimeTypeInternal, TaskTimeTypeAfter, TaskTimeTypeAfterAndInternal, TaskTimeTypeBetween:
		return createCommonTimer(task, now, timeType)
	case TaskTimeTypeDaily:
		return &dailyTimer{
			at:         task.TaskTime().AtDaily,
			lastUpTime: time.Time{},
		}
	case TaskTimeTypeWeekly:
		return &weeklyTimer{
			at:         task.TaskTime().AtWeekly,
			lastUpTime: time.Time{},
		}
	case TaskTimeTypeMonthly:
		return &monthlyTimer{
			at:         task.TaskTime().AtMonthly,
			lastUpTime: time.Time{},
		}
	default:
		logger.Panic("error unknown task time type")
		panic("")
	}
}

// 对象池
func createCommonTimer(task Task, now int64, timeType TaskTimeType) *commonTimer {
	t := &commonTimer{}
	t.timeType = timeType
	bt := task.TaskTime()
	t.internalTime = int64(bt.Interval / time.Second)
	t.internalTimeUp = now + t.internalTime

	t.afterTimeUp = now + int64(bt.After/time.Second)
	if !task.TaskTime().At.IsZero() {
		t.atTimeUp = bt.At.Unix()
	}
	if !task.TaskTime().Begin.IsZero() {
		t.begin = bt.Begin.Unix()
		t.beginTimeUp = bt.Begin.Unix()
		t.endTimeUp = bt.End.Unix()
		for t.beginTimeUp < now {
			t.beginTimeUp += t.internalTime
		}
	}
	if t.timeType == TaskTimeTypeBetween {
		t.internalTimeUp = t.beginTimeUp + t.internalTime
	}
	return t
}
