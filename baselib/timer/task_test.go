package timer

import (
	"testing"
	"time"

	"xim/baselib/logger"
)

type testTask struct {
	*BaseTask
}

// 各个业务根据需要 需要初始化提供对应初始化接口，没有使用BaseTask
func (t *testTask) Init() error {
	logger.Infof("name:%v init", t.name)
	return nil
}

func (t *testTask) Execute() error {
	logger.Infof("name:%v execute", t.name)
	return nil
}

// go test -v  -run TestTimerTaskAll
func TestTimerTaskAll(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	bAt := newBaseTask("TestTimerTaskAt", time.Now().Add(10*time.Second), timeZero, timeZero, 0, 0)
	bInternal := newBaseTask("TestTimerTaskInternal", timeZero, timeZero, timeZero, time.Second, 0)
	bAfter := newBaseTask("TestTimerTaskAfter", timeZero, timeZero, timeZero, 0, 5*time.Second)
	bAfterInternal := newBaseTask("TestTimerTaskAfterAndInternal", timeZero, timeZero, timeZero, time.Second, 5*time.Second)
	bBetween := newBaseTask("TestTimerTaskBetween", timeZero, time.Now().Add(-10*time.Second), time.Now().Add(88*time.Second), 5*time.Second, 0)

	at := &testTask{BaseTask: bAt}
	internal := &testTask{BaseTask: bInternal}
	after := &testTask{BaseTask: bAfter}
	afterInternal := &testTask{BaseTask: bAfterInternal}
	between := &testTask{BaseTask: bBetween}

	err := SubmitTask(at, internal, after, afterInternal, between)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}

// go test -v  -run TestTimerTaskAt
func TestTimerTaskAt(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	tt := newBaseTask("TestTimerTaskAt", time.Now().Add(10*time.Second), timeZero, timeZero, 0, 0)
	at := &testTask{BaseTask: tt}
	err := SubmitTask(at)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}

// go test -v  -run TestTimerTaskInternal
func TestTimerTaskInternal(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	tt := newBaseTask("TestTimerTaskInternal", timeZero, timeZero, timeZero, time.Second, 0)
	at := &testTask{BaseTask: tt}
	err := SubmitTask(at)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}

// go test -v  -run TestTimerTaskAfter
func TestTimerTaskAfter(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	tt := newBaseTask("TestTimerTaskAfter", timeZero, timeZero, timeZero, 0, 5*time.Second)
	at := &testTask{BaseTask: tt}
	err := SubmitTask(at)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}

// go test -v  -run TestTimerTaskAfterAndInternal
func TestTimerTaskAfterAndInternal(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	tt := newBaseTask("TestTimerTaskAfterAndInternal", timeZero, timeZero, timeZero, time.Second, 5*time.Second)
	at := &testTask{BaseTask: tt}
	err := SubmitTask(at)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}

// go test -v  -run TestTimerTaskBetween
func TestTimerTaskBetween(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	tt := newBaseTask("TestTimerTaskBetween", timeZero, time.Now().Add(5*time.Second), time.Now().Add(88*time.Second), 5*time.Second, 0)
	at := &testTask{BaseTask: tt}
	err := SubmitTask(at)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}

// go test -v  -run TestTimerTaskBetween1
func TestTimerTaskBetween1(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	tt := newBaseTask("TestTimerTaskBetween1", timeZero, time.Now().Add(-10*time.Second), time.Now().Add(88*time.Second), 5*time.Second, 0)
	at := &testTask{BaseTask: tt}
	err := SubmitTask(at)
	if err != nil {
		logger.Info(err.Error())
	}
	select {}
}
