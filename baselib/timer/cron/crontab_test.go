package cron

import (
	"testing"
	"time"

	"xim/baselib/logger"

	"github.com/robfig/cron"
)

// go test -v  -run TestCronTab
func TestCronTab(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	loc, _ := time.LoadLocation("Asia/Shanghai")
	crontab := cron.NewWithLocation(loc)

	wrapper := NewCrontabWrapper("print", 2, print)
	err := crontab.AddFunc("*/1 * * * * ?", wrapper.Do) // 每隔1s 一次
	if err != nil {
		logger.Error(err.Error())
		return

	}
	crontab.Start()
	select {}
}

func print() error {
	logger.Infof("123455")
	return nil
}
