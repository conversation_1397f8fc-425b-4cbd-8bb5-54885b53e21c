package cron

import (
	"xim/baselib/logger"
	"xim/baselib/util"
)

func NewCrontabWrapper(name string, concurrency int64, process func() error) (c *CrontabWrapper) {
	c = &CrontabWrapper{name: name, process: process}
	if concurrency <= 0 {
		concurrency = 1
	}
	c.allowMulti = true
	c.pool = make(chan bool, concurrency)
	for i := int64(0); i < concurrency; i++ {
		c.pool <- true
	}
	return
}

type CrontabWrapper struct {
	allowMulti    bool
	name          string
	pool          chan bool
	process       func() error
	warnThreshold int64
	warnCounter   int64
}

// 连续多少次超过并发限制后，报错
func (c *CrontabWrapper) SetWarnThreshold(threshold int64) {
	c.warnThreshold = threshold
}

func (c *CrontabWrapper) Do() {
	defer util.Recover()

	select {
	case <-c.pool:
		c.warnCounter = 0
	default:
		c.warnCounter++
		if c.warnCounter >= c.warnThreshold {
			logger.Errorf("%s, other instance is running", c.name)
		} else {
			logger.Warnf("%s, other instance is running", c.name)
		}
		return
	}
	defer func() {
		c.pool <- true
	}()
	err := c.process()
	if err != nil {
		logger.Errorf("crontab %s fail: %v", c.name, err)
	}
	return
}
