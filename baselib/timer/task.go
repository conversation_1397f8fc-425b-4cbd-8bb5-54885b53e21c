package timer

import (
	"time"

	"github.com/pkg/errors"
)

var (
	ErrTaskUnknow             = errors.New("unknown task")
	ErrTaskNameInvalid        = errors.New("task name is empty")
	ErrTaskTimeBetweenInvalid = errors.New("task time between invalid")
	ErrTaskTimeAtInvalid      = errors.New("task time at invalid")

	timeZero = time.Time{}
)

func newBaseTask(name string, at, begin, end time.Time, internal, after time.Duration) *BaseTask {
	return &BaseTask{name: name, taskTime: newTaskTime(0, at, begin, end, internal, after)}
}

func NewBaseTaskInternal(name string, internal time.Duration) *BaseTask {
	return &BaseTask{name: name, taskTime: newTaskTime(TaskTimeTypeInternal, timeZero, timeZero, timeZero, internal, 0)}
}

func NewBaseTaskAt(name string, at time.Time) *BaseTask {
	return &BaseTask{name: name, taskTime: newTaskTime(TaskTimeTypeAt, at, timeZero, timeZero, 0, 0)}
}

func NewBaseTaskAtWeekly(name string, atTime *AtWeeklyTime) *BaseTask {
	return &BaseTask{name: name, taskTime: TaskTime{TaskType: TaskTimeTypeWeekly, AtWeekly: atTime}}
}

func NewBaseTaskAtDaily(name string, atTime *AtDailyTime) *BaseTask {
	return &BaseTask{name: name, taskTime: TaskTime{TaskType: TaskTimeTypeDaily, AtDaily: atTime}}
}

func NewBaseTaskAtMonthly(name string, atTime *AtMonthlyTime) *BaseTask {
	return &BaseTask{name: name, taskTime: TaskTime{TaskType: TaskTimeTypeMonthly, AtMonthly: atTime}}
}

func NewBaseTaskAfter(name string, after time.Duration) *BaseTask {
	return &BaseTask{name: name, taskTime: newTaskTime(TaskTimeTypeAfter, timeZero, timeZero, timeZero, 0, after)}
}

func NewBaseTaskAfterAndInternal(name string, internal, after time.Duration) *BaseTask {
	return &BaseTask{name: name, taskTime: newTaskTime(TaskTimeTypeAfterAndInternal, timeZero, timeZero, timeZero, internal, after)}
}

func NewBaseTaskBetween(name string, internale time.Duration, begin, end time.Time) *BaseTask {
	return &BaseTask{name: name, taskTime: newTaskTime(TaskTimeTypeBetween, timeZero, begin, end, internale, 0)}
}

func SubmitTask(tasks ...Task) (err error) {
	for _, task := range tasks {
		if task.Name() == "" {
			return ErrTaskNameInvalid
		}
		tt := task.TaskTime()
		typ, err := tt.checkParam()
		if err != nil {
			return err
		}
		err = newTaskRunner(task, typ)
		if err != nil {
			return err
		}
	}
	return nil
}

type Task interface {
	Name() string
	Init() error
	Execute() error
	TaskTime() TaskTime
}

type BaseTask struct {
	name     string
	taskTime TaskTime
}

func (bt *BaseTask) Name() string {
	return bt.name
}

func (bt *BaseTask) TaskTime() TaskTime {
	return bt.taskTime
}

func (bt *BaseTask) Init() error {
	return nil
}

func (bt *BaseTask) Execute() error {
	return nil
}

type TaskTime struct {
	TaskType  TaskTimeType
	At        time.Time
	Interval  time.Duration
	After     time.Duration
	Begin     time.Time
	End       time.Time
	AtDaily   *AtDailyTime
	AtWeekly  *AtWeeklyTime
	AtMonthly *AtMonthlyTime
}

type AtDailyTime struct {
	Hour   int
	Minute int
	Second int
}

type AtWeeklyTime struct {
	Day    time.Weekday
	Hour   int
	Minute int
	Second int
}

type AtMonthlyTime struct {
	Day    int
	Hour   int
	Minute int
	Second int
}

func newTaskTime(t TaskTimeType, at, begin, end time.Time, internal, after time.Duration) TaskTime {
	return TaskTime{TaskType: t, At: at, Interval: internal, After: after, Begin: begin, End: end}
}

func (t TaskTime) checkParam() (typ TaskTimeType, err error) {
	switch t.TaskType {
	case TaskTimeTypeAt:
		if t.At.Before(time.Now()) {
			err = ErrTaskTimeAtInvalid
			return
		}
	case TaskTimeTypeInternal:
		if t.Interval <= 0 {
			err = ErrTaskTimeAtInvalid
			return
		}
	case TaskTimeTypeAfter:
	case TaskTimeTypeAfterAndInternal:
		if t.Interval <= 0 {
			err = ErrTaskTimeAtInvalid
			return
		}
	case TaskTimeTypeBetween:
		if t.Begin.IsZero() || t.End.IsZero() || t.Interval <= 0 || t.End.Before(t.Begin) || t.End.Before(time.Now()) || t.Begin.After(t.End) {
			err = ErrTaskTimeBetweenInvalid
			return
		}
	case TaskTimeTypeDaily:
		if t.AtDaily == nil {
			err = ErrTaskTimeAtInvalid
		}
	case TaskTimeTypeWeekly:
		if t.AtWeekly == nil {
			err = ErrTaskTimeAtInvalid
		}
	case TaskTimeTypeMonthly:
		if t.AtMonthly == nil {
			err = ErrTaskTimeAtInvalid
		}
	default:
		err = ErrTaskUnknow
	}
	return t.TaskType, nil
	//
	//typ = TaskTimeTypeUnknown
	//if !t.Begin.IsZero() || !t.End.IsZero() {
	//	if t.Begin.IsZero() || t.End.IsZero() || t.Interval <= 0 || t.End.Before(t.Begin) || t.End.Before(time.Now()) || t.Begin.After(t.End) {
	//		err = ErrTaskTimeBetweenInvalid
	//		return
	//	}
	//	typ = TaskTimeTypeBetween
	//} else if !t.At.IsZero() {
	//	if t.At.Before(time.Now()) {
	//		err = ErrTaskTimeAtInvalid
	//		return
	//	}
	//	typ = TaskTimeTypeAt
	//} else if t.After > 0 {
	//	typ = TaskTimeTypeAfter
	//	if t.Interval > 0 {
	//		typ = TaskTimeTypeAfterAndInternal
	//	}
	//} else if t.Interval > 0 {
	//	typ = TaskTimeTypeInternal
	//} else {
	//	err = ErrTaskUnknow
	//}
	//return
}
