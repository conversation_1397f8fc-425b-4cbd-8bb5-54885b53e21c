package runner

import (
	"runtime"
	"time"

	"xim/baselib/logger"
)

type Runner interface {
	Start()
	Stop()
}

type runner struct {
	name         string
	tickInternal time.Duration
	ticker       *time.Ticker
	stopChan     chan struct{}
	action       func() (bool, error)
}

func (r *runner) Start() {
	go func() {
	Loop:
		for {
			select {
			case <-r.ticker.C:
				{
					r.tick()
				}
			case <-r.stopChan:
				break Loop
			}
		}
	}()
	logger.Infof("name:%v,elapseTime:%v, timer,start", r.name, float64(r.tickInternal)/float64(time.Second))
}

func (r *runner) tick() {
	defer func() {
		err := recover()
		if err != nil {
			buf := make([]byte, 10240)
			n := runtime.Stack(buf, false)
			buf = buf[:n]
			logger.Errorf("tick crash: %v\n%s", err, string(buf))
		}
	}()
	end, err := r.action()
	if err != nil {
		logger.Errorf("action fail timer name:%v,err:%v", r.name, err)
	}
	if end {
		r.Stop()
	}
}

func (r *runner) Stop() {
	logger.Infof("name:%v, timer,end", r.name)
	r.ticker.Stop()
	r.stopChan <- struct{}{}
}

func NewRunner(name string, action func() (bool, error), tickInternal time.Duration) (Runner, error) {
	r := &runner{}
	r.name = name
	r.stopChan = make(chan struct{})
	r.ticker = time.NewTicker(tickInternal)
	r.tickInternal = tickInternal
	r.action = action
	return r, nil
}
