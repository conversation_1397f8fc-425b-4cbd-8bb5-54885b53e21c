package timer

import (
	"time"

	"xim/baselib/timer/runner"
)

type TaskTimeType int32

const (
	TaskTimeTypeUnknown          TaskTimeType = 0
	TaskTimeTypeAt               TaskTimeType = 1
	TaskTimeTypeInternal         TaskTimeType = 2
	TaskTimeTypeAfter            TaskTimeType = 3
	TaskTimeTypeAfterAndInternal TaskTimeType = 4
	TaskTimeTypeBetween          TaskTimeType = 5
	TaskTimeTypeDaily                         = 6
	TaskTimeTypeWeekly                        = 7
	TaskTimeTypeMonthly                       = 8
)

// 定时任务
type timerTask struct {
	timer TimerInterface
	t     Task
}

func (h *timerTask) run(ts int64) (bool, error) {
	if h.timer.IsTimeUp(ts) {
		h.timer.Reset(ts)
		return h.timer.End(), h.t.Execute()
	}
	return h.timer.End(), nil
}

// newTimerTask
func newTimerTask(task Task, typ TaskTimeType) *timerTask {
	now := time.Now().Unix()
	hbtt := &timerTask{}
	hbtt.timer = createTimer(task, now, typ)
	hbtt.t = task
	return hbtt
}

type taskRunner struct {
	task *timerTask
}

func (htr *taskRunner) tick() (bool, error) {
	return htr.task.run(time.Now().Unix())
}

func (htr *taskRunner) addTask(task Task, typ TaskTimeType) {
	htr.task = newTimerTask(task, typ)
}

func newTaskRunner(task Task, typ TaskTimeType) error {
	err := task.Init()
	if err != nil {
		return err
	}
	htr := &taskRunner{}
	r, err := runner.NewRunner(task.Name(), htr.tick, time.Second)
	if err != nil {
		return err
	}
	htr.addTask(task, typ)
	r.Start()
	return nil
}
