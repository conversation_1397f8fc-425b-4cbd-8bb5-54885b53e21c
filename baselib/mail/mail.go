package mail

import (
	"errors"
	"net/mail"

	"xim/proto/consts/config"

	"github.com/go-gomail/gomail"
)

type ContentType string

const (
	ContentText = "text/plain"
	ContentHtml = "text/html;charset=utf-8"
)

type MailData struct {
	To       []string
	Subject  string
	Body     string
	Attaches []string
	Type     ContentType
}

var (
	dialer   *gomail.Dialer
	mailFrom string
)

func initDialer() {
	if dialer == nil {
		dialer = gomail.NewDialer(config.MailSMTPServer, config.MailSMTPPort, config.MailAccount, config.MailPassword)
		from := mail.Address{
			Name:    config.MailSenderName,
			Address: config.MailSenderAddress,
		}
		mailFrom = from.String()
	}
	return
}

func Send(data *MailData) (err error) {
	if data == nil {
		err = errors.New("mail data nil")
		return
	}
	if len(data.To) == 0 {
		data.To = []string{config.MailReceiver}
	}
	if len(data.Subject) == 0 {
		err = errors.New("mail subject nil")
		return
	}
	if len(data.Body) == 0 {
		err = errors.New("mail content nil")
		return
	}

	initDialer()

	m := gomail.NewMessage()
	m.SetHeader("From", mailFrom)
	m.SetHeader("To", data.To...)
	m.SetHeader("Subject", data.Subject)
	if data.Type == "" {
		data.Type = ContentText
	}
	m.SetBody(string(data.Type), data.Body)
	for _, item := range data.Attaches {
		m.Attach(item)
	}

	return dialer.DialAndSend(m)
}
