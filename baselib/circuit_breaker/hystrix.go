package circuitbreaker

import (
	"github.com/afex/hystrix-go/hystrix"
)

type EntityCircuitBreaker struct {
	Timeout                int `yaml:"timeout"`                  // 执行command的超时时间。默认时间是1000毫秒
	MaxConcurrentRequests  int `yaml:"max_concurrent_request"`   // command的最大并发量 默认值是10
	RequestVolumeThreshold int `yaml:"request_volume_threshold"` // 一个统计窗口10秒内请求数量。达到这个请求数量后才去判断是否要开启熔断。默认值是20
	SleepWindow            int `yaml:"sleep_window"`             // 当熔断器被打开后，SleepWindow的时间就是控制过多久后去尝试服务是否可用了。默认值是5000毫秒
	ErrorPercentThreshold  int `yaml:"error_percent_threshold"`  // 错误百分比，请求数量大于等于RequestVolumeThreshold并且错误率到达这个百分比后就会启动熔断 默认值是50
}

func SetCCBConf(ccbConfs map[string]*EntityCircuitBreaker) {
	hystrix.Flush()
	for k, v := range ccbConfs {
		hystrix.ConfigureCommand(k, hystrix.CommandConfig{
			RequestVolumeThreshold: int(v.RequestVolumeThreshold),
			SleepWindow:            int(v.SleepWindow),
			Timeout:                int(v.Timeout),
			MaxConcurrentRequests:  int(v.MaxConcurrentRequests),
			ErrorPercentThreshold:  int(v.ErrorPercentThreshold),
		})
	}
}
