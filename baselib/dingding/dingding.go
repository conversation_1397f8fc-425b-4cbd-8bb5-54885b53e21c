package dingding

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"xim/baselib/logger"

	"xim/baselib/server/env"
)

// 机器人文档
// https://open-doc.dingtalk.com/docs/doc.htm?spm=a219a.7629140.0.0.karFPe&treeId=257&articleId=105735&docType=1
type MsgType string

const (
	DINGDING_MSG_TYPE_TEXT     MsgType = "text"
	DINGDING_MSG_TYPE_LINK             = "link"
	DINGDING_MSG_TYPE_MARKDOWN         = "markdown"
)

type DingDingMsg struct {
	MsgType  MsgType  `json:"msgtype"`
	Text     Text     `json:"text"`
	Link     Link     `json:"link"`
	Markdown Markdown `json:"markdown"`
	At       At       `json:"at"`
}

type Markdown struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}
type At struct {
	AtMobiles []string `json:"atMobiles"`
	AtUserIds []string `json:"atUserIds"`
	IsAtAll   bool     `json:"isAtAll"`
}

type Link struct {
	Text       string `json:"text"`
	Title      string `json:"title"`
	PicURL     string `json:"picUrl"`
	MessageURL string `json:"messageUrl"`
}

type Text struct {
	Content string `json:"content"`
}

type DingdingClient struct {
	Secret  string
	BaseUrl string
}

func RobotClient() DingdingClient {
	if env.IsProd() {
		return DingdingClient{
			Secret:  "SEC5a3de9b535ecdaeeeb00900db2f889b599087c409f9fe333e4cea5c553248baa",
			BaseUrl: "https://oapi.dingtalk.com/robot/send?access_token=751cb006cf4f32e50f1858d9b747f083bec05df502c37075f40a1c4dc6b1f726",
		}
	} else {
		return DingdingClient{
			Secret:  "SEC8a87cf76054bff715598ed797343ad947cfeb88c43ca88a03c5561ae21e8e874",
			BaseUrl: "https://oapi.dingtalk.com/robot/send?access_token=486b70c6f545e074696124acb107a7e3405c86d601ce5b7048b612811dacccb9",
		}
	}
}

func (c DingdingClient) Send(param DingDingMsg) (err error) {
	buf, err := json.Marshal(param)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	text := string(buf)
	timeStamp := strconv.FormatInt(time.Now().UnixNano()/1000000, 10)
	h := hmac.New(sha256.New, []byte(c.Secret))
	h.Write([]byte(timeStamp + "\n" + c.Secret))
	sign := base64.StdEncoding.EncodeToString(h.Sum(nil))

	v := url.Values{}
	v.Set("timestamp", timeStamp)
	v.Set("sign", sign)

	turl := c.BaseUrl + "&" + v.Encode()
	req, err := http.NewRequest(http.MethodPost, turl, bytes.NewBufferString(text))
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	req.Header.Set("content-type", "application/json; charset=utf-8")
	client := http.Client{Timeout: time.Second * 3}
	resp, err := client.Do(req)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		logger.Infof("dingding resp staus %s", resp.Status)
		return fmt.Errorf("error status %d", resp.StatusCode)
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	type DingDingResp struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}
	dresp := DingDingResp{}
	err = json.Unmarshal(data, &dresp)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if dresp.Errcode != 0 {
		msg := strings.ReplaceAll(string(data), "\n", "\t")
		logger.Infof("dingding resp %s", msg)
		return fmt.Errorf("resp %s", string(data))
	}
	return nil
}

func DingDingSendMsg(param *DingDingMsg, URL string) (err error) {
	buf, err := json.Marshal(param)
	if err != nil {
		return
	}
	req, err := http.NewRequest("POST", URL, bytes.NewReader(buf))
	if err != nil {
		return
	}
	req.Header.Add("Content-Type", "application/json;charset=utf-8")

	client := &http.Client{
		Timeout: time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	var respData struct {
		ErrCode int64  `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}
	err = json.Unmarshal(body, &respData)
	if err != nil {
		return
	}
	if respData.ErrCode != 0 {
		err = fmt.Errorf("dingding resp: %s", body)
	}
	return
}

func DingDingSendText(URL, content string, atMobiles []string) (err error) {
	param := &DingDingMsg{
		MsgType: DINGDING_MSG_TYPE_TEXT,
	}
	param.Text.Content = content
	param.At.AtMobiles = atMobiles
	return DingDingSendMsg(param, URL)
}

func DingDingSendLink(URL, title, text, msgURL, picURL string, atMobiles []string) (err error) {
	param := &DingDingMsg{
		MsgType: DINGDING_MSG_TYPE_LINK,
	}
	param.Link.Title = title
	param.Link.Text = text
	param.Link.MessageURL = msgURL
	param.Link.PicURL = picURL
	param.At.AtMobiles = atMobiles
	return DingDingSendMsg(param, URL)
}

func DingDingSendMarkdown(URL, title, text string, atMobiles []string) (err error) {
	param := &DingDingMsg{
		MsgType: DINGDING_MSG_TYPE_MARKDOWN,
	}
	param.Markdown.Title = title
	param.Markdown.Text = text
	param.At.AtMobiles = atMobiles
	return DingDingSendMsg(param, URL)
}
