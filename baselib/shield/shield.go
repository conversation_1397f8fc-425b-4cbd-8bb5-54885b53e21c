package shield

import (
	"encoding/json"

	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/server/env"
)

var (
	shieldCheckUrlTest   = "https://risk-account-ssl.office.k8s.xunlei.cn/shield/v1.1/check"
	shieldCheckUrlOnline = "https://risk-account-ssl.xunlei.com/shield/v1.1/check"
)

const (
	SuggestionUnknown Suggestion = "unknown"
	SuggestionPass    Suggestion = "pass"
	SuggestionReview  Suggestion = "review"
	SuggestionReject  Suggestion = "reject"
)

type Suggestion string

func (s Suggestion) IsPass() bool {
	return s == SuggestionPass
}

func (s Suggestion) IsReview() bool {
	return s == SuggestionReview
}

func (s Suggestion) IsReject() bool {
	return s == SuggestionReject
}

type EventType int32

const (
	EventTypeSignUp EventType = 1 + iota // 注册
)

func (e EventType) Valid() bool {
	switch e {
	case EventTypeSignUp:
		return true
	default:
		return false
	}
}

func (e EventType) GetShieldEvent() string {
	switch e {
	case EventTypeSignUp:
		return "1v1-register"
	default:
		return ""
	}
}

type ShieldResp struct {
	Result    Suggestion `json:"result"`
	Score     uint8      `json:"score"`
	Creditkey string     `json:"creditkey"`
	Reviewurl string     `json:"reviewurl"`
}

func ShieldCheck(reqBody string) (result *ShieldResp, err error) {
	apiUrl := shieldCheckUrlOnline
	if !env.IsProd() {
		apiUrl = shieldCheckUrlTest
	}

	result = &ShieldResp{}
	resp, err := httpcli.PostJsonRawData(apiUrl, nil, []byte(reqBody))
	if err != nil {
		logger.Errorf("ShieldCheck reqBody:%v,err:%v", reqBody, err)
		return
	}
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		logger.Warnf("Check json err: %v, resp: %v,reqBody:%v", err, resp, reqBody)
		return
	}
	return
}
