package ginserver

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

var (
	serviceName = ""
	moduleList  = make(map[string]Controller)
)

func initRouter(g gin.IRouter) {
	if len(serviceName) > 0 {
		g = g.Group(fmt.Sprintf("/%s", serviceName))
	}
	for _, module := range moduleList {
		routerGroup := g.Group(fmt.Sprintf("/%s", module.Name()))
		for _, route := range module.Routes() {
			if route.Method == GET {
				routerGroup.GET(fmt.Sprintf("/%s%s", route.Name, route.ExtPath), route.Func)
			} else if route.Method == POST {
				routerGroup.POST(fmt.Sprintf("/%s%s", route.Name, route.ExtPath), route.Func)
			}
			routerGroup.HEAD(fmt.Sprintf("/%s%s", route.Name, route.ExtPath), defaultHeadFunc)
		}
	}
}

func defaultHeadFunc(ctx *gin.Context) {
	ctx.AbortWithStatus(http.StatusOK)
}

func SetServiceName(name string) {
	serviceName = name
}

func RegisterModule(c Controller) {
	moduleList[c.Name()] = c
}
