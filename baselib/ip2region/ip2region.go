package ip2region

import (
	_ "embed"
	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
	"sync"
)

//go:embed ip2region.xdb
var regionxdb []byte

var (
	searcher *xdb.Searcher
	mu       sync.Mutex
	once     sync.Once
)

func Search(ip string) (string, error) {
	mu.Lock()
	defer mu.Unlock()
	return searcher.SearchByStr(ip)
}

func init() {
	var err error
	searcher, err = xdb.NewWithBuffer(regionxdb)
	if err != nil {
		panic(err)
		return
	}
}
