package ip2region

import (
	"testing"

	"xim/baselib/logger"
)

func TestName(t *testing.T) {
	Get()
}

func Get() {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	for _, ip := range []string{
		"***************",
		"*************",
		"**************",
		"**********",
		"************",
		"************",
		"*************",
		"*************",
		"*************",
		"*************",
	} {
		s, err := Search(ip)
		if err != nil {
			logger.Errorf("error %v", err)
			return
		}
		logger.Infof("ip %s addr %s", ip, s)
	}

}
