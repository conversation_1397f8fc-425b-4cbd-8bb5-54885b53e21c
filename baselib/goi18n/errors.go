package goi18n

import (
	"context"
	"net/http"

	"github.com/golang/protobuf/proto"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TranslateError(ctx context.Context, code codes.Code, message string, translate map[string]string, details ...proto.Message) error {
	if translate != nil {
		if messageID, ok := translate["id"]; ok {
			localized := GetLocalizedMessageByXbaseMetadata(ctx, messageID, translate)
			if localized != nil {
				details = append(details, localized)
			}
		}
	}
	errorStatus, _ := status.New(code, message).WithDetails(details...)
	return errorStatus.Err()
}

func TranslateErrorWithHttpRequest(r *http.Request, code codes.Code, message string, translate map[string]string, details ...proto.Message) error {
	if translate != nil {
		if messageID, ok := translate["id"]; ok {
			localized := GetLocalizedMessageByHttpRequest(r, messageID, translate)
			if localized != nil {
				details = append(details, localized)
			}
		}
	}
	errorStatus, _ := status.New(code, message).WithDetails(details...)
	return errorStatus.Err()
}

// 用法return nil, I18nError(ctx, codes.PermissionDenied, "token_expired", fmt.Errorf("verification expired: %w", err))
func I18nError(ctx context.Context, code codes.Code, idStr string, err error, pairs ...string) error {
	kv := map[string]string{
		"id": idStr,
	}

	for i := 0; i < len(pairs); i += 2 {
		kv[pairs[i]] = pairs[i+1]
	}

	return TranslateError(ctx, code, err.Error(), kv)
}
