package goi18n

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"
	"sync"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
	"google.golang.org/genproto/googleapis/rpc/errdetails"
)

type Handle interface {
	LoadBundle(index string) (*i18n.Bundle, error)
}

var H Handle
var DefaultBundle *i18n.Bundle
var bundles sync.Map
var DefaultLang *language.Tag

const DefaultIndex = "default"

// Init is init language default file
func Init(dirPth string, lang *language.Tag, h Handle) error {
	DefaultLang = lang
	DefaultBundle = i18n.NewBundle(*DefaultLang)
	DefaultBundle.RegisterUnmarshalFunc("json", json.Unmarshal)
	paths, err := GetPathFiles(dirPth)
	if err != nil {
		return err
	}
	for _, path := range paths {
		DefaultBundle.MustLoadMessageFile(path)
	}
	bundles.Store(DefaultIndex, DefaultBundle)
	H = h
	return nil
}

func UpdateBundle(index string, buf []byte, path string) *i18n.Bundle {
	var bundle *i18n.Bundle
	if b, ok := bundles.Load(index); ok {
		bundle = b.(*i18n.Bundle)
	} else {
		if DefaultLang == nil {
			DefaultLang = &language.English
		}
		bundle = i18n.NewBundle(*DefaultLang)
		bundle.RegisterUnmarshalFunc("json", json.Unmarshal)
	}
	_, err := bundle.ParseMessageFileBytes(buf, path)
	if err != nil {
		return nil
	}
	bundles.Store(index, bundle)
	return bundle
}

func CreateBundle(index string, buf []byte, path string) *i18n.Bundle {
	var bundle *i18n.Bundle
	if DefaultLang == nil {
		DefaultLang = &language.English
	}
	bundle = i18n.NewBundle(*DefaultLang)
	bundle.RegisterUnmarshalFunc("json", json.Unmarshal)
	_, err := bundle.ParseMessageFileBytes(buf, path)
	if err != nil {
		return nil
	}
	bundles.Store(index, bundle)
	return bundle
}

func RemoveBundle(index string) {
	bundles.Delete(index)
}

// GetPathFiles is load language file
func GetPathFiles(dirPth string) (files []string, err error) {
	fis, err := ioutil.ReadDir(filepath.Clean(filepath.ToSlash(dirPth)))
	if err != nil {
		return nil, err
	}
	for _, f := range fis {
		_path := filepath.Join(dirPth, f.Name())
		// 选择匹配文件格式
		switch filepath.Ext(f.Name()) {
		case ".json":
			files = append(files, _path)
		}
	}
	return files, nil
}

// GetLocalizedMessage is get GetLocalizedMessage
func GetLocalizedMessage(b *i18n.Bundle, accept, messageID string, templateData map[string]string, tryDefault bool) *errdetails.LocalizedMessage {
	if b == nil {
		return nil
	}
	var message string
	var tag language.Tag
	var err error
	localizer := i18n.NewLocalizer(b, accept)
	message, tag, err = localizer.LocalizeWithTag(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID: messageID,
		},
		TemplateData: templateData,
	})
	// message 为空 尝试使用default bundle 进行翻译
	if tryDefault && DefaultBundle != nil && message == "" {
		defaultLocalizer := i18n.NewLocalizer(DefaultBundle, accept)
		message, tag, err = defaultLocalizer.LocalizeWithTag(&i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID: messageID,
			},
			TemplateData: templateData,
		})
	}
	if message == "" || accept == "" || err != nil {
		return nil
	}
	return &errdetails.LocalizedMessage{
		Locale:  tag.String(),
		Message: message,
	}
}

// GetLocalizedMessageByDefaultBundle is get GetLocalizedMessageByDefaultBundle
func GetLocalizedMessageByDefaultBundle(messageID string, templateData map[string]string) *errdetails.LocalizedMessage {
	if DefaultLang == nil || DefaultBundle == nil {
		return nil
	}
	localizer := i18n.NewLocalizer(DefaultBundle, DefaultLang.String())
	message, tag, err := localizer.LocalizeWithTag(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID: messageID,
		},
		TemplateData: templateData,
	})
	if message == "" || err != nil {
		return nil
	}
	return &errdetails.LocalizedMessage{
		Locale:  tag.String(),
		Message: message,
	}
}
