package goi18n

import (
	"context"
	"net/http"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"google.golang.org/genproto/googleapis/rpc/errdetails"
	"google.golang.org/grpc/metadata"
)

// GetLocalizedMessageByXbaseMetadata is get GetLocalizedMessageByXbaseMetadata
func GetLocalizedMessageByXbaseMetadata(ctx context.Context, messageID string, templateData map[string]string) *errdetails.LocalizedMessage {
	var tryDefault bool
	bundle := DefaultBundle
	if ctx != nil {
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			lang := md.Get("Accept-Language")
			project := md.Get("x-project-id")
			if project != nil {
				projectID := project[0]
				tryDefault = true
				if b, ok := bundles.Load(projectID); ok {
					bundle = b.(*i18n.Bundle)
				} else if H != nil {
					// 自动加载 bundle
					newBundle, err := H.LoadBundle(projectID)
					if err == nil {
						bundle = newBundle
					}
					bundles.Store(projectID, bundle)
				}
			}
			if lang != nil {
				return GetLocalizedMessage(bundle, lang[0], messageID, templateData, tryDefault)
			}
		}
	}
	return GetLocalizedMessageByDefaultBundle(messageID, templateData)
}

func GetLocalizedMessageByHttpRequest(r *http.Request, messageID string, templateData map[string]string) *errdetails.LocalizedMessage {
	var tryDefault bool
	bundle := DefaultBundle
	lang := r.Header.Get("Accept-Language")
	projectID := r.Header.Get("x-project-id")
	if lang != "" {
		tryDefault = true
		if b, ok := bundles.Load(projectID); ok {
			bundle = b.(*i18n.Bundle)
		} else if H != nil {
			// 自动加载 bundle
			newBundle, err := H.LoadBundle(projectID)
			if err == nil {
				bundle = newBundle
			}
			bundles.Store(projectID, bundle)
		}
		return GetLocalizedMessage(bundle, lang, messageID, templateData, tryDefault)
	}
	return GetLocalizedMessageByDefaultBundle(messageID, templateData)
}
