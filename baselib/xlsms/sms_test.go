package xlsms

import (
	"testing"

	"xim/baselib/logger"
)

func TestSms(t *testing.T) {

	logger.InitLogger("/dev/stdout")
	//InitSms(nil)

	//SendVerifyCode("13715321500")

	InitCLSms(nil)
	SendCLVerifyCode("13715321500", "123456")
}

func TestCLSms(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	InitCLSms(nil)
	sendCLMessage(CLTypeAvert, "19163532309", "", "测试测试")
}

// go test  -v -run TestWuxiSms
func TestWuxiSms(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	InitWuxiSms(nil)
	BatchSendWuxiMessage("愿聊", "线上线下", []string{"15396246544", "17710036509"})
}
