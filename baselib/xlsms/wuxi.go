package xlsms

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"xim/proto/consts/config"

	"xim/baselib/httpcli"
	"xim/baselib/logger"
)

type WuxiType int

const (
	WuxiTypeAvert WuxiType = 0 // 营销
	WuxiTypeCode  WuxiType = 1 // 验证码
)

type WuxiSmsConfig config.WuxiSmsConfig

func (ss *WuxiSmsConfig) Sign(r *http.Request, jsonByte []byte) {
	currentTimestring := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	signature := HmacSha256AndBase64(jsonByte, []byte(currentTimestring), []byte(ss.SpKey))
	signatureHeader := "HMAC-SHA256" + " " + currentTimestring + "," + signature
	r.Header.Add("Content-Type", "application/json;charset=utf-8")
	r.<PERSON>er.Add("Authorization", signatureHeader)
}

// 短信发送接口（单内容多号码）url
func (ss *WuxiSmsConfig) SingleSendUrl() string {
	return strings.TrimRight(ss.SmsSendUrl, "/") + "/sms/send/" + ss.SpId
}

func HmacSha256AndBase64(b1 []byte, b2 []byte, spKeyByte []byte) string {
	h := hmac.New(sha256.New, spKeyByte)
	h.Write(b1)
	h.Write(b2)
	buf := h.Sum(nil)
	return base64.StdEncoding.EncodeToString(buf)
}

type SingleSendRequestBody struct {
	//必须。最大长度1005。短信内容，例如： 【线上线下】您的验证码为123456，在10分钟内有效。
	Content string `json:"content"`
	//必须。多个号码用,分隔开，号码数量<=10000
	Mobile string `json:"mobile"`
	//可选。可选。扩展码，必须可解析为数字,最大长度12
	ExtCode string `json:"extCode"`
	//可选。最大长度64。批次号，可用于客户侧按照批次号对短信进行分组
	SId string `json:"sId"`
}

var (
	wuxiSmsConfig *WuxiSmsConfig
)

func InitWuxiSms(conf *WuxiSmsConfig) {
	wuxiSmsConfig = conf
	httpcli.Init()
	return
}

func BatchSendWuxiMessage(title, content string, phones []string) (err error) {
	if len(title) == 0 {
		title = clSmsConfig.SignName
	}
	requestBody := &SingleSendRequestBody{
		Content: fmt.Sprintf("【%s】%s", title, content),
		Mobile:  strings.Join(phones, ","),
	}
	if len(phones) == 1 {
		requestBody.Mobile = phones[0]
	}
	finalReqBody, err := json.Marshal(requestBody)
	if err != nil {
		logger.Errorf("BatchSendWuxiMessage error: %v", err)
		return
	}

	r, _ := http.NewRequest("POST", wuxiSmsConfig.SingleSendUrl(), bytes.NewReader(finalReqBody))
	wuxiSmsConfig.Sign(r, finalReqBody)

	client := http.Client{
		Transport: httpcli.Transport,
	}

	resp, err := client.Do(r)
	if err != nil {
		logger.Errorf("BatchSendWuxiMessage error: %v", err)
		return
	}

	defer resp.Body.Close()
	finalRespBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("BatchSendWuxiMessage error: %v", err)
		return
	}

	logger.Infof("BatchSendWuxiMessage result: %v", string(finalRespBody))
	return
}
