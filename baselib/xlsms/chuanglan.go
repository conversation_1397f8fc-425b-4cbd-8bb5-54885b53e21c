package xlsms

import (
	"fmt"
	"strings"

	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/util"
	"xim/proto/consts/config"
)

type CLType int

const (
	CLTypeCode  CLType = 1
	CLTypeAvert CLType = 0
)

type SmsAdvertTarget struct {
	Phoneno string
	Data    map[string]string
}

type CLMessageRequest struct {
	Account  string `json:"account,omitempty"`
	Password string `json:"password,omitempty"`
	Msg      string `json:"msg,omitempty"`
	Phone    string `json:"phone,omitempty"`
	Sendtime string `json:"sendtime,omitempty"`
	Report   string `json:"report,omitempty"`
	Extend   string `json:"extend,omitempty"`
	Uid      string `json:"uid,omitempty"`
}

var (
	clSmsConfig *config.CLSmsConfig
)

func InitCLSms(conf *config.CLSmsConfig) {
	clSmsConfig = conf

	httpcli.Init()
	return
}

func SendCLVerifyCode(phoneno, code string) error {
	content := config.GetSmsContent(config.SmsTplVerifyCode, map[string]string{"code": code})
	return sendCLMessage(CLTypeCode, phoneno, "", content)
}

func SendCLAdvert(title, tpl string, targets []SmsAdvertTarget) (err error) {
	for _, item := range targets {
		err = sendCLMessage(CLTypeAvert, item.Phoneno, title, config.GetSmsContent(tpl, item.Data))
	}
	return
}

func BatchSendCLAdvert(tpl string, data map[string]string, phonenos []string) (err error) {
	content := config.GetSmsContent(tpl, data)
	targets := strings.Join(phonenos, ",")
	err = sendCLMessage(CLTypeAvert, targets, "", content)
	return
}

func sendCLMessage(typ CLType, phoneno, title, content string) (err error) {
	account := clSmsConfig.AdvertAccount
	password := clSmsConfig.AdvertPassword
	url := clSmsConfig.AdvertUrl
	if typ == CLTypeCode {
		account = clSmsConfig.CodeAccount
		password = clSmsConfig.CodePassword
		url = clSmsConfig.CodeUrl
	}

	signName := title
	if len(signName) == 0 {
		signName = clSmsConfig.SignName
	}

	request := CLMessageRequest{
		Account:  account,
		Password: password,
		Phone:    phoneno,
		Msg:      fmt.Sprintf("【%s】%s", signName, content),
	}
	headers := make(map[string]string)
	bodyStr := util.JsonStr(request)
	fmt.Println("body", bodyStr)
	result, err := httpcli.PostJson(url, headers, request)
	if err != nil {
		logger.Errorf("sendCLMessage error: %v", err)
		return
	}
	fmt.Println("result", result)
	logger.Infof("sendCLMessage result: %v", result)
	return
}
