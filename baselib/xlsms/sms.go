package xlsms

import (
	"context"
	"strconv"

	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/util"
	"xim/proto/api/svcconfig"
	"xim/proto/consts"
	"xim/proto/consts/config"
	"xim/proto/consts/errcode"
	"xim/proto/svcmgr"
)

type SmsAbStrategyType int32

const (
	SmsAbStrategyTypeCL   SmsAbStrategyType = iota + 1 // 创蓝
	SmsAbStrategyTypeWuxi                              //无锡线上线下
)

func InitSms(conf *config.SmsConfig) (err error) {
	if conf == nil {
		return
	}
	if conf.CL == nil && conf.WX == nil {
		return
	}
	if conf.CL != nil {
		InitCLSms(conf.CL)
	}
	if conf.WX != nil {
		c := *conf.WX
		wsConfig := WuxiSmsConfig(c)
		InitWuxiSms(&wsConfig)
	}

	httpcli.Init()
	return
}

func SendAdvert(title, tpl string, targets []SmsAdvertTarget) (err error) {
	id := consts.AbStrategySmsAdvert
	resp, err := svcmgr.ConfigClient().AbStrategy(context.Background(), &svcconfig.AbStrategyReq{
		Type: consts.AbStrategyTypeServer,
		Id:   id,
	})
	if err != nil || errcode.NotOk(resp) {
		logger.Errorf("SendAdvert err %v resp %v", err, util.JsonStr(resp))
		return
	}
	var smsStrategyType int
	if resp.Data != nil {
		strategy := resp.Data[id]
		if strategy != nil {
			smsStrategyType, _ = strconv.Atoi(strategy.Config)
		}
	}

	if smsStrategyType == int(SmsAbStrategyTypeCL) {
		return SendCLAdvert(title, tpl, targets)
	} else if smsStrategyType == int(SmsAbStrategyTypeWuxi) {
		var phones []string
		for _, item := range targets {
			phones = append(phones, item.Phoneno)
		}
		return BatchSendWuxiMessage(title, config.GetSmsContent(tpl, nil), phones)
	}

	return
}
