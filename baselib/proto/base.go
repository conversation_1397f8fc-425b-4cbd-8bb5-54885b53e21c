package proto

import (
	"time"
)

type AuthInfo struct {
	Type     string `json:"type"`
	Token    string `json:"token"`
	ClientId string `json:"client_id"`
	Xluid    string `json:"xluid"`
	Userid   int64  `json:"userid"`
	Sex      int32  `json:"sex"`
	Ip       string `json:"ip"`
	Channel  string `json:"channel"`
	RegTime  int64  `json:"reg_time"`
	Issuer   string `json:"issuer"`
}

type XLUserInfo struct {
	Sub           string    `json:"sub"`
	Name          string    `json:"name"`
	Picture       string    `json:"picture"`
	Username      string    `json:"username"`
	Email         string    `json:"email"`
	EmailVerified bool      `json:"email_verified"`
	PhoneNumber   string    `json:"phone_number"`
	Status        string    `json:"status"`
	Gender        string    `json:"gender"`
	Birthdate     string    `json:"birthdate"`
	Zoneinfo      string    `json:"zoneinfo"`
	Locale        string    `json:"locale"`
	CreatedFrom   string    `json:"created_from"`
	CreatedAt     time.Time `json:"created_at"`
	Id            string    `json:"id"`
}

type UserBankAccount struct {
	Cardno   string `json:"cardno"`
	Phoneno  string `json:"phoneno"`
	Realname string `json:"realname"`
	Idcard   string `json:"idcard"`
	Bank     string `json:"bank"`
}
type UserAlipayAccount struct {
	Userno   string `json:"userno"`
	Name     string `json:"name"`
	Realname string `json:"realname"`
	Idcard   string `json:"idcard"`
	Phoneno  string `json:"phoneno"`
}
