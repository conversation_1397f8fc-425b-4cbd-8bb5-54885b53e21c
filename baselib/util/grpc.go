package util

import "google.golang.org/grpc"

func FilterEmptyUnaryClientInterceptor(s ...grpc.UnaryClientInterceptor) (result []grpc.UnaryClientInterceptor) {
	for _, i := range s {
		if i == nil {
			continue
		}
		result = append(result, i)
	}
	return
}

func FilterEmptyUnaryServerInterceptor(s ...grpc.UnaryServerInterceptor) (result []grpc.UnaryServerInterceptor ){
	for _, i := range s {
		if i == nil {
			continue
		}
		result = append(result, i)
	}
	return
}
