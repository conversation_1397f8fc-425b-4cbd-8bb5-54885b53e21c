package snow

import (
	"xim/baselib/logger"

	"github.com/go-redis/redis/v8"
)

var (
	defaultSn *SnowFlake
)

func GetDefaultSnowInstance() *SnowFlake {
	return defaultSn
}

func Init(client *redis.Client) {
	workId, err := GetWorkIdFromRedis(client)
	if err != nil {
		logger.Panic("error get work id")
		return
	}
	defaultSn, err = NewSnowFlake(workId % MaxWorkerId)
	if err != nil {
		panic(err)
	}
}

func Next() int64 {
	return int64(defaultSn.Next())
}
