package snow

import (
	"context"
	"fmt"
	"time"

	"xim/baselib/logger"
	"xim/baselib/server/env"

	"github.com/go-redis/redis/v8"
)

const (
	snowKey        = "redis_snow_work_id_key"
	snowWorkIdLock = "redis_snow_workid_lock_%d"
)

func GetWorkIdFromRedis(client *redis.Client) (workId uint32, err error) {
	res, err := client.Incr(context.Background(), snowKey).Uint64()
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	workId = uint32(res)
	workId %= MaxWorkerId
	key := fmt.Sprintf(snowWorkIdLock, workId)
	ok, err := client.SetNX(context.Background(), key, env.GetHostName(), time.Minute*5).Result()
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if !ok {
		err = fmt.Errorf("error workid repeated")
		return
	}
	go func() {
		ticker := time.NewTicker(time.Minute)
		for range ticker.C {
			err := client.Expire(context.Background(), key, time.Minute*5).Err()
			if err != nil {
				logger.Errorf("error %v")
			}
		}
	}()
	return uint32(res), nil
}
