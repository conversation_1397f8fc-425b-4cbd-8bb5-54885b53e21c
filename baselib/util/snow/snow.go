package snow

import (
	"fmt"
	"hash/crc32"
	"math/rand"
	"net"
	"sync"
	"time"
)

const (
	nano = 1000 * 1000
)

const (
	WorkerIdBits = 10              // worker id
	MaxWorkerId  = -1 ^ (-1 << 10) // worker id mask
	SequenceBits = 12              // sequence
	MaxSequence  = -1 ^ (-1 << 12) //sequence mask
)

var (
	Since int64 = time.Date(2012, 1, 0, 0, 0, 0, 0, time.UTC).UnixNano() / nano
)

type SnowFlake struct {
	lastTimestamp uint64
	workerId      uint32
	sequence      uint32
	lock          sync.Mutex
	r             *rand.Rand
}

func GetTimestampFromId(id int64) (ts int64) {
	uid := uint64(id)
	uid = uid >> (WorkerIdBits + SequenceBits)
	return int64((uid + uint64(Since)) / 1000)
}

func (sf *SnowFlake) uint64() uint64 {
	return (sf.lastTimestamp << (WorkerIdBits + SequenceBits)) |
		(uint64(sf.workerId) << SequenceBits) |
		(uint64(sf.sequence))
}

func (sf *SnowFlake) Next() uint64 {
	sf.lock.Lock()
	defer sf.lock.Unlock()

	ts := timestamp()
	// 如果出现时钟回拨，直接cover 住错误，哪有那么麻烦呢
	if ts < sf.lastTimestamp {
		ts = sf.lastTimestamp
	}
	if ts == sf.lastTimestamp {
		sf.sequence = (sf.sequence + 1) & MaxSequence
		if sf.sequence == 0 {
			ts = tilNextMillis(ts)
		}
	} else {
		sf.sequence = uint32(sf.r.Intn(100))
	}
	sf.lastTimestamp = ts
	return sf.uint64()
}

func NewSnowFlake(workerId uint32) (*SnowFlake, error) {
	if workerId < 0 || workerId > MaxWorkerId {
		return nil, fmt.Errorf("Worker id %v is invalid", workerId)
	}
	return &SnowFlake{
		lastTimestamp: 0,
		workerId:      workerId,
		sequence:      0,
		lock:          sync.Mutex{},
		r:             rand.New(rand.NewSource(time.Now().UnixNano())),
	}, nil
}

func timestamp() uint64 {

	return uint64(time.Now().UnixNano()/nano - Since)
}

func tilNextMillis(ts uint64) uint64 {
	i := timestamp()
	for i < ts {
		i = timestamp()
	}
	return i
}

// 下边两个方法，不是那么安全的
func DefaultWorkId() uint32 {
	var id uint32
	ift, err := net.Interfaces()
	if err != nil {
		rand.Seed(time.Now().UnixNano())
		id = rand.Uint32() % MaxWorkerId
	} else {
		h := crc32.NewIEEE()
		for _, value := range ift {
			h.Write(value.HardwareAddr)
		}
		id = h.Sum32() % MaxWorkerId
	}
	workId := id & MaxWorkerId
	fmt.Printf("snow workid %d", workId)
	return workId
}

func Default() (*SnowFlake, error) {
	return NewSnowFlake(DefaultWorkId())
}
