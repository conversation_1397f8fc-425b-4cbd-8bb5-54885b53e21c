package util

import (
	"bytes"
	"compress/gzip"
	"encoding/binary"
	"io/ioutil"
	"math/rand"
	"time"

	"xim/baselib/logger"

	"github.com/bits-and-blooms/bloom/v3"
)

// 解析int64 复合使用
func Resolve(val uint64, lowBitTotal uint32) (lowValue uint64, highValue uint32) {
	highValue = uint32(val >> (lowBitTotal))
	lowMax := uint64(1<<lowBitTotal - 1)
	lowValue = lowMax - val&lowMax
	return
}

// 组合int64 复合使用(数值小的放在高位)
func Combine(highValue uint32, lowValue uint64, lowBitTotal uint32) (val uint64) {
	hv := uint64(highValue) << (lowBitTotal)
	lowMax := uint64(1<<lowBitTotal - 1)
	return hv + lowMax - lowValue
}

func InInt64Slice(slice []int64, item int64) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func InStringSlice(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func GetEmptyUserBloom() *bloom.BloomFilter {
	return bloom.New(2<<15, 4)
}

// 获取布隆过滤器
func GetUserBloomFilter(data []byte) (bloomfilter *bloom.BloomFilter, err error) {
	bloomfilter = GetEmptyUserBloom()
	if len(data) == 0 {
		return
	}

	gr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		logger.Errorf("failed to uncompress bloom redis data: %s", err.Error())
		return bloomfilter, err
	}
	defer gr.Close()

	data, err = ioutil.ReadAll(gr)
	if err != nil {
		logger.Errorf("failed to read uncompressed bloom redis data: %s", err.Error())
		return bloomfilter, err
	}

	err = bloomfilter.UnmarshalJSON(data)
	if err != nil {
		logger.Errorf("failed to unmarshal bloom redis data: %s", err.Error())
		return
	}
	return
}

// 布隆过滤器增加命中用户
func AddBloomFilterId(bf *bloom.BloomFilter, userid int64) (data []byte, err error) {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(userid))
	bf.Add(bs)
	data, err = bf.MarshalJSON()
	if err != nil {
		logger.Errorf("failed to marshal bloom data: %s", err.Error())
		return
	}

	var buf bytes.Buffer
	gw := gzip.NewWriter(&buf)
	gw.Write(data)
	gw.Close()

	data, err = ioutil.ReadAll(&buf)
	if err != nil {
		logger.Errorf("failed to gzip bloom data: %s", err.Error())
		return
	}
	return data, nil
}

// 布隆过滤器增加命中用户
func AddBloomFilterIds(bf *bloom.BloomFilter, userids ...int64) (data []byte, err error) {
	for _, uid := range userids {
		bs := make([]byte, 8)
		binary.BigEndian.PutUint64(bs, uint64(uid))
		bf.Add(bs)
	}
	data, err = bf.MarshalJSON()
	if err != nil {
		logger.Errorf("failed to marshal bloom data: %s", err.Error())
		return
	}

	var buf bytes.Buffer
	gw := gzip.NewWriter(&buf)
	gw.Write(data)
	gw.Close()

	data, err = ioutil.ReadAll(&buf)
	if err != nil {
		logger.Errorf("failed to gzip bloom data: %s", err.Error())
		return
	}
	return data, nil
}

func RandomWeights(weights []int) (index int) {
	totalWeight := 0
	for _, weight := range weights {
		if weight < 0 {
			panic("weight should be no less than 0")
		}
		totalWeight += weight
	}
	if totalWeight == 0 {
		return -1
	}
	now := time.Now().UnixNano()
	rand.Seed(now)
	randomNum := rand.Intn(totalWeight) + 1
	for i, weight := range weights {
		if randomNum > weight {
			randomNum -= weight
			continue
		}
		index = i
		break
	}
	return
}

func BloomFilterEncode(bf *bloom.BloomFilter) (data []byte, err error) {
	var buf bytes.Buffer
	data, err = bf.MarshalJSON()
	if err != nil {
		logger.Errorf("failed to marshal bloom data: %s", err.Error())
		return
	}
	gw := gzip.NewWriter(&buf)
	gw.Write(data)
	gw.Close()
	data, err = ioutil.ReadAll(&buf)
	if err != nil {
		logger.Errorf("failed to gzip bloom data: %s", err.Error())
		return
	}
	return data, nil
}
