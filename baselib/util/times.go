package util

import (
	"fmt"
	"strings"
	"time"

	"xim/proto/consts"
)

// example: 08:12:13
func ParseDayTime(s string) (int64, error) {
	layout := consts.TimeFormatHMS
	if len(s) == 5 {
		layout = consts.TimeFormatHM
	}
	p, err := time.Parse(layout, s)
	if err != nil {
		return 0, err
	}
	return int64(p.Hour())*3600 + int64(p.Minute())*60 + int64(p.Second()), nil
}

func BeginOfToday() time.Time {
	now := time.Now()
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
}

func GetTodayTimestamps() (now, start, end int64) {
	nt := time.Now()
	now = nt.Unix()
	st := time.Date(nt.Year(), nt.Month(), nt.Day(), 0, 0, 0, 0, time.Local)
	start = st.Unix()
	nt = nt.Add(time.Hour * 24)
	et := time.Date(nt.Year(), nt.Month(), nt.Day(), 0, 0, 0, 0, time.Local)
	end = et.Unix()
	return
}

func GetDurationStr(totalTime int64) string {
	duration := ""
	hour, min, sec := int64(0), int64(0), int64(0)
	hour = totalTime / 3600
	totalTime -= (hour * 3600)
	min = totalTime / 60
	sec = totalTime - (min * 60)
	if hour > 0 {
		duration = fmt.Sprintf("%02d:%02d:%02d", hour, min, sec)
	} else if min > 0 {
		duration = fmt.Sprintf("%02d:%02d", min, sec)
	} else {
		duration = fmt.Sprintf("00:%02d", sec)
	}
	return duration
}

func GetDurationStrMoreDay(totalTime int64) string {
	duration := ""
	hour, min, sec := int64(0), int64(0), int64(0)
	hour = totalTime / 3600
	totalTime -= (hour * 3600)
	min = totalTime / 60
	sec = totalTime - (min * 60)

	if hour > 0 {
		duration = fmt.Sprintf("%d:%02d:%02d", hour, min, sec)
	} else if min > 0 {
		duration = fmt.Sprintf("%02d:%02d", min, sec)
	} else {
		duration = fmt.Sprintf("00:%02d", sec)
	}
	return duration
}

// total 30s 次数
func GetOnlineDurationStr(total int64) string {
	if total <= 0 {
		return ""
	} else {
		return GetDurationStr(total * 30)
	}
}

func ParseDateToTimestamp(ymd string) (ts int64, err error) {
	layout := consts.TimeFormatYMD
	if strings.Count(ymd, "-") == 0 {
		layout = consts.TimeFormatYMD2
	}
	date, err := time.ParseInLocation(layout, ymd, time.Local)
	if err != nil {
		return
	}
	ts = date.Unix()
	return
}

func ParseDateTimeToTimestamp(ymdhms string) (ts int64, err error) {
	layout := consts.TimeFormatYMDHMS
	if strings.Count(ymdhms, "-") == 0 {
		layout = consts.TimeFormatYMDHMS2
	}
	date, err := time.ParseInLocation(layout, ymdhms, time.Local)
	if err != nil {
		return
	}
	ts = date.Unix()
	return
}

func GetDatesOfTDays(days int) (dates []string) {
	now := time.Now()
	dates = make([]string, days)
	for i := 0; i < days; i++ {
		dates[i] = now.AddDate(0, 0, -(i + 1)).Format(consts.TimeFormatYMD)
	}
	return
}

// 注意每周的开始时周日
func StartOfWeek() time.Time {
	now := time.Now()
	nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	nowDate = nowDate.AddDate(0, 0, -int(now.Weekday()))
	return nowDate
}
