package util

import (
	"log"
	"testing"

	"xim/proto/api/common"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/anypb"
)

func TestAny(t *testing.T) {
	v, err := anypb.New(&common.BizBaseResp{
		Code: 1,
		Msg:  "asdasd",
		Data: nil,
	})
	if err != nil {
		log.Fatalln(err)
	}
	bs, er := protojson.Marshal(v)
	log.Println(string(bs), er)
	s := v.String()
	log.Println(s)
}
