package util

import (
	"fmt"
	"testing"
	"time"
)

func TestCombine(t *testing.T) {
	lv := uint64(time.Now().Unix())
	hv := uint32(0)
	val := Combine(hv, lv, 54)
	l, h := Resolve(val, 54)
	if l != lv || h != hv {
		fmt.Println(l, h)
	}

	hv = uint32(1023)
	lv = uint64(18014398509481984 - 1)
	val = Combine(hv, lv, 54)
	l, h = Resolve(val, 54)
	if l != lv || h != hv {
		fmt.Println(l, h)
	}

}

func TestBit(t *testing.T) {
	ts1 := int64(3022991219) // 2065-10-17 15:46:59
	src := ts1

	for i := 0; i <= 20; i++ {
		ts1 = ts1 << 5
		val := ts1 + int64(i)
		lowMax := 1<<5 - 1
		fmt.Println(i, val, src, val&int64(lowMax), val>>5)
		ts1 = src
	}

	ts2 := time.Now().Unix()
	ts3 := ts2 + 1
	fmt.Println(ts2<<5 + 31)
	fmt.Println(ts3<<5 + 1)

}
