package util

import (
	"regexp"
	"strings"
)

var (
	rnRegex, _  = regexp.Compile(`[\r\n]+`)
	rntRegex, _ = regexp.Compile(`[\r\n\t]+`)

	rnsRegex, _ = regexp.Compile(`(\r\n){2,}`)
	nsRegex, _  = regexp.Compile(`\n{2,}`)

	tsRegex, _  = regexp.Compile(`\t{2,}`)
	bsRegex, _  = regexp.Compile(` {2,}`)
	btsRegex, _ = regexp.Compile(`[ \t]{2,}`)
)

func TrimNickname(name string) string {
	name = strings.TrimSpace(name)
	name = rnRegex.ReplaceAllString(name, "")
	return btsRegex.ReplaceAllString(name, " ")
}

func TrimLovewords(text string) string {
	text = strings.TrimSpace(text)
	text = rnRegex.ReplaceAllString(text, "")
	text = tsRegex.ReplaceAllString(text, "\t")
	return bsRegex.ReplaceAllString(text, " ")
}

func TrimMultiLinesText(text string) string {
	text = strings.TrimSpace(text)
	text = rnsRegex.ReplaceAllString(text, "\r\n")
	text = nsRegex.ReplaceAllString(text, "\n")
	text = tsRegex.ReplaceAllString(text, "\t")
	return bsRegex.ReplaceAllString(text, " ")
}
