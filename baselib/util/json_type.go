package util

import (
	"strconv"
	"strings"
)

type Int64 int64

func (i Int64) Int64() int64 {
	return int64(i)
}

func (i Int64) MarshalJSON() ([]byte, error) {
	s := strconv.AppendInt(nil, int64(i), 10)
	s = append([]byte("\""), s...)
	s = append(s, []byte("\"")...)
	return s, nil
}

func (i *Int64) UnmarshalJSON(b []byte) error {
	s := string(b)
	s = strings.TrimLeft(s, "\"")
	s = strings.TrimRight(s, "\"")
	v, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return err
	}
	*i = Int64(v)
	return err
}
