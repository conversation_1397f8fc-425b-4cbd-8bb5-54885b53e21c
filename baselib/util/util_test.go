package util

import (
	"fmt"
	"regexp"
	"testing"
)

func TestIntToCode(t *testing.T) {
	//ts := time.Now().UnixNano()/1000 - 1641960000000000
	//result := IntToCode(ts)
	//fmt.Printf("%d, %s", ts, result)

	GetChineseIdCardData("******************")

	fmt.Println( MemberDecrypt("X9/gX9UUBM7EBmoTYhQY1h1Rv/BqUTbU8fy4fsJq4OkCXrMq8XwtUl43qztDKOc4"))
}

func TestRand(t *testing.T) {
	//rn := RandNumberString(10)
	//fmt.Printf("rn %s", rn)
	//
	//rn = RandLetterString(10)
	//fmt.Printf("rn %s", rn)
	//uuid1 := SimpleUUID()
	//uuid2 := UUID()
	//fmt.Println("--------------------")
	//fmt.Printf("%s", uuid1)
	//fmt.Println("--------------------")
	//fmt.Printf("%s", uuid2)

	p1 := "18026039402"
	fmt.Printf("%s", p1)
	fmt.Println("***********************")
	p2 := MemberEncrypt(p1)
	fmt.Printf("%s", p2)
	fmt.Println("***********************")
	p3 := MemberDecrypt(p2)
	fmt.Printf("%s", p3)
	fmt.Println("***********************")

	p1 = "15581302967"
	fmt.Printf("%s", p1)
	fmt.Println("***********************")
	p2 = MemberEncrypt(p1)
	fmt.Printf("%s", p2)
	fmt.Println("***********************")
	p3 = MemberDecrypt(p2)
	fmt.Printf("%s", p3)
	fmt.Println("***********************")
}

func TestCompareVersion(t *testing.T) {
	fmt.Println(CompareVersion("1.0.1", "1.0.1"))
	fmt.Println(CompareVersion("11221", ""))
	fmt.Println(CompareVersion("", "112221"))
	fmt.Println(CompareVersion("1.2.3", "1.3"))
	fmt.Println(CompareVersion("1.2.3", "1.2.3"))
	fmt.Println(CompareVersion("1.2.3", "1.1.3"))
	fmt.Println(CompareVersion("1.2.3", "1.3.3"))
	fmt.Println(CompareVersion("10.2.3", "1.63.3"))
	fmt.Println(CompareVersion("1.23.3", "1.3.34"))

	fmt.Println(CompareVersionRes("1.5.0", "7.53.0.13874.694:0x10800001") < 0)
	fmt.Println(CompareVersionRes("1.5.0", "1.5.0") == 0)
	fmt.Println(CompareVersionRes("1.5.0", "1.4.5") > 0)
}

func TestFmtMaskPhoneno(t *testing.T) {
	fmt.Println(FmtMaskPhoneno("+86 ***********"))
	fmt.Println(FmtMaskPhoneno("+86 ***********"))
}

func TestAlipayAccount(t *testing.T) {
	alipayAccountRegex := regexp.MustCompile("^(1[\\d]{10})|([\\w\\d\\.]{5,})$")

	fmt.Println(alipayAccountRegex.MatchString("<EMAIL>"))
	fmt.Println(alipayAccountRegex.MatchString("***********"))
	fmt.Println(alipayAccountRegex.MatchString("adefsdf_dfgdgf"))
}

func TestMemberDecrypt(t *testing.T) {
	phonenos := []string{
		"",
		"<EMAIL>",
		"***********",
		"adefsdf_dfgdgf",
		"2CqyJ8MZy0NQk6IHbCauJg==",
		"cSruAW06XhLh5exOMRPuPQ==",
		"RNFHEXFmeQQJ/dRmjLiBxA==",
		"04ftT0D8v3r8KIK4uPN2ww==",
		"ekpoYArHAsx04j8nVDol7g==",
		"R2fwzxLnbgTC2T5uSZbHPg==",
		"1QSla0BM7JvE+TfTbXP/Aw==",
		"2Kogtj5Z6UF81Lerx9Gamw==",
		"1nzrKwv2Sb0e2fWl+br24Q==",
		"dnW4m4563ttWoG4fvPFrIQ==",
		"HXrgTczYuFS9nXrJPtgWSg==",
		"mgpFwIeW3Ybc/aDgEJZz+A==",
		"SZZvmNPFUVWNI6mz4hx0Mw==",
		"h+vk1whSb7iTbELhQerXIA==",
		"QX4Uqx1CjQ7UdZbW2HaYUg==",
		"nUwnUP++TuMxJkT5svqcuA==",
		"KzLLADyCV9gUPdYrC1Wx1A==",
		"WZgoe2V11sgJTXZUxHJpuA==",
		"gHJAQo0qjB1b6+L21ZCDxg==",
		"eZqcsbw1hB5XbYAqO4GRmQ==",
		"XbXFSNtYIITsVeRFcJ3mOg==",
		"rbL/aDPuJAqerysYWkgXmg==",
		"o7XI+u6+jZoqivRzXsJSrA==",
		"c9QHZToDJn6PkKY2ZYqqlQ==",
		"GQZzcyIgn7jDD4VUTYdirg==",
		"+FcFRElTPM2US77dJ3Tijw==",
		"gW70I4htrW7X5Oi6Mybdbg==",
		"rr94Z3UHKWQgXXPA//BYJg==",
		"MIZF8UopOX8OHKT53Gh8/A==",
		"XvkOxUr1elTgd3xGHCE7hg==",
		"3OD2VQ3HfBYm84tbJo8/qQ==",
		"SlWbZBN4OuDzecHRuEh+AQ==",
		"vIpjkEi1nTbkMQm+DDzMtw==",
		"L8DicCZK/7E17PpIWfTN8A==",
		"kLnHqF3lOEWEjILUEFHtHA==",
		"1E9pl6ZNllz4Euh1zXEnfQ==",
		"Cj+AyjGs020rwwdqrFknug==",
		"N7Riofx/R2ccDX3UhTMz3g==",
		"qKYlasb5BlIugVCubIWe8g==",
		"AQl2l98IUi1sLXWpKt6c/Q==",
		"SMufDHMcLFuTxWYpTGCQOA==",
		"JPb2Iojq8DyxShX8vmTbgw==",
		"RiNGaEq7wI+D3vxw4jRjAA==",
		"vk0+L5YfFCrKa0j1pOWfWg==",
		"BxFDRGhVaHharsLY8/BVHA==",
		"c7ZZx9GZjNowfEL6MNwYBA==",
		"sezDujBsnzAfLfj//RAe/A==",
		"RNx7fSBnndmb39/x2PFxRQ==",
		"l6NJkIich13ntTQ8aDgtGg==",
		"bmaWYFl6H4Cow/U2DbY4Sw==",
		"fLzF9Fa0T0T1PTuBE4TjMQ==",
		"JjOGkt2sHCBkKxD0Urtb2Q==",
		"4rw5eYlTTtzCf+7sWHnoEA==",
	}
	for _, phone := range phonenos {
		phone2 := MemberDecrypt(phone)
		if !CheckChinaPhoneNo(phone2) {
			fmt.Println(phone, phone2)
		} else {
			fmt.Println(phone2)
		}

	}
}

func TestAvatar(t *testing.T) {
	avatar := "http://melon-image.oss-cn-shanghai.aliyuncs.com/avatar/1030265"

	fmt.Println(GetMediaUrlId(avatar))
	fmt.Println(GetMediaUrlId(""))
	fmt.Println(GetMediaUrlId("10302651030265"))
	fmt.Println(GetMediaUrlId("/103026265"))
}
