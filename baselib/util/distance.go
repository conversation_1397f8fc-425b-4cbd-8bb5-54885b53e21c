package util

import (
	"math"
	"strings"
)

// 返回单位为：(m)
func GetDistanceByLatLng(lat1, lng1, lat2, lng2 float64) float64 {
	radius := 6378137.0    //赤道半径(单位m)
	rad := math.Pi / 180.0 //转化为弧度(rad)
	lat1 = lat1 * rad
	lng1 = lng1 * rad
	lat2 = lat2 * rad
	lng2 = lng2 * rad
	theta := lng2 - lng1
	dist := math.Acos(math.Sin(lat1)*math.Sin(lat2) + math.Cos(lat1)*math.Cos(lat2)*math.Cos(theta))
	return dist * radius
}

func GetCityCodeByAddr(addr string) string {
	if len(addr) == 0 {
		return ""
	}
	splitIndex := strings.Index(addr, ":")
	if splitIndex != -1 {
		addrCode := addr[:splitIndex]
		return addrCode
	}
	return ""
}
