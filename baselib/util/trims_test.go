package util

import (
	"fmt"
	"testing"
	"time"
)

func TestTime(t *testing.T) {
	p, err := time.Parse("15:04:05", "08:12:13")
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}
	fmt.Println(p.Hour(), p.<PERSON>(), p.Second())
}

// go test -v  -run TestMemberEncrypt
func TestTrims(t *testing.T) {

	name := "  xhg\r\n\t\tiher\t\t\tdfdgf     addsds "
	fmt.Println("nickname1", name)
	name = TrimNickname(name)
	fmt.Println("nickname2", name, "1111111")

	lovewords := "  xhg\n\t\tiher\t\t\tdfdgf     addsds "
	fmt.Println("nickname1", lovewords)
	lovewords = TrimLovewords(lovewords)
	fmt.Println("nickname2", lovewords)

	content := "  xhg\r\niher\n\n\n\n\t\tdfd\n\n\ngf "
	fmt.Println("content1", content)
	content = TrimMultiLinesText(content)
	fmt.Println("content2", content)
}

func TestName(t *testing.T) {
	//fmt.Println(GetStartOfWeek())
}
