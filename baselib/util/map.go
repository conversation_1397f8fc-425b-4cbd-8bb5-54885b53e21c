package util

func MergeMap(mapa, mapb map[string]interface{}) {
	for s, i := range mapb {
		if _, ok := mapa[s]; !ok {
			mapa[s] = i
		}
	}
}

func MergeMapString(mapa, mapb map[string]string) map[string]string {
	if mapb == nil {
		return nil
	}

	if mapa == nil {
		mapa = make(map[string]string)
	}
	for s, i := range mapb {
		if _, ok := mapa[s]; !ok {
			mapa[s] = i
		}
	}
	return mapa
}

func IsNotSetStr(m map[string]interface{}, field string, value string) (set bool) {
	if value == "" {
		return false
	}
	if _, ok := m[field]; !ok {
		m[field] = value
		return true
	}
	return false
}

func IsNotSetValue(m map[string]interface{}, field string, value interface{}) (set bool) {
	if value == "" {
		return false
	}
	if _, ok := m[field]; !ok {
		m[field] = value
		return true
	}
	return false
}
