package util

import (
	"fmt"
	"math/rand"

	"xim/proto/consts/enums"
)

var (
	nicknameHeadList  = []string{}
	nicknameHeadTotal = len(nicknameHeadList)

	nicknameFootList  = []string{}
	nicknameFootTotal = len(nicknameFootList)

	femaleFirstNameList  = []string{}
	femaleFirstNameTotal = len(femaleFirstNameList)

	maleFirstNameList  = []string{}
	maleFirstNameTotal = len(maleFirstNameList)

	familyNameList  = []string{}
	familyNameTotal = len(familyNameList)
)

var (
	// 男用户
	maleNicknames = []string{}
	// 女用户
	femaleNicknames = []string{}
	// 随机
	randomNicknames = []string{}
)

var (
	allDefaultNickNameMap    = map[string]bool{}
	allDefaultNickNameNewMap = map[string]bool{}
	maleNicknamesTotal       int
	femaleNicknamesTotal     int
	randomNicknamesTotal     int
)

func init() {
	for _, i := range familyNameList {
		for _, j := range nicknameFootList {
			allDefaultNickNameMap[i+j] = true
		}
	}

	for _, nickname := range maleNicknames {
		allDefaultNickNameNewMap[nickname] = true
	}
	maleNicknamesTotal = len(maleNicknames)
	for _, nickname := range femaleNicknames {
		allDefaultNickNameNewMap[nickname] = true
	}
	femaleNicknamesTotal = len(femaleNicknames)
	for _, nickname := range randomNicknames {
		allDefaultNickNameNewMap[nickname] = true
	}
	randomNicknamesTotal = len(randomNicknames)
}

func IsDefaultNickName(s string) bool {
	return allDefaultNickNameNewMap[s] //allDefaultNickNameMap[s]
}

func RandChineseName(sex int32) string {
	rn := rand.Intn(familyNameTotal)
	familyName := familyNameList[rn]
	firstName := ""
	esex := enums.UserSex(sex)
	if esex != enums.UserSexMale && esex != enums.UserSexFemale {
		rn = rand.Intn(1)
		if rn == 1 {
			esex = enums.UserSexMale
		} else {
			esex = enums.UserSexFemale
		}
	}
	if esex == enums.UserSexMale {
		rn = rand.Intn(maleFirstNameTotal)
		firstName = maleFirstNameList[rn]
	} else {
		rn = rand.Intn(femaleFirstNameTotal)
		firstName = femaleFirstNameList[rn]
	}
	return fmt.Sprintf("%s%s", familyName, firstName)
}

//func RandNickname() string {
//	head := ""
//	rn := rand.Intn(familyNameTotal+nicknameHeadTotal)
//	if rn < familyNameTotal{
//		head = familyNameList[rn]
//	}else{
//		rn = rn - familyNameTotal
//		head = nicknameHeadList[rn]
//	}
//	rn = rand.Intn(nicknameFootTotal)
//	foot := nicknameFootList[rn]
//	return fmt.Sprintf("%s%s", head, foot)
//}

func RandNickname() string {
	rn := rand.Intn(familyNameTotal)
	head := familyNameList[rn]
	rn = rand.Intn(nicknameFootTotal)
	foot := nicknameFootList[rn]
	return fmt.Sprintf("%s%s", head, foot)
}

func RandNicknameWithSex(sex int32) string {
	if sex == enums.UserSexFemale.Int32() || sex == enums.UserSexMale.Int32() {
		if rand.Intn(100) <= 10 {
			if sex == enums.UserSexFemale.Int32() {
				rn := rand.Intn(femaleNicknamesTotal)
				return femaleNicknames[rn]
			}
			rn := rand.Intn(maleNicknamesTotal)
			return maleNicknames[rn]
		}
	}
	rn := rand.Intn(randomNicknamesTotal)
	return randomNicknames[rn]
}
