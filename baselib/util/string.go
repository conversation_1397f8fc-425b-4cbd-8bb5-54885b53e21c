package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	crand "crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"io"
	"math/rand"
	"strings"
	"time"
	"unsafe"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"xim/proto/consts"

	"github.com/google/uuid"
)

const (
	numbersAndLetters = "0123456789abcdefghijklmnopqrstuvwxyz"
	memberAesKey      = "dv2qOj0CAvd*#rVGFoJxbgO$#n9Y0751"
)

func init() {
	rand.Seed(NowTimeNano())
}

func UUID() string {
	return uuid.New().String()
}

func TimeUUID() string {
	return fmt.Sprintf("%s-%s", UUID(), IntToCode(NowTimeNano()/1000))
}

func NowTimeNano() int64 {
	return time.Now().UnixNano()
}

func NowTime() int64 {
	return time.Now().Unix()
}

func NowTimeMillis() int64 {
	return time.Now().UnixNano() / 1000000
}

func NowYmd() string {
	return time.Now().Format(consts.TimeFormatYMD)
}

func YesterdayYmd() string {
	return time.Now().Add(time.Hour * -24).Format(consts.TimeFormatYMD)
}

func NowYmdhms() string {
	return time.Now().Format(consts.TimeFormatYMDHMS)
}

func NowYmdhms2() string {
	return time.Now().Format(consts.TimeFormatYMDHMS2)
}

func NowWeekDate(now time.Time) string {
	if now.IsZero() {
		now = time.Now()
	}
	year, week := now.ISOWeek()
	return GetWeekDate(year, week)
}

func GetWeekDate(year, week int) string {
	format := "%d-00-%d"
	if week < 10 {
		format = "%d-00-0%d"
	}
	return fmt.Sprintf(format, year, week)
}

func GetTodayStartTimestamp() int64 {
	currentTime := time.Now()
	return time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location()).Unix()
}

func Md5(bs []byte) string {
	has := md5.Sum(bs)
	return fmt.Sprintf("%x", has)
}

func Md5String(data string) string {
	if len(data) > 0 {
		has := md5.Sum([]byte(data))
		return fmt.Sprintf("%x", has)
	}
	return ""
}

func JsonStr(v interface{}) string {
	b, _ := json.Marshal(v)
	return string(b)
}

func ProtoJsonStr(v proto.Message) string {
	b, _ := protojson.Marshal(v)
	return string(b)
}

func String2Pb(s string, pb proto.Message) error {
	return proto.Unmarshal([]byte(s), pb)
}

func PbToString(pb proto.Message) string {
	s, _ := proto.Marshal(pb)
	return string(s)
}

func IntToCode(num int64) string {
	result := ""
	size := int64(len(numbersAndLetters))
	for num != 0 {
		rem := num % 36
		result = string(numbersAndLetters[rem]) + result
		num = num / size
	}
	return result
}

func GetUsercode() string {
	ts := NowTimeNano()/1000 - 1641960000000000
	return strings.ToUpper(IntToCode(ts))
}

func CheckYmdDate(ymd string) (year, month, day int, err error) {
	layout := consts.TimeFormatYMD
	if strings.Count(ymd, "-") == 0 {
		layout = consts.TimeFormatYMD2
	}
	date, err := time.Parse(layout, ymd)
	if err != nil {
		return
	}
	year = date.Year()
	month = int(date.Month())
	day = date.Day()
	return
}

func GetAgeByBirthday(birthday string) (age int, err error) {
	y, month, day, err := CheckYmdDate(birthday)
	if err != nil {
		return
	}
	now := time.Now()
	ny := now.Year()
	nm := int(now.Month())
	age = ny - y
	if age > 0 {
		if month > nm || (month == nm && day > now.Day()) {
			age = age - 1
		}
	}
	return
}

func GetConstellation(ymd string) (year, constellation int32, err error) {
	y, month, day, err := CheckYmdDate(ymd)
	if err != nil {
		return
	}
	year = int32(y)
	switch {
	case month <= 0, month >= 13, day <= 0, day >= 32:
		constellation = 0
	case month == 1 && day >= 20, month == 2 && day <= 18:
		constellation = 1
	case month == 2 && day >= 19, month == 3 && day <= 20:
		constellation = 2
	case month == 3 && day >= 21, month == 4 && day <= 19:
		constellation = 3
	case month == 4 && day >= 20, month == 5 && day <= 20:
		constellation = 4
	case month == 5 && day >= 21, month == 6 && day <= 21:
		constellation = 5
	case month == 6 && day >= 22, month == 7 && day <= 22:
		constellation = 6
	case month == 7 && day >= 23, month == 8 && day <= 22:
		constellation = 7
	case month == 8 && day >= 23, month == 9 && day <= 22:
		constellation = 8
	case month == 9 && day >= 23, month == 10 && day <= 22:
		constellation = 9
	case month == 10 && day >= 23, month == 11 && day <= 21:
		constellation = 10
	case month == 11 && day >= 22, month == 12 && day <= 21:
		constellation = 11
	case month == 12 && day >= 22, month == 1 && day <= 19:
		constellation = 12
	}
	return
}

func RandInt64(start, end int64) int64 {
	return rand.Int63n(end-start) + start
}

func RandInt32(start, end int32) int32 {
	return rand.Int31n(end-start) + start
}

func RandString(size int) string {
	bytes := make([]byte, size)
	for i := 0; i < size; i++ {
		b := rand.Intn(10) + 48
		bytes[i] = byte(b)
	}
	return string(bytes)
}

func RandNumberString(size int) string {
	bytes := make([]byte, size)
	for i := 0; i < size; i++ {
		b := rand.Intn(10) + 48
		bytes[i] = byte(b)
	}
	return string(bytes)
}

func RandLetterString(size int) string {
	bytes := make([]byte, size)
	for i := 0; i < size; i++ {
		b := rand.Intn(26) + 65
		bytes[i] = byte(b)
	}
	return string(bytes)
}

func GetHashCode(s string) int64 {
	return int64(crc32.ChecksumIEEE([]byte(s)))
}

func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// AES加密,CBC
func AesEncrypt(origData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	origData = PKCS7Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	crypted := make([]byte, len(origData))
	blockMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

// AES解密
func AesDecrypt(crypted, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = PKCS7UnPadding(origData)
	return origData, nil
}

func hashBytes(key string) (hash []byte) {
	h := sha1.New()
	io.WriteString(h, key)
	hashStr := hex.EncodeToString(h.Sum(nil))
	hash = []byte(hashStr)[:32]
	return
}

func Encrypt(plainText string, key string) (cipherText string, err error) {
	var block cipher.Block
	keyBytes := hashBytes(key)
	plainTextBytes := []byte(plainText)
	block, err = aes.NewCipher(keyBytes)
	if err != nil {
		return
	}

	cipherTextBytes := make([]byte, aes.BlockSize+len(plainTextBytes))
	iv := cipherTextBytes[:aes.BlockSize]
	if _, err = io.ReadFull(crand.Reader, iv); err != nil {
		return
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(cipherTextBytes[aes.BlockSize:], plainTextBytes)
	cipherText = "crypt-" + hex.EncodeToString(cipherTextBytes)
	return
}

func Decrypt(cipherText string, key string) (plainText string, err error) {
	if len(cipherText) == 0 || len(cipherText) < 6 || cipherText[:6] != "crypt-" {
		err = errors.New("Illegal ciphertext")
		return
	}
	cipherText = string(cipherText[6:])
	var block cipher.Block
	keyBytes := hashBytes(key)
	cipherTextBytes, _ := hex.DecodeString(cipherText)
	block, err = aes.NewCipher(keyBytes)
	if err != nil {
		return
	}

	if len(cipherTextBytes) < aes.BlockSize {
		err = errors.New("Ciphertext too short")
		return
	}

	iv := cipherTextBytes[:aes.BlockSize]
	cipherTextBytes = cipherTextBytes[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)

	plainTextBytes := make([]byte, len(cipherTextBytes))
	stream.XORKeyStream(plainTextBytes, cipherTextBytes)
	plainText = string(plainTextBytes)
	return
}

func MemberEncrypt(data string) string {
	if len(data) > 0 {
		encrypted, err := AesEncrypt([]byte(data), []byte(memberAesKey))
		if err == nil {
			return base64.StdEncoding.EncodeToString(encrypted)
		}
	}
	return data
}

func MemberDecrypt(data string) string {
	if len(data) > 0 {
		encrypted, err := base64.StdEncoding.DecodeString(data)
		if err != nil {
			return data
		}
		tempBytes, err := AesDecrypt(encrypted, []byte(memberAesKey))
		if err == nil {
			return string(tempBytes)
		}
	}
	return data
}

func MemberIdcardMd5(code string) string {
	return Md5([]byte(fmt.Sprintf("%s:%s", memberAesKey, code)))
}

// implementate zero-copy convert
func StringToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(&s))
}

func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func Int64ToBytes(v int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(v))
	return bs
}

func EncryptSha256(s string) string {
	hash := sha256.New()
	hash.Write([]byte(s))
	sum := hash.Sum(nil)
	return hex.EncodeToString(sum)
}

func GetThunderH5PartnerIDAndAv(ver string) (partnerID, av string) {
	datas := strings.Split(ver, ":")
	if len(datas) == 2 {
		av = datas[0]
		partnerID = datas[1]
		return
	}
	av = ver
	return
}

func GetRandomString(n int) string {
	randBytes := make([]byte, n/2)
	rand.Read(randBytes)
	return fmt.Sprintf("%x", randBytes)
}

func StringInArray(v string, slice []string) bool {
	for _, s := range slice {
		if s == v {
			return true
		}
	}
	return false
}
