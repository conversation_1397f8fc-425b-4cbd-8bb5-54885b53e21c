package util

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"testing"
	"time"

	"xim/baselib/logger"
)

func TestWeek(t *testing.T) {
	fmt.Println(time.Date(2022, 1, 3, 1, 0, 0, 0, time.Local).ISOWeek())
}

func TestPhone(t *testing.T) {
	v := CheckChinaPhoneNo("")
	fmt.Println(v)
}

func TestInt64(t *testing.T) {
	i := Int64(100)
	bs, err := json.Marshal(i)
	log.Println(string(bs), err)
	v := Int64(0)
	err = json.Unmarshal(bs, &v)
	if err != nil {
		log.Fatalln(err)
	}
	log.Println(v)
}

type IntStructTest struct {
	V Int64 `json:"v"`
}

func TestInt64S(t *testing.T) {
	v := IntStructTest{
		V: 100,
	}
	bs, err := json.Marshal(v)
	if err != nil {
		t.Error(err)
	}
	dv := IntStructTest{}
	err = json.Unmarshal(bs, &dv)
	if err != nil {
		t.Error(err)
	}
	if dv.V != 100 {
		t.Fail()
	}
}

func TestMediaUrlParse(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	logger.Infof(ParseMediaIdFromUrl("http://melon-image-test.oss-cn-shanghai.aliyuncs.com/avatar/3024?a=b"))
	logger.Infof(ParseMediaIdFromUrl("https://melon-image-test.oss-cn-shanghai.aliyuncs.com/chat/1%20%20%20%20%20%20%20%20%20%20%204588?Expires=1657511760&OSSAccessKeyId=TMP.3Kib5ErNu17ewaUB9GsDeNxHnCgQJN5YFZ2rmN2Kro4PQJx5kgMTBb4f9FRBNoh7x2uTLSzH9yccfNpYBRwomGfvP6ftKS&Signature=NmBRjbX4QdO2US09%2B9mVRbVShZ0%3D"))
	logger.Infof(ParseMediaIdFromUrl("https://melon-image.oss-cn-shanghai.aliyuncs.com/cert/001c325b-dd1d-49b2-b3a7-e29d5cc45b34-gba00t4k43?Expires=1657512050&OSSAccessKeyId=TMP.3Kib5ErNu17ewaUB9GsDeNxHnCgQJN5YFZ2rmN2Kro4PQJx5kgMTBb4f9FRBNoh7x2uTLSzH9yccfNpYBRwomGfvP6ftKS&Signature=7e4vrG%2Bn6cOE5O9TGCh%2BNhku3EM%3D"))
	time.Sleep(time.Second * 2)
}

func TestCdn(t *testing.T) {
	url := "https://image-mozhi.xunleizhichui.com/icon/202762475"
	if strings.HasPrefix(url, "http") {
		oldImageCdn := "https://image-mozhi.xunleizhichui.com"
		if strings.HasPrefix(url, oldImageCdn) {
			url = strings.ReplaceAll(url, oldImageCdn, "https://image.qindear.com")
		}
	}
	fmt.Println(url)
}

type UserGiveGiftData struct {
	From     int64   `json:"from"`
	GiftId   int64   `json:"gift_id"`
	Amount   int64   `json:"amount"`
	GiftCoin int64   `json:"gift_coin"`
	Roomid   int64   `json:"roomid"`
	Sid      int64   `json:"sid"`
	Receives []int64 `json:"receives"`
	IsBag    bool    `json:"is_bag"`
	BaseCoin int64   `json:"base_coin"`
	FreeCoin int64   `json:"free_coin"`
	Creator  int64   `json:"creator"`
	IsSale   bool    `json:"is_sale"`
}

func TestJson(t *testing.T) {
	var e UserGiveGiftData
	err := json.Unmarshal([]byte("{\"id\":\"a6ec862160a8431b923ee2c40fa0770c\",\"otype\":\"stats_daily\",\"etype\":\"room_gift\",\"userid\":624357,\"data\":{\"from\":624357,\"gift_id\":10085,\"amount\":1,\"gift_coin\":10,\"roomid\":88,\"sid\":2493,\"receives\":[624027],\"is_bag\":false,\"base_coin\":0,\"free_coin\":10,\"creator\":624027,\"is_sale\":false,\"from_base_coin\":0,\"from_free_coin\":0,\"scene_type\":0}}"), &e)
	if err != nil {
		t.Errorf("err:%v", err)
	}
	_ = e
	t.Log(e)
}
