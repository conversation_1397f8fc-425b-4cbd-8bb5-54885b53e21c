package util

import (
	"regexp"
	"strconv"
)

var (
	// chinaAreaCodes = map[string]string{
	// 	"11": "北京", "12": "天津", "13": "河北", "14": "山西", "15": "内蒙古",
	// 	"21": "辽宁", "22": "吉林", "23": "黑龙江",
	// 	"31": "上海", "32": "江苏", "33": "浙江", "34": "安徽", "35": "福建", "36": "江西", "37": "山东",
	// 	"41": "河南", "42": "湖北", "43": "湖南", "44": "广东", "45": "广西", "46": "海南",
	// 	"50": "重庆", "51": "四川", "52": "贵州", "53": "云南", "54": "西藏",
	// 	"61": "陕西", "62": "甘肃", "63": "青海", "64": "宁夏", "65": "新疆",
	// 	"71": "台湾",
	// 	"81": "香港", "82": "澳门",
	// 	"91": "国外",
	// }

	chinaAreaCodes = map[string]string{}

	chineseNameRegex       = regexp.MustCompile("^[\u4e00-\u9fa5]+(·[\u4e00-\u9fa5]+)*$")
	chineseIdCardNo15Regex = regexp.MustCompile(`^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$`)
	chineseIdCardNo18Regex = regexp.MustCompile(`^([1-9]\d)\d{4}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[\dXx]$`)

	chinaPhoneNoRegex = regexp.MustCompile(`^(\+86\s+)?1\d{10}$`)
)

type ChineseIdCard struct {
	Area     string `json:"area"`
	Birthday string `json:"birthday"`
	Age      int    `json:"age"`
	Sex      int    `json:"sex"`
}

func CheckChinaPhoneNo(no string) bool {
	return chinaPhoneNoRegex.MatchString(no)
}

func CheckChineseName(name string) bool {
	return chineseNameRegex.MatchString(name)
}

func CheckChineseIdCardNo(no string) bool {
	size := len(no)
	if size == 18 {
		params := chineseIdCardNo18Regex.FindStringSubmatch(no)
		if len(params) > 0 {
			if _, ok := chinaAreaCodes[params[1]]; ok {
				return true
			}
		}
	} else if size == 15 {
		return chineseIdCardNo15Regex.MatchString(no)
	}
	return false
}

func GetChineseIdCardData(no string) (data *ChineseIdCard) {
	size := len(no)
	pattern := ""
	if size == 18 {
		pattern = `^(\d{6})(\d{8})\d{2}(\d)[\dXx]$`
	} else if size == 15 {
		pattern = `^(\d{6})(\d{6})\d{2}(\d)$`
	} else {
		return
	}
	if len(pattern) > 0 {
		idCardRegex := regexp.MustCompile(pattern)
		params := idCardRegex.FindStringSubmatch(no)
		if len(params) > 0 {
			p3, err := strconv.Atoi(params[3])
			if err != nil {
				return
			}
			sex := p3 % 2
			birthday := params[2]
			if len(birthday) == 6 {
				birthday = "19" + birthday
			}
			age, err := GetAgeByBirthday(birthday)
			if err != nil {
				return
			}
			data = &ChineseIdCard{
				Area:     params[1],
				Birthday: birthday,
				Age:      age,
				Sex:      sex,
			}
			//fmt.Println(JsonStr(data))
		}
	}

	return
}
