package util

import (
	"fmt"
	"testing"
)

func TestAddr(t *testing.T) {
	code := GetCityCodeByAddr("0563:宣城市,广德市,天官山路,28号,金峰万象广场,德克士(金峰万象店)")
	fmt.Println(code)
}

//
//百度地图测试距离  941 113.97064,22.5375  113.979551,22.53977
//百度地图测试距离  321 113.973658,22.536565  113.97064,22.5375
//百度地图测试距离  4300 113.979408,22.561665  113.963166,22.525616
func TestDistance(t *testing.T) {
	lng1, lat1 := 113.979408, 22.561665 //113.979408, 22.561665 //
	lng2, lat2 := 113.963166, 22.525616
	distance := GetDistanceByLatLng(lat1, lng1, lat2, lng2)
	fmt.Printf("%fm", distance)
}
