package util

import (
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"
)

func GetMsgIDByMD5(sendID string) string {
	t := strconv.FormatInt(time.Now().UnixNano(), 10)
	return Md5([]byte(t + sendID + strconv.FormatInt(rand.Int63n(time.Now().UnixNano()), 10)))
	// todo   sonw
}

func OperationIDGenerator() string {
	return strconv.FormatInt(time.Now().UnixNano()+int64(rand.Uint32()), 10)
}

func GenConversationIDForSingle(sendID, recvID string) string {
	l := []string{sendID, recvID}
	sort.Strings(l)
	return "s_" + strings.Join(l, "_")
}

func GenConversationUniqueKeyForGroup(groupID string) string {
	return groupID
}

func GenGroupConversationID(groupID string) string {
	return "sg_" + groupID
}

func GenConversationUniqueKeyForSingle(sendID, recvID string) string {
	l := []string{sendID, recvID}
	sort.Strings(l)
	return strings.Join(l, "_")
}

func GetNotificationConversationIDByConversationID(conversationID string) string {
	l := strings.Split(conversationID, "_")
	if len(l) > 1 {
		l[0] = "n"
		return strings.Join(l, "_")
	}
	return ""
}

func GetSelfNotificationConversationID(userID string) string {
	return "n_" + userID + "_" + userID
}
