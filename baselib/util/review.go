package util

import (
	"xim/baselib/logger"
)

// 支持针对渠道号，指定id打开/关闭伪消息和牵线开关
var (
	HuaweiAddr     = ""
	OppoAddr       = ""
	filterUserids  = []int64{889170, 2548654} // 过滤用户
	filterChannels = []string{}               // 过滤渠道
)

/*
*
// 针对渠道号,指定用户屏蔽此功能
现在这里 有两个地方实际在用
1. 匹配分发
2. 女用户匹配检查
*/
func PreFilter(userid int64, channel string) bool {
	if InInt64Slice(filterUserids, userid) {
		return true
	}
	if InStringSlice(filterChannels, channel) {
		return true
	}
	return false
}

func PreFilterNew(userid int64, channel, av string) bool {
	if InInt64Slice(filterUserids, userid) {
		return true
	}
	if InStringSlice(filterChannels, channel) {
		return true
	}
	if channel == "baidu" && CompareVersionRes(av, "2.2.1") >= 0 {
		return true
	}
	return false
}

func GetFilterCh() []string { return filterChannels }

func FilterReviewChannel(userid int64, ch, addr, av string) bool {
	if ch == "huawei" || ch == "release" {
		if len(addr) == len(HuaweiAddr) && addr == HuaweiAddr {
			logger.Warnf("FilterReviewChannel ch:%v, userid:%v", ch, userid)
			return true
		}
	}
	if ch == "oppo" && CompareVersionRes(av, "1.7.2") >= 0 {
		if len(addr) > 4 && addr[0:3] == "028" {
			logger.Warnf("FilterReviewChannel ch:%v,   userid:%v", ch, userid)
			return true
		}
	}
	return false
}
