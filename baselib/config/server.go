package config

import (
	"flag"
	"fmt"
	"xim/baselib/server/env"

	"github.com/spf13/viper"
)

var (
	serverConfig = ServerConfig{}

	Conf string
)

func init() {
	flag.StringVar(&Conf, "config", "deploy/config.yml", "config file")
}

func GetServerConfig() *ServerConfig {
	return &serverConfig
}

func Init() {
	if env.IsTest() || env.IsProd() {
		initRemote()
		return
	}
	// 保证本地测试服务启动无需调整
	initLocal()
}

func initLocal() {
	flag.Parse()
	viper.SetConfigType("yaml")
	viper.SetConfigFile(Conf)
	checkAndPanic("addr", viper.BindEnv("addr", "MODULES_XLSOA_SERVER_CONTEXT_ADDR"))
	checkAndPanic("prometheus.addr", viper.BindEnv("prometheus.addr", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_LISTEN_ADDR"))
	checkAndPanic("prometheus.metrics_path", viper.BindEnv("prometheus.metrics_path", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_PATH"))
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("error read config %s error %v", Conf, err))
	}
	err = viper.Unmarshal(&serverConfig)
	if err != nil {
		panic(fmt.Errorf("errpr viper.Unnarshal %v", err))
	}
}

func initRemote() {
	checkAndPanic("addr", viper.BindEnv("addr", "MODULES_XLSOA_SERVER_CONTEXT_ADDR"))
	checkAndPanic("prometheus.addr", viper.BindEnv("prometheus.addr", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_LISTEN_ADDR"))
	checkAndPanic("prometheus.metrics_path", viper.BindEnv("prometheus.metrics_path", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_PATH"))
	flag.Parse()
	viper.SetConfigFile(Conf)
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("error read config %s error %v", Conf, err))
	}
	InitVipers()
	err = viper.Unmarshal(&serverConfig)
	if err != nil {
		panic(fmt.Errorf("errpr viper.Unnarshal %v", err))
	}

	serverConfig.FilterConfigWithLabels()
}

// FilterConfigWithLabels 根据标签过滤某些配置
func (s *ServerConfig) FilterConfigWithLabels() {
	var listToMap = func(list []string) map[string]struct{} {
		result := make(map[string]struct{})
		for _, item := range list {
			result[item] = struct{}{}
		}
		return result
	}

	var (
		mysqlDBs  []*MysqlConfig
		redisList []*RedisConfig
	)

	labels, err := ParseLabels(s.ConfigLabels)
	if err != nil {
		fmt.Printf("ParseLabels err:%v\n", err)
		return
	}

	for scheme, items := range labels {
		switch scheme {
		case Mysql:
			var itemMap = listToMap(items)
			for _, db := range s.Mysql {
				if _, ok := itemMap[db.DbName]; ok {
					mysqlDBs = append(mysqlDBs, db)
				}
			}
		case Redis:
			var itemMap = listToMap(items)
			for _, db := range s.Redis {
				if _, ok := itemMap[db.Name]; ok {
					redisList = append(redisList, db)
				}
			}
		}
	}
	s.Mysql = mysqlDBs
	s.Redis = redisList
}

func checkAndPanic(msg string, err error) {
	if err != nil {
		panic(fmt.Sprintf("error "+msg+" :%v", err))
	}
}

type ServerConfig struct {
	Name         string             `yaml:"name"`
	Addr         string             `yaml:"addr"`
	Logger       string             `yaml:"logger"`
	LogLevel     string             `yaml:"log-level" mapstructure:"log-level"`
	Zap          *ZapConfig         `yaml:"zap"`
	Prometheus   PrometheusConfig   `yaml:"prometheus"`
	Jaeger       *JaegerConfig      `yaml:"jaeger"`
	Mysql        []*MysqlConfig     `yaml:"mysql"`
	Clickhouse   []ClickhouseConfig `yaml:"clickhouse" mapstructure:"clickhouse"`
	Redis        []*RedisConfig     `yaml:"redis"`
	Mongodb      *MongodbConfig     `yaml:"mongodb"`
	MongodbIm    *MongodbConfig     `mapstructure:"mongodb_im" yaml:"mongodb_im"`
	MongodbXbase *MongodbConfig     `mapstructure:"mongodb_xbase" yaml:"mongodb_xbase"`
	Interceptor  *InterceptorConfig `yaml:"interceptor"`
	Nsq          NsqConfig          `yaml:"nsq"`
	Pay          *PayConfig         `yaml:"pay"`
	Pusher       *PusherConfig      `yaml:"pusher"`
	ConfigLabels []string           `mapstructure:"config_labels" yaml:"config_labels"`
	Kafka        *Kafka             `yaml:"kafka"`
}

type PrometheusConfig struct {
	Addr        string `yaml:"addr"`
	MetricsPath string `yaml:"metrics_path"`
}

type JaegerConfig struct {
	ServiceName string `yaml:"service_name" mapstructure:"service_name"`
	Endpoint    string `yaml:"endpoint"`
	Enable      bool   `yaml:"enable"`
	Environment string `yaml:"environment"`
}

/**

 下边这些是环境变量中的 key 之类的，或许是有用的。

env.transportAddr = checkEnv("MODULES_XLSOA_ENVIRONMENT_TRANSPORT_ADDR")
env.PromHttpAddr = checkEnv("MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_LISTEN_ADDR")
env.PromMetricsPath = checkEnv("MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_PATH")
env.expiredTime = checkEnv("MODULES_XLSOA_ENVIRONMENT_CLIENT_EXPIREDTIME")


	c.addr = checkEnv("MODULES_XLSOA_SERVER_CONTEXT_ADDR")
	sw := checkEnv("MODULES_XLSOA_SERVER_CONTEXT_OAUTH_SECURE_SWITCH")
	if sw != "" {
		if sw == "on" {
			c.withOauthSecure = true
		} else if sw == "off" {
			c.withOauthSecure = false
		}
	}


	if f := checkEnv("XLSOA_KUBESERVICE_CONFIG_FILE"); f != "" {
		//It's a special handling of xlsoa service deployed in
		//k8s cluster. The xlsoa config file is not convenient
		//to be placed in a relative path along with the program.
		paths = []string{f}
	}
*/
