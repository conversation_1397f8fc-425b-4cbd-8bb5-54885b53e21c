package config

type Kafka struct {
	Username     string   `mapstructure:"username"`
	Password     string   `mapstructure:"password"`
	ProducerAck  string   `mapstructure:"producerAck"`
	CompressType string   `mapstructure:"compressType"`
	Address      []string `mapstructure:"address"`
	//Tls            TLSConfig `mapstructure:"tls"`
	Close        bool  `yaml:"close" mapstructure:"close"` // 防止kafka 出故障，兜底走grpc
	StateTopic   Topic `yaml:"stateTopic" mapstructure:"stateTopic"`
	MessageTopic Topic `yaml:"messageTopic" mapstructure:"messageTopic"`
}

type Topic struct {
	Group string `yaml:"group" mapstructure:"group"`
	Topic string `yaml:"topic" mapstructure:"topic"`
}

func (k *Kafka) Build() *Config {
	if k == nil {
		return nil
	}

	return &Config{
		// Username:     k.Username,
		// Password:     k.Password,
		ProducerAck: k.Producer<PERSON>ck,
		//CompressType: k.CompressType,
		Addr: k.Address,
		// TLS: kafka.TLSConfig{
		// 	EnableTLS:          k.Tls.EnableTLS,
		// 	CACrt:              k.Tls.CACrt,
		// 	ClientCrt:          k.Tls.ClientCrt,
		// 	ClientKey:          k.Tls.ClientKey,
		// 	ClientKeyPwd:       k.Tls.ClientKeyPwd,
		// 	InsecureSkipVerify: k.Tls.InsecureSkipVerify,
		// },
	}
}

type Config struct {
	Username     string   `yaml:"username"`
	Password     string   `yaml:"password"`
	ProducerAck  string   `yaml:"producerAck"`
	CompressType string   `yaml:"compressType"`
	Addr         []string `yaml:"addr"`
	//TLS          TLSConfig `yaml:"tls"`
}
