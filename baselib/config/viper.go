package config

import (
	"fmt"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
)

var pathDir = "configs"

var configPaths = []string{
	filepath.Join(pathDir, "mysql.yaml"),
	filepath.Join(pathDir, "redis.yaml"),
	filepath.Join(pathDir, "nsq.yaml"),
	filepath.Join(pathDir, "mongo.yaml"),
}

func InitVipers() {
	for _, cPath := range configPaths {
		_, err := os.Stat(cPath)
		if !os.IsNotExist(err) {
			viper.SetConfigFile(cPath)
			if err = viper.MergeInConfig(); err != nil {
				fmt.Printf("init config viper err:%v,config name:%s\n", err, cPath)
				return
			}
		}

	}
}
