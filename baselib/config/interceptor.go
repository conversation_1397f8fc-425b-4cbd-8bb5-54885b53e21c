package config

type InterceptorConfig struct {
	Auth *AuthConfig `json:"auth" yaml:"auth"`
}

type AuthConfig struct {
	AdminPassword string   `json:"admin_password" mapstructure:"admin_password" yaml:"admin_password"` // 管理员密码
	AdminIPs      string   `json:"admin_ips" mapstructure:"admin_ips" yaml:"admin_ips"`                // 管理员ip正则表达式
	ClientId      string   `json:"client_id" mapstructure:"client_id" yaml:"client_id"`
	ClientSecret  string   `json:"client_secret" mapstructure:"client_secret" yaml:"client_secret"`
	Scopes        []string `json:"scopes" mapstructure:"scopes" yaml:"scopes"`
	Issuer        string   `json:"issuer" mapstructure:"issuer" yaml:"issuer"`
	Projects      []string `json:"projects" mapstructure:"projects" yaml:"projects"`
	ExcludeAPIs   []string `json:"exclude_apis" mapstructure:"exclude_apis" yaml:"exclude_apis"`
	Domain        string   `json:"domain" mapstructure:"domain" yaml:"domain"`
}
