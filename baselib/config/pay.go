package config

type PayConfig struct {
	AliPay *AliPayConfig
	WxPay  *WxPayConfig
	IapPay *IapPayConfig
}

type AliPayConfig struct {
	Id            int    `yaml:"id"`
	AppId         string `yaml:"app_id"`
	AppPrivateKey string `yaml:"app_private_key"`
	PublicKey     string `yaml:"public_key"`
	IsProd        bool   `yaml:"is_prod"`
	NotifyUrl     string `yaml:"notify_url"`
	ReturnUrl     string `yaml:"return_url"`
}

type WxPayConfig struct {
	Id int `yaml:"id"`
	//AppId        string `yaml:"app_id"`
	MchId        string `yaml:"mch_id"`
	SerialNo     string `yaml:"serial_no"`
	Apiv3Key     string `yaml:"apiv3_key"`
	PrivateKey   string `yaml:"private_key"`
	NotifyUrl    string `yaml:"notify_url"`
	ComplaintUrl string `yaml:"complaint_url"`
	SupportH5    bool   `yaml:"support_h5"`
}

type IapPayConfig struct {
	Password string `yaml:"password"`
}

type YZHConfig struct {
	BrokerID     string // 代征主体ID
	DealerID     string // 商户ID
	Appkey       string // 商户appkey
	Des3Key      string // 商户des3key
	PrivateKey   string // 商户秘钥
	PublicKey    string //商户公钥
	YunPublicKey string // 云账户公钥
	NotifyURL    string
}

type HYGConfig struct {
	CooperatorId  string // 商户ID
	AESSecret     string //AES秘钥 用于请求参数和响应数据的加密和解密
	RSAPublicKey  string //RSA公钥 用于签名校验
	RSAPrivateKey string //RSA私钥 用于签名
	NotifyURL     string //回调通知接口
	TaskId        string //该笔下发所选任务ID
}
