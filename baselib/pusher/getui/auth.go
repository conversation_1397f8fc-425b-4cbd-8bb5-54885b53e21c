package getui

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"xim/baselib/cache/lock"
	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/util"

	"github.com/go-redis/redis/v8"
)

const (
	gtAuthKey     = "pusher:gt_auth:%s"
	gtAuthLockKey = "pusher:gt_auth_lock:%s"
)

type AuthToken struct {
	Token         string
	ExpireMillion int64
	m             sync.RWMutex
}

func (a *AuthToken) getToken() (exist bool, token string) {
	a.m.RLock()
	defer a.m.RUnlock()
	if time.Unix(a.ExpireMillion/1000, 0).Add(-time.Minute).Before(time.Now()) {
		return false, ""
	}
	if a.Token == "" {
		return false, ""
	}
	return true, a.Token
}

func (a *AuthToken) setToken(token string, expireMillion int64) bool {
	a.m.Lock()
	defer a.m.Unlock()
	if a.ExpireMillion > expireMillion {
		return false
	}
	a.Token = token
	a.ExpireMillion = expireMillion
	return true
}

type GtRedisTokenData struct {
	Token         string `json:"token"`
	ExpireMillion int64  `json:"expire_million"`
}

func (p *GTPusher) getTokenFromRedis(app string) (token *GtRedisTokenData, err error) {

	data, err := p.client.Get(context.Background(), fmt.Sprintf(gtAuthKey, app)).Bytes()
	if err != nil && err != redis.Nil {
		logger.Errorf("error %v", err)
		return
	}

	if len(data) == 0 {
		return nil, nil
	}

	var v GtRedisTokenData
	err = json.Unmarshal(data, &v)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	return &v, nil
}

func (p *GTPusher) getToken(app string) (token string, err error) {
	authToken := p.tokens[app]
	if authToken == nil {
		return "", fmt.Errorf("error no config for app %s", app)
	}
	exist, token := authToken.getToken()
	if exist {
		return token, nil
	}

	tokenInfo, err := p.getTokenFromRedis(app)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if tokenInfo != nil {
		if authToken.setToken(tokenInfo.Token, tokenInfo.ExpireMillion) {
			return tokenInfo.Token, nil
		}
	}

	l, err := lock.NewRetryLock(p.client, fmt.Sprintf(gtAuthLockKey, app), time.Minute, time.Second, 5)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	defer l.Release()

	exist, token = authToken.getToken()
	if exist {
		return token, nil
	}

	tokenInfo, err = p.getTokenFromRedis(app)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if tokenInfo != nil {
		if authToken.setToken(tokenInfo.Token, tokenInfo.ExpireMillion) {
			return tokenInfo.Token, nil
		}
	}

	// 从api 获取token, 并设置
	token, expireMillion, err := p.auth(app)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	authToken.setToken(token, expireMillion)
	e := p.client.Set(context.Background(), fmt.Sprintf(gtAuthKey, app), util.JsonStr(GtRedisTokenData{
		Token:         token,
		ExpireMillion: expireMillion,
	}), time.Hour*24).Err()
	if e != nil {
		logger.Errorf("error %v", e)
	}
	return token, nil
}

func (p *GTPusher) auth(app string) (token string, expireMillion int64, err error) {
	conf := p.configs[app]
	timestamp := fmt.Sprintf("%d", util.NowTimeMillis())
	signStr := fmt.Sprintf("%s%s%s", conf.AppKey, timestamp, conf.Secret)
	bodyData := make(map[string]string)
	bodyData["timestamp"] = timestamp
	bodyData["appkey"] = conf.AppKey
	bodyData["sign"] = util.EncryptSha256(signStr)

	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, authUrl)
	bodyStr := util.JsonStr(bodyData)
	logger.Debugf("getui auth url: %s, data: %s", url, bodyStr)
	headers := make(map[string]string)
	res, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui auth error: %v", err)
		return
	}
	fmt.Println("getui auth response: ", res)
	if len(res) == 0 {
		logger.Errorf("getui auth response error: %v", res)
		return
	}

	resp := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Token      string `json:"token"`
			ExpireTime string `json:"expire_time"`
		} `json:"data"`
	}{}
	err = json.Unmarshal([]byte(res), &resp)
	if err != nil {
		logger.Errorf("getui auth response json unmarshal error: %v", err)
		return
	}
	if resp.Code != 0 {
		logger.Errorf("error resp code %d", resp.Code)
		err = fmt.Errorf("error resp code %d", resp.Code)
		return
	}
	expireMillion, err = strconv.ParseInt(resp.Data.ExpireTime, 10, 64)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	token = resp.Data.Token
	return
}
