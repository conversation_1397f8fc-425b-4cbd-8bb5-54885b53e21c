package getui

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"xim/baselib/config"
	"xim/baselib/httpcli"
	"xim/baselib/logger"
	"xim/baselib/pusher/proto"
	"xim/baselib/util"

	"github.com/go-redis/redis/v8"
)

const (
	// errcode https://docs.getui.com/getui/server/rest_v2/code/
	Success      = 0
	TokenInvalid = 10001
)

type GTPusher struct {
	configs map[string]*config.GTConfig
	tokens  map[string]*AuthToken
	client  *redis.Client
}

func (g *GTPusher) getConfig(app string) *config.GTConfig {
	return g.configs[app]
}

type GTClickType string

const (
	pushTypeToSingle = 1 //指向单个用户推送消息
	pushTypeToList   = 2 //指向指定的一批用户推送消息
	pushTypeToGroup  = 3 //指向APP符合筛选条件的所有用户推送消息，支持定速推送、定时推送，支持条件的交并补功能

	authUrl        = "/auth"
	singleCidUrl   = "/push/single/cid"
	singleAliasUrl = "/push/single/alias"
	taskUrl        = "/push/list/message"
	listCidUrl     = "/push/list/cid"
	listAliasUrl   = "/push/list/alias"

	ClickTypeIntent        = "intent"
	ClickTypeUrl           = "url"
	ClickTypePayload       = "payload"
	ClickTypePayloadCustom = "payload_custom"
	ClickTypeStartapp      = "startapp"
	ClickTypeNone          = "none"
)

type GTStrategy struct {
	Default int  `json:"default,omitempty"`
	Ios     *int `json:"ios,omitempty"`
	Hw      *int `json:"hw,omitempty"`
	Xm      *int `json:"xm,omitempty"`
	Vv      *int `json:"vv,omitempty"`
	Op      *int `json:"op,omitempty"`
	Mz      *int `json:"mz,omitempty"`
	St      *int `json:"st,omitempty"`
}

type GTSettings struct {
	Ttl      int         `json:"ttl,omitempty"`
	Strategy *GTStrategy `json:"strategy,omitempty"`
}

type GTAudience struct {
	Cid   []string `json:"cid,omitempty"`
	Alias []string `json:"alias,omitempty"`
}

type GTPushMessage struct {
	Notification *GTNotification `json:"notification"`
	Transmission *string         `json:"transmission"`
}

type GTNotification struct {
	Title        string  `json:"title"`
	Body         string  `json:"body"`
	LogoUrl      *string `json:"logo_url,omitempty"`
	BigText      *string `json:"big_text,omitempty"`
	BigImage     *string `json:"big_image,omitempty"`
	ClickType    string  `json:"click_type"`
	Url          *string `json:"url,omitempty"`
	Intent       *string `json:"intent,omitempty"`
	Payload      *string `json:"payload,omitempty"`
	NotifyId     *int32  `json:"notify_id,omitempty"`
	ChannelId    *string `json:"channel_id,omitempty"`
	ChannelName  *string `json:"channel_name,omitempty"`
	ShowFront    bool    `json:"show_front,omitempty"`
	ShowTotal    bool    `json:"show_total,omitempty"`
	ChannelLevel *int    `json:"channel_level"`
}

type GTPushChannel struct {
	Android *GTPushChannelAndroid `json:"android,omitempty"`
	Ios     *GTPushChannelIos     `json:"ios,omitempty"`
}

type GTPushChannelAndroid struct {
	Ups GTPushChannelAndroidUps `json:"ups,omitempty"`
}

type GTPushChannelAndroidUps struct {
	Notification *GTNotification                   `json:"notification,omitempty"`
	Options      map[string]map[string]interface{} `json:"options,omitempty"`
}

type GTPushChannelIos struct {
	Type      string              `json:"type"`
	Payload   string              `json:"payload"`
	Aps       GTPushChannelIosAps `json:"aps,omitempty"`
	AutoBadge string              `json:"auto_badge,omitempty"`
}

type GTAlert struct {
	Title string `json:"title"`
	Body  string `json:"body"`
}

type GTPushChannelIosAps struct {
	Alert            GTAlert `json:"alert"`
	ContentAvailable int     `json:"content_available"`
	Sound            string  `json:"sound"`
}

type GTSingleData struct {
	RequestId   string         `json:"request_id"`
	Settings    *GTSettings    `json:"settings"`
	Audience    GTAudience     `json:"audience"`
	PushMessage GTPushMessage  `json:"push_message"`
	PushChannel *GTPushChannel `json:"push_channel,omitempty"`
}

type GTTaskData struct {
	RequestId   string         `json:"request_id"`
	GroupName   string         `json:"group_name"`
	Settings    *GTSettings    `json:"settings"`
	PushMessage GTPushMessage  `json:"push_message"`
	PushChannel *GTPushChannel `json:"push_channel,omitempty"`
}

type TaskData struct {
	Taskid string `json:"taskid"`
}
type TaskResponse struct {
	Code int       `json:"code"`
	Msg  string    `json:"msg"`
	Data *TaskData `json:"data"`
}

type GTListData struct {
	Audience GTAudience `json:"audience"`
	Taskid   string     `json:"taskid"`
	IsAsync  bool       `json:"is_async"`
}

type BaseResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func Init(client *redis.Client, confs map[string]*config.GTConfig) *GTPusher {
	if confs != nil {
		pusher := &GTPusher{
			configs: confs,
			tokens:  map[string]*AuthToken{},
			client:  client,
		}
		for app, _ := range confs {
			pusher.tokens[app] = &AuthToken{}
			pusher.getToken(app)
		}
		return pusher
	}
	return nil
}

func (p *GTPusher) Push(platform proto.PushPlatform, targets []*proto.PushTarget, data *proto.PushData) {
	if data == nil && len(targets) == 0 {
		logger.Warnf("Push target or data error")
		return
	}

	total := len(targets)
	if total == 1 {
		p.singlePush(platform, targets[0], data)
	} else if total > 1 {
		tempTargets := make(map[string][]string)
		for _, target := range targets {
			tempTargets[target.App] = append(tempTargets[target.App], target.Alias)
		}
		for app, aliasList := range tempTargets {
			p.batchPush(platform, app, aliasList, data)
		}
	}
}

func (p *GTPusher) singlePush(platform proto.PushPlatform, target *proto.PushTarget, data *proto.PushData) (code int) {
	for i := 0; i < 3; i++ {
		token, err := p.getToken(target.App)
		if err != nil {
			logger.Errorf("error %v", err)
			return
		}

		if platform == proto.PlatformAndroid {
			code = p.singlePushAndroid(target.App, token, target.Alias, data)
		} else if platform == proto.PlatformIos {
			code = p.singlePushIos(target.App, token, target.Alias, data)
		}

		if code == TokenInvalid {
			time.Sleep(time.Millisecond * 50)
			continue
		}
		break
	}
	return
}

func (p *GTPusher) singlePushAndroid(app, token, alias string, data *proto.PushData) (code int) {
	audience := GTAudience{
		Alias: []string{alias},
	}
	notification := &GTNotification{
		Title:       data.Title,
		Body:        data.Text,
		LogoUrl:     data.Icon,
		BigImage:    data.Image,
		NotifyId:    data.NotifyId,
		ChannelId:   data.ChannelId,
		ChannelName: data.ChannelName,
		ShowFront:   data.ShowFront,
		ShowTotal:   data.ShowTotal,
	}

	if data.OpenType != nil {
		notification.ClickType = data.OpenType.Type
		switch data.OpenType.Type {
		case ClickTypeUrl:
			notification.Url = &data.OpenType.Data
		case ClickTypeIntent:
			intent := strings.ReplaceAll(data.OpenType.Data, "{{app}}", app)
			notification.Intent = &intent
		case ClickTypePayload, ClickTypePayloadCustom:
			notification.Payload = &data.OpenType.Data
		}
	}

	transmission := util.JsonStr(notification)

	upsOptions := map[string]map[string]interface{}{
		"HW": {},
		"XM": {
			"/extra.channel_id": "high_system",
		},
		"OP": {},
		"VV": {
			//"/classification": 1,
			"/category": "IM",
		},
	}
	if data.ChannelId != nil && len(*data.ChannelId) > 0 {
		upsOptions["HW"]["/message/android/notification/channel_id"] = *data.ChannelId
		upsOptions["OP"]["/channel_id"] = *data.ChannelId
	}
	if data.MiChannelId != nil && len(*data.MiChannelId) > 0 {
		upsOptions["XM"]["/extra.channel_id"] = *data.MiChannelId
	}

	bodyData := GTSingleData{
		RequestId: p.getRequestId(),
		Settings: &GTSettings{
			Ttl: 3600000,
			Strategy: &GTStrategy{
				Default: 1,
			},
		},
		Audience: audience,
		PushMessage: GTPushMessage{
			Transmission: &transmission,
		},
		PushChannel: &GTPushChannel{
			Android: &GTPushChannelAndroid{
				Ups: GTPushChannelAndroidUps{
					Notification: notification,
					Options:      upsOptions,
				},
			},
		},
	}
	logger.Infof("gtpush bodydata %s", util.JsonStr(bodyData))

	conf := p.getConfig(app)
	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, singleAliasUrl)
	//logger.Infof("getui push app: %v, url: %s, data: %s", app, url, util.JsonStr(bodyData))
	headers := make(map[string]string)
	headers["token"] = token
	result, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui push error: %v, %v, %v, %v, %v, %v", app, alias, url, token, util.JsonStr(bodyData), err)
		return
	}

	var baseResp BaseResp
	err = json.Unmarshal([]byte(result), &baseResp)
	if err != nil {
		logger.Errorf("getui push error %v, %v", result, err)
		return
	}

	if baseResp.Code != Success {
		logger.Errorf("getui push resp %s", result)
		code = baseResp.Code
		return
	}
	return
}

func (p *GTPusher) singlePushIos(app, token, alias string, data *proto.PushData) (code int) {
	audience := GTAudience{
		Alias: []string{alias},
	}
	notification := &GTNotification{
		Title:     data.Title,
		Body:      data.Text,
		LogoUrl:   data.Icon,
		BigImage:  data.Image,
		NotifyId:  data.NotifyId,
		ShowFront: data.ShowFront,
		ShowTotal: data.ShowTotal,
	}

	if data.OpenType != nil {
		notification.ClickType = data.OpenType.Type
		switch data.OpenType.Type {
		case ClickTypeUrl:
			notification.Url = &data.OpenType.Data
		case ClickTypePayload, ClickTypePayloadCustom:
			notification.Payload = &data.OpenType.Data
		}
	}

	transmission := util.JsonStr(notification)
	bodyData := GTSingleData{
		RequestId: p.getRequestId(),
		Settings: &GTSettings{
			Ttl: 3600000,
			Strategy: &GTStrategy{
				Default: 1,
			},
		},
		Audience: audience,
		PushMessage: GTPushMessage{
			Transmission: &transmission,
		},
		PushChannel: &GTPushChannel{
			Ios: &GTPushChannelIos{
				Type:    "notify",
				Payload: data.OpenType.Data,
				Aps: GTPushChannelIosAps{
					Alert: GTAlert{
						Title: data.Title,
						Body:  data.Text,
					},
					Sound: "default",
				},
			},
		},
	}

	conf := p.configs[app]
	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, singleAliasUrl)
	//logger.Infof("getui push url: %s, data: %s", url, util.JsonStr(bodyData))
	headers := make(map[string]string)
	headers["token"] = token
	result, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui push error: %v, %v, %v, %v, %v, %v", app, alias, url, token, util.JsonStr(bodyData), err)
		return
	}

	var baseResp BaseResp
	err = json.Unmarshal([]byte(result), &baseResp)
	if err != nil {
		logger.Errorf("getui push error %v, %v", result, err)
		return
	}

	if baseResp.Code != Success {
		logger.Errorf("getui push resp %s", result)
		code = baseResp.Code
		return
	}
	return
}

func (p *GTPusher) getRequestId() string {
	return fmt.Sprintf("%s-%s", util.RandString(10), util.IntToCode(util.NowTimeMillis()))
}

func (p *GTPusher) batchPush(platform proto.PushPlatform, app string, aliasList []string, data *proto.PushData) (code int) {
	for i := 0; i < 3; i++ {
		token, err := p.getToken(app)
		if err != nil {
			logger.Errorf("error %v", err)
			return
		}

		if platform == proto.PlatformAndroid {
			code, _ = p.batchPushAndroid(app, token, aliasList, data)
		} else if platform == proto.PlatformIos {
			code, _ = p.batchPushIos(app, token, aliasList, data)
		}

		if code == TokenInvalid {
			time.Sleep(time.Millisecond * 50)
			continue
		}
		break
	}
	return
}

func (p *GTPusher) batchPushAndroid(app, token string, aliasList []string, data *proto.PushData) (code int, err error) {
	taskid, code, err := p.createAndroidTask(app, token, data)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if code != 0 {
		return code, nil
	}
	if len(taskid) == 0 {
		return
	}

	bodyData := GTListData{
		Audience: GTAudience{
			Alias: aliasList,
		},
		Taskid:  taskid,
		IsAsync: true,
	}

	conf := p.getConfig(app)
	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, listAliasUrl)
	logger.Debugf("getui list push url: %s, data: %s", url, util.JsonStr(bodyData))
	headers := make(map[string]string)
	headers["token"] = token
	result, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui push error: %v, %v, %v, %v, %v, %v", app, util.JsonStr(aliasList), url, token, util.JsonStr(bodyData), err)
		return
	}

	var baseResp BaseResp
	err = json.Unmarshal([]byte(result), &baseResp)
	if err != nil {
		logger.Errorf("getui list push error %v, %v", result, err)
		return
	}
	if baseResp.Code != 0 {
		logger.Errorf("getui list push resp %s", result)
		return baseResp.Code, nil
	}
	return
}

func (p *GTPusher) batchPushIos(app, token string, aliasList []string, data *proto.PushData) (code int, err error) {
	taskid, code, err := p.createIosTask(app, token, data)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	if code != 0 {
		return code, nil
	}
	if len(taskid) == 0 {
		return
	}

	bodyData := GTListData{
		Audience: GTAudience{
			Alias: aliasList,
		},
		Taskid:  taskid,
		IsAsync: true,
	}

	conf := p.configs[app]
	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, listAliasUrl)
	//logger.Debugf("getui list push url: %s, data: %s", url, util.JsonStr(bodyData))
	headers := make(map[string]string)
	headers["token"] = token
	result, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui push error: %v, %v, %v, %v, %v, %v", app, util.JsonStr(aliasList), url, token, util.JsonStr(bodyData), err)
		return
	}

	var baseResp BaseResp
	err = json.Unmarshal([]byte(result), &baseResp)
	if err != nil {
		logger.Errorf("getui list push error %v, %v", result, err)
		return
	}
	if baseResp.Code != 0 {
		logger.Errorf("getui list push resp %s", result)
		return baseResp.Code, nil
	}
	return
}

func (p *GTPusher) createAndroidTask(app, token string, data *proto.PushData) (taskid string, errorCode int, err error) {
	notification := &GTNotification{
		Title:       data.Title,
		Body:        data.Text,
		LogoUrl:     data.Icon,
		BigImage:    data.Image,
		NotifyId:    data.NotifyId,
		ChannelId:   data.ChannelId,
		ChannelName: data.ChannelName,
		ShowFront:   data.ShowFront,
		ShowTotal:   data.ShowTotal,
	}

	if data.OpenType != nil {
		notification.ClickType = data.OpenType.Type
		switch data.OpenType.Type {
		case ClickTypeUrl:
			notification.Url = &data.OpenType.Data
		case ClickTypeIntent:
			intent := strings.ReplaceAll(data.OpenType.Data, "{{app}}", app)
			notification.Intent = &intent
		case ClickTypePayload, ClickTypePayloadCustom:
			notification.Payload = &data.OpenType.Data
		}
	}

	transmission := util.JsonStr(notification)

	upsOptions := map[string]map[string]interface{}{
		"HW": {},
		"XM": {
			"/extra.channel_id": "high_system",
		},
		"OP": {},
		"VV": {
			"/classification": 1,
		},
	}
	if data.ChannelId != nil && len(*data.ChannelId) > 0 {
		upsOptions["HW"]["/message/android/notification/channel_id"] = *data.ChannelId
		upsOptions["XM"]["/extra.channel_id"] = data.ChannelId
		upsOptions["OP"]["/channel_id"] = data.ChannelId
	}
	if data.MiChannelId != nil && len(*data.MiChannelId) > 0 {
		upsOptions["XM"]["/extra.channel_id"] = *data.MiChannelId
	}

	bodyData := GTSingleData{
		RequestId: p.getRequestId(),
		Settings: &GTSettings{
			Ttl: 3600000,
			Strategy: &GTStrategy{
				Default: 1,
			},
		},
		PushMessage: GTPushMessage{
			Transmission: &transmission,
		},
		PushChannel: &GTPushChannel{
			Android: &GTPushChannelAndroid{
				Ups: GTPushChannelAndroidUps{
					Notification: notification,
					Options:      upsOptions,
				},
			},
		},
	}

	bodyStr := util.JsonStr(bodyData)
	conf := p.getConfig(app)
	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, taskUrl)
	logger.Debugf("getui task url: %s, data: %s", url, bodyStr)
	headers := make(map[string]string)
	headers["token"] = token
	res, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui push error: %v, %v, %v, %v, %v", app, url, token, util.JsonStr(bodyData), err)
		return
	}

	resp := &TaskResponse{}
	err = json.Unmarshal([]byte(res), resp)
	if err != nil {
		logger.Errorf("getui task resp json error: %v", err)
		return
	}
	if resp.Code != 0 {
		logger.Errorf("getui task resp %s", res)
		return "", resp.Code, nil
	}
	logger.Infof("gt_resp %s", res)

	taskid = resp.Data.Taskid

	return
}

func (p *GTPusher) createIosTask(app, token string, data *proto.PushData) (taskid string, errorCode int, err error) {
	notification := &GTNotification{
		Title:     data.Title,
		Body:      data.Text,
		LogoUrl:   data.Icon,
		BigImage:  data.Image,
		NotifyId:  data.NotifyId,
		ShowFront: data.ShowFront,
		ShowTotal: data.ShowTotal,
	}

	if data.OpenType != nil {
		notification.ClickType = data.OpenType.Type
		switch data.OpenType.Type {
		case ClickTypeUrl:
			notification.Url = &data.OpenType.Data
		case ClickTypePayload, ClickTypePayloadCustom:
			notification.Payload = &data.OpenType.Data
		}
	}

	transmission := util.JsonStr(notification)

	bodyData := GTSingleData{
		RequestId: p.getRequestId(),
		Settings: &GTSettings{
			Ttl: 3600000,
			Strategy: &GTStrategy{
				Default: 1,
			},
		},
		PushMessage: GTPushMessage{
			Transmission: &transmission,
		},
		PushChannel: &GTPushChannel{
			Ios: &GTPushChannelIos{
				Type:    "notify",
				Payload: data.OpenType.Data,
				Aps: GTPushChannelIosAps{
					Alert: GTAlert{
						Title: data.Title,
						Body:  data.Text,
					},
				},
			},
		},
	}

	conf := p.configs[app]
	url := fmt.Sprintf("%s%s%s", conf.BaseUrl, conf.AppId, taskUrl)
	//logger.Debugf("getui task url: %s, data: %s", url, util.JsonStr(bodyData))
	headers := make(map[string]string)
	headers["token"] = token
	res, err := httpcli.PostJson(url, headers, bodyData)
	if err != nil {
		logger.Errorf("getui push error: %v, %v, %v, %v, %v", app, url, token, util.JsonStr(bodyData), err)
		return
	}

	resp := &TaskResponse{}
	err = json.Unmarshal([]byte(res), resp)
	if err != nil {
		logger.Errorf("getui task resp json error: %v", err)
		return
	}
	if resp.Code != 0 {
		logger.Errorf("getui task resp %s", res)
		return "", resp.Code, nil
	}

	taskid = resp.Data.Taskid
	return
}
