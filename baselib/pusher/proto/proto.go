package proto

type PushPlatform int
type PushOpenType string

const (
	PlatformAndroid PushPlatform = 1
	PlatformIos     PushPlatform = 2
)

func (t PushPlatform) Valid() bool {
	switch t {
	case PlatformAndroid,
		PlatformIos:
		return true
	default:
		return false
	}
}

type PushTarget struct {
	Alias string `json:"alias"`
	App   string `json:"app"`
	Av    string `json:"av"`
	Bd    string `json:"bd"`
	Ch    string `json:"ch"`
}

type PushData struct {
	Alert        string
	Icon         *string
	Title        string
	Text         string
	Image        *string
	Sound        *string
	ChannelId    *string
	ChannelName  *string
	MiChannelId  *string
	ChannelLevel int
	NotifyId     *int32
	ShowFront    bool
	ShowTotal    bool
	Message      *PushMessage
	OpenType     *OpenTypeData
}

type PushMessage struct {
	Content string
	Type    string
	Title   string
	Extras  interface{}
}

type OpenTypeData struct {
	Type  string
	Data  string
	Data2 string
}

type Pusher interface {
	Push(platform PushPlatform, targets []*PushTarget, data *PushData)
}
