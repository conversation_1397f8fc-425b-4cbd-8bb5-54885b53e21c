package pusher

import (
	"fmt"

	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/pusher/getui"
	"xim/baselib/pusher/proto"
	"xim/baselib/util"

	"github.com/go-redis/redis/v8"
)

type ProviderType int

const (
	ProviderGeTui ProviderType = 1
)

var pusher proto.Pusher
var provider ProviderType

func Init(client *redis.Client, conf *config.PusherConfig) {
	if conf == nil {
		logger.Warnf("pusher no config for initialized!")
		return
	}

	if conf.GeTui != nil {
		pusher = getui.Init(client, conf.GeTui)
	}

}

func GetUserAlias(userid int64, pid string) string {
	return util.Md5([]byte(fmt.Sprintf("%d@%s", userid, pid)))
}

func PushAndroid(targets []*proto.PushTarget, data *proto.PushData) {
	if pusher != nil {
		pusher.Push(proto.PlatformAndroid, targets, data)
	}
}

func PushIos(targets []*proto.PushTarget, data *proto.PushData) {
	if pusher != nil {
		pusher.Push(proto.PlatformIos, targets, data)
	}
}
