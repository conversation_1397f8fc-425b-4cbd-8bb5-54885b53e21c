package agoraio

const (
	// !!! 文件夹名字不能带 / - _ 等特殊字符
	OssStorageRoot     = "callonline" //录制oss 根目录
	OssStorageRootTest = "calltest"   //录制oss
	ModeIndividual     = "individual" //单流录制
	ModeMix            = "mix"        //合流录制
)

const (
	ModeCommunicationScene = 1 //通信场景
	ModeLiveScene          = 2 //直播场景
)

// 第三方云存储平台
const (
	StorageConfigVendorTypeQiniu          int32 = 0 //七牛云
	StorageConfigVendorTypeAmazon         int32 = 1 //Amazon S3
	StorageConfigVendorTypeAli            int32 = 2 //阿里云
	StorageConfigVendorTypeTencent        int32 = 3 //腾讯云
	StorageConfigVendorTypeJinshan        int32 = 4 //金山云
	StorageConfigVendorTypeMicrosoftAzure int32 = 5 //Microsoft Azure
	StorageConfigVendorTypeGoogle         int32 = 6 //谷歌云
	StorageConfigVendorTypeHaiwei         int32 = 7 //华为云
	StorageConfigVendorTypeBaidu          int32 = 8 //百度智能云
)

// 即第三方云存储为阿里云时 region
const (
	CN_Hangzhou = iota
	CN_Shanghai
	CN_Qingdao
	CN_Beijing
	CN_Zhangjiakou
	CN_Huhehaote
	CN_Shenzhen
	CN_Hongkong
	US_West_1
	US_East_1
	AP_Southeast_1
	AP_Southeast_2
	AP_Southeast_3
	AP_Southeast_5
	AP_Northeast_1
	AP_South_1
	EU_Central_1
	EU_West_1
	EU_East_1
	AP_Southeast_6
	CN_Heyuan
	CN_Guangzhou
	CN_Chengdu
)

// 订阅的媒体流类型
const (
	StreamTypesAudio        int64 = 0 //仅订阅音频
	StreamTypesVideo        int64 = 1 //仅订阅视频
	StreamTypeAudioAndVideo int64 = 2 //订阅音频和视频
)

// 云录制事件类型
type RecordEventType int32

const (
	RecordEventTypeUploaded     RecordEventType = 31 //所有录制文件已上传至指定的第三方云存储
	RecordEventTypeSnapshotFile RecordEventType = 45 //视频截图成功并上传至第三方云存储回调
)

// 频道管理事件类型
type ChannelEventType int32

func (ce ChannelEventType) IsChannelEvent() bool {
	return ce == ChannelEventDestory || ce == ChannelEventCreate
}

// 101	channel create	创建频道。
// 102	channel destroy	销毁频道。
// 103	broadcaster join channel	直播场景下，主播加入频道。
// 104	broadcaster leave channel	直播场景下，主播离开频道。
// 105	audience join channel	直播场景下，观众加入频道。
// 106	audience leave channel	直播场景下，观众离开频道。
// 107	user join channel with communication mode	通信场景下，用户加入频道。
// 108	user leave channel with communication mode	通信场景下，用户离开频道。
// 111	client role change to broadcaster	直播场景下，用户将角色切换为主播。
// 112	client role change to audience	直播场景下，用户将角色切换为观众。
const (
	ChannelEventCreate  ChannelEventType = 101
	ChannelEventDestory ChannelEventType = 102
	ChannelEventBJoin   ChannelEventType = 103
	ChannelEventBLeave  ChannelEventType = 104
	ChannelEventAJoin   ChannelEventType = 105
	ChannelEventALeave  ChannelEventType = 106
	ChannelEventUJoin   ChannelEventType = 107
	ChannelEventULeave  ChannelEventType = 108
	ChannelEventCToB    ChannelEventType = 111
	ChannelEventCToA    ChannelEventType = 112
)

func (ct ChannelEventType) ValidInRoom() bool {
	switch ct {
	case ChannelEventCreate,
		ChannelEventDestory,
		ChannelEventBJoin,
		ChannelEventBLeave,
		ChannelEventAJoin,
		ChannelEventALeave,
		ChannelEventUJoin,
		ChannelEventULeave,
		ChannelEventCToB,
		ChannelEventCToA:
		return true
	}
	return false
}

// 101-108
func (ct ChannelEventType) Valid() bool {
	switch ct {
	case ChannelEventCreate,
		ChannelEventDestory,
		ChannelEventBJoin,
		ChannelEventBLeave,
		ChannelEventAJoin,
		ChannelEventALeave,
		ChannelEventUJoin,
		ChannelEventULeave:
		return true
	}
	return false
}

// 离开原因
type LeaveReasonType int32

// 观众离开频道的原因：
// 1：观众正常离开频道。
// 2：客户端与 Agora 业务服务器连接超时。判断标准为 Agora SD-RTN 超过 10 秒未收到该观众的任何数据包。
// 3：权限问题。如被运营人员通过踢人 RESTful API 踢出频道。
// 4：Agora 业务服务器内部原因。如 Agora 业务服务器在调整负载，和客户端短暂断开连接，之后会重新连接。
// 5：观众切换新设备，迫使旧设备下线。
// 9：由于客户端有多个 IP 地址，SDK 主动与 Agora 业务服务器断开连接并重连。此过程用户无感知。请检查用户是否存在多个公网 IP 或使用了 VPN。
// 10：由于网络连接问题，例如 SDK 超过 4 秒未收到来自 Agora 业务服务器的任何数据包或 socket 连接错误，SDK 主动与 Agora 业务服务器断开连接并重连。此过程用户无感知。请检查网络连接状态。
// 999：异常用户。例如，用户短时间内频繁登录登出频道会被判定为异常用户。
// Note：你的 app 服务端需要在收到 reason 为 999 的 106 事件 60 秒后调用踢人 API 将该用户踢出频道。否则，该用户再次加入频道后，可能无法收到相关事件通知。
// 0：其他原因。
const (
	ReasonTypeOther                LeaveReasonType = 0
	ReasonTypeNormal               LeaveReasonType = 1
	ReasonTypeClientConnectTimeout LeaveReasonType = 2
	ReasonTypePermission           LeaveReasonType = 3
	ReasonTypeAgoraInternal        LeaveReasonType = 4
	ReasonTypeChangeDevice         LeaveReasonType = 5
	ReasonTypeClientMultipleIp     LeaveReasonType = 9
	ReasonTypeNetConnect           LeaveReasonType = 10
	ReasonTypeAbnormalUser         LeaveReasonType = 999
)

// 踢人
const (
	KickoffTimeInSeconds  = int32(86430)
	KickoffPrivilegeJoin  = "join_channel"
	KickoffPrivilegeAudio = "publish_audio"
	KickoffPrivilegeVideo = "publish_video"
)
