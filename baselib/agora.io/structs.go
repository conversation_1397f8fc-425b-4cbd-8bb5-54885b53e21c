package agoraio

type GenerateSimpleTokenItem struct {
	Token string `json:"token,omitempty"`
	Uuid  int32  `json:"uuid,omitempty"`
}

type GenTokenWithRecordInfo struct {
	Cname string                            `json:"cname"`
	Items map[int64]GenerateSimpleTokenItem `json:"items"`
}

type SnapshotConfigParam struct {
	CaptureInterval int      `json:"captureInterval"`
	FileType        []string `json:"fileType"`
}

type RecordingFileConfigParam struct {
	AvFileType []string `json:"avFileType"`
}

type StorageConfigParam struct {
	SecretKey      string   `json:"secretKey"`
	Region         int64    `json:"region"`
	AccessKey      string   `json:"accessKey"`
	Bucket         string   `json:"bucket"`
	Vendor         int32    `json:"vendor"`
	FileNamePrefix []string `json:"fileNamePrefix"` //支持多级目录
}

type LayoutConfigParam struct {
	Uid    string  `json:"uid"`
	XAxis  float32 `json:"x_axis"`
	YAxis  float32 `json:"y_axis"`
	Width  float32 `json:"width"`
	Height float32 `json:"height"`
}

type TranscodingConfigParam struct {
	Width   int64
	Height  int64
	Fps     int64
	Bitrate int64
}

type RecordingConfigParam struct {
	StreamTypes       int64 `json:"streamTypes"`
	ChannelType       int64 `json:"channelType"`
	MaxIdleTime       int64 `json:"maxIdleTime"`
	SubscribeUidGroup int   `json:"subscribeUidGroup"`
	VideoStreamType   int   `json:"videoStreamType"` // 0 大流录制 1小流录制
}

type ClientRequestParam struct {
	Token           string               `json:"token"`
	StorageConfig   StorageConfigParam   `json:"storageConfig"`
	RecordingConfig RecordingConfigParam `json:"recordingConfig"`
}

type AcquireClientRequestParam struct {
	Region              string `json:"region"`
	ResourceExpiredHour int32  `json:"resourceExpiredHour"`
}

type AcquireParam struct {
	Uid           string                    `json:"uid"`
	CName         string                    `json:"cname"`
	ClientRequest AcquireClientRequestParam `json:"clientRequest"`
}

type AcquireResp struct {
	ResourceId string `json:"resourceId,omitempty"`
}

type StartParam struct {
	Uid           string             `json:"uid"`
	CName         string             `json:"cname"`
	ClientRequest ClientRequestParam `json:"clientRequest"`
}

type StartResp struct {
	ResourceId string `json:"resourceId,omitempty"`
	Sid        string `json:"sid,omitempty"`
}

type ClientRequestSingleWithSnapshotParam struct {
	Token               string                   `json:"token"`
	RecordingConfig     RecordingConfigParam     `json:"recordingConfig"`
	RecordingFileConfig RecordingFileConfigParam `json:"recordingFileConfig"`
	SnapshotConfig      SnapshotConfigParam      `json:"snapshotConfig"`
	StorageConfig       StorageConfigParam       `json:"storageConfig"`
}

type StartSingleWithSnapshotParam struct {
	Uid           string                               `json:"uid"`
	CName         string                               `json:"cname"`
	ClientRequest ClientRequestSingleWithSnapshotParam `json:"clientRequest"`
}

type ClientRequestSingleParam struct {
	Token           string               `json:"token"`
	StorageConfig   StorageConfigParam   `json:"storageConfig"`
	RecordingConfig RecordingConfigParam `json:"recordingConfig"`
}

type StartSingleParam struct {
	Uid           string                   `json:"uid"`
	CName         string                   `json:"cname"`
	ClientRequest ClientRequestSingleParam `json:"clientRequest"`
}

type StopClientRequestParam struct {
	AsyncStop bool `json:"async_stop"`
}

type StopParam struct {
	Cname         string                 `json:"cname"`
	Uid           string                 `json:"uid"`
	ClientRequest StopClientRequestParam `json:"clientRequest"`
}

type StopResp struct {
	ResourceId      string `json:"resourceId"`
	Sid             string `json:"sid"`
	UploadingStatus string `json:"uploadingStatus"`
}

type ChannelNameData struct {
	ChannelExist  bool    `json:"channel_exist"`
	Mode          int     `json:"mode"`           //1：通信场景  2：直播场景
	Total         int     `json:"total"`          //频道内的用户总人数。该字段仅在通信场景 （mode 的值为 1）
	Users         []int64 `json:"users"`          //频道内所有用户的用户 ID。该字段仅在通信场景 （mode 的值为 1）
	Broadcasters  []int64 `json:"broadcasters"`   //频道内所有主播的用户 ID。该字段仅在直播场景 （mode 的值为 2）
	Audience      []int64 `json:"audience"`       //频道内观众的用户 ID 该字段仅在直播场景 （mode 的值为 2）
	AudienceTotal int64   `json:"audience_total"` //频道内的观众总人数。该字段仅在直播场景 （mode 的值为 2）
}

type ChannelNameInfoResp struct {
	Success bool            `json:"success"`
	Data    ChannelNameData `json:"data"`
}

type Channel struct {
	ChannelName string `json:"channel_name"`
	UserCount   int    `json:"user_count"`
}

type ChannelListData struct {
	Channels  []Channel `json:"channels"`
	TotalSize int       `json:"total_size"`
}

type ChannelNameListResp struct {
	Success bool            `json:"success"`
	Data    ChannelListData `json:"data"`
}

type KickoffReq struct {
	Appid         string   `json:"appid"`
	Cname         string   `json:"cname"`
	TimeInSeconds int32    `json:"time_in_seconds"`
	Privileges    []string `json:"privileges"`
}

type KickoffUidReq struct {
	Appid         string   `json:"appid"`
	Cname         string   `json:"cname"`
	Uid           int64    `json:"uid"`
	TimeInSeconds int32    `json:"time_in_seconds"`
	Privileges    []string `json:"privileges"`
}

type KickoffResp struct {
	Status string `json:"status"` //success 表示请求成功
	Id     int    `json:"id"`     //规则 ID
}
type ChannelSignCreatePayload struct {
	ChannelName string `json:"channelName"`
	Ts          int64  `json:"ts"`
}

type ChannelSignCreate struct {
	Sid       string                   `json:"sid"`
	NoticeId  string                   `json:"noticeId"`
	ProductId int64                    `json:"productId"`
	EventType int64                    `json:"eventType"`
	NotifyMs  int64                    `json:"notifyMs"`
	Payload   ChannelSignCreatePayload `json:"payload"`
}

type ChannelSignJoinPayload struct {
	Account       string `json:"account"`
	ChannelName   string `json:"channelName"`
	ClientSeq     int64  `json:"clientSeq"`
	Platform      int64  `json:"platform"`
	Ts            int64  `json:"ts"`
	Uid           int64  `json:"uid"`
	UserParameter string `json:"userParameter"`
}

type ChannelSignJoin struct {
	Sid       string                 `json:"sid"`
	NoticeId  string                 `json:"noticeId"`
	ProductId int64                  `json:"productId"`
	EventType int64                  `json:"eventType"`
	NotifyMs  int64                  `json:"notifyMs"`
	Payload   ChannelSignJoinPayload `json:"payload"`
}

type ChannelSignLeavePayload struct {
	Account       string `json:"account"`
	ChannelName   string `json:"channelName"`
	ClientSeq     int64  `json:"clientSeq"`
	Duration      int64  `json:"duration"`
	Platform      int64  `json:"platform"`
	Reason        int64  `json:"reason"`
	Ts            int64  `json:"ts"`
	Uid           int64  `json:"uid"`
	UserParameter string `json:"userParameter"`
}

type ChannelSignLeave struct {
	Sid       string                  `json:"sid"`
	NoticeId  string                  `json:"noticeId"`
	ProductId int64                   `json:"productId"`
	EventType int64                   `json:"eventType"`
	NotifyMs  int64                   `json:"notifyMs"`
	Payload   ChannelSignLeavePayload `json:"payload"`
}

type ChannelSignDestroyPayload struct {
	ChannelName string `json:"channelName"`
	Ts          int64  `json:"ts"`
}

type ChannelSignDestory struct {
	Sid       string                    `json:"sid"`
	NoticeId  string                    `json:"noticeId"`
	ProductId int64                     `json:"productId"`
	EventType int64                     `json:"eventType"`
	NotifyMs  int64                     `json:"notifyMs"`
	Payload   ChannelSignDestroyPayload `json:"payload"`
}

type RecordSignDetail struct {
	Status    int32      `json:"status"`
	MsgName   string     `json:"msgName"`
	FileLists []FileList `json:"fileList"`
}
type RecordSignPayload struct {
	ServiceType  int32            `json:"serviceType"`
	Uid          string           `json:"uid"`
	ServiceScene string           `json:"serviceScene"`
	Sendts       int64            `json:"sendts"`
	Sequence     int32            `json:"sequence"`
	Cname        string           `json:"cname"`
	Details      RecordSignDetail `json:"details"`
	Sid          string           `json:"sid"`
}

type FileList struct {
	IsPlayable     bool   `json:"isPlayable"`
	Uid            string `json:"uid"`
	FileName       string `json:"fileName"`
	SliceStartTime int64  `json:"sliceStartTime"`
	MixedAllUser   bool   `json:"mixedAllUser"`
	TrackType      string `json:"trackType"`
}

type RecordSignUploaded struct {
	NoticeId  string            `json:"noticeId"`
	NotifyMs  int64             `json:"notifyMs"`
	EventType int32             `json:"eventType"`
	Sid       string            `json:"sid"`
	Payload   RecordSignPayload `json:"payload"`
	ProductId int32             `json:"productId"`
}

type AgoraChannelCallbackReq struct {
	Sid       string              `json:"sid,omitempty"`
	NoticeId  string              `json:"noticeId,omitempty"`
	ProductId int64               `json:"productId,omitempty"`
	EventType int64               `json:"eventType,omitempty"`
	NotifyMs  int64               `json:"notifyMs,omitempty"`
	Payload   ChannelPayloadParam `json:"payload,omitempty"`
}

type ChannelPayloadParam struct {
	Account       string `json:"account,omitempty"`
	ChannelName   string `json:"channelName,omitempty"`
	ClientType    int64  `json:"clientType,omitempty"`
	ClientSeq     int64  `json:"clientSeq,omitempty"`
	Duration      int64  `json:"duration,omitempty"`
	Platform      int64  `json:"platform,omitempty"`
	Reason        int64  `json:"reason,omitempty"`
	Ts            int64  `json:"ts,omitempty"`
	Uid           int64  `json:"uid,omitempty"`
	UserParameter string `json:"userParameter,omitempty"`
}

type AgoraRecordCallbackReq struct {
	NoticeId  string             `json:"noticeId,omitempty"`
	NotifyMs  int64              `json:"notifyMs,omitempty"`
	EventType int64              `json:"eventType,omitempty"`
	Sid       string             `json:"sid,omitempty"`
	Payload   AgoraRecordPayload `json:"payload,omitempty"`
	ProductId int32              `json:"productId,omitempty"`
}

type AgoraRecordPayload struct {
	ServiceType  int32             `json:"serviceType,omitempty"`
	Uid          string            `json:"uid,omitempty"`
	ServiceScene string            `json:"serviceScene,omitempty"`
	Sendts       int64             `json:"sendts,omitempty"`
	Sequence     int32             `json:"sequence,omitempty"`
	Cname        string            `json:"cname,omitempty"`
	Details      AgoraRecordDetail `json:"details,omitempty"`
	Sid          string            `json:"sid,omitempty"`
}

type AgoraRecordDetail struct {
	Status   int32      `json:"status,omitempty"`
	MsgName  string     `json:"msgName,omitempty"`
	FileList []FileList `json:"fileList,omitempty"`
	FileName string     `json:"fileName,omitempty"`
}

type FilterFile struct {
	Type   string
	Prefix string
	Filter string
}

type AudioStreamAgoraParamShumei struct {
	AppId           string `json:"appId"`           //声网提供的appId，注意与数美的appId区分开
	Channel         string `json:"channel"`         //声网提供的频道名
	Token           string `json:"token"`           //
	Uid             int32  `json:"uid"`             //32 位无符号整数
	IsMixingEnabled bool   `json:"isMixingEnabled"` // 默认值为true true:合流 false:分流
	ChannelProfile  int    `json:"channelProfile"`  // 可选值如下（默认值为0）：0: 通信 1: 直播
}
