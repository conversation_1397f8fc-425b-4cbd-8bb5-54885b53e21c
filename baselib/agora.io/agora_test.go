package agoraio

import (
	"crypto/hmac"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"testing"
	"time"

	"xim/baselib/aliyun"
	"xim/baselib/logger"
	"xim/baselib/util"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func TestCheckChannelCallbackSign(t *testing.T) {
	header_sign := "c127c7a4703bb4f98ddf7ca622b1b6beb6402325f84bd24001054eb000821585"
	content := `{"sid":"93FC04B8717C41248B2E7C765A6C205E","noticeId":"**********:369724:108","productId":1,"eventType":108,"notifyMs":*************,"payload":{"account":"","channelName":"video-DBJH-DBK1-R7UXCC","clientSeq":*************,"duration":11,"platform":1,"reason":3,"ts":**********,"uid":6308941,"userParameter":"RXh0cmEgT3B0aW9uYWwgRGF0YQ=="}}`
	hn := hmac.New(sha256.New, []byte("0Z73FG7Ck"))
	hn.Write([]byte(content))
	dsign := hex.EncodeToString(hn.Sum(nil))
	fmt.Println(dsign)
	if header_sign == dsign {
		fmt.Println("sucess")
	}
}

func TestDemo(t *testing.T) {
	content := `{"eventMs":*************,"eventType":10,"noticeId":"4eb720f0-8da7-11e9-a43e-53f411c2761f","notifyMs":*************,"payload":{"a":"1","b":2},"productId":1}`

	// 033c62f40f687675f17f0f41f91a40c71c0f134c
	secret := "secret"
	hn := hmac.New(sha1.New, []byte(secret))
	hn.Write([]byte(content))
	sign := hex.EncodeToString(hn.Sum(nil))
	fmt.Println(sign) //
}

func TestToken(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	Init()
	token1, _ := genRecordToken(getAgoraAppid(), "123")
	token2, _ := genRecordToken(getAgoraAppid(), "123")
	fmt.Println(token1, token2)
}

// go test -v  -run TestOssSignURL
func TestOssSignURL(t *testing.T) {
	aliyun.InitOss()
	Init()
	ossClient := aliyun.GetOssClient()

	bucketName := GetVideoBucketName()
	bucket, err := ossClient.Bucket(bucketName)
	if err != nil {
		logger.Errorf("error get oss bucket:%s fail , err:%v", bucketName, err)
		return
	}

	prefix := "e56c2c46c346ac3857129e92184ebcd8_video-DBJH-DBK1-R83YMM"
	m3u8 := prefix + ".m3u8"
	url, err := bucket.SignURL(m3u8, oss.HTTPGet, 3600)
	if err != nil {
		logger.Warnf("warn oss bucket  signUrl objectKey:%s fail , err:%v", m3u8, err)
		return
	}
	fmt.Println(url)
}

func TestChannelList(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	Init()

	agora := AgoraApi{}
	resp, err := agora.GetChannelList(0, 50)
	if err != nil {
		logger.Errorf("%v", err.Error())
		return
	}
	logger.Infof("%v", util.JsonStr(resp))

}

func TestGenChannelName(t *testing.T) {
	cname := GenCname(2, 622015, 622049, time.Now().Unix())
	fmt.Println(cname)
}

func TestBasic(t *testing.T) {
	v := AGORA_API_AUTHORIZATION
	fmt.Println(v)
}
