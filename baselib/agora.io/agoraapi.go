package agoraio

import (
	"encoding/json"
	"fmt"

	"net/url"

	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/baselib/util"

	"github.com/pkg/errors"
)

var (
	AGORA_APP_HOST = "http://api.agora.io"
	// 录制
	AGORA_RECORD_SERVICE_CATEGORY = "v1/apps"
	AGORA_RECORD_ACQUIRE_MODEL    = "cloud_recording/acquire"
	AGORA_RECORD_START_MODEL      = "cloud_recording/resourceid/%s/mode/%s/start"
	AGORA_RECORD_STOP_MODEL       = "cloud_recording/resourceid/%s/sid/%s/mode/%s/stop"
	// 频道管理
	AGORA_CHANNEL_SERVICE_CATEGORY = "dev/v1"
	AGORA_CHANNEL_USER_LIST_MODEL  = "channel/user/%s/%s"
	AGORA_CHANNEL_KICK_RULE        = "kicking-rule"
	AGORA_CHANNEL_LIST             = "channel/%s"
)

type AgoraApi struct {
}

func NewAgoraApi() *AgoraApi {
	return &AgoraApi{}
}

func (aa *AgoraApi) buildUri(category, model, action string) string {
	uri := ""
	if category != "" {
		uri = uri + "/" + category
	}
	if model != "" {
		uri = uri + "/" + model
	}
	if action != "" {
		uri = uri + "/" + action
	}
	return uri
}

func (aa *AgoraApi) Acquire(appid string, param *AcquireParam) (resp *AcquireResp, err error) {
	url := AGORA_APP_HOST + aa.buildUri(AGORA_RECORD_SERVICE_CATEGORY, appid, AGORA_RECORD_ACQUIRE_MODEL)

	jsonStr, err := util.Marshal(param)
	if err != nil {
		errors.Wrapf(err, "marshar data:%#v", param)
		return
	}

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": agoraApiAuthorization,
	}

	data, err := Post(url, jsonStr, header, true)
	if err != nil {
		err = errors.Wrapf(err, "acquire, url:%v,header:%v", url, header)
		return
	}
	//logger.Infof("==========  acquire req:%v,resp:%v", string(jsonStr), string(data))
	resp = &AcquireResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) Start(appid string, param *StartParam, resourceId, mode string) (resp *StartResp, err error) {
	action := fmt.Sprintf(AGORA_RECORD_START_MODEL, resourceId, mode)
	url := AGORA_APP_HOST + aa.buildUri(AGORA_RECORD_SERVICE_CATEGORY, appid, action)

	jsonStr, err := util.Marshal(param)
	if err != nil {
		errors.Wrapf(err, "marshar data:%#v", param)
		return
	}

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": agoraApiAuthorization,
	}

	data, _err := Post(url, jsonStr, header, true)
	if _err != nil {
		err = errors.Wrapf(_err, "start, url:%v,header:%v", url, header)
		return
	}
	//logger.Infof("==========  start req:%v,resp:%v", string(jsonStr), string(data))
	resp = &StartResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) StartWithByte(appid string, jsonStr []byte, resourceId, mode string) (resp *StartResp, err error) {
	action := fmt.Sprintf(AGORA_RECORD_START_MODEL, resourceId, mode)
	url := AGORA_APP_HOST + aa.buildUri(AGORA_RECORD_SERVICE_CATEGORY, appid, action)

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": agoraApiAuthorization,
	}

	data, _err := Post(url, jsonStr, header, true)
	if _err != nil {
		err = errors.Wrapf(_err, "start, url:%v,header:%v", url, header)
		return
	}
	//logger.Infof("==========  start req:%v,resp:%v", string(jsonStr), string(data))
	resp = &StartResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) Stop(appid string, param *StopParam, resourceId, sid, mode string) (resp *StopResp, err error) {
	action := fmt.Sprintf(AGORA_RECORD_STOP_MODEL, resourceId, sid, mode)
	if len(appid) == 0 {
		appid = getAgoraAppid()
	}
	url := AGORA_APP_HOST + aa.buildUri(AGORA_RECORD_SERVICE_CATEGORY, appid, action)
	jsonStr, err := util.Marshal(param)
	if err != nil {
		errors.Wrapf(err, "marshar data:%#v", param)
		return
	}

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": agoraApiAuthorization,
	}
	data, _err := Post(url, jsonStr, header, true)
	if _err != nil {
		err = errors.Wrapf(_err, "stop, url:%v,header:%v,body:%v", url, header, string(jsonStr))
		return
	}

	resp = &StopResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) GetChannelNameInfo(channelName string) (resp *ChannelNameInfoResp, err error) {
	appid := getAgoraAppid()
	action := fmt.Sprintf(AGORA_CHANNEL_USER_LIST_MODEL, appid, channelName)
	domain := AGORA_APP_HOST + aa.buildUri(AGORA_CHANNEL_SERVICE_CATEGORY, "", action)

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Authorization": agoraApiAuthorization,
	}
	data, _err := Get(domain, header, nil, true)
	if _err != nil {
		err = _err
		return
	}
	resp = &ChannelNameInfoResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) GetChannelList(pageNo, pageSize int) (resp *ChannelNameListResp, err error) {
	appid := getAgoraAppid()
	action := fmt.Sprintf(AGORA_CHANNEL_LIST, appid)
	domain := AGORA_APP_HOST + aa.buildUri(AGORA_CHANNEL_SERVICE_CATEGORY, "", action)
	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Authorization": agoraApiAuthorization,
	}
	data, _err := Get(domain, header, url.Values{
		"page_no":   []string{fmt.Sprintf("%d", pageNo)},
		"page_size": []string{fmt.Sprintf("%d", pageSize)},
	}, true)
	if _err != nil {
		err = _err
		return
	}
	resp = &ChannelNameListResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		logger.Errorf("get channel list err:%v,resp:%v", err.Error(), util.JsonStr(resp))
		errors.Wrapf(err, "unmarshar data:%v", string(data))
		return
	}
	return
}

func (aa *AgoraApi) Kickoff(cname string, timeInSeconds int32, privileges []string, appid string) (resp *KickoffResp, err error) {
	domain := "http://api.sd-rtn.com" /* AGORA_APP_HOST*/ + aa.buildUri(AGORA_CHANNEL_SERVICE_CATEGORY, "", AGORA_CHANNEL_KICK_RULE)

	if len(appid) == 0 {
		if env.IsProd() {
			appid = oldAppid
		} else {
			appid = getAgoraAppid()
		}
	}

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": agoraApiAuthorization,
	}

	param := &KickoffReq{
		Appid:         appid,
		Cname:         cname,
		TimeInSeconds: timeInSeconds,
		Privileges:    privileges,
	}

	jsonStr, err := util.Marshal(param)
	if err != nil {
		errors.Wrapf(err, "marshar data:%#v", param)
		return
	}

	data, _err := Post(domain, jsonStr, header, false)
	if _err != nil {
		err = _err
		return
	}
	resp = &KickoffResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) KickoffUid(cname string, timeInSeconds int32, privileges []string, appid string, userid int64) (resp *KickoffResp, err error) {
	domain := "http://api.sd-rtn.com" /* AGORA_APP_HOST*/ + aa.buildUri(AGORA_CHANNEL_SERVICE_CATEGORY, "", AGORA_CHANNEL_KICK_RULE)

	if len(appid) == 0 {
		if env.IsProd() {
			appid = oldAppid
		} else {
			appid = getAgoraAppid()
		}
	}

	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	header := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": agoraApiAuthorization,
	}

	param := &KickoffUidReq{
		Appid:         appid,
		Cname:         cname,
		TimeInSeconds: timeInSeconds,
		Privileges:    privileges,
		Uid:           UseridToUid(userid),
	}

	jsonStr, err := util.Marshal(param)
	if err != nil {
		errors.Wrapf(err, "marshar data:%#v", param)
		return
	}

	data, _err := Post(domain, jsonStr, header, false)
	if _err != nil {
		err = _err
		return
	}
	resp = &KickoffResp{}
	err = json.Unmarshal(data, resp)
	if err != nil {
		errors.Wrapf(err, "unmarshar data:%v ", string(data))
		return
	}
	return
}

func (aa *AgoraApi) GetCnameReqInfo(cname string, appid string) (url string, headers map[string]string) {
	if len(appid) == 0 {
		if env.IsProd() {
			appid = oldAppid //兼容迁移声网  没有给老的平台
		} else {
			appid = getAgoraAppid()
		}
	}
	agoraApiAuthorization := agoraConfigMap[appid].agoraApiAuthorization
	action := fmt.Sprintf(AGORA_CHANNEL_USER_LIST_MODEL, appid, cname)
	url = AGORA_APP_HOST + aa.buildUri(AGORA_CHANNEL_SERVICE_CATEGORY, "", action)
	headers = map[string]string{
		"Authorization": agoraApiAuthorization,
	}
	return
}
