package agoraio

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"

	"xim/baselib/agora.io/AccessToken"
	"xim/baselib/agora.io/SimpleTokenBuilder"
	"xim/baselib/aliyun"
	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/baselib/util"
	"xim/proto/consts/config"
	mproto "xim/proto/model/chat"

	"github.com/pkg/errors"
	"google.golang.org/grpc/metadata"
)

var (
	agoraConfigMap map[string]AgoraConfig // 迁移使用
	switchTime     = int64(1693905600)    // todo 修改升级时间 声网升级时间
	oldAppid       = "********************************"
	newAppid       = "********************************" // todo 换成新的
)

func UpdateAgoraNeedKickLogin(at int64) bool {
	return at <= switchTime && canSwitchAgora()
}

func canSwitchAgora() bool {
	return time.Now().Unix() > switchTime
}

// // 老的 online
// var (
// 	// api secret 一个账号对应一个
// 	agoraResultfulKey    = "********************************"
// 	agoraResultfulSecret = "********************************"

// 	// token secret
// 	agoraAppidOnline   = "********************************"
// 	agoraAppcertOnline = "********************************"
// )

// 牵寻新的  已验证
var (
	//api secret 一个账号对应一个 new
	agoraResultfulKey    = "********************************"
	agoraResultfulSecret = "********************************"

	// token secret
	agoraAppidOnline   = "********************************"
	agoraAppcertOnline = "********************************"
)

// test
const (
	agoraResultfulKeyTest    = "********************************"
	agoraResultfulSecretTest = "********************************"

	// // token secret test
	agoraAppidTest   = "********************************"
	agoraAppcertTest = "********************************"
	// 回调secret test
	agoraRecordSecretTest  = "omx3-DTE0"
	agoraChannelSecretTest = "3Fvqoop5c0"

	// 回调 online secret
	agoraRecordSecretOnline  = "5dYG8hzuF"
	agoraChannelSecretOnline = "-pXD3ow5CA"
)

var (
	AGORA_RECORDER                   = uint32(8899) // 云录制账号
	AGORA_SHUMEI                     = uint32(8898) // 数美
	AGORA_CALL_TIMEOUT               = int32(60)
	AGORA_CALL_TIMEOUT2              = int32(30)
	AGORA_CALL_TIMEOUT_EXT           = AGORA_CALL_TIMEOUT + 5
	AGORA_AUTHORIZATION              = ""
	AGORA_API_AUTHORIZATION          = ""
	AGORA_CHECK_ONLINE_TIME_INTERVAL = int64(10)
	ERROR_BAD_HEADER_PARAM           = errors.New("bad header param")
)

const (
	RenewTokenDuration uint32 = 3600 * 24
	UuidOffset         uint32 = 5687456
	AdjustBigData      int64  = 29264000000
	VoiceRoomBigData   int64  = 28000000000
)

func init() {
	var plainCredentialsResultful string
	if env.IsProd() {
		plainCredentialsResultful = agoraResultfulKey + ":" + agoraResultfulSecret
	} else {
		plainCredentialsResultful = agoraResultfulKeyTest + ":" + agoraResultfulSecretTest
	}
	fmt.Printf("agoraResultfulSecret %v", plainCredentialsResultful)

	AGORA_API_AUTHORIZATION = "Basic " + base64.StdEncoding.EncodeToString([]byte(plainCredentialsResultful))
}

func Init() {
	agoraConfigMap = make(map[string]AgoraConfig)
	if env.IsProd() {
		agoraConfig.agoraAppid = agoraAppidOnline
		agoraConfig.agoraAppcert = agoraAppcertOnline
		agoraConfig.agoraRecordSecret = agoraRecordSecretOnline
		agoraConfig.agoraChannelSecret = agoraChannelSecretOnline
		agoraConfig.storageRoot = OssStorageRoot
		agoraConfig.agoraApiAuthorization = AGORA_API_AUTHORIZATION
		agoraConfigMap[agoraConfig.agoraAppid] = agoraConfig
		initOld()
		logger.Infof("load prod env agora config success")
	} else {
		agoraConfig.agoraAppid = agoraAppidTest
		agoraConfig.agoraAppcert = agoraAppcertTest
		agoraConfig.agoraRecordSecret = agoraRecordSecretTest
		agoraConfig.agoraChannelSecret = agoraChannelSecretTest
		agoraConfig.storageRoot = OssStorageRootTest
		agoraConfig.agoraApiAuthorization = AGORA_API_AUTHORIZATION
		agoraConfigMap[agoraConfig.agoraAppid] = agoraConfig
		logger.Infof("load test env agora config success")
	}

	plainCredentials := getAgoraAppid() + ":" + getAgoraAppcert()
	AGORA_AUTHORIZATION = "Basic " + base64.StdEncoding.EncodeToString([]byte(plainCredentials))
}

func initOld() {
	if agoraConfigMap == nil {
		agoraConfigMap = make(map[string]AgoraConfig)
	}
	old := AgoraConfig{
		agoraAppid:         oldAppid,
		agoraAppcert:       "********************************",
		storageRoot:        OssStorageRoot,
		agoraRecordSecret:  "DlE9QhIXv",
		agoraChannelSecret: "eIOf2vB_K",
	}
	plainCredentialsResultful := "********************************" + ":" + "********************************"
	old.agoraApiAuthorization = "Basic " + base64.StdEncoding.EncodeToString([]byte(plainCredentialsResultful))
	agoraConfigMap[old.agoraAppid] = old
}

func InitTest() {
	agoraConfig.agoraAppid = agoraAppidTest
	agoraConfig.agoraAppcert = agoraAppcertTest
	agoraConfig.agoraRecordSecret = agoraRecordSecretTest
	agoraConfig.agoraChannelSecret = agoraChannelSecretTest
	agoraConfig.storageRoot = OssStorageRootTest
	plainCredentials := getAgoraAppid() + ":" + getAgoraAppcert()
	AGORA_AUTHORIZATION = "Basic " + base64.StdEncoding.EncodeToString([]byte(plainCredentials))
	logger.Infof("load prod env agora config success")
}

func InitOnline() {
	agoraConfig.agoraAppid = agoraAppidOnline
	agoraConfig.agoraAppcert = agoraAppcertOnline
	agoraConfig.agoraRecordSecret = agoraRecordSecretOnline
	agoraConfig.agoraChannelSecret = agoraChannelSecretOnline
	agoraConfig.storageRoot = OssStorageRoot
	plainCredentials := getAgoraAppid() + ":" + getAgoraAppcert()
	AGORA_AUTHORIZATION = "Basic " + base64.StdEncoding.EncodeToString([]byte(plainCredentials))
	logger.Infof("load prod env agora config success")
}

type AgoraConfig struct {
	agoraAppid            string //token
	agoraAppcert          string
	agoraRecordSecret     string //callback
	agoraChannelSecret    string
	storageRoot           string //cloud record root dir
	agoraApiAuthorization string
}

var agoraConfig = AgoraConfig{}

func GetVideoBucketName() string {
	// if env.IsProd() {  // 回放的线上也放test
	// 	return aliyun.GetOssConfig().VideoBucket
	// }
	return aliyun.GetOssConfig().TestVideoBucket
}

func GetVoiceBucketName() string {
	// if env.IsProd() {
	// 	return aliyun.GetOssConfig().AudioBucket
	// }
	return aliyun.GetOssConfig().TestAudioBucket
}

func GetAgoraChannelSecret() string {
	return agoraConfig.agoraChannelSecret
}

func GetAgoraRecordSecret() string {
	return agoraConfig.agoraRecordSecret
}

func GetStorageRoot() string {
	return agoraConfig.storageRoot
}

func getAgoraAppid() string {
	if !env.IsProd() {
		return agoraConfig.agoraAppid
	}
	appid := oldAppid
	if canSwitchAgora() {
		appid = newAppid
	}
	return appid
}

func getAgoraAppcert() string {
	if !env.IsProd() {
		return agoraConfig.agoraAppcert
	}
	appid := oldAppid
	if canSwitchAgora() {
		appid = newAppid
	}
	return agoraConfigMap[appid].agoraAppcert
}

func GetAgoraAppid() string {
	return getAgoraAppid()
}

// !!! 云录制 同一个事件的回调 声网body的key是无序的,应该层这边获取不到原生body 对录制上传完成不做签名校验
func CheckCallbackSign(ctx context.Context, content []byte, secret string) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return ERROR_BAD_HEADER_PARAM
	}
	sign, ok := md["agora-signature-v2"]
	if !ok {
		return ERROR_BAD_HEADER_PARAM
	}
	hn := hmac.New(sha256.New, []byte(secret))
	hn.Write(content)
	dsign := hex.EncodeToString(hn.Sum(nil))
	if sign[0] == dsign {
		return nil
	}
	logger.Errorf(" checkCallbackSign hsign:%v,sign:%v, content:%v", sign[0], dsign, string(content))
	return ERROR_BAD_HEADER_PARAM
}

func CheckCallbackSignGin(sign string, content []byte, secret string) error {
	hn := hmac.New(sha256.New, []byte(secret))
	hn.Write(content)
	dsign := hex.EncodeToString(hn.Sum(nil))
	if sign == dsign {
		return nil
	}
	// logger.Errorf(" checkCallbackSignGin hsign:%v,sign:%v, content:%v", sign, dsign, string(content))
	return ERROR_BAD_HEADER_PARAM
}

func GenCname(callType mproto.CallType, smallId, bigId, ts int64) string {
	if smallId > bigId {
		smallId = smallId ^ bigId
		bigId = smallId ^ bigId
		smallId = smallId ^ bigId
	}
	prefix := callType.GetDesc()
	ids := []int64{smallId, bigId}
	var buffer bytes.Buffer
	buffer.WriteString(prefix)
	for _, id := range ids {
		buffer.WriteString(util.IntToCode(id))
	}
	buffer.WriteString(util.IntToCode(ts))
	return buffer.String()
}

// 语音房使用
func GetTokenForChannelUid(channel string, userid int64) (token string, err error) {
	builder := SimpleTokenBuilder.CreateSimpleTokenBuilder(getAgoraAppid(), getAgoraAppcert(), channel, uint32(VoiceRoomUseridToUid(userid)))
	builder.InitPrivileges(SimpleTokenBuilder.Role_Attendee)
	expireTs := uint32(time.Now().Unix()) + RenewTokenDuration
	builder.SetPrivilege(AccessToken.KJoinChannel, expireTs)        //join
	builder.SetPrivilege(AccessToken.KPublishAudioStream, expireTs) //audio
	//builder.SetPrivilege(AccessToken.KPublishVideoStream, expireTs) //video
	token, err = builder.BuildToken()
	if err != nil {
		logger.Errorf("error %v", err)
	}
	return
}

func genToken(callType mproto.CallType, myId, uid, ts int64) (cname string, items map[int64]GenerateSimpleTokenItem, err error) {
	cname = GenCname(callType, myId, uid, ts)
	logger.Infof("genToken cname:%v", cname)
	expireTs := uint32(time.Now().Unix()) + RenewTokenDuration
	items = make(map[int64]GenerateSimpleTokenItem, 2)
	appid := getAgoraAppid()
	agoraAppcert := agoraConfigMap[appid].agoraAppcert
	mids := []int64{myId, uid}
	for _, mid := range mids {
		uuid := UseridToUid(int64(mid))
		builder := SimpleTokenBuilder.CreateSimpleTokenBuilder(appid, agoraAppcert, cname, uint32(uuid))
		builder.InitPrivileges(SimpleTokenBuilder.Role_Attendee)
		builder.SetPrivilege(AccessToken.KJoinChannel, expireTs)        //join
		builder.SetPrivilege(AccessToken.KPublishAudioStream, expireTs) //audio
		builder.SetPrivilege(AccessToken.KPublishVideoStream, expireTs) //video
		token, _err := builder.BuildToken()
		if _err != nil {
			err = _err
			errors.Wrapf(err, "BuildToken mid: %v, mids: %v", mid, mids)
			return
		}
		items[mid] = GenerateSimpleTokenItem{
			Token: token,
			Uuid:  int32(uuid),
		}
	}
	return
}

func getTokenByCname(cname string, myId, uid, ts int64) (appid string, items map[int64]GenerateSimpleTokenItem, err error) {
	expireTs := uint32(time.Now().Unix()) + RenewTokenDuration
	items = make(map[int64]GenerateSimpleTokenItem, 2)
	mids := []int64{myId, uid}
	appid = getAgoraAppid()
	agoraAppcert := agoraConfigMap[appid].agoraAppcert
	for _, mid := range mids {
		uuid := UseridToUid(int64(mid))
		builder := SimpleTokenBuilder.CreateSimpleTokenBuilder(appid, agoraAppcert, cname, uint32(uuid))
		builder.InitPrivileges(SimpleTokenBuilder.Role_Attendee)
		builder.SetPrivilege(AccessToken.KJoinChannel, expireTs)        //join
		builder.SetPrivilege(AccessToken.KPublishAudioStream, expireTs) //audio
		builder.SetPrivilege(AccessToken.KPublishVideoStream, expireTs) //video
		token, _err := builder.BuildToken()
		if _err != nil {
			err = _err
			errors.Wrapf(err, "BuildToken mid: %v, mids: %v", mid, mids)
			return
		}
		items[mid] = GenerateSimpleTokenItem{
			Token: token,
			Uuid:  int32(uuid),
		}
	}
	return
}

func genRecordToken(appid, cname string) (token string, err error) {
	// get record token
	uuid := UseridToUid(int64(AGORA_RECORDER))
	expireTs := uint32(time.Now().Unix()) + RenewTokenDuration
	agoraAppcert := agoraConfigMap[appid].agoraAppcert
	builder := SimpleTokenBuilder.CreateSimpleTokenBuilder(appid, agoraAppcert, cname, uint32(uuid))
	builder.InitPrivileges(SimpleTokenBuilder.Role_Attendee)
	builder.SetPrivilege(AccessToken.KJoinChannel, expireTs)
	builder.SetPrivilege(AccessToken.KInvitePublishAudioStream, expireTs)
	builder.SetPrivilege(AccessToken.KInvitePublishVideoStream, expireTs)
	token, err = builder.BuildToken()
	if err != nil {
		errors.Wrap(err, "recorder BuildToken")
	}
	return
}

func GetRecordUid() int64 {
	return int64(AGORA_RECORDER) + int64(UuidOffset)
}

func UseridToUid(mid int64) int64 {
	if mid > AdjustBigData {
		mid = mid - AdjustBigData
	}
	return mid + int64(UuidOffset)
}

func UidToUserid(uid int64) int64 {
	return uid - int64(UuidOffset)
}

// 兼容大数userid
func UidToUseridCompatibility(uid int64, smallId int64, bigId int64) (userid int64) {
	userid = UidToUserid(uid)
	if userid == smallId || userid == bigId {
		return
	}
	adjustBigUserid := userid + AdjustBigData
	if adjustBigUserid == smallId || adjustBigUserid == bigId {
		return adjustBigUserid
	}
	return userid
}

// 语音房
func VoiceRoomUseridToUid(userid int64) (uid int64) {
	if userid >= VoiceRoomBigData {
		userid = userid - VoiceRoomBigData
	}
	return UseridToUid(userid)
}

// 语音房
func VoiceRoomUidToUserid(uid int64) int64 {
	userid := UidToUserid(uid)
	if userid >= int64(1264500001+UuidOffset) {
		userid += VoiceRoomBigData
	}
	return userid
}

func GenToken(callType mproto.CallType, myId, uid, ts int64) (info *GenTokenWithRecordInfo, err error) {
	cname, items, err := genToken(callType, myId, uid, ts)
	if err != nil {
		return
	}
	info = &GenTokenWithRecordInfo{}
	info.Cname = cname
	info.Items = items
	return
}

func GenTokensByCname(cname string, myId, uid, ts int64) (appid string, info *GenTokenWithRecordInfo, err error) {
	id, items, err := getTokenByCname(cname, myId, uid, ts)
	if err != nil {
		return
	}
	return id, &GenTokenWithRecordInfo{Items: items, Cname: cname}, nil
}

func GenTokenByCname(cname string, ts, mid int64) (info *GenTokenWithRecordInfo, err error) {
	expireTs := uint32(time.Now().Unix()) + RenewTokenDuration
	uuid := UseridToUid(int64(mid))
	appid := getAgoraAppid()
	agoraAppcert := agoraConfigMap[appid].agoraAppcert
	builder := SimpleTokenBuilder.CreateSimpleTokenBuilder(appid, agoraAppcert, cname, uint32(uuid))
	builder.InitPrivileges(SimpleTokenBuilder.Role_Attendee)
	builder.SetPrivilege(AccessToken.KJoinChannel, expireTs)        //join
	builder.SetPrivilege(AccessToken.KPublishAudioStream, expireTs) //audio
	builder.SetPrivilege(AccessToken.KPublishVideoStream, expireTs) //video
	token, _err := builder.BuildToken()
	if _err != nil {
		err = _err
		errors.Wrapf(err, "BuildToken mid: %v", mid)
		return
	}
	info = &GenTokenWithRecordInfo{}
	items := make(map[int64]GenerateSimpleTokenItem, 1)
	items[mid] = GenerateSimpleTokenItem{Token: token, Uuid: int32(uuid)}
	info.Items = items
	info.Cname = cname
	return
}

func GetFilterFile(typ string, userid1, userid2 int64, sid string, cname string) (list []FilterFile) {
	if typ == "voice" {
		prefix := fmt.Sprintf("%s/%s/%s/%s", GetStorageRoot(), typ, cname, sid+"_"+cname)
		filte := FilterFile{
			Type:   "voice",
			Prefix: prefix,
			Filter: prefix + ".m3u8",
		}
		list = append(list, filte)
	} else if typ == "video" {
		uuid1 := fmt.Sprintf("%d", UseridToUid(userid1))
		uuid2 := fmt.Sprintf("%d", UseridToUid(userid2))
		prefix := fmt.Sprintf("%s/%s/%s/%s", GetStorageRoot(), typ, cname, sid+"_"+cname+"__uid_s_")
		prefix1 := prefix + uuid1 + "__uid_e_"
		prefix2 := prefix + uuid2 + "__uid_e_"
		list = append(list, FilterFile{
			Type:   fmt.Sprintf("%d-voice", userid1),
			Prefix: prefix1 + "audio",
			Filter: prefix1 + "audio.m3u8",
		})

		list = append(list, FilterFile{
			Type:   fmt.Sprintf("%d-video", userid1),
			Prefix: prefix1 + "video",
			Filter: prefix1 + "video.m3u8",
		})

		list = append(list, FilterFile{
			Type:   fmt.Sprintf("%d-voice", userid2),
			Prefix: prefix2 + "audio",
			Filter: prefix2 + "audio.m3u8",
		})

		list = append(list, FilterFile{
			Type:   fmt.Sprintf("%d-video", userid2),
			Prefix: prefix2 + "video",
			Filter: prefix2 + "video.m3u8",
		})
		return
	}
	return
}

func GetJpgPrefix(userid int64, sid, cname string) string {
	typ := "video"
	uuid := fmt.Sprintf("%d", UseridToUid(userid))
	return fmt.Sprintf("%s/%s/%s/%s%s%s", GetStorageRoot(), typ, cname, sid+"_"+cname+"__uid_s_", uuid, "__uid_e_video_")
}

func GetVideoM3u8File(userid int64, sid, cname string) string {
	typ := "video"
	uuid := fmt.Sprintf("%d", UseridToUid(userid))
	return fmt.Sprintf("%s/%s/%s/%s%s%s", GetStorageRoot(), typ, cname, sid+"_"+cname+"__uid_s_", uuid, "__uid_e_video.m3u8")
}

func GetVideoAudioM3u8File(userid int64, sid, cname string) string {
	typ := "video"
	uuid := fmt.Sprintf("%d", UseridToUid(userid))
	return fmt.Sprintf("%s/%s/%s/%s%s%s", GetStorageRoot(), typ, cname, sid+"_"+cname+"__uid_s_", uuid, "__uid_e_audio.m3u8")
}

func GetAudioM3u8File(sid, cname string) string {
	return fmt.Sprintf("%s/%s/%s/%s_%s.m3u8", GetStorageRoot(), "voice", cname, sid, cname)
}

// 音频合流录制
func AgoraSoundCloudAudio(appid, cname string, callType mproto.CallType) (resourceId, sid string, err error) {
	if AGORA_RECORDER <= 0 {
		return
	}
	if callType == mproto.CallTypeVideo {
		return
	}
	token, err := genRecordToken(appid, cname)
	if err != nil {
		logger.Errorf("acquire_sound_cloud token err:%v cname:%v", err, cname)
		errors.Wrapf(err, "agora genRecordToken")
		return
	}
	uid := fmt.Sprintf("%d", UseridToUid(int64(AGORA_RECORDER)))
	agora := NewAgoraApi()
	acquireParam := &AcquireParam{
		Uid:   uid,
		CName: cname,
		ClientRequest: AcquireClientRequestParam{
			Region:              "CN",
			ResourceExpiredHour: 24,
		},
	}
	resp, err := agora.Acquire(appid, acquireParam)
	if err != nil {
		logger.Errorf("acquire_sound_cloud acquire err:%v req:%v", err, util.JsonStr(acquireParam))
		errors.Wrapf(err, "agora acquire req:%#v", acquireParam)
		return
	}
	if len(resp.ResourceId) == 0 {
		logger.Errorf("acquire_sound_cloud resourceid is empty req:%v", util.JsonStr(acquireParam))
		errors.Wrapf(err, "agora acquire get resourceid is empty req:%#v", acquireParam)
		return
	}

	streamTypes := StreamTypesAudio
	bucket := GetVoiceBucketName()
	if callType == mproto.CallTypeVideo {
		streamTypes = StreamTypeAudioAndVideo
		bucket = GetVideoBucketName()
	}

	storageConfig := StorageConfigParam{
		SecretKey: config.AgoraCloudAccessKeySecret,
		Region:    CN_Shanghai,
		AccessKey: config.AgoraCloudAccessKeyId,
		Bucket:    bucket,
		Vendor:    StorageConfigVendorTypeAli,
		FileNamePrefix: []string{
			GetStorageRoot(),
			callType.GetDesc(),
			cname,
		},
	}
	recordingConfig := RecordingConfigParam{
		StreamTypes:     streamTypes,
		MaxIdleTime:     30,
		VideoStreamType: 1,
	}
	startParam := &StartParam{
		Uid:   uid,
		CName: cname,
		ClientRequest: ClientRequestParam{
			Token:           token,
			StorageConfig:   storageConfig,
			RecordingConfig: recordingConfig,
		},
	}

	startResp, err := agora.Start(appid, startParam, resp.ResourceId, ModeMix) //合流录制
	if err != nil {
		logger.Errorf("acquire_sound_cloud start err:%v, req:%v", err, util.JsonStr(startParam))
		errors.Wrapf(err, "agora start req:%#v,resourceId:%v", startParam, resp.ResourceId)
		return
	}
	if startResp.ResourceId != resp.ResourceId {
		logger.Errorf("acquire_sound_cloud start  resource diff")
		errors.Wrapf(err, "agora start resourceId diff respid:%v reqid:%v,req:%#v", startResp.ResourceId, resp.ResourceId, startParam)
		return
	}
	if len(startResp.Sid) == 0 {
		logger.Errorf("acquire_sound_cloud start sid is empty")
		errors.Wrapf(err, "agora start resp sid is empty,req:%#v", startParam)
		return
	}
	return resp.ResourceId, startResp.Sid, nil
}

// 单流录制+截图
func AgoraSoundCloudSingleWithSnapshot(appid, cname string, callType mproto.CallType) (resourceId, sid string, err error) {
	if AGORA_RECORDER <= 0 {
		return
	}
	if callType == mproto.CallTypeVoice {
		return
	}
	token, err := genRecordToken(appid, cname)
	if err != nil {
		logger.Errorf("acquire_sound_cloud_video token err:%v cname:%v", err, cname)
		errors.Wrapf(err, "agora genRecordToken")
		return
	}
	uid := fmt.Sprintf("%d", UseridToUid(int64(AGORA_RECORDER)))
	agora := NewAgoraApi()
	acquireParam := &AcquireParam{
		Uid:   uid,
		CName: cname,
		ClientRequest: AcquireClientRequestParam{
			Region:              "CN",
			ResourceExpiredHour: 24,
		},
	}
	resp, err := agora.Acquire(appid, acquireParam)
	if err != nil {
		logger.Errorf("acquire_sound_cloud_video acquire err:%v req:%v", err, util.JsonStr(acquireParam))
		errors.Wrapf(err, "agora acquire req:%#v", acquireParam)
		return
	} else {
		logger.Infof("video cname:%v acquirereq: %v, resp:%v ", cname, util.JsonStr(acquireParam), util.JsonStr(resp))
	}
	if len(resp.ResourceId) == 0 {
		logger.Errorf("acquire_sound_cloud_video resourceid is empty req:%v", util.JsonStr(acquireParam))
		errors.Wrapf(err, "agora acquire get resourceid is empty req:%#v", acquireParam)
		return
	}

	streamTypes := StreamTypesAudio
	bucket := GetVoiceBucketName()
	if callType == mproto.CallTypeVideo {
		streamTypes = StreamTypeAudioAndVideo
		bucket = GetVideoBucketName()
	}

	storageConfig := StorageConfigParam{
		SecretKey: config.AgoraCloudAccessKeySecret,
		Region:    CN_Shanghai,
		AccessKey: config.AgoraCloudAccessKeyId,
		Bucket:    bucket,
		Vendor:    StorageConfigVendorTypeAli,
		FileNamePrefix: []string{
			GetStorageRoot(),
			callType.GetDesc(),
			cname,
		},
	}
	recordingConfig := RecordingConfigParam{
		StreamTypes:       streamTypes,
		MaxIdleTime:       30,
		SubscribeUidGroup: 0,
		VideoStreamType:   1,
	}
	startParam := &StartSingleWithSnapshotParam{
		Uid:   uid,
		CName: cname,
		ClientRequest: ClientRequestSingleWithSnapshotParam{
			Token:         token,
			StorageConfig: storageConfig,
			SnapshotConfig: SnapshotConfigParam{
				CaptureInterval: 5,
				FileType: []string{
					"jpg",
				},
			},
			RecordingFileConfig: RecordingFileConfigParam{
				AvFileType: []string{
					"hls",
				},
			},
			RecordingConfig: recordingConfig,
		},
	}

	byt, err := util.Marshal(startParam)
	if err != nil {
		return
	}

	startResp, err := agora.StartWithByte(appid, byt, resp.ResourceId, ModeIndividual) //录制
	if err != nil {
		logger.Errorf("acquire_sound_cloud_video start err:%v,acquireReq:%v, startreq:%v", err, util.JsonStr(acquireParam), util.JsonStr(startParam))
		errors.Wrapf(err, "agora start req:%#v,resourceId:%v", startParam, resp.ResourceId)
		return
	}
	if startResp.ResourceId != resp.ResourceId {
		logger.Errorf("acquire_sound_cloud_video start  resource diff")
		errors.Wrapf(err, "agora start resourceId diff respid:%v reqid:%v,req:%#v", startResp.ResourceId, resp.ResourceId, startParam)
		return
	}
	if len(startResp.Sid) == 0 {
		logger.Errorf("acquire_sound_cloud_video start sid is empty")
		errors.Wrapf(err, "agora start resp sid is empty,req:%#v", startParam)
		return
	}
	return resp.ResourceId, startResp.Sid, nil
}

// post
func Post(apiUrl string, data []byte, header map[string]string, isHttps bool) ([]byte, error) {
	client := &http.Client{Timeout: 5 * time.Second}

	if isHttps {
		client.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	}
	reqest, err := http.NewRequest("POST", apiUrl, bytes.NewReader(data))
	if err != nil {
		errors.Wrapf(err, "http newRquest POST apiUrl:%v,data:%v", apiUrl, string(data))
		return nil, err
	}

	for k, v := range header {
		reqest.Header.Set(k, v)
	}

	response, err := client.Do(reqest)
	if nil != err {
		errors.Wrapf(err, "client do apiUrl:%v,header:%v,data:%v", apiUrl, header, string(data))
		return nil, err
	}

	defer response.Body.Close()
	if response.StatusCode != 200 {
		logger.Errorf("====== Post %v,response_body:%v", response.Status, util.JsonStr(response.Body))
		return nil, errors.New(response.Status)
	}

	body, err := ioutil.ReadAll(response.Body)
	if nil != err {
		errors.Wrapf(err, "ioutil readAll ")
		return nil, err
	}

	return body, nil
}

func Get(apiUrl string, header map[string]string, params url.Values, isHttps bool) ([]byte, error) {
	url, err := url.Parse(apiUrl)
	if err != nil {
		return nil, errors.Wrapf(err, "url.Parse apiUrl:%v", apiUrl)
	}
	if params != nil {
		url.RawQuery = params.Encode()
	}

	urlPath := url.String()

	client := &http.Client{Timeout: 3 * time.Second}
	if isHttps {
		client.Transport = &http.Transport{
			MaxIdleConnsPerHost: 1024,
			TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		}
	}

	reqest, err := http.NewRequest("GET", urlPath, nil)
	if err != nil {
		errors.Wrapf(err, "http newRquest GET urlPath:%v", url)
		return nil, err
	}

	for k, v := range header {
		reqest.Header.Set(k, v)
	}

	response, err := client.Do(reqest)
	if nil != err {
		errors.Wrapf(err, "client do urlPath:%v,header:%v", urlPath, header)
		return nil, err
	}

	defer response.Body.Close()
	logger.Infof("urlPath:%v,resp:%v,err:%v", urlPath, response, err)
	if response.StatusCode != 200 {
		return nil, errors.New(response.Status)
	}

	body, err := ioutil.ReadAll(response.Body)
	if nil != err {
		errors.Wrapf(err, "ioutil readAll ")
		return nil, err
	}
	return body, nil
}

func BuildAudioStreamAgoraParamShumei(cname string) AudioStreamAgoraParamShumei {
	expireTs := uint32(time.Now().Unix()) + RenewTokenDuration
	uuid := int32(UseridToUid(int64(AGORA_SHUMEI)))
	builder := SimpleTokenBuilder.CreateSimpleTokenBuilder(getAgoraAppid(), getAgoraAppcert(), cname, uint32(uuid))
	builder.InitPrivileges(SimpleTokenBuilder.Role_Subscriber)
	builder.SetPrivilege(AccessToken.KJoinChannel, expireTs) //join
	token, _ := builder.BuildToken()
	return AudioStreamAgoraParamShumei{
		AppId:           agoraConfig.agoraAppid,
		Channel:         cname,
		Token:           token,
		IsMixingEnabled: false,
		Uid:             uuid,
		ChannelProfile:  0,
	}
}
