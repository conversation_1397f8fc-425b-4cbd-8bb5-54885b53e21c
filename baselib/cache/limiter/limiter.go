package limiter

import (
	"context"
	"github.com/go-redis/redis/v8"
	"time"
)

func NewNatureDailyLimiter(client *redis.Client, key string, limit int) (*Limiter, error) {
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 5, 0, 0, now.Location())
	return NewLimiter(client, key + ":" + now.Format("20060102"), limit, midnight.Sub(now))
}

func NewLimiter(client *redis.Client, key string, limit int, timeout time.Duration) (*Limiter, error) {
	l := &Limiter{
		client: client,
		key:    key,
		Limit:  limit,
	}
	if err := l.init(timeout); err != nil {
		return nil, err

	}
	return l, nil

}

type Limiter struct {
	client *redis.Client
	key    string
	Limit  int
}

func (r Limiter) init(d time.Duration) error {
	return r.client.SetNX(context.Background(), r.getKey(), 0, d).Err()
}

func (r Limiter) getKey() string {
	return r.key
}

func (r Limiter) AllowN(n int) (allow bool, err error) {
	res, err := r.client.IncrBy(context.Background(), r.getKey(), int64(n)).Result()
	if err != nil {
		return false, err
	}
	if res <= int64(r.Limit) {
		return true, nil
	} else {
		err = r.client.IncrBy(context.Background(), r.getKey(), int64(-n)).Err()
		if err != nil {
			return false, err
		}
		return false, nil
	}
}

func (r Limiter) Allow() (allow bool, err error) {
	return r.AllowN(1)
}

func (r Limiter) Reserve() error {
	return r.ReserveN(1)
}

func (r Limiter) ReserveN(n int) (err error) {
	return r.client.IncrBy(context.Background(), r.getKey(), -int64(n)).Err()
}
