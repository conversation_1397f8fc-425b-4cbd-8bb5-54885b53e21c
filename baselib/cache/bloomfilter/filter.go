package bloomfilter

import (
	"context"
	"sync"
	"time"

	"xim/baselib/logger"

	"github.com/go-redis/redis/v8"
)

type keyExpireSetItem struct {
	Key        string
	ExpireTime time.Time
}

type keyExpireSet struct {
	Map map[string]keyExpireSetItem
	m   sync.RWMutex
}

func (k *keyExpireSet) CheckKeySetExpired(key string) bool {
	k.m.RLock()
	defer k.m.RUnlock()
	_, ok := k.Map[key]
	return ok
}

func (k *keyExpireSet) Set(key string, d time.Duration) {
	k.m.Lock()
	defer k.m.Unlock()

	// check expire
	var toDeleteKeys []string
	for _, item := range k.Map {
		if time.Now().After(item.ExpireTime) {
			toDeleteKeys = append(toDeleteKeys, item.Key)
		}
	}
	for _, deleteKey := range toDeleteKeys {
		delete(k.Map, deleteKey)
	}

	if _, ok := k.Map[key]; ok {
		return
	}
	k.Map[key] = keyExpireSetItem{
		Key:        key,
		ExpireTime: time.Now().Add(d),
	}
}

var keySet = keyExpireSet{
	Map: map[string]keyExpireSetItem{},
	m:   sync.RWMutex{},
}

func NewBloomFilter(client *redis.Client, count int, errorRate float64) Bloom {
	return Bloom{
		client:   client,
		Error:    errorRate,
		Capacity: count,
	}
}

type Bloom struct {
	client   *redis.Client
	Error    float64
	Capacity int
}

func (b Bloom) CountKey(key string) string {
	return key + "_bloom_filter_count"
}

func (b Bloom) Add(key string, items ...string) (count int, err error) {
	var vals = []interface{}{"BF.INSERT", key, "CAPACITY", b.Capacity, "ERROR", b.Error, "ITEMS"}
	for _, item := range items {
		vals = append(vals, item)
	}
	res, err := b.client.Do(context.Background(), vals...).BoolSlice()
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	for _, re := range res {
		if re {
			count++
		}
	}
	e := b.client.IncrBy(context.Background(), b.CountKey(key), int64(count)).Err()
	if err != nil {
		logger.Errorf("error %v", e)
	}
	return
}

func (b Bloom) SetExpireOnce(key string, duration time.Duration) {
	if keySet.CheckKeySetExpired(key) {
		return
	}
	err := b.client.Expire(context.Background(), key, duration).Err()
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	keySet.Set(key, duration)
}

func (b Bloom) Check(key string, items ...string) (result map[string]bool, err error) {
	var vals = []interface{}{"BF.MEXISTS", key}
	for _, item := range items {
		vals = append(vals, item)
	}
	res, err := b.client.Do(context.Background(), vals...).BoolSlice()
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	result = make(map[string]bool, len(items))
	for index, item := range items {
		result[item] = res[index]
	}
	return
}

func (b Bloom) MultipleCheck(keys []string, items ...string) (result map[string]bool, err error) {
	var res []*redis.Cmd
	_, err = b.client.Pipelined(context.Background(), func(p redis.Pipeliner) (err error) {
		for _, key := range keys {
			var vals = []interface{}{"BF.MEXISTS", key}
			for _, item := range items {
				vals = append(vals, item)
			}
			r := b.client.Do(context.Background(), vals...)
			res = append(res, r)
		}
		return nil
	})
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	var rebBools [][]bool
	for _, re := range res {
		b, err := re.BoolSlice()
		if err != nil {
			logger.Errorf("error %v", err)
			return nil, err
		}
		rebBools = append(rebBools, b)
	}
	result = map[string]bool{}
	for _, bs := range rebBools {
		for index, b := range bs {
			result[items[index]] = result[items[index]] || b
		}
	}
	return
}
