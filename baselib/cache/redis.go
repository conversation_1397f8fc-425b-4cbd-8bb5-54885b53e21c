package cache

import (
	"strings"

	"xim/baselib/cache/redis_hook"
	"xim/baselib/cache/rediscollector"
	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/metric"

	"github.com/go-redis/redis/v8"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"
)

var (
	redisClients map[string]*redis.Client
)

func GetRedisClient(name string) *redis.Client {
	return redisClients[name]
}

func InitRedis(configs []*config.RedisConfig) {
	if len(configs) == 0 {
		logger.Warnf("no redis config")
		return
	}
	redisClients = make(map[string]*redis.Client)
	for _, config := range configs {
		if len(config.Name) == 0 {
			config.Name = "default"
		}
		addr := config.Addr
		if len(strings.Split(config.Addr, ":")) == 1 {
			addr = addr + ":6379"
		}
		client := redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: config.Password,
			DB:       config.Db,
			PoolSize: 512,
		})
		client.AddHook(redis_hook.CollectorHook{
			Addr: addr,
			Name: config.Name,
		})

		if client != nil {
			redisClients[config.Name] = client
			metric.MustRegister(
				rediscollector.NewDBStatsCollector(client,
					prometheus.Labels(metric.ConstLabel().
						With("name", config.Name).With("addr", addr),
					),
				),
			)
			logger.Info("Redis connected sucess", zap.Any("name", config.Name), zap.Any("addr", config.Addr))
		} else {
			logger.Panic("Redis Connection Exception", zap.Any("config", config))
			return
		}
	}
}
