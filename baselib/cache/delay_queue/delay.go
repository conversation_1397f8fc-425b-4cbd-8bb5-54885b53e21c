package delay_queue

import (
	"context"
	"fmt"

	"xim/baselib/logger"
	"xim/baselib/util"

	"github.com/go-redis/redis/v8"
)

type DelayQueueOption struct {
	IndexCount int64
}

func NewDelayQueue(client *redis.Client, key string) *DelayQueue {
	return &DelayQueue{
		client:     client,
		key:        key,
		indexCount: 10,
	}
}

func NewDelayQueueWithOpt(client *redis.Client, key string, opt DelayQueueOption) *DelayQueue {
	if opt.IndexCount <= 0 {
		opt.IndexCount = 10
	}
	return &DelayQueue{
		client:     client,
		key:        key,
		indexCount: opt.IndexCount,
	}
}

type DelayQueue struct {
	client     *redis.Client
	key        string
	indexCount int64
}

func (d *DelayQueue) IndexCount() int64 {
	return d.indexCount
}

func (d *DelayQueue) keyByIndex(index int64) string {
	return d.key + fmt.Sprintf(":%d", index)
}

func (d *DelayQueue) keyById(id string) string {
	return d.keyByIndex(util.GetHashCode(id) % d.IndexCount())
}

func (d *DelayQueue) CheckExist(id string) (exist bool, err error) {
	_, err = d.client.ZScore(context.Background(), d.keyById(id), id).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		logger.Errorf("error %v", err)
		return false, err
	}
	return true, nil
}

func (d *DelayQueue) SetTask(id string, time int64) (err error) {
	err = d.client.ZAdd(context.Background(), d.keyById(id), &redis.Z{
		Score:  float64(time),
		Member: id,
	}).Err()
	if err != nil {
		logger.Errorf("error set task %v", err)
		return err
	}
	return
}

func (d *DelayQueue) DeleteIndexTask(index int64, id string) (err error) {
	err = d.client.ZRem(context.Background(), d.keyByIndex(index), id).Err()
	if err != nil {
		logger.Errorf("error delete task %v", err)
		return err
	}
	return
}

func (d *DelayQueue) DeleteTasks(ids ...string) (err error) {
	if len(ids) == 0 {
		return nil
	}
	karray := map[string][]interface{}{}
	for _, s := range ids {
		key := d.keyById(s)
		karray[key] = append(karray[key], s)
	}
	for key, members := range karray {
		err = d.client.ZRem(context.Background(), key, members...).Err()
		if err != nil {
			logger.Errorf("error rem %v", err)
			return err
		}
	}
	return nil
}

func (d *DelayQueue) TimeoutTasks(time, index, limit int64) (tasks []string, err error) {
	key := d.keyByIndex(index)
	tasks, err = d.client.ZRangeByScore(context.Background(), key, &redis.ZRangeBy{
		Min:    "-1",
		Max:    fmt.Sprintf("%d", time),
		Offset: 0,
		Count:  limit,
	}).Result()
	if err != nil {
		logger.Errorf("error zrangebyscore %v", err)
		return nil, err
	}
	return tasks, nil
}
