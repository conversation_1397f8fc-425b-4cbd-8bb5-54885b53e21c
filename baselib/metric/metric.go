package metric

import (
	"sort"
	"sync"

	"xim/baselib/server/env"

	"github.com/prometheus/client_golang/prometheus"
)

type ConstLabels map[string]string

func (c ConstLabels) With(key, value string) ConstLabels {
	c[key] = value
	return c
}

func ConstLabel() ConstLabels {
	return ConstLabels{
		"pod":     env.GetHostName(),
		"service": env.GetServiceName(),
	}
}

var (
	mutex        sync.RWMutex
	countMap     = map[string]*prometheus.CounterVec{}
	gaugeMap     = map[string]*prometheus.GaugeVec{}
	histogramMap = map[string]*prometheus.HistogramVec{}
	summaryMap   = map[string]*prometheus.SummaryVec{}
)

func counterVec(name string, labels []string) *prometheus.CounterVec {
	mutex.RLock()
	if c, ok := countMap[name]; ok {
		mutex.RUnlock()
		return c
	}
	mutex.RUnlock()
	mutex.Lock()
	defer mutex.Unlock()
	if c, ok := countMap[name]; ok {
		return c
	}
	c := prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "",
		Subsystem: "",
		Name:      name + "_total",
		Help:      "",
		ConstLabels: prometheus.Labels{
			"pod":     env.GetHostName(),
			"service": env.GetServiceName(),
		},
	}, labels)
	MustRegister(c)
	countMap[name] = c
	return c
}

// CounterWithLabels name 相同， labels 的key 必须相同
// 如果不相同，会 panic
func CounterWithLabels(name string, labels prometheus.Labels) prometheus.Counter {
	return counterVec(name, sortLabelKeys(labels)).With(labels)
}

func Counter(name string) prometheus.Counter {
	return counterVec(name, nil).With(nil)
}

func gaugeVec(name string, labels []string) *prometheus.GaugeVec {
	mutex.RLock()
	if c, ok := gaugeMap[name]; ok {
		mutex.RUnlock()
		return c
	}
	mutex.RUnlock()

	mutex.Lock()
	defer mutex.Unlock()
	if c, ok := gaugeMap[name]; ok {
		return c
	}
	c := prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "",
		Subsystem: "",
		Name:      name + "_gauge",
		Help:      "",
		ConstLabels: prometheus.Labels{
			"pod":     env.GetHostName(),
			"service": env.GetServiceName(),
		},
	}, labels)
	MustRegister(c)
	gaugeMap[name] = c
	return c
}

func Gauge(name string) prometheus.Gauge {
	return gaugeVec(name, nil).With(nil)
}

func GaugeLabels(name string, labels prometheus.Labels) prometheus.Gauge {
	return gaugeVec(name, sortLabelKeys(labels)).With(labels)
}

func histogramVec(name string, labels []string, buckets []float64) *prometheus.HistogramVec {
	if len(buckets) == 0 {
		buckets = []float64{10, 50, 100, 200, 300, 500}
	}
	mutex.RLock()
	if c, ok := histogramMap[name]; ok {
		mutex.RUnlock()
		return c
	}
	mutex.RUnlock()
	mutex.Lock()
	defer mutex.Unlock()
	if c, ok := histogramMap[name]; ok {
		return c
	}
	c := prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "",
		Subsystem: "",
		Name:      name + "_gauge",
		Help:      "",
		ConstLabels: prometheus.Labels{
			"pod":     env.GetHostName(),
			"service": env.GetServiceName(),
		},
		Buckets: buckets,
	}, labels)
	histogramMap[name] = c
	MustRegister(c)
	return c
}

func Histogram(name string) prometheus.Observer {
	return histogramVec(name, nil, nil).With(nil)
}

func HistogramLabelsWithBuckets(name string, labels prometheus.Labels, buckets []float64) prometheus.Observer {
	return histogramVec(name, sortLabelKeys(labels), buckets).With(labels)
}

func HistogramLabels(name string, labels prometheus.Labels) prometheus.Observer {
	return histogramVec(name, sortLabelKeys(labels), nil).With(labels)
}

func summaryVec(name string, label []string) *prometheus.SummaryVec {
	mutex.RLock()
	if c, ok := summaryMap[name]; ok {
		mutex.RUnlock()
		return c
	}
	mutex.RUnlock()
	mutex.Lock()
	defer mutex.Unlock()
	if c, ok := summaryMap[name]; ok {
		return c
	}
	c := prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace: "",
		Subsystem: "",
		Name:      name + "_summary",
		Help:      "",
		ConstLabels: prometheus.Labels{
			"pod":     env.GetHostName(),
			"service": env.GetServiceName(),
		},
		Objectives: map[float64]float64{
			0.5:  0.0001,
			0.8:  0.05,
			0.9:  0.05,
			0.95: 0.0001,
			0.99: 0.0001,
		},
		MaxAge:     0,
		AgeBuckets: 0,
		BufCap:     0,
	}, label)
	summaryMap[name] = c
	MustRegister(c)
	return c
}

func Summary(name string) prometheus.Observer {
	return summaryVec(name, nil).With(nil)
}

func SummaryLabels(name string, labels prometheus.Labels) prometheus.Observer {
	return summaryVec(name, sortLabelKeys(labels)).With(nil)
}

func sortLabelKeys(label prometheus.Labels) []string {
	var keys []string
	for key := range label {
		keys = append(keys, key)
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	return keys
}

func withRLock(m *sync.RWMutex, f func()) {
	m.RLock()
	defer m.RUnlock()
	f()
}

func withLock(m *sync.RWMutex, f func()) {
	m.Lock()
	defer m.Unlock()
	f()
}
