package grpccli

import (
	"context"
	"strconv"
	"time"
	"xim/baselib/trace"

	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/baselib/server/usermd"
	"xim/baselib/util"

	"github.com/pborman/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

var (
	noCallLogMap = map[string]bool{

		"/vc.basemsgtransfer.s/HandleMsg": true,
	}

	closeClientLog bool
)

func init() {
	if !env.IsProd() {
		noCallLogMap = map[string]bool{}
	}
}

func SetCloseClientLog() {
	closeClientLog = true
}

func callLog(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	_, span := trace.NewSpan(ctx, "ClientLog")
	traceId := trace.GetTraceID(span)
	if traceId == "" {
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			ms, ok := md[logger.VcTraceKey]
			if ok && len(ms) > 0 {
				traceId = ms[0]
			}
		}
		if traceId == "" {
			traceId = uuid.NewUUID().String()
		}
	}
	userid := usermd.GetUserIdFromContext(ctx)
	lang := usermd.GetLangFromContext(ctx)
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{
		logger.VcTraceKey:         traceId,
		usermd.GrpcUserContextKey: strconv.FormatInt(userid, 10),
		usermd.LangContextKey:     lang,
	}))
	st := time.Now()
	err := invoker(ctx, method, req, reply, cc, opts...)
	if !closeClientLog && (!env.IsProd() || !noCallLogMap[method]) {
		logger.Infof("rpc call %s fromUserid %d timeCost %d ms error %v req %s resp %s", method, userid, time.Since(st).Milliseconds(), err,
			util.JsonStr(req), util.JsonStr(reply))
	}
	return err
}

func CallLog(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	return callLog(ctx, method, req, reply, cc, invoker, opts...)
}
