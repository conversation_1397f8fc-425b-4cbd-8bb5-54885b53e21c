package grpccli

import (
	"context"
	"strings"

	"xim/baselib/httpcli"
	"xim/baselib/logger"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

func devToHttp(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	if strings.HasPrefix(method, "/vc.basemsg") {
		return invoker(ctx, method, req, reply, cc, opts...)
	}
	reqData, err := protojson.Marshal(req.(proto.Message))
	if err != nil {
		logger.Errorf("error %v", err)
		return err
	}
	resp, err := httpcli.PostJsonRawData("https://api-test.boychat.net"+method, nil, reqData)
	if err != nil {
		logger.Errorf("error %v", err)
		return err
	}
	err = protojson.Unmarshal([]byte(resp), reply.(proto.Message))
	if err != nil {
		logger.Errorf("error %v", err)
		return err
	}
	return nil
}
