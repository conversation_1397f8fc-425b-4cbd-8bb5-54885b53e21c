package grpccli

//
//import (
//	"golang.org/x/net/context"
//	"google.golang.org/grpc/metadata"
//)
//
//var ZIPKIN_TRACER_HEADERS_TO_PROPAGATE = []string{
//	"x-request-id",
//	"x-ot-span-context",
//	"x-b3-traceid",
//	"x-b3-spanid",
//	"x-b3-parentspanid",
//	"x-b3-sampled",
//	"x-b3-flags",
//}
//
//var JAEGER_TRACER_HEADERS_TO_PROPAGATE = []string{
//	// Jaeger header (for native client)
//	"uber-trace-id",
//}
//
//type zipkinTracer struct {
//}
//
//func NewZipkinTracer() *zipkinTracer {
//	return &zipkinTracer{}
//}
//
//func (z *zipkinTracer) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
//	var meta = map[string]string{}
//
//	if md, ok := metadata.FromIncomingContext(ctx); ok {
//		for _, v := range ZIPKIN_TRACER_HEADERS_TO_PROPAGATE {
//			//当request的header中某个key不存在，往下传递时不能凭空产生（哪怕是空都不行，比如x-b3-parentspanid）
//			if value, ok := retrieveFromMeta(md, v); ok {
//				meta[v] = value
//			}
//		}
//		for _, v := range JAEGER_TRACER_HEADERS_TO_PROPAGATE {
//			if value, ok := retrieveFromMeta(md, v); ok {
//				meta[v] = value
//			}
//		}
//	}
//
//	return meta, nil
//}
//
//func retrieveFromMeta(md metadata.MD, key string) (string, bool) {
//	if vals, ok := md[key]; ok && len(vals) > 0 {
//		return vals[0], true
//	}
//	return "", false
//}
