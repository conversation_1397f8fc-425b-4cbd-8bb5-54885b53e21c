package csv_helper

import (
	"fmt"
	"io"
	"os"
	"testing"

	"xim/baselib/util"
)

type WriterStruct struct {
	Id     int64  `json:"id"`
	Name   string `json:"name"`
	Remark string `json:"remark"`
}

func TestCsvReader(t *testing.T) {
	file, err := os.OpenFile("test.csv", os.O_RDONLY, 0666)
	if err != nil {
		panic(err)
	}
	defer file.Close()
	reader := NewCsvReaderHelper(file)
	for {
		var m WriterStruct
		err = reader.Read(&m)
		if err == io.EOF {
			break
		} else if err != nil {
			panic(err)
		} else {
			fmt.Println(util.JsonStr(m))
		}
	}
}

func TestCsvWrite(t *testing.T) {
	file, err := os.OpenFile("test.csv", os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666)
	if err != nil {
		panic(err)
	}
	writer := NewCsvWriter(file, WriterStruct{}, nil)
	for i := 0; i < 1000; i++ {
		writer.Write(&WriterStruct{
			Id:     1,
			Name:   fmt.Sprintf("name:%d", i),
			Remark: fmt.Sprintf("remark:%d", i),
		})
	}
	writer.Flush()
	file.Close()
}
