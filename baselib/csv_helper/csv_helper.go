package csv_helper

import (
	"encoding/csv"
	"fmt"
	"io"
	"reflect"
	"strconv"
)

type CsvWriterHelper struct {
	writer       io.Writer
	csvWriter    *csv.Writer
	Type         reflect.Type
	excludeNames map[string]bool
}

func NewCsvWriter(writer io.Writer, data interface{}, excludeName map[string]bool) *CsvWriterHelper {
	c := &CsvWriterHelper{}
	c.writer = writer
	c.csvWriter = csv.NewWriter(c.writer)
	t := reflect.Indirect(reflect.ValueOf(data)).Type()
	if t.Kind() != reflect.Struct {
		panic("error kind is not a struct")
		return nil
	}
	c.Type = t
	c.excludeNames = excludeName

	var nameList []string
	for i := 0; i < t.NumField(); i++ {
		name := t.Field(i).Name
		if excludeName[name] {
			continue
		}
		nameList = append(nameList, name)
	}
	err := c.csvWriter.Write(nameList)
	if err != nil {
		panic(err)
	}

	return c
}

func (c *CsvWriterHelper) Write(data interface{}) {
	v := reflect.Indirect(reflect.ValueOf(data))
	if reflect.Indirect(v).Type() != c.Type {
		panic("type not same")
	}
	var columns []string
	for i := 0; i < v.NumField(); i++ {
		if c.excludeNames[c.Type.Field(i).Name] {
			continue
		}
		columns = append(columns, fmt.Sprintf("%v", v.Field(i).Interface()))
	}
	err := c.csvWriter.Write(columns)
	if err != nil {
		panic(err)
	}
	return
}

func (c *CsvWriterHelper) Flush() {
	c.csvWriter.Flush()
}

type CsvReaderHelper struct {
	csvReader *csv.Reader
	fieldMap  map[string]int
}

func NewCsvReaderHelper(reader io.Reader) *CsvReaderHelper {
	c := &CsvReaderHelper{}
	c.csvReader = csv.NewReader(reader)
	columns, err := c.csvReader.Read()
	if err != nil {
		panic(err)
	}
	c.fieldMap = map[string]int{}
	for index, column := range columns {
		c.fieldMap[column] = index
	}
	return c
}

func (c *CsvReaderHelper) Read(v interface{}) (err error) {
	record, err := c.csvReader.Read()
	if err != nil {
		return err
	}
	rv := reflect.ValueOf(v)
	value := reflect.Indirect(rv)
	t := value.Type()
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fieldName := t.Field(i).Name
		index, ok := c.fieldMap[fieldName]
		if !ok {
			continue
		}
		if index >= len(record) {
			continue
		}
		s := record[index]
		switch field.Type.Kind() {
		case reflect.Int8, reflect.Int32, reflect.Int16, reflect.Int64, reflect.Int:
			v, err := strconv.ParseInt(s, 10, 64)
			if err != nil {
				return err
			}
			value.Field(i).SetInt(v)
		case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
			v, err := strconv.ParseUint(s, 10, 64)
			if err != nil {
				return err
			}
			value.Field(i).SetUint(v)
		case reflect.String:
			value.Field(i).SetString(s)
		default:
			panic(fmt.Sprintf("not support of kind %v", field.Type.Kind()))
		}
	}
	return
}
