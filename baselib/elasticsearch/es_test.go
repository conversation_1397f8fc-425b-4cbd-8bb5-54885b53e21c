package elasticsearch

import (
	"fmt"
	"testing"
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"
)

func TestAdd(t *testing.T) {
	logger.InitLogger("D:/doc/logs/go_test.log")
	Init(&config.ElasticSearchConfig{
		Addresses: []string{
			"http://es-cn-7pp2s6v4m001kwuwt.public.elasticsearch.aliyuncs.com:9200",
		},
		Username: "elastic",
		Password: "9sb175QUkQUo",
	})

	model := IndexModel{
		IndexPrefix: "aaaa",
		IndexType:   IndexMonth,
		Refresh:     true,
	}
	data := map[string]interface{}{
		"text": "现在上着班，晚上下班吧，你先发张照片看看呗",
	}
	ts := fmt.Sprintf("%d", time.Now().Unix())
	model.AddDocument("100001"+ts, data)

	data = map[string]interface{}{
		"text":  "你那边方便视频吗，我们视频玩点好玩的，刺激的",
		"f_uid": "",
		"t_uid": "你那边方便视频吗，我们视频玩点好玩的，刺激的",
	}
	model.AddDocument("100002"+ts, data)

	data = map[string]interface{}{
		"text": "看到这双腿，我都一基动啊",
	}
	model.AddDocument("100003"+ts, data)

	data = map[string]interface{}{
		"text": "我知道好玩的你要玩吗",
	}
	model.AddDocument("100004"+ts, data)

	data = map[string]interface{}{
		"text": "嗨喽，你来这是想找我聊天的吗，是不是哪里难受啊，是想玩玩吗？",
	}
	model.AddDocument("100008"+ts, data)

	time.Sleep(5000)
}

func TestQuery(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	Init(&config.ElasticSearchConfig{
		Addresses: []string{
			"http://es-cn-7pp2s6v4m001kwuwt.public.elasticsearch.aliyuncs.com:9200",
		},
		Username: "elastic",
		Password: "9sb175QUkQUo",
	})

	model := IndexModel{
		IndexPrefix: "chat_test",
		IndexType:   IndexMonth,
		Refresh:     true,
	}

	model.QueryDocuments("视频")
	model.QueryDocuments("刺激")
	model.QueryDocuments("好玩")

	time.Sleep(5000)
}
