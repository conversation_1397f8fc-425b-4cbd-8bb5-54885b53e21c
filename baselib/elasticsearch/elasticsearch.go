package elasticsearch

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/util"
	"xim/proto/consts"

	elasticsearch7 "github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
)

var (
	esClient *elasticsearch7.Client
)

func Init(conf *config.ElasticSearchConfig) (err error) {
	if conf == nil {
		logger.Errorf("elasticsearch init config error")
		return
	}
	if len(conf.Addresses) == 0 {
		logger.Errorf("elasticsearch init addresses error")
		return
	}
	cfg := elasticsearch7.Config{
		Addresses: conf.Addresses,
		Username:  conf.Username,
		Password:  conf.Password,
	}
	esClient, err = elasticsearch7.NewClient(cfg)
	if err != nil {
		logger.Errorf("elasticsearch new client error: %v", err)
		return
	}
	fmt.Println("es init")
	return
}

type ESIndexType int

const (
	IndexAll   ESIndexType = 0
	IndexDay   ESIndexType = 1
	IndexMonth ESIndexType = 2
	IndexYear  ESIndexType = 3
)

type IndexModel struct {
	IndexPrefix string
	IndexType   ESIndexType
	Refresh     bool
}

func (m IndexModel) getCurrentIndex() string {
	switch m.IndexType {
	case IndexDay:
		return fmt.Sprintf("%s_%s", m.IndexPrefix, time.Now().Format(consts.TimeFormatYMD2))
	case IndexMonth:
		return fmt.Sprintf("%s_%s", m.IndexPrefix, time.Now().Format(consts.TimeFormatYM2))
	case IndexYear:
		return fmt.Sprintf("%s_%d", m.IndexPrefix, time.Now().Year())
	default:
		return m.IndexPrefix
	}
}

func (m IndexModel) getQueryIndexList() (indexList []string) {
	switch m.IndexType {
	case IndexDay:
		now := time.Now()
		indexList = append(indexList, fmt.Sprintf("%s_%s", m.IndexPrefix, now.Format(consts.TimeFormatYMD2)))
		for i := 0; i < 30; i++ {
			indexList = append(indexList, fmt.Sprintf("%s_%s", m.IndexPrefix, now.AddDate(0, 0, -(i+1)).Format(consts.TimeFormatYMD2)))
		}
	case IndexMonth:
		now := time.Now()
		indexList = append(indexList, fmt.Sprintf("%s_%s", m.IndexPrefix, now.Format(consts.TimeFormatYM2)))
		for i := 0; i < 3; i++ {
			indexList = append(indexList, fmt.Sprintf("%s_%s", m.IndexPrefix, now.AddDate(0, -(i+1), 0).Format(consts.TimeFormatYM2)))
		}
	case IndexYear:
		year := time.Now().Year()
		indexList = append(indexList, fmt.Sprintf("%s_%d", m.IndexPrefix, year))
		indexList = append(indexList, fmt.Sprintf("%s_%d", m.IndexPrefix, year-1))
	default:
		indexList = append(indexList, m.IndexPrefix)
	}
	return
}

func (m IndexModel) AddDocument(id string, data interface{}) (err error) {
	dataStr, ok := data.(string)
	if !ok {
		dataStr = util.JsonStr(data)
	}
	req := esapi.IndexRequest{
		Index:      m.getCurrentIndex(),
		DocumentID: id,
		Body:       bytes.NewReader([]byte(dataStr)),
		Refresh:    strconv.FormatBool(m.Refresh),
	}

	res, err := req.Do(context.Background(), esClient)
	if err != nil {
		fmt.Println(err)
		logger.Errorf("Error getting response: %s", err)
		return
	}
	defer res.Body.Close()

	fmt.Println("res", res)
	if res.IsError() {
		logger.Errorf("[%s] Error indexing document", res.Status())
		return
	}

	var result map[string]interface{}
	if err = json.NewDecoder(res.Body).Decode(&result); err != nil {
		logger.Errorf("Error parsing the response body: %s", err)
		return
	}
	fmt.Println("result", result)

	//logger.Infof("[%s] %s; version=%d", res.Status(), result["result"], int(result["_version"].(float64)))

	return
}

func (m IndexModel) QueryDocuments(keyword string) (err error) {

	var buf bytes.Buffer
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"title": keyword,
			},
		},
	}
	if err = json.NewEncoder(&buf).Encode(query); err != nil {
		logger.Errorf("Error encoding query: %s", err)
		return
	}

	indexList := m.getQueryIndexList()
	fmt.Println("indexList", indexList)
	res, err := esClient.Search(
		esClient.Search.WithContext(context.Background()),
		esClient.Search.WithIndex(indexList...),
		esClient.Search.WithBody(&buf),
		esClient.Search.WithTrackTotalHits(true),
		esClient.Search.WithPretty(),
	)
	if err != nil {
		logger.Errorf("Error getting response: %s", err)
		return
	}
	defer res.Body.Close()

	if res.IsError() {
		var e map[string]interface{}
		if err = json.NewDecoder(res.Body).Decode(&e); err != nil {
			logger.Errorf("Error parsing the response body: %s", err)
		} else {
			logger.Errorf("[%s] %s: %s",
				res.Status(),
				e["error"].(map[string]interface{})["type"],
				e["error"].(map[string]interface{})["reason"],
			)
		}
		return
	}

	var result map[string]interface{}
	if err = json.NewDecoder(res.Body).Decode(&result); err != nil {
		logger.Errorf("Error parsing the response body: %s", err)
		return
	}

	fmt.Println("result", result)

	//logger.Infof(
	//	"[%s] %d hits; took: %dms",
	//	res.Status(),
	//	int(result["hits"].(map[string]interface{})["total"].(map[string]interface{})["value"].(float64)),
	//	int(result["took"].(float64)),
	//)
	// Print the ID and document source for each hit.
	//for _, hit := range result["hits"].(map[string]interface{})["hits"].([]interface{}) {
	//	logger.Infof(" * ID=%s, %s", hit.(map[string]interface{})["_id"], hit.(map[string]interface{})["_source"])
	//}

	return
}
