package nsqutil

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/nsqutil/nsqproducerutil"
	"xim/baselib/server/env"

	"github.com/nsqio/go-nsq"
	"github.com/prometheus/client_golang/prometheus"
)

var producerHelper *Producer

type Producer struct {
	nsqdBussi   *nsqproducerutil.ProducerBussi
	topicPrefix string
}

func (p Producer) getTopic(topic string) string {
	if !env.IsProd() && len(topic) > 0 && len(p.topicPrefix) > 0 {
		return fmt.Sprintf("%s-%s", p.topicPrefix, topic)
	}
	return topic
}

func NewProducer(conf config.NsqConfig) error {
	nsqdBussi, err := nsqproducerutil.NewProducerBussi(conf.Lookupds)
	if err != nil {
		return err
	}
	producerHelper = &Producer{
		nsqdBussi:   nsqdBussi,
		topicPrefix: conf.TopicPrefix,
	}
	return nil
}

func PublishDeferredJSON(topic string, d time.Duration, message interface{}) error {
	bs, err := json.Marshal(message)
	if err != nil {
		logger.Errorf("error json.Marshal %v", err)
		return err
	}
	topic = producerHelper.getTopic(topic)
	startTime := time.Now()
	err = producerHelper.nsqdBussi.Do(func(producer *nsq.Producer) error {
		return producer.DeferredPublishAsync(topic, d, bs, nil)
	})
	var result string = "ok"
	if err != nil {
		result = "error"
	}
	metric.HistogramLabelsWithBuckets("nsq_publish", prometheus.Labels{
		"method": "PublishDeferredJSON",
		"topic":  topic,
		"result": result,
	}, []float64{5, 10, 20, 50, 100, 200, 300, 400, 500, 750, 1000}).Observe(time.Since(startTime).Seconds() * 1000)
	if err != nil {
		logger.Errorf("error publish deferedjson %v", err)
		return err
	}
	return nil
}

func PublishJSON(ctx context.Context, topic string, message interface{}) error {
	stream, err := json.Marshal(message)
	if err != nil {
		logger.Errorf("error json.Marshal %v", err)
		return err
	}
	startTime := time.Now()
	topic = producerHelper.getTopic(topic)
	err = producerHelper.nsqdBussi.Publish(topic, string(stream))
	var result string = "ok"
	if err != nil {
		result = "error"
	}
	metric.HistogramLabelsWithBuckets("nsq_publish", prometheus.Labels{
		"method": "PublishJSON",
		"topic":  topic,
		"result": result,
	}, []float64{5, 10, 20, 50, 100, 200, 300, 400, 500, 750, 1000}).Observe(time.Since(startTime).Seconds() * 1000)
	if err != nil {
		return err
	}
	return nil
}

func Publish(ctx context.Context, topic string, message string) error {
	startTime := time.Now()
	topic = producerHelper.getTopic(topic)
	err := producerHelper.nsqdBussi.Publish(topic, message)
	var result string = "ok"
	if err != nil {
		result = "error"
	}
	metric.HistogramLabelsWithBuckets("nsq_publish", prometheus.Labels{
		"method": "PublishJSON",
		"topic":  topic,
		"result": result,
	}, []float64{5, 10, 20, 50, 100, 200, 300, 400, 500, 750, 1000}).Observe(time.Since(startTime).Seconds() * 1000)
	if err != nil {
		return err
	}
	return nil
}
