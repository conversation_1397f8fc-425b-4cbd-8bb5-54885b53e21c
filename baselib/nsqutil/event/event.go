package event

import (
	"encoding/json"
	"fmt"
	"strings"

	"xim/baselib/util"

	"github.com/pborman/uuid"
)

type Event interface {
	String() string
	GetId() string
	GetOType() string
	GetEType() string
	GetUserid() int64
	GetObject() int64
	GetData() []byte
	ExtractEventData(data interface{}) (err error)
}

// event中的mid是触发该事件的用户id，oid是触发事件对象id
// case1: otype=post, etype=create mid=发帖人的mid, oid是帖子id
// case2: otype=user, etype=attention mid=发起关注的用户mid, oid是被关注用户的mid
type BaseEvent struct {
	// 事件ID，业务可以选择根据这个做消息幂等处理逻辑
	Id     string          `json:"id"`
	OType  string          `json:"otype"`
	EType  string          `json:"etype"`
	Userid int64           `json:"userid,omitempty"`
	Sex    int32           `json:"sex,omitempty"`
	Object int64           `json:"object,omitempty"`
	Data   json.RawMessage `json:"data,omitempty"`
}

func (e *BaseEvent) String() string {
	return fmt.Sprintf("id: %s, otype:%s, etype:%s, object:%d, userid: %d, data: %s",
		e.Id, e.OType, e.EType, e.Object, e.Userid, string(e.Data))
}

func (e *BaseEvent) GetId() string {
	return e.Id
}

func (e *BaseEvent) GetOType() string {
	return e.OType
}

func (e *BaseEvent) GetEType() string {
	return e.EType
}

func (e *BaseEvent) GetUserid() int64 {
	return e.Userid
}

func (e *BaseEvent) GetObject() int64 {
	return e.Object
}

func (e *BaseEvent) GetData() []byte {
	return e.Data
}

// data must be ptr type
func (e *BaseEvent) ExtractEventData(data interface{}) (err error) {
	return util.Unmarshal(e.Data, data)
}

func GenBaseEvent(otype, etype string, userid, object int64, data interface{}) (e *BaseEvent) {
	d, _ := util.Marshal(data)
	e = &BaseEvent{
		Id:     genMsgId(),
		OType:  otype,
		EType:  etype,
		Object: object,
		Userid: userid,
		Data:   d,
	}
	return
}

func genMsgId() string {
	return strings.Replace(uuid.New(), "-", "", -1)
}
