package nsqutil

import (
	"fmt"

	"xim/baselib/config"
	"xim/baselib/metric"
	"xim/baselib/nsqutil/event"
	"xim/baselib/nsqutil/nsqcollector"
	"xim/baselib/server/env"

	"github.com/nsqio/go-nsq"
	"github.com/prometheus/client_golang/prometheus"
)

// Consumer 消费者
type Consumer struct {
	lookups     []string
	topicPrefix string
	conf        *nsq.Config
}

var consumerHelper *Consumer

func (c Consumer) getTopic(topic string) string {
	if !env.IsProd() && len(topic) > 0 && len(c.topicPrefix) > 0 {
		return fmt.Sprintf("%s-%s", c.topicPrefix, topic)
	}
	return topic
}

// NewConsumer 构造消费者
func NewConsumer(conf config.NsqConfig) error {
	consumerHelper = &Consumer{
		lookups:     conf.Lookupds,
		topicPrefix: conf.TopicPrefix,
		conf:        nsq.NewConfig(),
	}
	//设置的值必须大于等于nsqd的节点数,默认只消费一个节点
	consumerHelper.conf.MaxInFlight = 40
	return nil
}

// Subscribe 订阅事件
func Subscribe(topic string, channel string, handler nsq.Handler) (*nsq.Consumer, error) {
	if consumerHelper == nil {
		return nil, fmt.Errorf("error consumerHelper not inited")
	}
	topic = consumerHelper.getTopic(topic)
	consumer, err := nsq.NewConsumer(topic, channel, consumerHelper.conf)
	if err != nil {
		return consumer, err
	}
	consumer.Stats()
	metric.MustRegister(nsqcollector.NewNsqStatsCollector(consumer,
		prometheus.Labels(
			metric.ConstLabel().With("topic", topic).With("channel", channel),
		),
	),
	)
	consumer.AddConcurrentHandlers(handler, 50)
	if err := consumer.ConnectToNSQLookupds(consumerHelper.lookups); err != nil {
		return consumer, err
	}
	return consumer, nil
}

func SubscribeEvent(topic string, channel string) (*nsq.Consumer, error) {
	return Subscribe(topic, channel, nsq.HandlerFunc(event.EventProc))
}

func SubscribeSingle(topic string, channel string, handler nsq.Handler) (*nsq.Consumer, error) {
	if consumerHelper == nil {
		return nil, fmt.Errorf("error consumerHelper not inited")
	}
	topic = consumerHelper.getTopic(topic)
	consumer, err := nsq.NewConsumer(topic, channel, consumerHelper.conf)
	if err != nil {
		return consumer, err
	}
	consumer.Stats()
	metric.MustRegister(nsqcollector.NewNsqStatsCollector(consumer,
		prometheus.Labels(
			metric.ConstLabel().With("topic", topic).With("channel", channel),
		),
	),
	)
	consumer.AddConcurrentHandlers(handler, 1)
	if err := consumer.ConnectToNSQLookupds(consumerHelper.lookups); err != nil {
		return consumer, err
	}
	return consumer, nil
}
