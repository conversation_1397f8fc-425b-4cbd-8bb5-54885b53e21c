package apppkg

import (
	"fmt"
	"strconv"
	"strings"
)

// 解析版本号，以及一些常量

type Apppkg string

const (
	App          Apppkg = "com.ylab.xlonetoone"
	XunleiPlugin        = "com.xunlei.downloadprovider"
	XunleiH5            = "com.xunlei.downloadprovider.h5"
)

func ParseAppVersion(ver string) (mainVersion, subVersion, sVersion int64, err error) {
	vers := strings.Split(ver, ".")
	if len(vers) != 3 {
		err = fmt.Errorf("error parse version")
		return
	}
	mainVersion, _ = strconv.ParseInt(vers[0], 10, 64)
	subVersion, _ = strconv.ParseInt(vers[1], 10, 64)
	sVersion, _ = strconv.ParseInt(vers[2], 10, 64)
	return
}
