- [trace](#trace)
  * [工作原理](#%E5%B7%A5%E4%BD%9C%E5%8E%9F%E7%90%86)
  * [快速开始](#%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B)
  * [完整示例](#%E5%AE%8C%E6%95%B4%E7%A4%BA%E4%BE%8B)
  * [操作指南](#%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97)
  * [API 参考](#api-%E5%8F%82%E8%80%83)
  * [变更日志](#%E5%8F%98%E6%9B%B4%E6%97%A5%E5%BF%97)

# trace

基于 OpenTelemetry 的服务链路追踪工具库。

相较于 OpenTelemetry 的官方 SDK 库，本库提供了更加简单的使用方式，无需关注复杂的 SDK 和 API。

## 工作原理

基于 OpenTelemetry 的 `trace` 包可以接入 Jaeger、Zipkin 等链路追踪工具，实现对服务的链路追踪。

![](./docs/trace_work.png)

## 快速开始

1. 安装 `trace` 包：

```bash
go get gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace
```

2. 在代码中使用 `trace` 包：

```go
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"
)

func main() {
	ctx := context.Background()

	prv, err := trace.NewProvider(ctx, trace.ProviderConfig{
		Endpoint:       "127.0.0.1:4317", // Jaeger / 阿里云 的 OTEL 协议的 gRPC 端口
		ServiceName:    "demo",
		ServiceVersion: "v0.0.1",
		Environment:    "dev",
	})
	if err != nil {
		log.Fatalf("Failed to create trace provider: %v", err)
	}

	defer prv.Close(ctx)

	func() {
		ctx, span := trace.NewSpan(ctx, "do_some_work")
		defer span.End()

		// do some work
		time.Sleep(100 * time.Millisecond)
	}()
}
```

## [完整示例](./docs/full_example.md)

## [操作指南](./docs/operations_guide.md)

## [API 参考](./docs/api_reference.md)

## 变更日志

