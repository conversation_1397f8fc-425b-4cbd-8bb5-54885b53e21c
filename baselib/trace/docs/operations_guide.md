# 操作指南

## 如何配置服务追踪？

可以通过 `trace.ProviderConfig` 结构体来配置：

```go
type ProviderConfig struct {
	Disabled       bool
	Endpoint       string
	URLPath        string
	ServiceName    string
	ServiceVersion string
	Environment    string
}
```

配置示例：

```go
prv, err := trace.NewProvider(ctx, trace.ProviderConfig{
	Disabled:       false,
	Endpoint:       "127.0.0.1:4318", // jaeger 的 OTEL 协议的 HTTP 端口
	URLPath:        "/v1/traces",
	ServiceName:    "demo",
	ServiceVersion: "v0.0.1",
	Environment:    "dev",
})
```

## 如何创建 Span？

通过 `ctx` 上下文对象和 `trace.NewSpan` 方法创建一个新的 `Span`：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

ctx, span := trace.NewSpan(ctx, "span_name")
defer span.End()
```

## 如何为 HTTP 服务端/客户端添加服务追踪？

### 服务端

通过 `trace.HTTPHandlerFunc` 来包装处理函数：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

http.HandleFunc("/api/v1/echo", trace.HTTPHandlerFunc(doEcho, "do_echo"))
```

上面的 `do_echo` 表示当前处理函数的 `span` 名称。

### 客户端

为 `request.Header` 注入 `tracing` 信息：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

trace.InjectHTTPHeader(ctx, req)
```

## 如何为 gRPC 服务端/客户端添加服务追踪？

### 服务端

服务端需要在创建 `grpc.Server` 时添加 `trace.GRPCServerStatsHandler`：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

s := grpc.NewServer(
	trace.GRPCServerStatsHandler(),
)
```

### 客户端

客户端需要在创建 `grpc.ClientConn` 时添加 `trace.WithGRPCClientStatsHandler()`：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

var conn *grpc.ClientConn
conn, err = grpc.NewClient("127.0.0.1:7777", 
    grpc.WithTransportCredentials(insecure.NewCredentials()),
	trace.WithGRPCClientStatsHandler(),
)
```

## 如何为 gin 服务添加服务追踪？

通过中间件来为 gin 服务添加服务追踪：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

r := gin.New()
r.Use(trace.GinMiddleware("service-name"))
```

## 如何在业务代码中使用服务追踪？

可以直接使用 `trace.NewSpan` 方法从 `ctx` 中创建一个新的 `Span`：

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

ctx, span := trace.NewSpan(ctx, "span_name")
```

## 如何获取 Trace ID 和 Span ID？

```go
import "gitlab.xunlei.cn/pub/scaffold/xframework/pkg/trace"

traceID := trace.GetTraceID(span) 
spanID := trace.GetSpanID(span)
```
