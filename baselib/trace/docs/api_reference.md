# API 参考

```go
// Gin 中间件
func GinMiddleware(service string, opts ...otelgin.Option) gin.HandlerFunc

// gRPC 服务端
func GRPCServerStatsHandler(opts ...otelgrpc.Option) grpc.ServerOption

// gRPC 客户端
func WithGRPCClientStatsHandler(opts ...otelgrpc.Option) grpc.DialOption

// HTTP 服务端
func HTTPHandler(handler http.Handler, name string) http.Handler

// HTTP 服务器
func HTTPHandlerFunc(handler http.HandlerFunc, name string) http.HandlerFunc

// HTTP 客户端
func InjectHTTPHeader(ctx context.Context, req *http.Request)

// 创建服务追踪
func NewProvider(ctx context.Context, cfg ProviderConfig) (*Provider, error)

// 创建 Span 并开始
func NewSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span)

// 从上下文中获取 Span
func SpanFromContext(ctx context.Context) trace.Span

// 添加 Span 标签
func AddSpanTags(span trace.Span, tags map[string]string)

// 添加 Span 事件
func AddSpanEvents(span trace.Span, name string, events map[string]string)

// 获取 Trace ID 
func GetTraceID(span trace.Span) string

// 获取 Span ID
func GetSpanID(span trace.Span) string
```
