.build_release:
  stage: build
  rules:
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/
      when: always
    - if: $TARGET_SERVICE != ""
      when: always
  script:
    - |
      VERSION=${CI_COMMIT_TAG}
      echo "Building service: ${SERVICE} version: ${VERSION}"
      cd ${SERVICE}
      if [[ -n "$CI_COMMIT_TAG" ]]; then
        make build VERSION=${VERSION} || exit 1
      else
        make build || exit 1
      fi
  after_script:
    - |
      # 获取构建状态和信息
      if [ "$CI_JOB_STATUS" == "success" ]; then
        STATUS="✅ 构建成功"
        COLOR="#36a64f"
      else
        STATUS="❌ 构建失败"
        COLOR="#dc3545"
      fi
      
      # 构建通知内容
      TITLE="[通知]【${CI_PROJECT_NAME}】${SERVICE} 服务构建${STATUS}"
      TEXT="### ${TITLE}\n\n"
      TEXT+="- 分支/标签: ${CI_COMMIT_REF_NAME}\n"
      TEXT+="- 提交信息: ${CI_COMMIT_MESSAGE}\n"
      TEXT+="- 提交人: ${GITLAB_USER_NAME}\n"
      TEXT+="- 构建时间: $(date '+%Y-%m-%d %H:%M:%S')\n"
      TEXT+="- [查看流水线详情](${CI_PIPELINE_URL})\n"
      
      # 发送钉钉通知
      curl -X POST "${DINGTALK_WEBHOOK}" \
        -H 'Content-Type: application/json' \
        -d "{
          \"msgtype\": \"markdown\",
          \"markdown\": {
            \"title\": \"${TITLE}\",
            \"text\": \"${TEXT}\"
          }
        }"
  tags:
    - vcproject

.build_test:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_COMMIT_BRANCH == "test"
      when: always
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.+$/
      when: always
    - if: $TARGET_SERVICE != ""
      when: always
    - when: never
  script:
    - |
      VERSION=$(echo "${CI_COMMIT_BRANCH}" | sed 's/[^a-zA-Z0-9]/_/g')
      echo "Building service: ${SERVICE}  version: ${VERSION}"
      cd ${SERVICE}
      make build VERSION=${VERSION}|| exit 1
  after_script:
    - |
      # 获取构建状态和信息
      if [ "$CI_JOB_STATUS" == "success" ]; then
        STATUS="✅ 构建成功"
        COLOR="#36a64f"
      else
        STATUS="❌ 构建失败"
        COLOR="#dc3545"
      fi
      
      # 构建通知内容
      TITLE="[通知]【${CI_PROJECT_NAME}】${SERVICE} 服务构建${STATUS}"
      TEXT="### ${TITLE}\n\n"
      TEXT+="- 分支/标签: ${CI_COMMIT_REF_NAME}\n"
      TEXT+="- 提交信息: ${CI_COMMIT_MESSAGE}\n"
      TEXT+="- 提交人: ${GITLAB_USER_NAME}\n"
      TEXT+="- 构建时间: $(date '+%Y-%m-%d %H:%M:%S')\n"
      TEXT+="- [查看流水线详情](${CI_PIPELINE_URL})\n"
      
      # 发送钉钉通知
      curl -X POST "${DINGTALK_WEBHOOK}" \
        -H 'Content-Type: application/json' \
        -d "{
          \"msgtype\": \"markdown\",
          \"markdown\": {
            \"title\": \"${TITLE}\",
            \"text\": \"${TEXT}\"
          }
        }"
  tags:
    - vcproject

.test_services:
  stage: test
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_COMMIT_BRANCH == "test"
      when: always
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.+$/
      when: always
    - if: $TARGET_SERVICE != ""
      when: always
    - when: never
  script:
    - |
      echo "Testing service: ${SERVICE}"
      cd ${SERVICE}
      #make test || exit 1
      cd ..
  tags:
    - vcproject