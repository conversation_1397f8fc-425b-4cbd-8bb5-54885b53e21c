.vars_tag: &vars_tag
  # 动态设置缓存key
  CACHE_KEY: "release-commits"
.vars_test: &vars_test
  # 动态设置缓存key
  CACHE_KEY: "${CI_COMMIT_REF_SLUG}-commits"

.vars_rules: &vars_rules
  rules:
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/  # 版本tag
      when: always
    - if: $CI_COMMIT_BRANCH == "master"          # master分支
      when: always
    - if: $CI_COMMIT_BRANCH == "main"            # main分支
      when: always
    - if: $CI_COMMIT_BRANCH == "test"     # test/xxx分支
      when: always
    - if: $CI_COMMIT_BRANCH =~ /^feature\/.+$/   # feature/xxx分支
      when: always
    - if: $TARGET_SERVICE != ""                   # 指定服务构建
      when: always
    - when: never                                 # 其他情况不执行


variables:
  CACHE_DIR: ".gitlab-cache"
  LAST_COMMIT_FILE_TEST: ".gitlab-cache/last_test_commit.txt"
  LAST_COMMIT_FILE_RELEASE: ".gitlab-cache/last_release_commit.txt"
  TARGET_SERVICE: ""  # 新增：指定要构建的服务
  DINGTALK_WEBHOOK: "https://oapi.dingtalk.com/robot/send?access_token=71da552882c2ee91c5877c00ca881e0624726e53d23748a873d01be3c8a5ea82"

workflow:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/
      variables:
        <<: *vars_tag
    - when: always
      variables:
        <<: *vars_test

cache:
  key: $CACHE_KEY
  paths:
    - ${CACHE_DIR}/
  policy: pull-push

stages:
  - trigger

detect_changes:
  stage: .pre
  <<: *vars_rules
  script:
    - |
      # 确保在工作目录中创建文件
      CHANGES_ENV="${CI_PROJECT_DIR}/changes.env"
      # 初始化 "${CHANGES_ENV}"，避免之前构建的变量残留
      echo "" > "${CHANGES_ENV}"

      
      # 创建缓存目录
      mkdir -p ${CACHE_DIR}
      
      # 根据构建类型选择commit记录文件
      if [[ -n "$CI_COMMIT_TAG" ]]; then
        LAST_COMMIT_FILE="${LAST_COMMIT_FILE_RELEASE}"
      else
        LAST_COMMIT_FILE="${LAST_COMMIT_FILE_TEST}"
      fi
      echo "CACHE_KEY=${CACHE_KEY},CI_COMMIT_REF_SLUG=${CI_COMMIT_REF_SLUG},TARGET_SERVICE=${TARGET_SERVICE}"
      # 检查上次构建的commit
      if [ -f "${LAST_COMMIT_FILE}" ]; then
        LAST_COMMIT=$(cat "${LAST_COMMIT_FILE}")
        echo "Found last commit: ${LAST_COMMIT}"
      else
        echo "No previous commit found"
      fi
      # 保存当前commit
      echo "${CI_COMMIT_SHA}" > "${LAST_COMMIT_FILE}"
      
      # 确保以正确的格式写入变量（每行一个 KEY=VALUE，没有空格）
      {
        # 获取所有服务目录
        ALL_SERVICES=$(find . -mindepth 2 -maxdepth 2 -name "Makefile" -exec dirname {} \; | sed 's|^./||' | tr '\n' ' ')
        echo "ALL_SERVICES=${ALL_SERVICES}"
        echo "LAST_COMMIT_FILE=${LAST_COMMIT_FILE}"
        # 设置构建类型
        if [[ -n "$CI_COMMIT_TAG" ]]; then
          echo "BUILD_TYPE=tag"
          echo "TAG_MODE=true"
          echo "VERSION=${CI_COMMIT_TAG#v}"
        elif [[ "$CI_COMMIT_BRANCH" == "master" || "$CI_COMMIT_BRANCH" == "main" ]]; then
          echo "BUILD_TYPE=main"
        elif [[ "$CI_COMMIT_BRANCH" == "test" ]]; then
          echo "BUILD_TYPE=test"
        elif [[ "$CI_COMMIT_BRANCH" =~ ^feature\/.+ ]]; then
          echo "BUILD_TYPE=feature"
        fi
        # 处理目标服务
        if [[ -n "${TARGET_SERVICE}" ]]; then
          # 将 TARGET_SERVICE 按逗号分割成数组
          IFS=',' read -ra TARGET_SERVICES <<< "${TARGET_SERVICE}"
          VALID_SERVICES=""
          # 检查每个服务是否有效
          for service in "${TARGET_SERVICES[@]}"; do
            # 去除空格
            service=$(echo "$service" | xargs)
            if echo "${ALL_SERVICES}" | grep -w -q "${service}"; then
              VALID_SERVICES="${VALID_SERVICES:+$VALID_SERVICES,}${service}"
            fi
          done
           if [[ -n "${VALID_SERVICES}" ]]; then
            echo "CHANGED_SERVICES=${VALID_SERVICES}"
            echo "SINGLE_SERVICE_MODE=true"
          else
            #echo "Error: Specified service '${TARGET_SERVICE}' not found"
            echo "CHANGED_SERVICES="
            echo "SINGLE_SERVICE_MODE=error"
          fi
        else
      
          # 检测变更的服务
          if [ -z "${LAST_COMMIT}" ]; then
            # 将空格替换为逗号
            SERVICES_CSV=$(echo "${ALL_SERVICES}" | tr ' ' ',')
            echo "CHANGED_SERVICES=${SERVICES_CSV}" >> "${CHANGES_ENV}"
          else
            CHANGED_FILES=$(git diff --name-only ${LAST_COMMIT} ${CI_COMMIT_SHA})
            CHANGED_SERVICES=""
            for service in ${ALL_SERVICES}; do
              if echo "${CHANGED_FILES}" | grep -q "^${service}/"; then
                CHANGED_SERVICES="${CHANGED_SERVICES:+$CHANGED_SERVICES,}${service}"
              fi
            done
            echo "CHANGED_SERVICES=${CHANGED_SERVICES}" >> "${CHANGES_ENV}"
          fi
        fi
      } > "${CHANGES_ENV}"
      # 打印最终的变量文件内容
      echo "=== Generated "${CHANGES_ENV}" ==="
      cat "${CHANGES_ENV}"
      echo "==========================="
      
      # 验证文件格式
      if ! grep -E '^[A-Z_]+=.*$' "${CHANGES_ENV}" > /dev/null; then
        echo "Error: Invalid environment variable format"
        exit 1
      fi
  artifacts:
    reports:
      dotenv: changes.env
    paths:
      - changes.env
  tags:
    - vcproject

generate_matrix:
  stage: .pre
  <<: *vars_rules

  needs: ["detect_changes"]
  script:
    - |
      # 创建动态配置文件
      echo "Creating dynamic configuration..."
      
      # 如果有变更的服务，生成对应的测试和构建配置
      if [[ -z "${CHANGED_SERVICES}" ]]; then
        # 如果没有变更的服务，生成一个空的配置文件
        cat << EOF > dynamic_config.yml
      stages:
        - build

      no_changes_build:
        stage: build
        script:
          - echo "No services were changed, skipping build"
        rules:
          - when: always
        tags:
          - vcproject
      EOF
        echo "No services changed, skipping all tasks."
      else
        FORMATTED_SERVICES=$(echo "${CHANGED_SERVICES}" | sed 's/,$//' | sed 's/,/, /g')
        echo "Formatted services: ${FORMATTED_SERVICES}"
      
        # 生成测试任务配置
        cat << EOF >> dynamic_config.yml
      stages:
        - test
        - build

      include:
        - local: '.gitlab/ci/matrix-template.yml'
      test_services:
        extends: .test_services
        parallel:
          matrix:
            - SERVICE: [${FORMATTED_SERVICES}]
      build_release:
        extends: .build_release
        parallel:
          matrix:
            - SERVICE: [${FORMATTED_SERVICES}]
      build_test:
        extends: .build_test
        parallel:
          matrix:
            - SERVICE: [${FORMATTED_SERVICES}]
      EOF
      fi
  artifacts:
    paths:
      - dynamic_config.yml
  tags:
    - vcproject

trigger_matrix:
  stage: trigger
  <<: *vars_rules
  needs: ["generate_matrix"]
  trigger:
    include:
      - artifact: dynamic_config.yml
        job: generate_matrix
    strategy: depend


default:
  before_script:
    - go version
    - git --version
