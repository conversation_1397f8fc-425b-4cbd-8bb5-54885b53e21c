package event

import (
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/nsqutil/event"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
)

// 剧本创建事件载荷结构
type ScriptCreatedPayload struct {
	ScriptId int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 剧本删除事件载荷结构
type ScriptDeletedPayload struct {
	ScriptId int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// LikeCreatedPayload 点赞创建事件载荷结构
type LikeCreatedPayload struct {
	LikeType svcscript.LikeType `json:"like_type"`
	TargetID int64              `json:"target_id"`
	UserID   int64              `json:"user_id"`
}

// LikeCanceledPayload 点赞取消事件载荷结构
type LikeCanceledPayload struct {
	LikeType svcscript.LikeType `json:"like_type"`
	TargetID int64              `json:"target_id"`
	UserID   int64              `json:"user_id"`
}

// DubbingCreatedPayload 配音创建事件载荷结构
type DubbingCreatedPayload struct {
	ScriptID    int64 `json:"script_id"`
	UserID      int64 `json:"user_id"`
	CharacterID int64 `json:"character_id"`
}

// DubbingDeletedPayload 配音删除事件载荷结构
type DubbingDeletedPayload struct {
	ScriptID    int64 `json:"script_id"`
	UserID      int64 `json:"user_id"`
	CharacterID int64 `json:"character_id"`
}

// 评论创建事件载荷结构
type CommentCreatedPayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 评论删除事件载荷结构
type CommentDeletedPayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 关注剧本事件载荷结构
type ScriptFollowedPayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 取消关注剧本事件载荷结构
type ScriptUnFollowedPayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 分享剧本事件载荷结构
type ScriptSharePayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 剧本浏览事件载荷结构
type ScriptViewedPayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
}

// 剧本话题浏览数
type ScriptTopicViewedPayload struct {
	ScriptID int64 `json:"script_id"`
	UserId   int64 `json:"user_id"`
	TopicID  int64 `json:"topic_id"`
}

// 剧本审核
type ScriptReviewPayload struct {
	ScriptID int64    `json:"script_id"`
	UserId   int64    `json:"user_id"`
	Topics   []string `json:"topics"`
	Cover    string   `json:"cover"`
	Title    string   `json:"title"`
}

// 剧本配音审核
type ScriptReviewDubbingPayload struct {
	ScriptID   int64  `json:"script_id"`
	UserId     int64  `json:"user_id"`
	DubbingId  int64  `json:"dubbing_id"`
	DubbingUrl string `json:"dubbing_url"`
}

// 剧本评论审核
type ScriptReviewCommentPayload struct {
	ScriptID    int64                 `json:"script_id"`
	UserId      int64                 `json:"user_id"`
	CommentId   int64                 `json:"comment_id"`
	ContentType svcscript.ContentType `json:"content_type"`
	Content     string                `json:"content"`
	VoiceUrl    string                `json:"voice_url"`
}

// 生成点赞创建事件
func GenLikeCreatedEvent(likeType svcscript.LikeType, targetID, userID int64) *event.BaseEvent {
	payload := &LikeCreatedPayload{
		LikeType: likeType,
		TargetID: targetID,
		UserID:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeLikeCreated, payload)
}

// 生成点赞取消事件
func GenLikeCanceledEvent(likeType svcscript.LikeType, targetID, userID int64) *event.BaseEvent {
	payload := &LikeCanceledPayload{
		LikeType: likeType,
		TargetID: targetID,
		UserID:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeLikeCanceled, payload)
}

// 生成配音创建事件
func GenDubbingCreatedEvent(scriptID, CharacterID, userID int64) *event.BaseEvent {
	payload := &DubbingCreatedPayload{
		ScriptID:    scriptID,
		CharacterID: CharacterID,
		UserID:      userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeDubbingCreated, payload)
}

// 生成配音删除事件
func GenDubbingDeletedEvent(scriptID, CharacterID, userID int64) *event.BaseEvent {
	payload := &DubbingDeletedPayload{
		ScriptID:    scriptID,
		CharacterID: CharacterID,
		UserID:      userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeDubbingDeleted, payload)
}

// 生成剧本创建事件
func GenScriptCreatedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &ScriptCreatedPayload{
		ScriptId: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptCreated, payload)
}

// 生成剧本删除事件
func GenScriptDeletedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &ScriptDeletedPayload{
		ScriptId: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptDeleted, payload)
}

// 生成评论创建事件
func GenCommentCreatedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &CommentCreatedPayload{
		ScriptID: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeCommentCreated, payload)
}

// 生成评论删除事件
func GenCommentDeletedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &CommentDeletedPayload{
		ScriptID: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeCommentDeleted, payload)
}

// 生成关注剧本事件
func GenScriptFollowedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &ScriptFollowedPayload{
		ScriptID: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptFollowed, payload)
}

// 生成取消关注剧本事件
func GenScriptUnFollowedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &ScriptUnFollowedPayload{
		ScriptID: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptUnFollowed, payload)
}

// 生成分享剧本事件
func GenScriptShareEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &ScriptSharePayload{
		ScriptID: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptShared, payload)
}

// 生成浏览剧本事件
func GenScriptViewedEvent(scriptID, userID int64) *event.BaseEvent {
	payload := &ScriptViewedPayload{
		ScriptID: scriptID,
		UserId:   userID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptViewed, payload)
}

// 生成话题浏览数事件
func GenScriptTopicViewedEvent(scriptID, topicID, userID int64) *event.BaseEvent {
	payload := &ScriptTopicViewedPayload{
		ScriptID: scriptID,
		UserId:   userID,
		TopicID:  topicID,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptTopicViewed, payload)
}

// 生成剧本审核事件
func GenScriptReviewEvent(scriptID, userID int64, topics []string, cover, title string) *event.BaseEvent {
	payload := &ScriptReviewPayload{
		ScriptID: scriptID,
		UserId:   userID,
		Topics:   topics,
		Cover:    cover,
		Title:    title,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptReview, payload)
}

// 生成剧本配音审核事件
func GenScriptReviewDubbingEvent(scriptID, userID, dubbingID int64, dubbingUrl string) *event.BaseEvent {
	payload := &ScriptReviewDubbingPayload{
		ScriptID:   scriptID,
		UserId:     userID,
		DubbingId:  dubbingID,
		DubbingUrl: dubbingUrl,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptReviewDubbing, payload)
}

// 生成剧本评论审核事件
func GenScriptReviewCommentEvent(scriptID, userID, commentID int64, contentType svcscript.ContentType, content, voiceUrl string) *event.BaseEvent {
	payload := &ScriptReviewCommentPayload{
		ScriptID:    scriptID,
		UserId:      userID,
		CommentId:   commentID,
		ContentType: contentType,
		Content:     content,
		VoiceUrl:    voiceUrl,
	}
	return event.GenBaseEvent(OTypeScript, ETypeScriptReviewComment, payload)
}
