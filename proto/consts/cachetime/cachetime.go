package cachetime

import "time"

const (
	FOREVER = time.Duration(0)

	SEC_1  = time.Duration(1 * time.Second)
	SEC_2  = time.Duration(2 * time.Second)
	SEC_3  = time.Duration(3 * time.Second)
	SEC_5  = time.Duration(5 * time.Second)
	SEC_10 = time.Duration(10 * time.Second)
	SEC_20 = time.Duration(20 * time.Second)
	SEC_30 = time.Duration(30 * time.Second)

	MIN_1  = time.Duration(1 * time.Minute)
	MIN_2  = time.Duration(2 * time.Minute)
	MIN_3  = time.Duration(3 * time.Minute)
	MIN_5  = time.Duration(5 * time.Minute)
	MIN_10 = time.Duration(10 * time.Minute)
	MIN_20 = time.Duration(20 * time.Minute)
	MIN_30 = time.Duration(30 * time.Minute)

	HOUR_1  = time.Duration(1 * time.Hour)
	HOUR_2  = time.Duration(2 * time.Hour)
	HOUR_6  = time.Duration(6 * time.Hour)
	HOUR_12 = time.Duration(12 * time.Hour)

	DAY_1 = time.Duration(24 * time.Hour)
	DAY_3 = DAY_1 * 3
	DAY_7 = DAY_1 * 7

	MONTH_1 = DAY_1 * 30
	MONTH_3 = DAY_1 * 90

	YEAR_1 = DAY_1 * 365
)
