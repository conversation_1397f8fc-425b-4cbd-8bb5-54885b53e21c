package cachekey

const (
	UserCoinTransferKey = "user_coin_new:%d"
)

const (
	ConfigList           = "config_list"
	ConfigDict           = "config_dict"
	ConfigItem           = "config_item:%s"
	ConfigChecksum       = "config_checksum"
	ConfigBanner         = "config_banner:%s"
	ConfigVersion        = "config_version:%d:%s:%s"
	MediaDurationConfig  = "video_duration:%s"
	AbStrategy           = "ab_strategy:%d"
	ConfigLiveVersion    = "config_live_version:%d:%s:%s:%s"
	ConfigLiveMaxVersion = "config_live_version_max:%d:%s:%s"

	RechargeProducts = "recharge_products:%d"

	UserPayDiscountList = "user_pay_discount_list:%d"
	UserPayStartLock    = "user_pay_start_lock:%d:%d"
	UserH5PayStartLock  = "user_h5_pay_start_lock:%d:%s"

	WithdrawalProducts = "withdrawal_products"
	WithdrawalAccounts = "withdrawal_accounts:%d"

	AccountState      = "account_state:%s"
	AccountBlockInfo  = "account_block:%d"
	AccountLogin      = "account_login:%s:%s"
	AccountAsset      = "account_asset:%d"
	AccountAssetItems = "account_asset_items:%d"
	AccountCert       = "account_cert:%d"
	AccountUnderage   = "account_underage:%s"

	CertifyStat = "certify_stat:%s:%d:%s"

	AccountNiceidQueue = "account_niceid_queue"
	AccountNiceidLock  = "account_niceid_lock"

	AccountSignupLock = "account_signup_lock:%s"

	AccountWithdrawLock = "account_user_withdraw:%d"

	MemberIcode           = "member_icode:%d"
	MemberInviter         = "member_inviter:%d"
	MemberInvitationChain = "member_invitation_chain:%d"
	MemberInvitationGuild = "member_invitation_guild:%d"
	MemberH5Recharge      = "member_h5_recharge:%d"

	MemberOnline     = "member_online:%d"
	MemberOnlineLock = "member_online_lock:%d"
	MemberOnlineInfo = "member_online_info:%d"
	MemberDetail     = "member_detail:%d"
	MemberTagsKey    = "member_tags:%d"

	MemberOnlineDuration           = "member_online_duration:%d"
	MemberMonthOnlineDurationCache = "member_month_online_duration:%d:%s"

	MemberAlbum          = "member_album:%d"
	MemberLabels         = "member_labels:%d"          //map{"vip":0, realface:0, realname:0}
	MemberRelationTotals = "member_relation_totals:%d" //map{friends:0, followings:0, followers:0}
	MemberFriendList     = "member_friend_list:%d"
	MemberSettings       = "member_settings:%d:%d"
	MemberSettingsLock   = "member_settings_lock:%d"
	MemberVerifyCode     = "member_verify_code:%d:%s"

	MemberDetailReview = "member_detail:review:%d"
	MemberAlbumReview  = "member_album:review:%d"

	MemberFollowingCount = "member_following_count:%d"
	MemberFollowingList  = "member_following_list:%d" // zset 格式
	MemberFollowerCount  = "member_follower_count:%d"
	MemberFollowerList   = "member_follower_list:%d"
	MemberBlackList      = "member_blacklist:%d"

	MemberFemaleQuality        = "member_female_quality:%d"
	MemberFemaleCallCover      = "member_female_call_cover:%d"
	MemberFemaleHot            = "member_female_hot:%d"
	MemberRegister             = "member_register:%d"
	MemberUserInfo             = "member_user_info:%s"
	MemberFemaleCallCoverCount = "member_female_call_cover_count:%d:%s"
	MemberVideoWatchCount      = "member_video_watch_count:%d:%s"

	MemberNewAppLogin = "member_new_app_login_%d"

	UserActionLock  = "user_action_lock:%s:%d"
	OrderActionLock = "order_action_lock:%s:%s"

	UserFeedbackTotal = "user_feedback_total:%d:%s"
	UserComplainTotal = "user_complain_total:%d:%s"

	UserPushInfo = "user_push_info:%d"

	RelationRemark = "user_relation_remark:%d-%d"

	CoverViewedBloom    = "cover_viewed:%s"    // time
	CoverViewedCacheKey = "cover_viewed:%d:%s" // userid:cover

	UserSendReqLimiter = "user_send_req_limit:%d"

	UserChatGreetMsg                = "user_chat_greet_msg:%d"
	UserChatCurSeqid                = "user_chat_seqid_cur:%d"
	UserChatMaxSeqid                = "user_chat_seqid_max:%d"
	UserChatSession                 = "user_chat_session:%d:%d"
	UserChatSessionMsgCount         = "user_chat_session_msg_count:%d:%d"
	UserChatGreet                   = "user_chat_greet:%s:%d"
	UserChatGreetCheck              = "user_chat_greet_check:%d:%d"
	UserChatFemaleStartSessionLimit = "user_chat_female_start_session:%d:%s"
	UserChatStartSessionRateLimit   = "user_chat_start_session_rate_limit:%d:%s"
	UserFirstEnterSession           = "user_first_enter_session:%d:%d"

	UserBatchMsgMaxSeq  = "user_batch_msg_seq:%d"
	UserBatchMsgLockKey = "user_batch_msg_lock:%d"

	UserChatMsgInfo   = "user_chat_msg_info:%d:%d"
	UserChatMsgStates = "user_chat_msg_states:%d"

	ChatTodaySweetAdded = "chat:sweet_added:%s:%d"

	ChatFeeFreeCallReward   = "chat_fee:free_call_reward:%d"
	ChatFeeTimeoutQueue     = "chat_fee:chat_timeout_rollback_queue"
	ChatFeeTimeoutJobLock   = "chat_fee:chat_timeout_job_lock"
	CallMatchFemalePriority = "chat_match_female_priority:%d"

	UserChatCommonTerms = "user_chat_common_terms:%d"
	SystemCommonTerms   = "sys_common_terms"

	ChatSessionFeeInfo = "chat_session_fee:%d" // sid

	ChatLastTimeSmall = "chat_last_time:%d:%d:small"
	ChatLastTimeBig   = "chat_last_time:%d:%d:big"
	ChattedCacheKey   = "chat_last_time:%s"

	ChatStatusCache = "chat_status_cache:%d"

	UserChatQuickMenu             = "chat_quick_menu:%d" // sex
	ChatLoveHouse                 = "chat_love_house:%d:%d"
	LoveHouseMiss                 = "chat_love_house_miss:%d:%d"
	ChatUserSessionStart          = "chat_love_house_daily_tips:%d"
	LoveHouseInUseAsset           = "chat_love_house_inuse:%d:%d"
	LovehousePatchVideoSigninPush = "chat_love_house_patch:%d:%d"

	// chat stats
	UserNewChatSource = "new_chat_source_cache:%d:%d"
	UserOldChatSet    = "user_old_chat:%d"
	UserNewChatSet    = "user_new_chat:%d"

	MomentInfoKey          = "svcmoment:moment_info:%d"
	MomentStarCountKey     = "svcmoment:star_count:%d"
	MomentCommentsCountKey = "svcmoment:comment_count:%d"

	MomentCommentCacheKey    = "svcmoment:comment_info:%d" // hash
	MomentCommentListKey     = "svcmoment:comment_list:%d"
	CommentAllInCacheFlagKey = "svcmoment:comment_all_in_cache:%d"

	UserFollowingFeedCache          = "svcmember:user_following_feed:%d"
	UserFollowingFeedAllInCacheFlag = "svcmember:user_following_feed_all_in:%d"

	UserMomentCacheKey       = "svcmoment:user_moments:%d"
	UserMomentAllInCacheFlag = "svcmoment:user_moments_aic:%d"

	RecommendMomentForMaleKey  = "svcmoment:user_recommend:male"
	RecommendMomentForFemalKey = "svcmoment:user_recommend:female"

	UserMomentStarKey = "moment:star:%d"

	GiftInfo = "gift_info:%d"
	GiftList = "gift_list"
	GiftBag  = "gift_bag:%d"

	GiftOrderNew = "{home_giftorder_new}"
	GiftOrderOld = "{home_giftorder_old}"
	GiftOrderKey = "gift_order_%d"

	AccessoryInfo = "accessory_info:%d_%s"
	UserAccessory = "user_accessory:%d:%d"

	ActivityTaskInfo      = "activity_task_info:%d:%d"
	ActivityTaskList      = "activity_task_list:%d:%d"
	ActivityUserTaskState = "activity_user_state:%d:%d_%s"
	ActivitySigninAward   = "activity_signin_award:%d:%d"
	ActivitySigninDay     = "activity_signin_day:%d"

	ActivityLovehouseTaskInfo         = "activity_lovehouse_task_info:%d:%d"
	ActivityLovehouseTaskList         = "activity_lovehouse_task_list:%d:%d"
	ActivityLovehouseTaskState        = "activity_lovehouse_state:%d_%d:%d_%s"
	ActivityLovehouseTypeTasksAward   = "activity_lovehouse_type_tasks_award:%d"
	ActivityLovehouseTypeTasksAwardId = "activity_lovehouse_type_tasks_award_id:%d"
	ActivityLovehouseSignin           = "activity_lovehouse_signin:%d_%d:%s"
	ActivityLovehouseVideoSignin      = "activity_lovehouse_video_signin:%d_%d"
	ActivityLovehouseUserBox          = "activity_lovehouse_user_box:%d_%d"

	ChatBagExistCacheKey       = "chat_bag_exist:%d:%d"
	ChatBagCountCacheKey       = "chat_bag_count:%d:%d"
	ChatBagUserFirstTriggerKey = "chat_bag_first_trigger:%d"
	ChatBagTypeCacheKey        = "chat_bag_type:%d"
	ChatBagConfigKey           = "chat_bag_by_id:%d"

	ActivityLatest                    = "activity_latest"
	ActivityInfo                      = "activity_info:%s"
	ActivityRecommends                = "activity_recommends:%d"
	ActivityList                      = "activity_list"
	ActivityNotice                    = "activity_notice:%d"
	ActivityGiftCollectRank           = "activity_gift_collect_rank:%d"
	ActivityGiftCollectUser           = "activity_gift_collect_user:%d"
	ActivityCallSingleUserMaxAwardKey = "activity_user_max_award:%d_%d"
	ActivityCallSingleMaxAwardKey     = "activity_max_award:%d"
	ActivityLoveCardUserBox           = "activity_love_card_box:%d"
	ActivityLoveCardAwards            = "activity_love_card_awards:%d"
	ActivityLoveCardAutoRecvAwards    = "activity_love_card_auto_recv_awards:%d"
	ActivityLoveCardAwardLockKey      = "activity_love_card_award_lock:%d"

	ActivityJackpotTaskInfo = "activity_jackpot_task_info:%s:%d"
	ActivityJackpotTaskList = "activity_jackpot_task_list:%s:%d"
	ActivityJackpotTaskState
	JackpotTaskAwardLock = "jackpot_task_award_lock:%s:%d"
	JackpotOpen          = "jackpot_open:%s:%s"
	JackpotInfo          = "jackpot_info:%s:%s"
	JackpotUser          = "jackpot_user:%s:%d"
	JackpotHistories     = "jackpot_histories:%s"
	JackpotRanking       = "jackpot_ranking:%s:%s:%d"

	ActivityUserCharmInfo       = "activity_user_charm:%d"
	ActivityUserCharmPercentile = "{activity_user_percentile}"

	UserLotteryChance = "user_lottery_chance:%d:%d"
	LotteryDaily      = "lottery_daily:%s"
	LotteryPlayLock   = "lottery_play_lock:%d:%d"

	CallCacheKey                   = "svcchat_call"
	CallMatchedCountKey            = "call_matched_count:%d"
	CallMatchKey                   = "call_match:%d"
	CallMatchCancelKey             = "call_match_cancel:%d"
	CallMatchVideoFreeMinKey       = "call_match:video_free_min:%d"
	CallMatchedSuccessKey          = "call_matched_success:%d"
	CallMatchedDurationDayKey      = "call_matched_duration:%d"
	CallJoinCnameKey               = "call_join_cname:%s_%d"
	CallVideoForbidKey             = "call_video_forbid:%d"
	CallOperateCnameKey            = "call_operate_cname:%s"
	CallHongbaoAwardKey            = "call_hongbao_award:%d"
	CallSingleHongbaoAwardKey      = "call_single_hongbao_award:%s"
	CallSingleAwardNextDurationKey = "call_single_award_next_duration:%s"
	CallSingleEndDurationKey       = "call_single_end_duration:%s"
	CallHongbaoAwardActionKey      = "call_hongbao_award_action:%s_%d"
	CallHongbaoAwardBroadcast      = "call_hongbao_award_broadcast"

	MatchOnlineUserListBaseKey = "match_online_user_list_base"

	MaleCallMatchQueue        = "call_match_queue"
	MaleCallMatchTimeoutQueue = "call_match_timeout"
	ChannelAcceptKey          = "call_match_accept_channel_%d"
	UserCallState             = "call_state_%d"

	CallSubFeeLockKey       = "svcchat_call_subfee:%d"         // sid
	CallSubFeeChannelEndKey = "svcchat_call_subfee_channel:%s" // channel

	OfflineMsgCacheFlagKey = "svcchat_offline_msg_flag:%d"
	OfflineMsgListKey      = "svcchat_offline_msg_zset:%d"
	OfflineMsgInfoKey      = "svcchat_offline_msg_info:%d" // hash
	ChatUserFrontCache     = "svcchat_user_front_cache:%d"
	UserCalledCacheKey     = "svcchat_user_called:%s:%d:%d"
	UserNewChatOverheating = "svcchat_user_new_chat_overheating:%d"

	OnlineGradeFemales          = "online_grade_females"
	OnlineGradeWhiteListFemales = "online_grade_whitelist_females"

	/*************** admin begin ****************/
	AdminLoginToken = "admin_login_token:%s"
	AdminUserToken  = "admin_user_token:%d"

	/*************** admin end ****************/

	/*************** partner begin ****************/
	PartnerLoginToken = "partner_login_token:%s:%s"
	PartnerUserToken  = "partner_user_token:%s:%d"

	/*************** partner end ****************/

	HomeDayFirstTime           = "home:day_first:%d" // 当日首次进入首页或消息略表
	HomeDayFirstConcurrenceKey = "home:day_first:concurrence:%d"

	//  ------------activity
	HongbaoAwardTimeoutQueue   = "hongbao_award_queue"
	HongbaoAwardTimeoutTaskKey = "hongbao_award_timeout_task"

	// limit
	UserPublishMomentLimitKey    = "user_publish_limit_key:%d"
	UserPublishCommentLimitKey   = "user_comment_limit_key:%d"
	UserDailyFeedbackLimitKey    = "user_feedback_limit_key:%d"
	DailyAvatarChangeLimitKey    = "user_avatar_change_key:%d"
	DailyNicknameChangeLimitKey  = "user_nickname_change_key:%d"
	DailyLovewordsChangeLimitKey = "user_lovewords_change_key:%d"
	DailyFieldChangeLimitKey     = "user_field_change_key:%d"
	UserFollowLimitKey           = "user_follow_limit_key:%d"

	MaleSecretaryRemindFemale = "male_secretary_remind_female:%d:%d"
	MaleSecretaryRemindFlag   = "male_secretary_remind_flag:%d:%s"

	//
	MatchLastKey         = "day_match_last:%d"
	MatchIntervalKey     = "day_match_interval:%d" //在线半小时内是否推送了
	MatchOpenTimeKey     = "day_match_open_time:%d"
	MatchOpenTimeAppKey  = "day_match_open_time:%d_%d"
	MatchPeakClippingKey = "day_match_peak_clipping:%d"
	MatchMsgCountKey     = "day_match_msg_count:%s"

	// new_match
	UserMatchLevelKey   = "recommend:user_match_level:%d" // user match level
	UserHourMaxScoreKey = "recommend:user_hour_score:%d"
	UserCalLevelKey     = "recommend:user_cal_level:%d"

	// match_base_efficiency
	UserSynthesizeScoreKey      = "recommend:user_synthesize_score:%d"
	UserCalSynthesizeScoreCDKey = "recommend:cal_synthesize_score_cd:%d"
	UserSysAccostNumKey         = "recommend:sys_accost_num:%d"

	BigAgeMatch = "big_age_match_count"

	UserMonitorData = "user_monitor_data:%d"
	PeerMonitorData = "peer_monitor_data:%s"

	AppSlaveUserMonitorData = "user_monitor_data:%s:%d"
	AppSlavePeerMonitorData = "peer_monitor_data:%s:%s"

	AdvertVivoTokenData = "advert_vivo_token_data:%s"
	AdvertVivoTokenLock = "advert_vivo_token_lock:%s"

	AdvertOceanEngineTokenData  = "advert_oceanengine_token_data:%d"
	AdvertOceanEngineTokenLock  = "advert_oceanengine_token_lock:%d"
	AdvertOceanEngineReportLock = "advert_oceanengine_report_lock:%d"
	AdvertOceanEngineDMPLock    = "advert_oceanengine_dmp_lock"

	// sdk h5 user has chat today
	UserSDkH5HasChatKey  = "user_h5_has_chat:%d"
	UserAppMaleChatTsKey = "user_app_male_chat:%d"
	UserFemaleChatTsKey  = "user_female_chat:%d"

	// stat active online user
	UserAppOnlineStatKey = "user_app_online_stat:%d:%d"
	UserWebOnlineStatKey = "user_web_online_stat:%d:%d"
	UserOnlineStatKey    = "user_online_stat:%d"

	// stats
	UserActiveMarker = "stats_user_active_marker"

	UserNoSendMsgKey = "user_no_send_msg:%d"

	UserLovehouseActionKey       = "lovehouse_action_lock:%s:%d_%d"
	UserLovehouseActionPersonKey = "lovehouse_person_action_lock:%s:%d"

	// recall
	RecallAwardKey       = "recall_award:%d"
	RecallAwardActionKey = "recall_award_lock:%d_%d"

	// bag
	UserBagReddotKey = "bag_reddot:%d"

	//browse
	UserBrowseKey      = "user_browse:%d"
	UserBrowseMeKey    = "user_browse_me:%d"
	UserBrowseOtherKey = "user_browse_other:%d"

	// pay
	PayRefreshWxAccessTokenKey = "refresh_wx_access_token_lock"

	StatUserScoreIncome = "stat_user_score_income:%d:%s"

	PayIosSMS = "pay_ios_sms:%d:%s"
)
