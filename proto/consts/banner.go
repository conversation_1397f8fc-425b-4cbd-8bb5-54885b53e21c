package consts

const (
	BannerMessageTop = "message_top"
	RechargeTop      = "recharge_top"
	GiftCardTop      = "gift_card_top"
)

// 1 关闭消息页banner
// 2 关闭充值页banner
// 3 关闭消息页banner、关闭充值页banner
// 4 关闭礼物banner
// 5  关闭消息页banner、 关闭礼物banner
// 6  关闭充值页banner、关闭礼物banner
// 7 关闭消息页banner、关闭充值页banner、关闭礼物banner
type BannerCloseStatus int32

const (
	BannerCloseMessageTop  BannerCloseStatus = 1 << iota //bit1 关闭消息页banner
	BannerCloseRechargeTop                               //bit2 关闭充值页banner
	BannerCloseGiftCardTop                               //bit3 关闭礼物banner
)

func (s BannerCloseStatus) IsCloseMessageTop() bool {
	return s&BannerCloseMessageTop != 0
}

func (s BannerCloseStatus) IsCloseRechargeTop() bool {
	return s&BannerCloseRechargeTop != 0
}

func (s BannerCloseStatus) IsCloseGiftCardTop() bool {
	return s&BannerCloseGiftCardTop != 0
}

type BannerClose struct {
	// Ios           BannerCloseStatus `json:"ios"`
	// Android       BannerCloseStatus `json:"android"`
	IosMale       BannerCloseStatus `json:"ios_male"`
	IosFemale     BannerCloseStatus `json:"ios_female"`
	AndroidMale   BannerCloseStatus `json:"android_male"`
	AndroidFemale BannerCloseStatus `json:"android_female"`
}
