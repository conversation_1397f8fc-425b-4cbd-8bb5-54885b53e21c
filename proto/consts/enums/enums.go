package enums

import "fmt"

type UserSex int32

const (
	UserSexUnknown UserSex = 0
	UserSexMale    UserSex = 1
	UserSexFemale  UserSex = 2
)

func (us UserSex) Int32() int32 {
	return int32(us)
}

type UserStatus int32

const (
	UserStatusOK     UserStatus = 1
	UserStatusBlock  UserStatus = 2
	UserStatusCancel UserStatus = 3
)

func (us UserStatus) Int32() int32 {
	return int32(us)
}

type AssetCurrency int

const (
	AssetCurrencyBaseCoinInvalid AssetCurrency = 10 // 金币
	AssetCurrencyFreeCoinInvalid AssetCurrency = 11 // 赠送金币

	AssetCurrencyBaseCoin AssetCurrency = 15 // 金币
	AssetCurrencyFreeCoin AssetCurrency = 16 // 赠送金币

	AssetCurrencyChatCard      AssetCurrency = 12 // 普通聊天卡
	AssetCurrencyChatCardHello AssetCurrency = 13 // 打招呼聊天卡

	AssetCurrencyScore AssetCurrency = 20 //积分

)

const (
	AccountCancelActionQuery   = 0
	AccountCancelActionApply   = 1
	AccountCancelActionRevoke  = 2
	AccountCancelActionConfirm = 3

	AccountCancelStatusUndo     = 0
	AccountCancelStatusApplying = 1
	AccountCancelStatusDone     = 2
)

type ChatMsgState int

const (
	ChatMsgState_new ChatMsgState = 0

	//收到A消息，并应答A
	ChatMsgState_received ChatMsgState = 1

	//转发到B后，未收到B应答之前
	ChatMsgState_forwarding ChatMsgState = 2

	//收到B应答后
	ChatMsgState_forwarded ChatMsgState = 3

	//通知A消息投递成功后
	ChatMsgState_finished ChatMsgState = 3
)

const (
	ChatFeeTypeCoin           int32 = 1
	ChatFeeTypeChatCard       int32 = 2 // 普通聊天卡
	ChatFeeTypeFreeCall       int32 = 3 // 免费赠送的通话
	ChatFeeTypeChatCardHello  int32 = 4 // 打招呼聊天卡
	ChatFeeTypeAwardVideoCall int32 = 5
)

const (
	ChatCard      = 0
	ChatCardHello = 1
)

const (
	ChatTypeMessage   int32 = 1
	ChatTypeVoiceCall int32 = 2
	ChatTypeVideoCall int32 = 3
	ChatTypeAccost    int32 = 4
)

const (
	PayTypeNone        int32 = 0
	PayTypeAliPay      int32 = 1
	PayTypeWxPay       int32 = 2
	PayTypeIapPay      int32 = 3
	PayTypePlatformPay int32 = 4
)

type PaymentOrderStatus int

const (
	PaymentOrderStatusWaiting  PaymentOrderStatus = 1
	PaymentOrderStatusFinished PaymentOrderStatus = 2
	PaymentOrderStatusFailed   PaymentOrderStatus = 3
	PaymentOrderStatusRefunded PaymentOrderStatus = 4
	PaymentOrderStatusCanceled PaymentOrderStatus = 5
)

const (
	GiftOrderSourceSys = "gift_sys"
	GiftOrderSourceBag = "gift_bag"

	GiftOrderTypeConsume = 1
	GiftOrderTypeBag     = 2
)

const (
	AccessoryTypeChatPop     int32 = 1
	AccessoryTypeHeadBox     int32 = 2
	AccessoryTypeEnterEffect int32 = 3
	AccessoryTypeVehicle     int32 = 4
	AccessoryTypeBadge       int32 = 5
)

type AwardType int32

const (
	AwardTypeCoin          AwardType = 1
	AwardTypeScore         AwardType = 2
	AwardTypeGift          AwardType = 3
	AwardTypeAccessory     AwardType = 4
	AwardTypeChatCard      AwardType = 5
	AwardTypeVideoCall     AwardType = 6 // 视频卡
	AwardTypeCoinHongbao   AwardType = 7 // 金币红包
	AwardTypeChatCardHello AwardType = 8
	AwardTypePayDiscount   AwardType = 10 //充值折扣
)

func (t AwardType) Value() int32 {
	return int32(t)
}

func (t AwardType) Invalid() bool {
	switch t {
	case AwardTypeCoin,
		AwardTypeScore,
		AwardTypeGift,
		AwardTypeAccessory,
		AwardTypeChatCard,
		AwardTypeChatCardHello,
		AwardTypeVideoCall:
		return false
	default:
		return true
	}
}

func (t AwardType) Icon() string {
	switch t {
	case AwardTypeCoin:
		return "201468588"
	case AwardTypeScore:
		return "201468587"
	case AwardTypeGift:
		return ""
	case AwardTypeAccessory:
		return ""
	case AwardTypeChatCard:
		return "201468591"
	case AwardTypeVideoCall:
		return "201468591"
	case AwardTypeChatCardHello:
		return ""
	default:
		return ""
	}
}

const (
	ReviewTypeUserAvatar    = 1
	ReviewTypeUserNickname  = 2
	ReviewTypeUserLovewords = 3

	ReviewTypeChatText  = 4
	ReviewTypeChatImage = 5
	ReviewTypeChatVoice = 6
	ReviewTypeChatVideo = 7
	ReviewTypeVoiceCall = 8
	ReviewTypeVideoCall = 9

	ReviewTypeMomentText  = 10
	ReviewTypeMomentImage = 11
	ReviewTypeMomentVideo = 12
)

const (
	RegisterMaleUserToNewUserTime = 7 * 24 * 3600 //男用户注册15天内是新用户
)

const (
	RechargeProductTagFirst = "first"
	RechargeProductTagLast  = "last"
	RechargeProductTagSuper = "super"
)

func GetRechargeProductTagRank(s string) int {
	return map[string]int{
		RechargeProductTagSuper: 3,
		RechargeProductTagFirst: 2,
	}[s]
}

const (
	MemberFieldAvatar      = "avatar"
	MemberFieldNickname    = "nickname"
	MemberFieldBirthday    = "birthday"
	MemberFieldLovewords   = "lovewords"
	MemberFieldSignVoice   = "sign_voice"
	MemberFieldHometown    = "hometown"
	MemberFieldCareer      = "career"
	MemberFieldHeight      = "height"
	MemberFieldWeight      = "weight"
	MemberFieldIncome      = "income"
	MemberFieldEducation   = "education"
	MemberFieldMarital     = "marital_status"
	MemberFieldCouplesSnap = "couples_snap"
)

var (
	updateMemberFields = map[string]interface{}{
		MemberFieldAvatar:    "", //头像 string
		MemberFieldNickname:  "", //昵称 string
		MemberFieldBirthday:  "", //生日 string
		MemberFieldLovewords: "", //爱情宣言 string
		MemberFieldSignVoice: "", //语音签名 svcmember.SignVoiceData
		MemberFieldHometown:  "", //家乡 common.IdNameItem
		MemberFieldCareer:    "", //职业 common.IdNameItem
		MemberFieldHeight:    "", ////身高 厘米
		MemberFieldWeight:    "", //体重 KG
		MemberFieldIncome:    "", //年收入 万元
		MemberFieldEducation: "", // 学历
		MemberFieldMarital:   "", // 婚姻状态
	}
)

func CheckUpdateMemberField(field string) bool {
	if _, ok := updateMemberFields[field]; ok {
		return true
	}
	return false
}

const (
	InviterTypeGuild = 1
	InviterTypeUser  = 2

	InviterGuildPrefix = "G"
	InviterUserPrefix  = "U"
	InviterUserDefault = "U0"
)

const (
	// 赠送道具默认有效期 比如视频卡 充值优惠券
	SendPropExpireDuration = 15 * 86400
)

const EditorFieldPrefix = "editor:"

func AddEditorPrefix(fieldName string) string {
	return fmt.Sprintf("%s%s", EditorFieldPrefix, fieldName)
}
