package consts

import (
	"xim/proto/consts/enums"
)

// 划卡 需求文档 https://shimo.im/docs/m5kv9GoaoQcz6bqX
const (
	HuakaCountMaleDayDefault   = 40 //男用户每日默认可划卡XX次
	HuakaCountFemaleDayDefault = 30 //女用户每日默认可划卡XX次
	HuakaSucceedMaxCount       = 3  //自然日男用户最多可匹配成功X次

	HuakaCountUploadRealAvatar = 10 //上传头像可以再获得XX次划卡次数
	HuakaCountRealface         = 15 //真人认证可以再获得XX次划卡次数

	HuakaColdBootCount        = 0  //自然日前X次划卡冷启动
	HuakaLikeMatchProbDefault = 10 //每次划卡互相喜欢成功率
	HuakaLikeMatchProbDelta   = 50 //每次划卡喜欢增加成功率

	HuakaPopupNumMin = 3 //
	HuakaPopupNumMax = 6 //

	HuakaSuccessFreeChatCardNum = 10 //划卡匹配成功赠送免费聊天卡数量
)

func GetHuakaCountMaleDayDefault() int   { return HuakaCountMaleDayDefault }
func GetHuakaCountFemaleDayDefault() int { return HuakaCountFemaleDayDefault }
func GetHuakaCountUploadRealAvatar() int { return HuakaCountUploadRealAvatar }
func GetHuakaCountRealface() int         { return HuakaCountRealface }
func GetHuakaSucceedMaxCount() int       { return HuakaSucceedMaxCount }
func GetHuakaColdBootCount() int         { return HuakaColdBootCount }
func GetHuakaLikeMatchProbDefault() int  { return HuakaLikeMatchProbDefault }
func GetHuakaLikeMatchProbDelta() int    { return HuakaLikeMatchProbDelta }
func GetHuakaCountDefaultBySex(sex int32) int {
	if sex == enums.UserSexFemale.Int32() {
		return GetHuakaCountFemaleDayDefault()
	} else if sex == enums.UserSexMale.Int32() {
		return GetHuakaCountMaleDayDefault()
	}
	return 0
}

func GetUploadRealAvatarHuakaCount(sex int32, source string) int {
	if sex != enums.UserSexMale.Int32() {
		return 0
	}

	return GetHuakaCountUploadRealAvatar()
}

func GetRealfaceCountHuakaCount(sex int32, source string) int {
	if sex != enums.UserSexMale.Int32() {
		return 0
	}
	return GetHuakaCountRealface()
}
