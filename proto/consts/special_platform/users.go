package special_platform

import "xim/baselib/server/env"

var (
	UserList []int64
	Users    = map[int64]bool{
		4584753: true,
		4586000: true,
		4587143: true,
		4587460: true,
		4587887: true,
		4588386: true,
		4588923: true,
		4589902: true,
		4589953: true,
		4590328: true,
		4590510: true,
		4590635: true,
		4590660: true,
		4590787: true,
		4590828: true,
		4590880: true,
		4591698: true,
		4591756: true,
		4592531: true,
		4592887: true,
		4594802: true,
		4594893: true,
		4595353: true,
		4595390: true,
		4595571: true,
		4595684: true,
		4596028: true,
		4596043: true,
		4596705: true,
		4596866: true,
		4597376: true,
		4597706: true,
		4597754: true,
		4597784: true,
		4598066: true,
		4598164: true,
		4598381: true,
		4598562: true,
		4599047: true,
		4599254: true,
		4599335: true,
		4599378: true,
		4599421: true,
		4599617: true,
		4599677: true,
		4599893: true,
		4600003: true,
		4600208: true,
		4600250: true,
		4600452: true,
		4600584: true,
		4600661: true,
		4600881: true,
		4600989: true,
		4601050: true,
		4601304: true,
		4601419: true,
		4601761: true,
		4601803: true,
		4602000: true,
		4602156: true,
		4602213: true,
		4602593: true,
		4602621: true,
		4602689: true,
		4603214: true,
		4603230: true,
		4603369: true,
		4603579: true,
		4603771: true,
		4603936: true,
		4603993: true,
		4604000: true,
		4604155: true,
		4604496: true,
		4604498: true,
		4604684: true,
		4604952: true,
		4605026: true,
		4605109: true,
		4605261: true,
		4605283: true,
		4605350: true,
		4605507: true,
		4605683: true,
		4605847: true,
		4606011: true,
		4607159: true,
		4607177: true,
		4607418: true,
		4607707: true,
		4607770: true,
		4607937: true,
		4608570: true,
		4608871: true,
		4609008: true,
		4609574: true,
		4609652: true,
		4610043: true,
		4610483: true,
		4610741: true,
		4610881: true,
		4610933: true,
		4610965: true,
		4611091: true,
		4611533: true,
		4611641: true,
		4612116: true,
		4612279: true,
		4612471: true,
		4612857: true,
		4612903: true,
		4612976: true,
		4613018: true,
		4613180: true,
		4613185: true,
		4613865: true,
		4613962: true,
		4613970: true,
		4613977: true,
		4613978: true,
		4614017: true,
		4614038: true,
		4614173: true,
		4614201: true,
		4614204: true,
		4614318: true,
		4614405: true,
		4614556: true,
		4614647: true,
		4614687: true,
		4614689: true,
		4614761: true,
		4614803: true,
		4614863: true,
		4615202: true,
		4615219: true,
		4615266: true,
		4615285: true,
		4615296: true,
		4615537: true,
		4615584: true,
		4615605: true,
		4615693: true,
		4616058: true,
		4616476: true,
		4616528: true,
		4617083: true,
		4617364: true,
		4617443: true,
		4618035: true,
		4618326: true,
		4618858: true,
		4619095: true,
		4619723: true,
		4620128: true,
		4620241: true,
		4620372: true,
		4620456: true,
		4620635: true,
		4620724: true,
		4620834: true,
		4621031: true,
		4621072: true,
		4621568: true,
		4621800: true,
		4621902: true,
		4621955: true,
		4622482: true,
		4622701: true,
		4623343: true,
		4623484: true,
		4623694: true,
		4624237: true,
		4625523: true,
		4626370: true,
		4626469: true,
		4627583: true,
		4627732: true,
		4628222: true,
		4629536: true,
		4629960: true,
		4630072: true,
		4630682: true,
		4630934: true,
		4632911: true,
		4632937: true,
		4633332: true,
		4633510: true,
		4636188: true,
		4637119: true,
		4638006: true,
		4638258: true,
		4638310: true,
		4639021: true,
		4639361: true,
		4639980: true,
		4640298: true,
		4640844: true,
		4641136: true,
		4641471: true,
		4641970: true,
		4642652: true,
		4644648: true,
		4644993: true,
		4645812: true,
		4646252: true,
		4646772: true,
		4647035: true,
		4648178: true,
		4649498: true,
		4650137: true,
		4650213: true,
		4650297: true,
		4650611: true,
		4650811: true,
		4650865: true,
		4651083: true,
		4652820: true,
		4653601: true,
		4653628: true,
		4653987: true,
		4654206: true,
		4655748: true,
		4656146: true,
		4656219: true,
		4656366: true,
		4656533: true,
		4656568: true,
		4656615: true,
		4656816: true,
		4657370: true,
		4657792: true,
		4658792: true,
		4658931: true,
		4660064: true,
		4660156: true,
		4660299: true,
		4660400: true,
		4660414: true,
		4660448: true,
		4660751: true,
		4661108: true,
		4661211: true,
		4661881: true,
		4663009: true,
		4663060: true,
		4663492: true,
		4663565: true,
		4664075: true,
		4664140: true,
		4664313: true,
		4664889: true,
		4665858: true,
		4666113: true,
		4666328: true,
		4668068: true,
		4669021: true,
		4669335: true,
		4670017: true,
		4670410: true,
		4671047: true,
		4671053: true,
		4671923: true,
		4672950: true,
		4674199: true,
		4674259: true,
		4674847: true,
		4674870: true,
		4676536: true,
		4678230: true,
		4678724: true,
		4679457: true,
		4680571: true,
		4682043: true,
		4683142: true,
		4684007: true,
		4684034: true,
		4684083: true,
		4684720: true,
		4685078: true,
		4686050: true,
		4686482: true,
		4687455: true,
		4688058: true,
		4688471: true,
		4690459: true,
		4690831: true,
		4691230: true,
		4691776: true,
		4692091: true,
		4692215: true,
		4692313: true,
		4692362: true,
		4692874: true,
		4693310: true,
		4693545: true,
		4694504: true,
		4695164: true,
		4695207: true,
		4695270: true,
		4696029: true,
		4696066: true,
		4697791: true,
		4698440: true,
		4699539: true,
		4699909: true,
		4700434: true,
		4700779: true,
		4700812: true,
		4701093: true,
		4701160: true,
		4701549: true,
		4701747: true,
		4702900: true,
		4703216: true,
		4704353: true,
		4705152: true,
		4705156: true,
		4705558: true,
		4705702: true,
		4706898: true,
		4707013: true,
		4707377: true,
		4707694: true,
		4707866: true,
		4708664: true,
		4709037: true,
		4709466: true,
		4709817: true,
		4710147: true,
		4710199: true,
		4710220: true,
		4710292: true,
		4711143: true,
		4711248: true,
		4711377: true,
		4711753: true,
		4711936: true,
		4712017: true,
		4712112: true,
		4712141: true,
		4712622: true,
		4713094: true,
		4713609: true,
		4713695: true,
		4713741: true,
		4714928: true,
		4715073: true,
		4715358: true,
		4715376: true,
		4715960: true,
		4716166: true,
		4716175: true,
		4716393: true,
		4716488: true,
		4716921: true,
		4717296: true,
		4717502: true,
		4717929: true,
		4718262: true,
		4718889: true,
		4719649: true,
		4719652: true,
		4723455: true,
		4723591: true,
		4724463: true,
		4727982: true,
		4730217: true,
		4730614: true,
		4731624: true,
		4731948: true,
		4733282: true,
		4733485: true,
		4733590: true,
		4733850: true,
		4734279: true,
		4734829: true,
		4736004: true,
		4736087: true,
		4736173: true,
		4736409: true,
		4737355: true,
		4737643: true,
		4737897: true,
		4738019: true,
		4738219: true,
		4738220: true,
		4738626: true,
		4738710: true,
		4738724: true,
		4738895: true,
		4739089: true,
		4739840: true,
		4740210: true,
		4740750: true,
		4741113: true,
		4741126: true,
		4741238: true,
		4741845: true,
		4742051: true,
		4742248: true,
		4742419: true,
		4743491: true,
		4743550: true,
		4743643: true,
		4744027: true,
		4744044: true,
		4744163: true,
		4744285: true,
		4744874: true,
		4745254: true,
		4745435: true,
		4745624: true,
		4745935: true,
		4746258: true,
		4746443: true,
		4746912: true,
		4747575: true,
		4748101: true,
		4748309: true,
		4748740: true,
		4749148: true,
		4749230: true,
		4749478: true,
		4749533: true,
		4749655: true,
		4749702: true,
		4750015: true,
		4750222: true,
		4750516: true,
		4750899: true,
		4751113: true,
		4751164: true,
		4751625: true,
		4752084: true,
		4752173: true,
		4752386: true,
		4752645: true,
		4753424: true,
		4753629: true,
		4753785: true,
		4754333: true,
		4754651: true,
		4755183: true,
		4755520: true,
		4756264: true,
		4757241: true,
		4757778: true,
		4758311: true,
		4758420: true,
		4758474: true,
		4759055: true,
		4759767: true,
		4759784: true,
		4760147: true,
		4761071: true,
		4761654: true,
		4761738: true,
		4763119: true,
		4763605: true,
		4763950: true,
		4764051: true,
		4765416: true,
		4765885: true,
		4766364: true,
		4766889: true,
		4767172: true,
		4767574: true,
		4768913: true,
		4769018: true,
		4769180: true,
		4769515: true,
		4770047: true,
		4770692: true,
		4770775: true,
		4771096: true,
		4771357: true,
		4771725: true,
		4771813: true,
		4771819: true,
		4772014: true,
		4772231: true,
		4772388: true,
		4772720: true,
		4772762: true,
		4773064: true,
		4773238: true,
		4773430: true,
		4773818: true,
		4774336: true,
		4775109: true,
		4775208: true,
		4775215: true,
		4775917: true,
		4776183: true,
		4776198: true,
		4776252: true,
		4776455: true,
		4776544: true,
		4776807: true,
		4777056: true,
		4777267: true,
		4777305: true,
		4777434: true,
		4777802: true,
		4777989: true,
		4778390: true,
		4779016: true,
		4779410: true,
		4779529: true,
		4779546: true,
		4779720: true,
		4779726: true,
		4779768: true,
		4780903: true,
		4781627: true,
		4781633: true,
		4782476: true,
		4783230: true,
		4783555: true,
		4783881: true,
		4783882: true,
		4784185: true,
		4784343: true,
		4784376: true,
		4784564: true,
		4784706: true,
		4784884: true,
		4784905: true,
		4785132: true,
		4785260: true,
		4785274: true,
		4785302: true,
		4785303: true,
		4785829: true,
		4785995: true,
		4786063: true,
		4786147: true,
		4786904: true,
		4786957: true,
		4786987: true,
		4787109: true,
		4787344: true,
		4787802: true,
		4788916: true,
		4789674: true,
		4789956: true,
		4790219: true,
		4790820: true,
		4791540: true,
		4791933: true,
		4792165: true,
		4792200: true,
		4792411: true,
		4792416: true,
		4792577: true,
		4793201: true,
		4793260: true,
		4794471: true,
		4797160: true,
		4797644: true,
		4798102: true,
		4798212: true,
		4799143: true,
		4799445: true,
		4799525: true,
		4800121: true,
		4800593: true,
		4801138: true,
		4801167: true,
		4801177: true,
		4801286: true,
		4801417: true,
		4801953: true,
		4802465: true,
		4803350: true,
		4803839: true,
		4803926: true,
		4804172: true,
		4804574: true,
		4804716: true,
		4806998: true,
		4807643: true,
		4808180: true,
		4808277: true,
		4808396: true,
		4808695: true,
	}
)

func init() {
	if env.IsProd() {
		for userid := range Users {
			UserList = append(UserList, userid)
		}
	} else {
		UserList = append(UserList, 623525, 623570)
	}
}

func RankWhiteList(userid int64) bool {
	return true
}
