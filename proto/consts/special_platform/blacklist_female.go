package special_platform

func IsBlackListF<PERSON>le(userid int64) bool {
	return false
}

func IsBlackInviter(inviter string) bool {
	return false
}

var blackListFemale = map[int64]bool{
	8649515: true,
	8944397: true,
	8342621: true,
	8910767: true,
	8397025: true,
	8915142: true,
	2359075: true,
	8316095: true,
	3388860: true,
	4326544: true,
	8293394: true,
	8970338: true,
	8316398: true,
	3739666: true,
	3732255: true,
	7289150: true,
	2308876: true,
	8315688: true,
	8882287: true,
	8873764: true,
	8630998: true,
	8884640: true,
	3783962: true,
	7378625: true,
	8683245: true,
	8074998: true,
	3978201: true,
	8869749: true,
	3740685: true,
	3254932: true,
	5751817: true,
	3733042: true,
	3759875: true,
	8326460: true,
	8451409: true,
	6025180: true,
	8770990: true,
	8059443: true,
	4148587: true,
	8058877: true,
	3736147: true,
	3908145: true,
	4360155: true,
	8277499: true,
	2510858: true,
	8931961: true,
	3738989: true,
	8934169: true,
	8466463: true,
}

var blackInviter = map[string]bool{
	"G80189":   true,
	"G80140":   true,
	"G80126":   true,
	"U8029448": true,
	"U7868353": true,
	"U7991051": true,
	"U8393879": true,
}
