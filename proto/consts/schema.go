package consts

import (
	"fmt"
	"net/url"
)

const (
	schemaBase = "lianli://com.yb.lianli"

	// 跳转首页
	// 首页消息列表：lianli://com.yb.lianli/main?tab=2（首页tab传0，消息列表传2）
	schemaMainPage = schemaBase + "/main"

	//个人中心
	schemaUserInfo = schemaBase + "/user/info?user_id=%v"
	//私聊
	schemaChat = schemaBase + "/chat?user_id=%v"
	//视频聊天接听
	schemaChatVideoCall = schemaBase + "line/video?call_info=%v"
	//音频聊天接听
	schemaChatVoiceCall = schemaBase + "/line/voice?call_info=%v"
	//首页消息列表
	schemaChatSessions = schemaBase + "/main?tab=2"

	//动态详情
	schemaMomentDetail = schemaBase + "/dynamic/detail?moment_id=%v"
	//动态发布
	schemaMomentPublish = schemaBase + "/dynamic/publish"

	//资料完善
	schemaProfileEdit = schemaBase + "/mine/info/edit"
	//认证
	schemaCertify = schemaBase + "/mine/info/certification"
	//相册编辑
	schemaAlbumEdit = schemaBase + "/mine/info/album/edit"
	// 条形砖

	//插件内私聊
	schemaPluginChat = "lianli://com.yb.lianli/chat?user_id=%v"

	//恋爱小屋
	schemaLovehouse = schemaBase + "/love/cottage?user_id=%v"

	schemaLovehouseBase = schemaBase + "/love/cottage"
)

var (
	SchemaGreetMsgSetting = schemaBase + "/chatExclusiveGreet"
)

func GetRechargeSchema() string {
	return "lianli://com.yb.lianli/charge"
}

func GetRechargeSchemaBySelectedId(selectedId string) string {
	return fmt.Sprintf("fdialog://com.yb.lianli/charge/normal?selected_id=%v", selectedId)
}

func GetMainPageSchema(values url.Values) string {
	values.Add("tab", "0")
	return schemaMainPage + "?" + values.Encode()
}

func GetUserInfoSchema(userid int64) string {
	return fmt.Sprintf(schemaUserInfo, userid)
}

func GetChatSchema(userid int64) string {
	return fmt.Sprintf(schemaChat, userid)
}

func GetLovehouseSchema(userid int64) string {
	return fmt.Sprintf(schemaLovehouse, userid)
}

func GetLoveHouseSchemaOpenDayBook(userid int64, openDayBook bool) string {
	u := url.Values{}
	u.Add("user_id", fmt.Sprint(userid))
	u.Add("open_daybook", fmt.Sprint(openDayBook))
	u.Add("hideStatusBar", "true")
	return schemaLovehouseBase + "?" + u.Encode()
}

func GetRankSchema(categoryPos, rankPos int) string {
	p := fmt.Sprintf("lianli://com.yb.lianli/leaderboard?tab=%d&ranking_list_tab=%d&from=secretary", categoryPos, rankPos)
	p = url.QueryEscape(p)
	return "lianli://com.yb.lianli/dispatch?jump_uri=" + p
}

func GetChatVideoCallSchema(callInfo string) string {
	return fmt.Sprintf(schemaChatVideoCall, callInfo)
}

func GetChatVoiceCallSchema(callInfo string) string {
	return fmt.Sprintf(schemaChatVoiceCall, callInfo)
}

func GetChatSessionsSchema() string {
	return schemaChatSessions
}
