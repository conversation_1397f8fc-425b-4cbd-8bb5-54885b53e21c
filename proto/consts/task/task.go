package task

import (
	"xim/baselib/server/env"
	"xim/proto/consts/enums"
)

const (
	//非标准任务 id<50
	TaskIDUserSignup             int32 = 1
	TaskIDUserSweetRateAwardCoin int32 = 4
	TaskIDUserFirstRechargeAward int32 = 5
	TaskUserBuyLoveCard          int32 = 6 // 用户购买恋爱助力卡
	TaskIDUserChatDepth          int32 = 7 // 聊天深度首次超过XX
	TaskIDFirstAvatarBonus       int32 = 8 //头像金币弹窗

	TaskIDUserInternalLimitMax int32 = 10

	//福利任务 id>50 && id<100
	//新手任务
	TaskIDUserAlbum          int32 = 51 //上传相册
	TaskIDUserProfile        int32 = 52 //完善个人资料、标签
	TaskIDUserCert           int32 = 53 //真人认证
	TaskIDUserAttention      int32 = 54 //关注用户
	TaskIDUserVideoPepoleNum int32 = 55 //和X个人通视频并扣费
	TaskIDUserVideoFirstTime int32 = 56 //首次接听视频达x秒
	// 每日任务
	TaskIDUserSignin         int32 = 76 //每日签到
	TaskIDUserMoment         int32 = 77 //发布动态
	TaskIDUserVoiceChat      int32 = 78 //语音聊天
	TaskIDUserVideoChat      int32 = 79 //视频聊天
	TaskIDUserReceiveGift    int32 = 80 //收到礼物
	TaskIDUserChat           int32 = 81 //私信聊天
	TaskIDUserCall           int32 = 82 //音视频聊天
	TaskIDUserVideoFiveMin   int32 = 83 //单通视频时长5分钟
	TaskIDUserVideoThirtyMin int32 = 84 //单通视频时长30分钟
	TaskIDUserVideoHour      int32 = 85 //单通视频时长1小时
	//活动任务>100

)

const (
	// 恋爱小屋任务 >=200
	// 恋爱小屋通话任务场景 200-220
	TaskIDLovehouseCallSceneOne     int32 = 200 // 通话场景1
	TaskIDLovehouseCallSceneTwo     int32 = 201 // 通话场景2
	TaskIDLovehouseCallSceneThree   int32 = 202 // 通话场景3
	TaskIDLovehouseCallSceneOneV2   int32 = 203 // 通话场景4
	TaskIDLovehouseCallSceneTwoV2   int32 = 204 // 通话场景5
	TaskIDLovehouseCallSceneThreeV3 int32 = 205 // 通话场景6
	// 恋爱小屋送礼任务场景 221-240
	TaskIDLovehouseGiftSceneOne   int32 = 221 // 送礼场景1
	TaskIDLovehouseGiftSceneTwo   int32 = 222 // 送礼场景2
	TaskIDLovehouseGiftSceneThree int32 = 223 // 送礼场景3
	TaskIDLovehouseGiftSceneOneV1 int32 = 224 // 送礼场景4

	// 250开始其它
	TaskIDLovehouseMissTa        int32 = 250 //想他
	TaskIDLovehouseChat          int32 = 251 //互聊 X 条
	TaskIDLovehousePublishMoment int32 = 252 //发动态
	TaskIDLovehouseComment       int32 = 253 //评论对方动态
)

const (
	TaskIDJackpotOnlineSignIn    = 300
	TaskIDJackpotCallCount1      = 301
	TaskIDJackpotCallQuickMatch2 = 302
	TaskIDJackpotCallDuration5   = 303
	TaskIDJackpotCallDuration10  = 304
)

const (
	LovehouseBoxMaleTest   = 10020
	LovehouseBoxFemaleTest = 10015
	LovehouseBoxMale       = 10015
	LovehouseBoxFemale     = 10016
)

func GetLovehouseBoxId(sex int32) int32 {
	if !env.IsProd() {
		if sex == enums.UserSexFemale.Int32() {
			return LovehouseBoxFemaleTest
		}
		return LovehouseBoxMaleTest
	} else {
		if sex == enums.UserSexFemale.Int32() {
			return LovehouseBoxFemale
		}
		return LovehouseBoxMale
	}
}

var (
	taskIDLovehouseCallScenes = []int32{
		TaskIDLovehouseCallSceneOne,
		TaskIDLovehouseCallSceneTwo,
		TaskIDLovehouseCallSceneThree,
		TaskIDLovehouseCallSceneOneV2,
		TaskIDLovehouseCallSceneTwoV2,
		TaskIDLovehouseCallSceneThreeV3}

	taskIDLovehouseGiftScenes = []int32{
		TaskIDLovehouseGiftSceneOne,
		TaskIDLovehouseGiftSceneTwo,
		TaskIDLovehouseGiftSceneThree,
		TaskIDLovehouseGiftSceneOneV1,
	}
)

func GetTaskIDLovehouseCallScenes() []int32 {
	return taskIDLovehouseCallScenes
}

func GetTaskIDLovehouseGiftScenes() []int32 {
	return taskIDLovehouseGiftScenes
}

func IsLovehouseCallTaskID(taskid int32) bool {
	switch taskid {
	case TaskIDLovehouseCallSceneOne,
		TaskIDLovehouseCallSceneTwo,
		TaskIDLovehouseCallSceneThree,
		TaskIDLovehouseCallSceneOneV2,
		TaskIDLovehouseCallSceneTwoV2,
		TaskIDLovehouseCallSceneThreeV3:
		return true
	default:
		return false
	}
}

func IsLovehouseGiftTaskID(taskid int32) bool {
	switch taskid {
	case TaskIDLovehouseGiftSceneOne,
		TaskIDLovehouseGiftSceneTwo,
		TaskIDLovehouseGiftSceneThree,
		TaskIDLovehouseGiftSceneOneV1:
		return true
	default:
		return false
	}
}

type CallSingleAwardScene int32

const (
	CallSingleAwardSceneOne CallSingleAwardScene = iota + 1
	CallSingleAwardSceneTwo
	CallSingleAwardSceneThree
	CallSingleAwardSceneInterval
)

const (
	OpenCallSingleAwardFlag = false
	// test 单通视频时长奖励红包
	CallSingleAwardBoxSceneOneFirstTest = 10022 //每日首次1分钟
	CallSingleAwardBoxSceneOneTest      = 10023 //普通1分钟
	CallSingleAwardBoxSceneTwoTest      = 10024 //普通3分钟
	CallSingleAwardBoxSceneThreeTest    = 10025 //普通5分钟
	CallSingleAwardBoxSceneIntervalTest = 10026 //间隔10分钟
	// online
	CallSingleAwardBoxSceneOneFirst = 10022
	CallSingleAwardBoxSceneOne      = 10023
	CallSingleAwardBoxSceneTwo      = 10024
	CallSingleAwardBoxSceneThree    = 10025
	CallSingleAwardBoxSceneInterval = 10026

	// test 周累计视频时长红包
	CallWeekAwardBoxTest = 10027
	// online
	CallWeekAwardBox = 10027
)

var (
	callSingleDefaultAward              = int32(1)   //单通视频
	callSingleSceneOneDuration          = int64(60)  // 1分钟
	callSingleSceneTwoDuration          = int64(180) // 3分钟
	callSingleSceneThreeDuration        = int64(300) // 5分钟
	callSingleSceneIntervalBaseDuration = int64(600) // 间隔10分钟base 600
	callSingleSceneIntervalDuration     = int64(600) // 间隔时间 600
	callSingleAwardDurations            = []int64{
		callSingleSceneOneDuration,
		callSingleSceneTwoDuration,
		callSingleSceneThreeDuration,
	}
	callSingleDurationsSceneMap = map[int64]CallSingleAwardScene{
		callSingleSceneOneDuration:   CallSingleAwardSceneOne,
		callSingleSceneTwoDuration:   CallSingleAwardSceneTwo,
		callSingleSceneThreeDuration: CallSingleAwardSceneThree,
	}
)

func init() {
	for i := 0; i < 1; i++ { //间隔最多发放的次数
		duration := callSingleSceneIntervalBaseDuration + int64(i)*callSingleSceneIntervalDuration
		callSingleAwardDurations = append(callSingleAwardDurations, duration)
		callSingleDurationsSceneMap[duration] = CallSingleAwardSceneInterval
	}
}

func GetCallSingleDefaultAward() int32 {
	return callSingleDefaultAward * 100
}

func GetCallSingleFirstDuration() int64 {
	if !OpenCallSingleAwardFlag {
		return 0
	}
	if len(callSingleAwardDurations) > 0 {
		return callSingleAwardDurations[0]
	}
	return 0
}

func GetCallSingleAwardDurations() []int64 {
	if !OpenCallSingleAwardFlag {
		return []int64{}
	}
	return callSingleAwardDurations
}

func GetNextSingleAwardDuration(passTime int64) (duration int64) {
	if !OpenCallSingleAwardFlag {
		return 0
	}
	for _, singleAwardDuration := range callSingleAwardDurations {
		if passTime >= singleAwardDuration {
			continue
		}
		duration = singleAwardDuration
		break
	}
	return
}

func GetCallSingleAwardBoxId(first bool, duration int64) (hit bool, boxid int) {
	leftDuration := int64(0)
	hitDuration := int64(0)
	for _, d := range callSingleAwardDurations {
		if duration == d {
			hitDuration = d
			break
		}
		if duration > leftDuration && duration < d {
			hitDuration = leftDuration
			break
		}
		leftDuration = d
	}
	if hitDuration == 0 {
		return
	}
	scene, ok := callSingleDurationsSceneMap[hitDuration]
	if !ok {
		return
	}
	boxid = getCallSingleAwardBoxId(first, scene)
	if boxid != 0 {
		hit = true
	}
	return
}

func getCallSingleAwardBoxId(first bool, scene CallSingleAwardScene) (boxid int) {
	if env.IsProd() {
		switch scene {
		case CallSingleAwardSceneOne:
			if first {
				return CallSingleAwardBoxSceneOneFirst
			}
			return CallSingleAwardBoxSceneOne
		case CallSingleAwardSceneTwo:
			return CallSingleAwardBoxSceneTwo
		case CallSingleAwardSceneThree:
			return CallSingleAwardBoxSceneThree
		case CallSingleAwardSceneInterval:
			return CallSingleAwardBoxSceneInterval
		}
	} else {
		switch scene {
		case CallSingleAwardSceneOne:
			if first {
				return CallSingleAwardBoxSceneOneFirstTest
			}
			return CallSingleAwardBoxSceneOneTest
		case CallSingleAwardSceneTwo:
			return CallSingleAwardBoxSceneTwoTest
		case CallSingleAwardSceneThree:
			return CallSingleAwardBoxSceneThreeTest
		case CallSingleAwardSceneInterval:
			return CallSingleAwardBoxSceneIntervalTest
		}
	}
	return
}

func GetCallWeekAwardBoxId() int64 {
	if env.IsProd() {
		return CallWeekAwardBox
	}
	return CallWeekAwardBoxTest
}

func IsCallWeekAeardBoxId(boxId int64) bool {
	if env.IsProd() {
		return boxId == CallWeekAwardBox
	}
	return boxId == CallWeekAwardBoxTest
}

func IsLovehouseAwardNotifyTaskid(taskid int32) bool {
	for _, id := range []int32{TaskIDLovehouseCallSceneOneV2, TaskIDLovehouseCallSceneThreeV3, TaskIDLovehouseChat} {
		if id == taskid {
			return true
		}
	}
	return false
}

func AvatarCoinTaskAward(totalPayment int64) int64 {
	if totalPayment == 0 {
		return 0
	}
	if totalPayment <= 500 {
		return 2
	} else {
		return 5
	}
}
