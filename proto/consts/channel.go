package consts

const (
	ChannelOfficial = "official"

	ChannelXiaomi = "xiaomi"
	ChannelHuawei = "huawei"
	ChannelVivo   = "vivo"
	ChannelOppo   = "oppo"

	ChannelDouyin           = "douyin"
	ChannelKuaishou         = "kuaishou"
	ChannelGuangdiantong    = "guangdiantong"
	ChannelDouyinIos        = "douyin_ios"
	ChannelKuaishouIos      = "kuaishou_ios"
	ChannelGuangdiantongIos = "guangdiantong_ios"
	ChannelBaidu            = "baidu"
	ChannelBaiduIos         = "baidu_ios"
	ChannelUCBrowser        = "uc_browser"
	ChannelVivoAd           = "vivo_ad"
	ChannelYoumi            = "youmi"
	ChannelASA              = "asa"

	ChannelThunder = "thunder"

	ChannelAppleStore = "apple_store"
)

func IsAppStoreChannel(channel string) bool {
	switch channel {
	case ChannelXiaomi, ChannelHuawei, ChannelVivo, ChannelOppo:
		return true
	}
	return false
}

func IsAdvertChannel(channel string) bool {
	switch channel {
	case ChannelDouyin, ChannelKuaishou, ChannelGuangdiantong, ChannelBaidu:
		return true
	}
	return false
}
