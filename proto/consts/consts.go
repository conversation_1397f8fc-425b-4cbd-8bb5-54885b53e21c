package consts

import (
	"strings"
	"time"

	"xim/baselib/config"
	"xim/baselib/server/env"

	"xim/proto/consts/enums"
)

const (
	Yes = 1
	No  = 0

	ActionAdd = 1
	ActionDel = 2

	StatusEditing  = 0
	StatusOnline   = 1
	StatusOffline  = 2
	StatusAuditing = 3

	OrderStatusCreate  = 0
	OrderStatusSuccess = 1
	OrderStatusFailed  = 2

	SendGiftSourceSys = 1
	SendGiftSourceBag = 2

	CoinLogVisibleYes = 0
	CoinLogVisibleNo  = 1
)

var (
	ximStateTopic string // 改成配置先这么用吧 ，后面优化
	ximMsgTopic   string
	ximMsgGroup   string
	ximStateGroup string
)

const (
//	XIM_MSG_TOPIC   = env.GetEnvironment() + "v__xim_message" //
//
// XIM_STATE_TOPIC = "xim_state"
)

func XIM_MSG_TOPIC() string {
	// return fmt.Sprintf("%v_xim_message", string(env.GetEnvironment()))
	return ximMsgTopic
}

func XIM_STATE_TOPIC() string {
	// return fmt.Sprintf("%v_xim_state", string(env.GetEnvironment()))
	return ximStateTopic
}

func XIM_MSG_GROUP() string {
	// return fmt.Sprintf("%v_xim_message_group", string(env.GetEnvironment()))
	return ximMsgGroup
}

func XIM_STATE_GROUP() string {
	// return fmt.Sprintf("%v_xim_state_group", string(env.GetEnvironment()))
	return ximStateGroup
}

func SetKafkaTopic(kafka *config.Kafka) {
	if kafka == nil {
		return
	}
	ximStateTopic = kafka.StateTopic.Topic
	ximStateGroup = kafka.StateTopic.Group
	ximMsgTopic = kafka.MessageTopic.Topic
	ximMsgGroup = kafka.MessageTopic.Group
}

const (
	NsqTopicMsg        = "xim-message"
	NsqTopicChannelMsg = "msg"
)

func PackageName(app string) string {
	if name := pkgName[app]; name != "" {
		return name
	}
	return app
}

var (
	pkgName = map[string]string{}
)

const (
	MainAppPackage  = "com.date.lechat"
	MainAppPackage2 = "com.qindear.qianxun"

	//android 马甲包
	SlaveAppPackage1 = "com.date.lemate"
	SlaveAppPackage2 = "com.cn.side.feelgoodjob"
	SlaveAppPackage3 = "com.cn.xcub.make"
	SlaveAppPackage4 = "com.cn.xxlike.sme"
	SlaveAppPackage5 = "com.xleichui.xiangli"
	SlaveAppPackage6 = "com.xlchuizi.xianglimei"
	SlaveAppPackage7 = "com.qianxun.lover"

	SlaveAppPackage10 = "com.qingliaoqu.ziip"

	SlaveAppPackage20 = "com.qindear.yuanban"

	AppTypeNone          = 0
	AppTypeNativePrimary = 1
	AppTypeNativeSlave   = 2

	AdminPlatform  = "admin"
	AdminPlatform2 = "admin2"

	WebAppDomain     = "qindear.com"
	TestWebAppDomain = "qindegame.com"
)

const (
	// CanCancelledDay 注销申请满15天才可注销
	CanCancelledDay = 15
)

func IsFromWebsite(app string) bool {
	if strings.HasSuffix(app, WebAppDomain) || strings.HasSuffix(app, TestWebAppDomain) {
		return true
	}
	return false
}

func IsFromApp(app string) bool {
	return IsFromNewApp(app) || IsFromOldApp(app)
}

func IsFromOldApp(app string) bool {
	return IsFromMainApp(app) || IsFromSlaveApp(app)
}

func IsFromNewApp(app string) bool {
	return IsFromNewMainApp(app) || IsFromNewSlaveApp(app)
}

func IsFromMainApp(app string) bool {
	return app == MainAppPackage
}

func IsFromNewMainApp(app string) bool {
	return app == MainAppPackage2
}

func IsFromRealApp(app string) bool {
	return app == MainAppPackage || IsRealSlaveApp(app)
}

func IsFromAdminPlatform(app string) bool {
	return app == AdminPlatform
}

func IsFromAdminPlatform2(app string) bool {
	return app == AdminPlatform2
}

func IsFakeSlaveApp(app string) bool {
	switch app {
	case SlaveAppPackage1,
		SlaveAppPackage2,
		SlaveAppPackage4:
		return true
	}
	return false
}

func IsFromSlaveApp(app string) bool {
	switch app {
	case SlaveAppPackage1, SlaveAppPackage2, SlaveAppPackage4,
		SlaveAppPackage3, SlaveAppPackage5, SlaveAppPackage6, SlaveAppPackage7,
		SlaveAppPackage10:
		return true
	}
	return false
}

func IsFromNewSlaveApp(app string) bool {
	switch app {
	case SlaveAppPackage20:
		return true
	}
	return false
}

func IsRealSlaveApp(app string) bool {
	switch app {
	case SlaveAppPackage3,
		SlaveAppPackage5,
		SlaveAppPackage6,
		SlaveAppPackage7,
		SlaveAppPackage10:
		return true
	}
	return false
}

func GetAppTypeFromApp(app string) int32 {
	switch app {
	case MainAppPackage:
		return AppTypeNativePrimary
	case SlaveAppPackage1, SlaveAppPackage2, SlaveAppPackage3, SlaveAppPackage4,
		SlaveAppPackage5, SlaveAppPackage6, SlaveAppPackage7,
		SlaveAppPackage10:
		return AppTypeNativeSlave
	}
	return AppTypeNone
}

func IsMatchmakerApp(app, ch string) bool {
	return app == SlaveAppPackage5 && (ch == ChannelGuangdiantong || ch == ChannelHuawei)
}

const (
	AppPkg_Unknown = iota //未知
	AppPkg_App            //独立端
)

func AppNameToType(app string) int32 {
	switch app {
	case MainAppPackage, SlaveAppPackage1, SlaveAppPackage2, SlaveAppPackage3, SlaveAppPackage4,
		SlaveAppPackage5, SlaveAppPackage6, SlaveAppPackage7,
		SlaveAppPackage10:
		return AppPkg_App
	default:
		return AppPkg_Unknown
	}
}

type ActivityTypeBase int32

const (
	ActivityTypeBaseHide     ActivityTypeBase = 0
	ActivityTypeBaseNewcomer ActivityTypeBase = 1
	ActivityTypeBaseDaily    ActivityTypeBase = 2
)

func (t ActivityTypeBase) Invalid() bool {
	switch t {
	case ActivityTypeBaseHide,
		ActivityTypeBaseNewcomer,
		ActivityTypeBaseDaily:
		return false
	default:
		return true
	}
}

const (
	TimeNone          = "0000-00-00"
	TimeFormatYM      = "2006-01"
	TimeFormatYM2     = "200601"
	TimeFormatYMD     = "2006-01-02"
	TimeFormatYMD2    = "20060102"
	TimeFormatYMDHM   = "2006-01-02 15:04"
	TimeFormatYMDHM2  = "200601021504"
	TimeFormatYMDHMS  = "2006-01-02 15:04:05"
	TimeFormatYMDHMS2 = "20060102150405"
	TimeFormatHM      = "15:04"
	TimeFormatHMS     = "15:04:05"
)

const (
	ChannelMsgPayloadGzip = false
)

type MediaType int

const (
	MediaTypeImage MediaType = 1 // 图片
	MediaTypeVideo MediaType = 2 // 视频
	MediaTypeAudio MediaType = 3 // 音频

)

type MediaBiz struct {
	Dir     string
	Expired time.Duration
}

var (
	MediaBizAvatar    = MediaBiz{"avatar", 0}        // 用户头像
	MediaBizPhoto     = MediaBiz{"photo", 0}         // 用户相片
	MediaBizSignVoice = MediaBiz{"sign_voice", 0}    // 语音签名
	MediaBizCert      = MediaBiz{"cert", 3600}       // 用户认证
	MediaBizChat      = MediaBiz{"chat", 86400 * 30} //聊天
	MediaBizMoment    = MediaBiz{"moment", 0}        //动态
	MediaBizGift      = MediaBiz{"gift", 0}          //礼物
	MediaBizBanner    = MediaBiz{"banner", 0}        //轮播图
	MediaBizActivity  = MediaBiz{"activity", 0}      //礼物
	MediaBizTask      = MediaBiz{"task", 0}          //任务图标
	MediaCallCover    = MediaBiz{"call_cover", 0}    //通话视频封面
	MediaAppLog       = MediaBiz{"app_log", 3600}    //应用日志
	MediaBizFeedback  = MediaBiz{"feedback", 3600}   //反馈
	MediaBizIcon      = MediaBiz{"icon", 0}          //反馈
	MediaBizTaskAward = MediaBiz{"task_award", 0}    //任务奖励图标
	MediaBizDownLoad  = MediaBiz{"download", 0}      //下载
)

func GetMediaBiz(biz string) MediaBiz {
	switch biz {
	case "avatar":
		return MediaBizAvatar
	case "photo":
		return MediaBizPhoto
	case "sign_voice":
		return MediaBizSignVoice
	case "cert":
		return MediaBizCert
	case "chat":
		return MediaBizChat
	case "moment":
		return MediaBizMoment
	case "gift":
		return MediaBizGift
	case "banner":
		return MediaBizBanner
	case "task":
		return MediaBizTask
	case "call_cover":
		return MediaCallCover
	case "activity":
		return MediaBizActivity
	case "feedback":
		return MediaBizFeedback
	case "icon":
		return MediaBizIcon
	case "task_award":
		return MediaBizTaskAward
	case "download":
		return MediaBizDownLoad

		// case "chat_background":
		// 	return MediaBizChatBackground
		// case "message_icon":
		// 	return MediaBizMessageIcon
	}
	return MediaBiz{}
}

const (
	StatsFromApp    = 1
	StatsFromServer = 2
)

const (
	UserCertifyRealface int32 = 1
	UserCertifyRealname int32 = 2
)

const (
	BaseDevTypeAndroid = 1
	BaseDevTypeIos     = 2
	BaseDevTypeH5      = 3
)

const (
	RechargeChannelDefault        int32 = 1
	RechargeChannelAppAndroid     int32 = 2
	RechargeChannelAppIos         int32 = 3
	RechargeChannelAppIosIap      int32 = 4
	RechargeChannelWechat         int32 = 5
	RechargeChannelWeb            int32 = 6
	RechargeChannelVideoCallMatch int32 = 10
	RechargeChannelDefault2       int32 = 11
	RechargeChannelLoveCard       int32 = 12 // 恋爱助力卡购买
	RechargeChannelLoveCardDouble int32 = 13 // 恋爱助力卡翻倍领取宝箱奖励
	RechargeChannelLive           int32 = 14 // 直播充值

	H5PayProductChannel    int32 = 9999 // alipay-h5充值
	H5Pay2ProductChannel   int32 = 7777 // alipay2-h5充值
	H5WebPayProductChannel int32 = 6666 // webpay-alipay/webpay-wxpay 充值
)

const (
	WithdrawalAccountBankpay = 1
	WithdrawalAccountAlipay  = 2
	WithdrawalAccountWxpay   = 3

	WithdrawalStatusCreated  = 0
	WithdrawalStatusDeducted = 1
	WithdrawalStatusPaying   = 2
	WithdrawalStatusSuccess  = 3
	WithdrawalStatusFailed   = 4
	WithdrawalStatusCanceled = 5
	WithdrawalStatusRefunded = 6
	WithdrawalStatusWaiting  = 7

	WithdrawalCheckUnwanted = 0
	WithdrawalCheckWaiting  = 1
	WithdrawalCheckPassed   = 2
	WithdrawalCheckRejected = 3
)

// http://wiki.xunlei.cn/pages/viewpage.action?pageId=******** 详细定义
var (
	MatchTagNewToOldUpTime = 3 * 86400 // 匹配标签升级老普通时间 72h
	// overheating
	MatchOverheatingEarnFemaleThreshold  = 200      //150      //80       // 赚钱女过热匹配阀值 (2小时内匹配次数)
	MatchOverheatingOtherFemaleThreshold = 200      //150      //60       // 非赚钱女过热匹配阀值 (2小时内匹配次数)
	MatchOverheatingFemaleSpaceTime      = 2 * 3600 // 女用户过热匹配阀值控制时间范围
	MatchOverheatingFemaleCdTime         = 20 * 60  // 女用户匹配过热冷却时间
	MatchOverheatingMaleThreshold        = 3        // 男用户过热阀值 (1小时内被系统搭讪次数)
	MatchOverheatingMaleSpaceTime        = 1 * 3600 // 男用户过热匹配阀值控制时间范围
	MatchOverheatingMaleCdTime           = 2 * 3600 // 男用户匹配过热冷却时间

	// match filter
	MatchFilterRespCdTime = 72 * 3600 // 72h回复过女用户

	// tag condition
	MatchTagEarnFemaleConditionScore         = 700     //[前天，昨天]积分收益大于此值
	MatchTagEarnFemaleConditionRespRate      = 90      //[前天，昨天]回复率＞90%
	MatchTagBaipiaoFemaleConditionScore      = 200     //累计收益＞200积分
	MatchTagBaipiaoFemaleConditionMsgConvert = 2       //[前天，昨天]会话中男用户消息数/建立会话数量＜2
	MatchTagBottomFemaleConditionScore       = 50      //累计积分收益＜50积分
	MatchTagBaipiaoMaleConditionCharge       = 1 * 100 //当前时间-72h＜充值1元 数据库存分

	// sys accost 系统搭讪
	MatchSysAccostScopeAge = 20 // 系统搭讪年龄范围

	// match 伪消息
	MatchScopeAge = 3 // 伪消息年龄范围
)

var (
	CallMatchFilterMatchedCdTime       = 48 * 3600 // 48h内已匹配过
	CallMatchLastRejectInternalTime    = 10 * 60   //上次收到视频速配弹窗邀请拒绝时间
	CallMatchCallEndLimitUserScopeTime = 10 * 60   //上次视频邀请弹窗通话结束时间
)

const (
	NewUserSessionLimit = 60

	FreeCallRewardValidTime = time.Minute * 2
	FreeCallRewardMinutes   = 1
	FreeCallVoiceCoin       = 34
	FreeCallVideoCoin       = 68

	DailyFollowLimit         = 3
	DailyMomentPublishLimit  = 10
	DailyCommentLimit        = 300
	DailyFeedbackLimit       = 3
	DailyAvatarChangeLimit   = 5
	DailyNicknameChangeLimit = 3
	DailyFieldChangeLimit    = 100
)

const (
	GiftStylePortrait  = 8
	GiftStyleLandscape = 2
)

const (
	UnderageActionStart = 1
	UnderageActionStop  = 2
)

const (
	SettingsAll         = 0
	SettingsChatCharges = 1
	SettingsNotices     = 2
	SettingsPrivacy     = 3
)

const (
	ComplainDailyLimitTotal = 50
	FeedbackDailyLimitTotal = 3
	MomentDailyLimitTotal   = 10
)

const (
	TaskIDDailySignMale   = 1 // 每日签到男用户id
	TaskIDDailySignFemale = 2 // 每日签到女用户id
)

// 女用户等级分级标准
const (
	FemaleGradeS                = 5
	FemaleGradeS_CallIncome     = 400 * 100
	FemaleGradeS_CallDuration   = 300
	FemaleGradeS_CallAnswerRate = 85

	FemaleGradeA                = 4
	FemaleGradeA_CallIncome     = 150 * 100
	FemaleGradeA_CallDuration   = 150
	FemaleGradeA_CallAnswerRate = 80

	FemaleGradeB                = 3
	FemaleGradeB_CallIncome     = 0
	FemaleGradeB_CallDuration   = 0
	FemaleGradeB_CallAnswerRate = 80

	FemaleGradeC = 2

	SweetRateTipsLimit     = 3.6
	SweetRateAwardCoinRate = 6.0
)

const (
	CallRechargeCountDownSecond        = 120                   //音视频通话倒计时
	CallMatchRechargeCountDownSecond   = 60                    //视频速配通话倒计时
	CallMatchOpen                      = true                  //视频速配是否开启
	CallMatchCount                     = 10                    //视频速配次数
	CallMatchMaxCount                  = 20                    //视频速配最大次数
	CallMatchLimitDurationSuccessCount = 3                     //平均通话时长≤11s & 自然日通话次数≥3次 平均通话时长太短，今日无法再次使用该功能
	CallMatchAverageDuration           = 11                    //平均通话时长
	CallMatchSingleFailMaxLimit        = 5                     //当次视频速配容许最大失败次数
	CallMatchTimeout                   = 150                   //视频速配超时
	CallMatchTimeoutReal               = CallMatchTimeout - 20 //视频速配真实匹配时间

)

const (
	RankTypeAll   int32 = 0
	RankTypeDay   int32 = 1
	RankTypeWeek  int32 = 2
	RankTypeMonth int32 = 3
)

const (
	RechargeSceneVideoCallMatch = 1
	RechargeSceneLoveCardBase   = 2 // 直接购买恋爱助力卡
	RechargeSceneLoveCardDouble = 3 // 翻倍领取恋爱助力卡宝箱奖励
	RechargeSceneLive           = 4 // 直播场景
)

// https://shimo.im/docs/e1Az4vdbl0tvQvqW 需求文档
const (
	// 用户等级
	LevelZero = iota //低效用户
	LevelOne
	LevelTwo
	LevelThree
	LevelFour
	LevelFive
)

const (
	LevelOnlineSupport = 100 // 在线扶持
	LevelManualSupport = 99  // 手动扶持
)

const (
	// 最大 最小等级
	MaxLevel = LevelFive
	MinLevel = LevelZero
)

const (
	MatchURL                         = "https://oapi.dingtalk.com/robot/send?access_token=e641a0285bf27d0c51adc3d7cc369dd8ab159bb9d07e9d63d34a1755aca6c630"
	MatchOpen                        = true     // 匹配总开关
	MatchTimeCalLevelOpen            = true     // 计算女用户等级定时任务
	MatchOverheatingMaleThresholdNew = 5        // 男用户过热阀值 (1小时内被系统搭讪次数)
	MatchOverheatingMaleSpaceTimeNew = 1 * 3600 // 男用户过热匹配阀值控制时间范围
	MatchOverheatingMaleCdTimeNew    = 2 * 3600 // 男用户匹配过热冷却时间

	//  积分划分用户等级
	OneLevelScoreMin   = 0
	OneLevelScoreMax   = 99
	TwoLevelScoreMin   = OneLevelScoreMax + 1
	TwoLevelScoreMax   = 199
	ThreeLevelScoreMin = TwoLevelScoreMax + 1
	ThreeLevelScoreMax = 299
	FourLevelScoreMin  = ThreeLevelScoreMax + 1
	FourLevelScoreMax  = 999
	FiveLevelScore     = FourLevelScoreMax + 1

	// 划分等级1小时内并列条件2 AllotCalScoreCdTime
	OneLevelHourScore   = 5
	TwoLevelHourScore   = 20
	ThreeLevelHourScore = 30
	FourLevelHourScore  = 50
	FiveLevelHourScore  = 200

	//  用户等级分配占比
	AllotRatioOneLevel   = float64(8)  //10
	AllotRatioTwoLevel   = float64(18) //12
	AllotRatioThreeLevel = float64(22) //18
	AllotRatioFourLevel  = float64(24) //24
	AllotRatioFiveLevel  = float64(28) //36

	AllotRatioFiveNum  = AllotRatioFiveLevel
	AllotRatioFourNum  = AllotRatioFiveLevel + AllotRatioFourLevel
	AllotRatioThreeNum = AllotRatioFourNum + AllotRatioThreeLevel
	AllotRatioTwoNum   = AllotRatioThreeNum + AllotRatioTwoLevel
	AllotRatioOneNum   = AllotRatioTwoNum + AllotRatioOneLevel

	// 当前上线前分配时长
	AllotTimeNewUser     = 2 * 3600 // 新用户默认分配2小时从当天第一次打开app后
	AllotTimeOtherUser   = 1 * 3600 // 非新用户每日首次登录前1个小时内，定等级为4级，1小时后依据积分重新定等级
	AllotCalScoreCdTime  = 1 * 3600 // 计算小时积分间隔时间
	IntervalCalLevelTime = 30 * 60  // 女用户等级计算 30分钟计算等级

	// 过热 (新用户和非新用户首次登录1小时场景不会出现过热情况)
	ResponseRate     = float64(75) //半小时回复率≤75%
	MatchSilentCount = 5           //需要匹配的静默值
	MatchCdTime      = 30 * 60     //触发了过热冷却值

	// 男用户
	IntervalPushTime        = 30 * 60 //半小时内再推一次
	IntervalTriggerPushTime = 10 * 60 //需要间隔多久才推送

	//聊天消息超时退款时长
	chatMsgCoinTimeoutRefundTime     = time.Hour * 2
	testChatMsgCoinTimeoutRefundTime = time.Minute * 10

	//是否开启审核
	ReviewOpen = true
)

func GetChatMsgCoinTimeoutRefundTime() time.Duration {
	if env.IsProd() {
		return chatMsgCoinTimeoutRefundTime
	} else {
		return testChatMsgCoinTimeoutRefundTime
	}
}

const (
	AbStrategyTypeServer int32 = 1
	AbStrategyTypeApp    int32 = 2
	AbStrategyTypeH5     int32 = 3
)

// match 男用户匹配配置
const (
	VideoMatchPrice = 50
	VoiceMatchPrice = 18 //视频速配语音速配从16改成18
	MatchInterval   = 10
)

const (
	ReviewWait   = 1
	ReviewPass   = 2
	ReviewReject = 3
	ReviewDelete = 4

	ReviewFakeFemale = 100 // 虚假信息女用户审核信息
)

const (
	ShowCoverSweetRate = float32(3.0)
)

func SetIsCoverShow(sweetRate float32, sex int32, app string) bool {
	if sex != enums.UserSexMale.Int32() {
		return false
	}
	if !IsFromApp(app) {
		return false
	}
	if sweetRate >= ShowCoverSweetRate {
		return false
	}
	return true
}

type OnlineDurationType int

const (
	OnlineDurationTotal OnlineDurationType = 0
	OnlineDurationToday OnlineDurationType = 1
	OnlineDurationWeek  OnlineDurationType = 2
)

const (
	SessionListOrderByTime  = 1
	SessionListOrderBySweet = 2
)

const (
	FeedBackUploadLog = 100
)

const (
	CallVideoCardEffectiveThreshold = 120 // 带时效视频卡使用宽裕阀值
)

func GetCallMatchCount(userid int64, grade int32) (baseCount, maxCount int) {
	if env.IsProd() {
		switch grade {
		case FemaleGradeS:
			return 30, 40
		case FemaleGradeA:
			return 15, 25
		case FemaleGradeB:
			return 0, 0
		case FemaleGradeC:
			return 0, 0
		default:
			return 0, 0
		}
	} else {
		// S：622789　A：622781　B：623527　C：622792
		if userid == 622789 || userid == 622781 || userid == 623527 || userid == 622792 {
			switch grade {
			case FemaleGradeS:
				return 20, 30
			case FemaleGradeA:
				return 10, 20
			case FemaleGradeB:
				return 0, 0
			case FemaleGradeC:
				return 0, 0
			default:
				return 0, 0
			}
		}
		return CallMatchCount, CallMatchMaxCount
	}
}

const (
	JobCleanerPauseTimeBegin = "19:00:00"
	JobCleanerPauseTimeEnd   = "02:00:00"
)

func IsJobCleanerPauseTime() bool {
	hms := time.Now().Format(TimeFormatHMS)
	if JobCleanerPauseTimeEnd < JobCleanerPauseTimeBegin {
		if hms >= JobCleanerPauseTimeBegin && hms < "24:00:00" {
			return true
		}
		if hms >= "00:00:00" && hms < JobCleanerPauseTimeEnd {
			return true
		}
	} else {
		if hms >= JobCleanerPauseTimeBegin && hms < JobCleanerPauseTimeEnd {
			return true
		}
	}
	return false
}

const (
	UserTagTypeWithdrawalLarge = "daetixian" //大额提现

	UserTagTypeInvitationRecharge = "yaoqing_chongzhi" //邀请充值

	UserTagTypeInviter = "yaoqingren" //邀请人

)
