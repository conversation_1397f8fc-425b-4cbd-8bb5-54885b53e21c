package content

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

var (
	cityCodeName = map[string]string{}
)

func GetCityName(cityCode string) string {
	if name, ok := cityCodeName[cityCode]; ok {
		return name
	}
	return ""
}

// 系统搭讪文案
var (
	sysAccostContent = [...]string{}
)

// 组合文案  https://shimo.im/sheets/dPkpKvPmLoSX4JqO/MODOC
var (
	// 称呼
	salutation = [...]string{}
	// 语气词
	mood = [...]string{}
	// 通用不分场景
	contentCommon = [...]string{}
	// 相同城市
	contentSameCity = [...]string{}
	// 不同城市主文案
	contentDiffCity = [...]string{}

	// 凌晨0点到5点
	contentTimeZeroToFive = [...]string{}
	// 早上5-7点
	contentTimeFiveToSeven = [...]string{}
	// 早上7点-11点
	contentTimeSevenToEleven = [...]string{}
	// 11点-13点
	contentTimeElventToThirteen = [...]string{}
	// 13-18点
	contentTimeThirteenToEighteen = [...]string{}
	// 18-24点
	contentTimeEighteeOver = [...]string{}
)

func GetSysAccostContent() string {
	index := rand.Intn(len(sysAccostContent))
	return sysAccostContent[index]
}

// 优先跟据定位，两人为同城100%触发同城；非同城（含女方未开定位）25%触发不同城市，25%触发通用，50%触发时间；称呼和助词均10%触发
// 获取组合文案
func GetCompositeContent(mycity, city string) string {
	var str []string
	// 称谓
	if rand.Intn(100) <= 10 {
		index := rand.Intn(len(salutation))
		str = append(str, salutation[index])
	}

	// 主文案
	if mycity != "" { // 优先定位
		if mycity == city { // 相同城市
			str = append(str, getCityContent(true, mycity))
		} else {
			if rand.Intn(100) <= 25 { // 不同城市
				str = append(str, getCityContent(false, mycity))
			} else {
				if rand.Intn(75) <= 25 {
					str = append(str, getCommonContent())
				} else {
					str = append(str, getTimeContent())
				}
			}
		}
	} else {
		if rand.Intn(75) <= 25 {
			str = append(str, getCommonContent())
		} else {
			str = append(str, getTimeContent())
		}
	}

	// 语气词
	if rand.Intn(100) <= 10 {
		index := rand.Intn(len(mood))
		str = append(str, mood[index])
	}

	if len(str) == 1 {
		return str[0]
	} else if len(str) > 1 {
		return strings.Join(str, ",")
	}
	return ""
}

func getCityContent(sameCity bool, city string) (str string) {
	if sameCity {
		index := rand.Intn(len(contentSameCity))
		str = contentSameCity[index]
	} else {
		index := rand.Intn(len(contentDiffCity))
		str = contentDiffCity[index]
	}
	if strings.Contains(str, "%s") {
		return fmt.Sprintf(str, city)
	}
	return
}

func getTimeContent() string {
	h := time.Now().Hour()
	if h >= 0 && h < 5 {
		index := rand.Intn(len(contentTimeZeroToFive))
		return contentTimeZeroToFive[index]
	} else if h >= 5 && h < 7 {
		index := rand.Intn(len(contentTimeFiveToSeven))
		return contentTimeFiveToSeven[index]
	} else if h >= 7 && h < 11 {
		index := rand.Intn(len(contentTimeSevenToEleven))
		return contentTimeSevenToEleven[index]
	} else if h >= 11 && h < 13 {
		index := rand.Intn(len(contentTimeElventToThirteen))
		return contentTimeElventToThirteen[index]
	} else if h >= 13 && h < 18 {
		index := rand.Intn(len(contentTimeThirteenToEighteen))
		return contentTimeThirteenToEighteen[index]
	} else {
		index := rand.Intn(len(contentTimeEighteeOver))
		return contentTimeEighteeOver[index]
	}
}

func getCommonContent() string {
	index := rand.Intn(len(contentCommon))
	return contentCommon[index]
}

var (
	contentV2 = [...]string{}
)

func GetCompositeContentV2() string {
	index := rand.Intn(len(contentV2))
	return contentV2[index]
}
