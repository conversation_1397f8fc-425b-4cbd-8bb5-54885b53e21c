package stats

import (
	"context"
	"xim/baselib/logger"
	"xim/baselib/nsqutil"
	"xim/proto/consts"
	"xim/proto/consts/mqtopic"
)

type NewChatSource int32

func (n NewChatSource) IsNewChat() bool {
	return n != 0
}

func (n NewChatSource) IsLevelAccost() bool {
	return n >= NewChatSystemAccostL1 && n <= NewChatSystemAccostL5
}

func (n NewChatSource) IsFemaleGreet() bool {
	return n == NewChatFemaleGreet
}

func (n NewChatSource) IsOnlineAccost() bool {
	return n == NewChatSystemAccostNewLOnlineSupport || n == NewChatSystemAccostOldLOnlineSupport
}

func (n NewChatSource) IsSystemAccost() bool {
	return n >= NewChatSystemAccostL1 && n <= NewChatSystemAccostLManualSupport
}

const (
	NewChatSystemAccostL0                NewChatSource = consts.LevelZero
	NewChatSystemAccostL1                NewChatSource = consts.LevelOne
	NewChatSystemAccostL2                NewChatSource = consts.LevelTwo
	NewChatSystemAccostL3                NewChatSource = consts.LevelThree
	NewChatSystemAccostL4                NewChatSource = consts.LevelFour
	NewChatSystemAccostL5                NewChatSource = consts.LevelFive
	NewChatSystemAccostOldLOnlineSupport NewChatSource = 101
	NewChatSystemAccostNewLOnlineSupport NewChatSource = 102
	NewChatSystemAccostLManualSupport    NewChatSource = consts.LevelManualSupport
	NewChatFemaleGreet                   NewChatSource = 201
	NewChatMaleGreet                     NewChatSource = 202
	NewChatFemaleChat                    NewChatSource = 204
	NewChatMaleChat                      NewChatSource = 203
)

type UserDispatchItem struct {
	Female int64         `json:"female"`
	Male   int64         `json:"male"`
	Time   int64         `json:"time"`
	Source NewChatSource `json:"source"`
	IsNew  bool          `json:"is_new"`
}

type UserDispatch struct {
	Dispatch []UserDispatchItem
}

func PublishUserDispatch(list []UserDispatchItem) {
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicDispatchNewChat, UserDispatch{
		Dispatch: list,
	})
	if err != nil {
		logger.Errorf("error %v", err)
	}
}
