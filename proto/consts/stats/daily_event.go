package stats

import (
	"context"
	"time"

	"xim/baselib/logger"
	"xim/baselib/nsqutil"
	"xim/baselib/nsqutil/event"
	"xim/baselib/util"
	"xim/proto/consts/mqtopic"
	mproto "xim/proto/model/user"
)

const (
	OTypeStatsDaily = "stats_daily"
)

const (
	ETypeUserStatsCharge        = "user_stats_charge"         //用户充值
	ETypeUserStatsScoreIncrease = "user_stats_score_increase" //用户积分收益增加
	ETypeUserStatsCoinConsume   = "user_stats_coin_consume"   //用户金币消耗
	ETypeUserStatsCoinRefund    = "user_stats_coin_refund"    //用户聊天金币退回
	ETypeUserStatsWithdrawal    = "user_stats_withdrawal"     //用户提现
	ETypeUserStatsGiftConsume   = "user_stats_gift_consume"   //用户送礼物
	// match
	ETypeUserMatchOverheating = "user_stats_match_overheating" //用户匹配过热
	ETypeUserMatchCount       = "user_stats_match_count"       //用户匹配次数
	ETypeUserMatchNewCount    = "user_stats_match_new_count"   //用户新匹配次数
	ETypeUserMatchDispatch    = "user_stats_match_dispatch"    //用户基于效率分发

	ETypeUserFemaleCall        = "user_stats_female_call"   //女用户语音视频通话
	ETypeUserCallDuration      = "user_stats_call_duration" //音视频通话时长
	ETypeFemaleOnlineHeartbeat = "user_stats_female_online"

	// 用户通话来源
	ETypeCallSourceStat = "call_source_stat" //用户通话来源统计
)

type MatchCountData struct {
	IsAccost bool                       `json:"is_accost"`
	Tag      mproto.TagIdItem           `json:"tag"`
	Uids     []int64                    `json:"uids"`
	Tags     map[int64]mproto.TagIdItem `json:"tags"`
	IsFirst  bool                       `json:"is_first"`
}

type MatchNewCountData struct {
	IsAccost       bool    `json:"is_accost"`
	IsFirst        bool    `json:"is_first"`
	FakeCount      int     `json:"fake_count"`
	FakeUids       []int64 `json:"fake_uids"`
	SysAccostCount int     `json:"sys_accost_count"`
	SysAccostUids  []int64 `json:"sys_accost_uids"`
	IsApp          bool    `json:"is_app"`
}

type CallDurationData struct {
	CallType  int32 `json:"call_type"`
	Duration  int64 `json:"duration"`
	PayUserid int64 `json:"pay_userid"`
	Peerid    int64 `json:"peerid"`
}

type MatchDispatchCountData struct {
	IsApp          bool    `json:"is_app"`
	FakeCount      int     `json:"fake_count"` // 伪消息
	FakeUids       []int64 `json:"fake_uids"`
	LevelCount     int     `json:"level_count"` // 等级的系统搭讪
	LevelUids      []int64 `json:"level_uids"`
	ManualCount    int     `json:"manual_count"` //手动扶持
	ManualUids     []int64 `json:"manual_uids"`
	NewOnlineCount int     `json:"new_online_count"` //上线扶持新用户
	NewOnlineUids  []int64 `json:"new_online_uids"`
	OldOnlineCount int     `json:"old_online_count"` //上线扶持老用户
	OldOnlineUids  []int64 `json:"old_online_uids"`
}

func PubUserConsumeGift(orderid int64) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeUserStatsGiftConsume, 0, orderid, nil)
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

func PubMatchOverheating(sex int32) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeUserMatchOverheating, 0, int64(sex), nil)
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

func PubCallDuration(callType int32, payUserid, peerid int64, duration int64) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeUserCallDuration, payUserid, 0, CallDurationData{
		CallType:  callType,
		PayUserid: payUserid,
		Peerid:    peerid,
		Duration:  duration,
	})
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

type UserOnlineHeartBeatData struct {
	Userid int64
	Sex    int32
	Ct     int64
}

func PubFemaleUserOnlineHeartBeat(userid int64, sex int32) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeFemaleOnlineHeartbeat, userid, 0, UserOnlineHeartBeatData{
		Userid: userid,
		Sex:    sex,
		Ct:     time.Now().Unix(),
	})
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

func PubFemaleCall(userid int64, isAccept bool) {
	defer util.Recover()
	object := int64(0)
	if isAccept {
		object = 1
	}
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeUserFemaleCall, userid, object, nil)
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

func PubMatchNewCount(userid int64, fakeCount int, fakeUids []int64, sysAccostCount int, sysAccostUids []int64, isSysAccost, first, isApp bool) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeUserMatchNewCount, userid, 0, MatchNewCountData{
		IsAccost:       isSysAccost,
		IsFirst:        first,
		FakeCount:      fakeCount,
		FakeUids:       fakeUids,
		SysAccostCount: sysAccostCount,
		SysAccostUids:  sysAccostUids,
		IsApp:          isApp,
	})
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

func PubMatchDispatchStat(userid int64, data MatchDispatchCountData) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeUserMatchDispatch, userid, 0, data)
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
}

type CallSourceStatus int32

const (
	CallSourceStatusCall CallSourceStatus = iota
	CallSourceStatusAccept
	CallSourceStatusCancel
	CallSourceStatusReject
)

type CallSourceStatData struct {
	CallId int64            `json:"call_id"`
	Source string           `json:"source"`
	Status CallSourceStatus `json:"status"`
}

func PubCallSourceStat(data CallSourceStatData) {
	defer util.Recover()
	msg := event.GenBaseEvent(OTypeStatsDaily, ETypeCallSourceStat, data.CallId, 0, data)
	err := nsqutil.PublishJSON(context.Background(), mqtopic.NsqTopicStatsDaily, msg)
	if err != nil {
		logger.Errorf("%v", err.Error())
	}
	return
}
