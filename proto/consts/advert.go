package consts

import "xim/baselib/server/env"

const (
	AdvertiserDouyin           = ChannelDouyin
	AdvertiserDouyinIos        = ChannelDouyinIos
	AdvertiserKuaishou         = ChannelKuaishou
	AdvertiserKuaishouIos      = ChannelKuaishouIos
	AdvertiserGuangdiantong    = ChannelGuangdiantong
	AdvertiserGuangdiantongIos = ChannelGuangdiantongIos
	AdvertiserBaidu            = ChannelBaidu
	AdvertiserBaiduIos         = ChannelBaiduIos
	AdvertiserUCBrowser        = ChannelUCBrowser
	AdvertiserVivo             = ChannelVivoAd
	AdvertiserYoumi            = ChannelYoumi
	AdvertiserASA              = ChannelASA

	ReportActionMonitor  = 0
	ReportActionActivate = 1
	ReportActionRegister = 2
	ReportActionRecharge = 3
)

var (
	AdvertWhiteList = map[int64]bool{
		9895207: true,
		9396126: true,
		9119128: true,
	}
)

var (
	BaiduAKeyMap = map[string]string{
		"42369338": "NDIzNjkzMzg=",
		"42369337": "NDIzNjkzMzc=",
		"42105047": "NDIxMDUwNDc=",
		"42105061": "NDIxMDUwNjE=",
		"42501332": "NDI1MDEzMzI=",
		"42501339": "NDI1MDEzMzk=",
		"42776566": "NDI3NzY1NjY=",

		"42837266": "NDI4MzcyNjY=",
		"42837386": "NDI4MzczODY=",

		"42781474": "NDI3ODE0NzQ=",

		"42501346": "NDI1MDEzNDY=",
		"42501350": "NDI1MDEzNTA=",

		"43196719": "NDMxOTY3MTk=",

		"43609059": "NDM2MDkwNTk=",
		"43609083": "NDM2MDkwODM=",
		"43609113": "NDM2MDkxMTM=",
		"43609192": "NDM2MDkxOTI=",
		"43609200": "NDM2MDkyMDA=",
		"43848759": "NDM4NDg3NTk=",

		"43945643": "NDM5NDU2NDM=",
		"43945489": "NDM5NDU0ODk=",
		"44065762": "NDQwNjU3NjI=",
		"44069124": "NDQwNjkxMjQ=",
		"44081663": "NDQwODE2NjM=",
		"44100768": "NDQxMDA3Njg=",
		"44103883": "NDQxMDM4ODM=",
		// 有范儿
		"42233121": "NDIyMzMxMjE=",
		"42233124": "NDIyMzMxMjQ=",
		"42233123": "NDIyMzMxMjM=",
		"42233125": "NDIyMzMxMjU=",
		"42233122": "NDIyMzMxMjI=",

		"44344864": "NDQzNDQ4NjQ=",
		"44356783": "NDQzNTY3ODM=",

		//皮玩
		"44514261": "NDQ1MTQyNjE=",
		"44514274": "NDQ1MTQyNzQ=",
		"44514300": "NDQ1MTQzMDA=",
		"44514318": "NDQ1MTQzMTg=",

		//汇至博通
		"42031607": "NDIwMzE2MDc=",
		"42031611": "NDIwMzE2MTE=",
		"44651068": "NDQ2NTEwNjg=",

		"42031604": "NDIwMzE2MDQ=",
		"42031610": "NDIwMzE2MTA=",

		"44651071": "NDQ2NTEwNzE=",
		"44651069": "NDQ2NTEwNjk=",

		"44651073": "NDQ2NTEwNzM=",
		"44651077": "NDQ2NTEwNzc=",
		"44651076": "NDQ2NTEwNzY=",

		"44651070": "NDQ2NTEwNzA=",
		"44651075": "NDQ2NTEwNzU=",

		// 一束光
		"45045118": "NDUwNDUxMTg=",
		"45045196": "NDUwNDUxOTY=",

		"45163810": "NDUxNjM4MTA=",
		"45045231": "NDUwNDUyMzE=",

		"45448160": "NDU0NDgxNjA=",
		"45448241": "NDU0NDgyNDE=",

		"45170071": "NDUxNzAwNzE=",
		"45155177": "NDUxNTUxNzc=",

		"45628410": "NDU2Mjg0MTA=",
		"45628420": "NDU2Mjg0MjA=",
		"45628432": "NDU2Mjg0MzI=",

		"45617102": "NDU2MTcxMDI=",
		"45617147": "NDU2MTcxNDc=",

		"45884269": "NDU4ODQyNjk=",
		"45884348": "NDU4ODQzNDg=",

		"45884432": "NDU4ODQ0MzI=",
		"45884387": "NDU4ODQzODc=",

		"46032515": "NDYwMzI1MTU=",
		"46032568": "NDYwMzI1Njg=",
		"46086721": "NDYwODY3MjE=",

		"46182641": "NDYxODI2NDE=",
		"46182714": "NDYxODI3MTQ=",
		"46066656": "NDYwNjY2NTY=",
		"46082307": "NDYwODIzMDc=",
		"46459222": "NDY0NTkyMjI=",
		"46463284": "NDY0NjMyODQ=",
		"46463396": "NDY0NjMzOTY=",
		"46463481": "NDY0NjM0ODE=",
		"46463564": "NDY0NjM1NjQ=",

		"46449719": "NDY0NDk3MTk=",
		"46463744": "NDY0NjM3NDQ=",
		"46463809": "NDY0NjM4MDk=",
		"46463877": "NDY0NjM4Nzc=",
		"46463940": "NDY0NjM5NDA=",

		"46234046": "NDYyMzQwNDY=",

		"46216665": "NDYyMTY2NjU=",
		"46216674": "NDYyMTY2NzQ=",
		"46216694": "NDYyMTY2OTM=",
		"46216693": "NDYyMTY2OTQ=",
		"46216745": "NDYyMTY3NDU=",
		"46234095": "NDYyMzQwOTU=",

		"47321207": "NDczMjEyMDc=",
		"47321233": "NDczMjEyMzM=",

		"47354135": "NDczNTQxMzU=",
		"47354141": "NDczNTQxNDE=",
		"47354164": "NDczNTQxNjQ=",
		"47356159": "NDczNTYxNTk=",

		"46234144": "NDYyMzQxNDQ=",
		"46251372": "NDYyNTEzNzI=",

		"47602831": "NDc2MDI4MzE=",
		"47602848": "NDc2MDI4NDg=",

		"47610967": "NDc2MTA5Njc=",
		"47610932": "NDc2MTA5MzI=",

		"47659134": "NDc2NTkxMzQ=",
		"47659259": "NDc2NTkyNTk=",
		"47659317": "NDc2NTkzMTc=",

		"47685555": "NDc2ODU1NTU=",
		"47685564": "NDc2ODU1NjQ=",
		"47685572": "NDc2ODU1NzI=",

		"47707544": "NDc3MDc1NDQ=",

		"47695051": "NDc2OTUwNTE=",
		"47695062": "NDc2OTUwNjI=",

		"47775280": "NDc3NzUyODA=",
		"47775286": "NDc3NzUyODY=",
		"47775292": "NDc3NzUyOTI=",

		"46251456": "NDYyNTE0NTY=",
		"47846869": "NDc4NDY4Njk=",

		"47902868": "NDc5MDI4Njg=",

		"48088760": "NDgwODg3NjA=",
		"48088767": "NDgwODg3Njc=",

		"48123024": "NDgxMjMwMjQ=",
		"48123032": "NDgxMjMwMzI=",
		"48123043": "NDgxMjMwNDM=",
		"48123050": "NDgxMjMwNTA=",

		"48242189": "NDgyNDIxODk=",
		"48237217": "NDgyMzcyMTc=",
		"48237222": "NDgyMzcyMjI=",
		"48237227": "NDgyMzcyMjc=",
		"48237232": "NDgyMzcyMzI=",
		"48237229": "NDgyMzcyMjk=",

		"48237328": "NDgyMzczMjg=",
		"48237327": "NDgyMzczMjc=",
		"48237331": "NDgyMzczMzE=",
		"48237334": "NDgyMzczMzQ=",
		"48237332": "NDgyMzczMzI=",

		"48122892": "NDgxMjI4OTI=",
		"48122903": "NDgxMjI5MDM=",
		"48122915": "NDgxMjI5MTU=",
		"48122933": "NDgxMjI5MzM=",

		"48409754": "NDg0MDk3NTQ=",
		"48409769": "NDg0MDk3Njk=",
		"48409814": "NDg0MDk4MTQ=",
		"48409837": "NDg0MDk4Mzc=",
	}

	BaiduRegisterMap = map[string]int{
		"42501332": 1,
		"42501339": 1,
		"42781474": 1,

		"42501346": 1,
		"42501350": 1,

		"43609059": 1,
		"43609083": 1,
		"43609113": 1,
		"43609192": 1,
		"43609200": 1,
		"44069124": 1,
		"42233125": 1,

		"44514261": 1,
		"44514274": 1,
		"44514300": 1,
		"44514318": 1,
		"42233122": 1,

		"42031604": 1,
		"42031610": 1,

		"44651068": 1,

		"44651071": 1,
		"44651069": 1,

		"44651073": 1,
		"44651077": 1,
		"44651076": 1,

		"45163810": 1,
		"45045231": 1,

		"45448160": 1,
		"45448241": 1,

		"45170071": 1,
		"45155177": 1,

		"44651070": 1,
		"44651075": 1,

		"45628410": 1,
		"45628420": 1,
		"45628432": 1,

		"45617102": 1,
		"45617147": 1,

		"45884269": 1,
		"45884348": 1,

		"45884432": 1,
		"45884387": 1,

		"46032515": 1,
		"46032568": 1,
		"46086721": 1,

		"46182641": 1,
		"46182714": 1,
		"46066656": 1,
		"46082307": 1,
		"46459222": 1,
		"46463284": 1,
		"46463396": 1,
		"46463481": 1,
		"46463564": 1,

		"46449719": 1,
		"46463744": 1,
		"46463809": 1,
		"46463877": 1,
		"46463940": 1,

		"46234046": 1,

		"46216665": 1,
		"46216674": 1,
		"46216694": 1,
		"46216693": 1,
		"46216745": 1,
		"46234095": 1,

		"47321207": 1,
		"47321233": 1,

		"47354135": 1,
		"47354141": 1,
		"47354164": 1,
		"47356159": 1,

		"46234144": 1,
		"46251372": 1,

		"47602831": 1,
		"47602848": 1,

		"47610967": 1,
		"47610932": 1,

		"47659134": 1,
		"47659259": 1,
		"47659317": 1,

		"47685555": 1,
		"47685564": 1,
		"47685572": 1,

		"47707544": 1,

		"47695051": 1,
		"47695062": 1,

		"47775280": 1,
		"47775286": 1,
		"47775292": 1,

		"46251456": 1,

		"47846869": 1,

		"47902868": 1,

		"48088760": 1,
		"48088767": 1,

		"48123024": 1,
		"48123032": 1,
		"48123043": 1,
		"48123050": 1,

		"48242189": 1,
		"48237217": 1,
		"48237222": 1,
		"48237227": 1,
		"48237232": 1,
		"48237229": 1,

		"48237328": 1,
		"48237327": 1,
		"48237331": 1,
		"48237334": 1,
		"48237332": 1,

		"48122892": 1,
		"48122903": 1,
		"48122915": 1,
		"48122933": 1,

		"48409754": 1,
		"48409769": 1,
		"48409814": 1,
		"48409837": 1,
	}

	KuaishouRegisterMap = map[string]int{
		"15425344": 1,
		"15664562": 1,
		"15713339": 1,
	}

	GuangdiantongRegisterMap = map[string]int{
		"26914128": 1,
		"26914127": 1,

		"31132824": 1,
		"31192376": 1,
		"31217355": 1,
		"31217354": 1,
		"31186574": 1,
		"31186573": 1,

		"31170238": 1,
		"31170235": 1,
		"31170232": 1,
		"31170231": 1,
		"31170229": 1,
		"31170227": 1,
		"31170225": 1,
		"31170222": 1,
		"31170219": 1,
		"31170217": 1,

		"31190626": 1,
		"31191487": 1,
		"31191488": 1,
		"31191489": 1,
		"31191490": 1,

		"31385236": 1,
		"31385237": 1,

		"31408324": 1,
		"31414581": 1,

		"31192378": 1,
		"31192379": 1,
		"27109207": 1,
		"27108540": 1,

		"31192380": 1,

		"31179603": 1,
		"31602340": 1,

		"31661066": 1,
		"31661064": 1,
		"30183570": 1,

		"31385240": 1,
	}

	UCBrowserRegisterMap = map[string]int{}

	OceanEngineDevAppInfo = map[int64]string{
		1765579010679820: "bac622cf1bcba603d28010c3f86a4ba5969d5962",
	}
	OceanEngineProdAppInfo = map[int64]string{
		****************: "cfdeceef6fc77dd6a755d51bebe2246bca8fd2fa",
	}
)

func CheckBaiduAKey(userid, akey string) bool {
	if akey2, ok := BaiduAKeyMap[userid]; ok {
		return akey == akey2
	}
	return false
}

func CheckBaiduRegister(userid string) bool {
	if _, ok := BaiduRegisterMap[userid]; ok {
		return true
	}
	return false
}

func GetBaiduAKey(userid string) string {
	if akey, ok := BaiduAKeyMap[userid]; ok {
		return akey
	}
	return ""
}

func CheckKuaishouRegister(accountId string) bool {
	if _, ok := KuaishouRegisterMap[accountId]; ok {
		return true
	}
	return false
}

func CheckGuangdiantongRegister(accountId string) bool {
	if _, ok := GuangdiantongRegisterMap[accountId]; ok {
		return true
	}
	return false
}

func CheckUCBrowserRegister(accountId string) bool {
	if _, ok := UCBrowserRegisterMap[accountId]; ok {
		return true
	}
	return false
}

func GetAdvertAppKey(app string) string {
	if app == SlaveAppPackage3 {
		return "piwan"
	} else if app == SlaveAppPackage5 {
		return "xiangli"
	} else if app == SlaveAppPackage6 {
		return "xiangli2"
	} else if app == SlaveAppPackage7 {
		return "qianxun"
	} else if app == SlaveAppPackage10 {
		return "qingliao"
	}
	return ""
}

func GetAdvertByOs(srcAdvert, os string) string {
	switch srcAdvert {
	case AdvertiserDouyin, AdvertiserDouyinIos:
		if os == "1" {
			return AdvertiserDouyinIos
		}
	case AdvertiserKuaishou, AdvertiserKuaishouIos:
		if os == "1" {
			return AdvertiserKuaishouIos
		}
	case AdvertiserGuangdiantong, AdvertiserGuangdiantongIos:
		if os == "ios" {
			return AdvertiserGuangdiantongIos
		}
	case AdvertiserBaidu, AdvertiserBaiduIos:
		if os == "1" {
			return AdvertiserBaiduIos
		}
	}
	return srcAdvert
}

func GetAdvertByDevType(srcAdvert string, devType int32) string {
	const iosDT = 2
	// 默认为安卓渠道，只需处理ios渠道的情况
	switch srcAdvert {
	case AdvertiserDouyin:
		if devType == iosDT {
			return AdvertiserDouyinIos
		}
	case AdvertiserKuaishou:
		if devType == iosDT {
			return AdvertiserKuaishouIos
		}
	case AdvertiserGuangdiantong:
		if devType == iosDT {
			return AdvertiserGuangdiantongIos
		}
	}
	return srcAdvert
}

func GetOceanEngineAppInfo() map[int64]string {
	if !env.IsProd() {
		return OceanEngineDevAppInfo
	}
	return OceanEngineProdAppInfo
}
