package formula

import (
	"fmt"
	"math"
	"strings"
	"time"
)

// coin => score
const (
	coinPercentage     = 40.0 / 100
	freeCoinPercentage = 15.0 / 100
	coinScoreRatio     = 17.0 / 10
	coinMoneyRatio     = 17.0 / 100
	scoreMoneyRatio    = 10.0 / 1

	coinScoreRatio2 = 10.0 / 10
	coinMoneyRatio2 = 10.0 / 100

	//打招呼
	chatHelloPriceCoin  = 1
	chatHelloPriceCoin2 = 1

	//聊天

	OldChatMessagePriceCoin  = 8
	NewChatMessagePriceCoin2 = 4

	//语音/视频通话价格
	chatCallPriceCoin  = 34
	chatCallPriceCoin2 = 20

	//语音通话
	chatVoiceCallPriceCoin  = chatCallPriceCoin
	chatVoiceCallPriceCoin2 = chatCallPriceCoin2
	//视频通话
	chatVideoCallPriceCoin  = chatCallPriceCoin
	chatVideoCallPriceCoin2 = chatCallPriceCoin2

	maleMatchCallPriceCoin  = 16
	maleMatchCallPriceCoin2 = 10

	// 视频通话固定给女的16积分
	VideoCallAwardScore = 1600
	// 系统搭讪给女的 0.1 积分
	SystemAccostAwardScore = 10
	// 聊天卡女用户固定 0.6 积分
	ChatCardScore = 60
	// 打招呼卡女用户固定 0.1 积分
	ChatHelloCardScore = 10

	ChatCallPriceVersion = 1
)

func CalcIncomeScore(coin int64) (score int64) {
	if coin > 0 {
		if time.Now().Unix() < DiamoStartTime {
			return int64(math.Floor(float64(coin)*coinPercentage*100/coinScoreRatio + 0.5))
		}
		return int64(float64(coin) * coinPercentage * 100 / coinScoreRatio2)
	}
	return 0
}

func CalcOldIncomeScore(coin int64) (score int64) {
	return int64(math.Floor(float64(coin)*coinPercentage*100/coinScoreRatio + 0.5)) // 保持和前边代码一样
}

func CalcFreeCoinScore(coin int64) (score int64) {
	return int64(float64(coin) * freeCoinPercentage * 100 / coinScoreRatio2)
}

func CalcIncomeMoney(score int64) (money int64) {
	if score > 0 {
		money = score / scoreMoneyRatio
	}
	return
}

func CalcCoinMoney(coin int64) (money int64) {
	if coin > 0 {
		ratio := coinMoneyRatio
		if time.Now().Unix() < DiamoStartTime {
			ratio = coinMoneyRatio2
		}
		money = int64(float64(coin) / ratio)
	}
	return
}

func CalcCoinIncomeMoney(coin int64) int64 {
	score := CalcIncomeScore(coin)
	return CalcIncomeMoney(score)
}

func FormatIncomeScore(score int64) string {
	result := fmt.Sprintf("%.2f", float64(score)/100)
	result = strings.TrimRight(result, "0")
	result = strings.TrimRight(result, ".")
	return result
}

func FormatScoreSingleDigits(score int64) string {
	result := fmt.Sprintf("%.1f", float64(score)/100)
	result = strings.TrimRight(result, "0")
	result = strings.TrimRight(result, ".")
	return result
}

func CalcSweetRate(coin int64) float32 {
	return float32(coin) / 10
}

// 大于 50 度每日期望亲密度增加
func SweetRateExpectedAdd(rate float32) float32 {
	if rate <= 0 {
		return 0
	}
	return rate * 0.03
}

func SweetRateSub(rate float32) float32 {
	if rate <= 0 {
		return 0
	}
	return rate * 0.08
}

func IsHitRiskFreeScoreRatio(score, chatScore, freeTotal, total, chatTotal int64) bool {
	if score >= 50*100 && ((total != 0 && 100*freeTotal/total >= 80) || (chatTotal != 0 && 100*freeTotal/chatTotal >= 80 && 100*chatScore/score >= 60)) {
		return true
	}
	return false
}
