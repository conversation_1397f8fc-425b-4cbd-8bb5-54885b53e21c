package formula

import (
	"fmt"
	"testing"

	"xim/baselib/util"
)

func TestFormula(t *testing.T) {
	fmt.Println(FormatIncomeScore(-100))

	var coin int64
	coin = 210
	score := CalcIncomeScore(coin)
	money := CalcIncomeMoney(score)
	fmt.Println("coin", coin)
	fmt.Println("money", money, util.FormatMoney(money))
	fmt.Println("score", score, FormatIncomeScore(score))
	fmt.Println("====================================")
	coin = 170
	score = CalcIncomeScore(coin)
	money = CalcIncomeMoney(score)
	fmt.Println("coin", coin)
	fmt.Println("money", money, util.FormatMoney(money))
	fmt.Println("score", score, FormatIncomeScore(score))
	fmt.Println("====================================")
	coin = 130
	score = CalcIncomeScore(coin)
	money = CalcIncomeMoney(score)
	fmt.Println("coin", coin)
	fmt.Println("money", money, util.FormatMoney(money))
	fmt.Println("score", score, FormatIncomeScore(score))
	fmt.Println("====================================")
	coin = 2
	score = CalcIncomeScore(coin)
	money = CalcIncomeMoney(score)
	fmt.Println("coin", coin)
	fmt.Println("money", money, util.FormatMoney(money))
	fmt.Println("score", score, FormatIncomeScore(score))
	fmt.Println("====================================")
	coin = 34
	score = CalcIncomeScore(coin)
	money = CalcIncomeMoney(score)
	fmt.Println("coin", coin)
	fmt.Println("money", money, util.FormatMoney(money))
	fmt.Println("score", score, FormatIncomeScore(score))
	fmt.Println("====================================")
	coin = 68
	score = CalcIncomeScore(coin)
	money = CalcIncomeMoney(score)
	fmt.Println("coin", coin)
	fmt.Println("money", money, util.FormatMoney(money))
	fmt.Println("score", score, FormatIncomeScore(score))
	fmt.Println("====================================")
}

func TestCoinToScore(t *testing.T) {
	coin := int64(2086)
	score := CalcFreeCoinScore(coin)
	fmt.Println("coin=", coin, "score=", FormatIncomeScore(score))
	fmt.Println("coin=", coin, "score=", FormatIncomeScore(coin*15))
}
