package formula

import (
	"math"
	"time"
)

const (
	//2023-04-06 03:25:00
	DiamoStartTime = 1680809700

	//2023-08-01 15:00:00
	oldAssetLastTime = 1690873200
)

func IsNewAssetTime() bool {
	if time.Now().Unix() > oldAssetLastTime {
		return true
	}
	return false
}

func ChatHelloPriceCoin() int64 {
	return chatHelloPriceCoin2
}

func ChatMessagePriceCoin() int64 {
	return NewChatMessagePriceCoin2
}

func MaleMatchCallPriceCoin() int64 {
	return maleMatchCallPriceCoin2
}

func ChatVoiceCallPriceCoin() int32 {
	return chatVoiceCallPriceCoin2
}

func ChatVideoCallPriceCoin() int32 {
	return chatVideoCallPriceCoin2
}

func ChatCallPriceCoin() int64 {
	return chatCallPriceCoin2
}

func TransferNewCoin(coin int64) (coin2 int64) {
	if coin > 0 {
		// 向上取整
		coin2 = int64(math.Ceil(float64(coin) / coinMoneyRatio * coinMoneyRatio2))
	}
	return
}

func EnableNewCoin() bool {
	return true
}
