package errcode

import (
	"errors"
	"fmt"
	"reflect"

	"github.com/spf13/cast"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"xim/proto/api/common"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
)

var (
	ErrorOK  = NewErrorCode(0, "success")
	ErrorNOK = NewErrorCode(1, "error")
	// 100 - 1000 系统类错误,请重试
	// ErrorStop     = NewErrorCode(100, "系统停服维护中")
	ErrorInternal = NewErrorCode(101, "server error")
	ErrorParam    = NewErrorCode(102, "parameter error")
	// ErrorPermission = NewErrorCode(103, "无权限访问")
	// ErrorIllegal    = NewErrorCode(104, "非法请求")

	// ErrorAuthorization = NewErrorCode(201, "您的账号未登录")
	// ErrorUserNull      = NewErrorCode(202, "您的账号未注册")
	// ErrorUserCancel    = NewErrorCode(203, "您的账号已注销")
	// ErrorUserBlock     = NewErrorCode(204, "您的账号已封禁")
	// ErrorUserKickOut   = NewErrorCode(205, "您的账号已在其它设备登录，现已退出登录")

	// ErrorReviewReject         = NewErrorCode(206, "内容审核不通过")
	// ErrorUserUnderage         = NewErrorCode(207, "已开启青少年模式")
	// ErrorOperationLimit       = NewErrorCode(208, "今日本操作已达上限")
	// ErrorUserMute             = NewErrorCode(209, "您已被禁言")
	// ErrorInBlacklist          = NewErrorCode(210, "您已被对方拉黑")
	// ErrorRepeatedOperation    = NewErrorCode(211, "重复的操作")
	ErrorCanceled = NewErrorCode(212, "cancel the operation")
	// ErrorRecordExisted        = NewErrorCode(213, "记录已存在或不存在")
	// ErrorReviewFakeSend       = NewErrorCode(214, "审核未发送")
	// ErrorReviewSweetNotEnough = NewErrorCode(215, "亲密度不够")
	// ErrorDeadlineExceeded     = NewErrorCode(216, "请求超时")

	// // 1000 > 业务类错误
	// ErrorCoinNotEnough             = NewErrorCode(1001, "金币余额不足")
	// ErrorCallPeerCancel            = NewErrorCode(1002, "对方已取消")
	// ErrorChatCallReject            = NewErrorCode(1003, "对方设置拒绝接听")
	// ErrorChatCallOffOnline         = NewErrorCode(1004, "对方不在线")
	// ErrorChatCallBusy              = NewErrorCode(1005, "对方忙线中")
	// ErrorRealName                  = NewErrorCode(1006, "实名认证未通过")
	// ErrorRealFace                  = NewErrorCode(1007, "真人认证未通过")
	// ErrorCallMatchCancel           = NewErrorCode(1008, "用户已取消视频速配")
	// ErrorCallMatchPrice            = NewErrorCode(1009, "您还未设置视频速配费用,请到设置里设置!")
	// ErrorCallMatching              = NewErrorCode(1010, "对方已匹配上")
	// ErrorCallMatchTodayFinish      = NewErrorCode(1011, "您今日次数已用完,明日再来!")
	// ErrorCallMatchingStatus        = NewErrorCode(1012, "您当前已在匹配状态中,请勿重复操作!")
	// ErrorCallMatchDurationTooShort = NewErrorCode(1013, "平均通话时长太短，今日无法再次使用该功能")
)

func NewErrorCode(code int32, msg string) ErrorCode {
	return ErrorCode{
		Code: code,
		Msg:  msg,
	}
}

type ErrorCode struct {
	Code int32
	Msg  string
}

func (e ErrorCode) ErrorString() string {
	return fmt.Sprintf("error code: %d, msg: %s", e.Code, e.Msg)
}

func (e ErrorCode) Error() string {
	return e.ErrorString()
}

type SvcBaseResp interface {
	GetBase() *common.SvcBaseResp
}

func IsOk(s SvcBaseResp) bool {
	return ErrorOK.EqualResp(s)
}

func NotOk(s SvcBaseResp) bool {
	return !IsOk(s)
}

func Error(s SvcBaseResp) error {
	code := s.GetBase().Code
	if code != ErrorOK.Code {
		e := ErrorCode{
			Code: code,
			Msg:  s.GetBase().Msg,
		}
		return errors.New(e.ErrorString())
	}
	return nil
}

func (e ErrorCode) EqualResp(resp SvcBaseResp) bool {
	return resp.GetBase() != nil && e.Code == resp.GetBase().GetCode()
}

func (e ErrorCode) WithErrorMsg(msg string) ErrorCode {
	ne := e
	ne.Msg = msg
	return ne
}

func (e ErrorCode) WithErrorFormatMsg(msg string, args ...interface{}) ErrorCode {
	ne := e
	ne.Msg = fmt.Sprintf(msg, args...)
	return ne
}

func (e ErrorCode) ToSvcResp() *common.SvcCommonResp {
	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{
			Code: e.Code,
			Msg:  e.Msg,
		},
	}
}

func (e ErrorCode) ToSvcRespMsg(msg string, args ...interface{}) *common.SvcCommonResp {
	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{
			Code: e.Code,
			Msg:  e.Msg + ":" + fmt.Sprintf(msg, args...),
		},
	}
}

func (e ErrorCode) ToSvcBaseResp() *common.SvcBaseResp {
	return &common.SvcBaseResp{
		Code: e.Code,
		Msg:  e.Msg,
	}
}

func (e ErrorCode) ToSvcBaseRespWithMsg(msg string) *common.SvcBaseResp {
	if len(msg) == 0 {
		msg = e.Msg
	}
	return &common.SvcBaseResp{
		Code: e.Code,
		Msg:  msg,
	}
}

func (e ErrorCode) ToSvcBaseRespMsg(msg string, args ...interface{}) *common.SvcBaseResp {
	return &common.SvcBaseResp{
		Code: e.Code,
		Msg:  e.Msg + ":" + fmt.Sprintf(msg, args...),
	}
}

type bizBaseRespInterface interface {
	GetCode() int32
	GetMsg() string
	proto.Message
}

func (e ErrorCode) ApplyResult(v bizBaseRespInterface) {
	vi := reflect.Indirect(reflect.ValueOf(v))
	vi.FieldByName("Code").SetInt(int64(e.Code))
	vi.FieldByName("Msg").SetString(e.Msg)
}

func (e ErrorCode) ApplyResultMsg(v bizBaseRespInterface, msg string) {
	vi := reflect.Indirect(reflect.ValueOf(v))
	vi.FieldByName("Code").SetInt(int64(e.Code))
	vi.FieldByName("Msg").SetString(msg)
}

// 不用再用到 BizResp 的东西了

func (e ErrorCode) ToBizResp() *common.BizBaseResp {
	return &common.BizBaseResp{
		Code: e.Code,
		Msg:  e.Msg,
	}
}

func (e ErrorCode) ToBizRespWithMsg(msg string) *common.BizBaseResp {
	return &common.BizBaseResp{
		Code: e.Code,
		Msg:  msg,
	}
}

func (e *ErrorCode) WithBizResp(resp proto.Message) (*common.BizBaseResp, error) {
	var (
		data *anypb.Any
		err  error
	)
	if resp != nil {
		data, err = anypb.New(resp)
		if err != nil {
			return nil, err
		}
	}
	return &common.BizBaseResp{
		Code: e.Code,
		Msg:  e.Msg,
		Data: data,
	}, nil
}

func SuccessWithBizResp(resp proto.Message) (*common.BizBaseResp, error) {
	return ErrorOK.WithBizResp(resp)
}

func FromResp(s SvcBaseResp) ErrorCode {
	return ErrorCode{
		Code: s.GetBase().GetCode(),
		Msg:  s.GetBase().GetMsg(),
	}
}

func TransSvcRespError(s SvcBaseResp) *common.BizBaseResp {
	return FromResp(s).ToBizResp()
}

func ConvertError(err error) ErrorCode {
	if err == nil {
		return ErrorOK
	}
	s, ok := status.FromError(err)
	if !ok {
		return ErrorInternal
	}
	if s.Code() == codes.Canceled {
		return ErrorCanceled
	}
	return ErrorInternal
}

// check svc resp
func CheckSvcResp(resp SvcBaseResp, err error) ErrorCode {
	if err != nil {
		s, ok := status.FromError(err)
		if !ok {
			return ErrorInternal
		}
		if s.Code() == codes.Canceled {
			return ErrorCanceled
		}
		return ErrorInternal
	}
	if resp.GetBase() == nil {
		return ErrorInternal
	}
	if resp.GetBase().GetCode() != 0 {
		return ErrorCode{
			Code: resp.GetBase().Code,
			Msg:  resp.GetBase().Msg,
		}
	}
	return ErrorOK
}

func GetSvcRspErr(err error, resp SvcBaseResp) error {
	if err != nil || NotOk(resp) {
		return getSvcRspError(err, resp)
	}
	return nil
}

func getSvcRspError(err error, resp SvcBaseResp) error {
	if err != nil {
		return err
	}
	errMsg := resp.GetBase().GetMsg()
	if errMsg == "" {
		errMsg = cast.ToString(resp.GetBase().GetCode())
	}
	return errors.New(errMsg)
}
