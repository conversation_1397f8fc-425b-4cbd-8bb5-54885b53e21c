package live

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"xim/baselib/httpcli"
	"xim/baselib/metric"
	"xim/baselib/pusher/proto"
	"xim/baselib/server/env"
	"xim/baselib/util"
	"xim/proto/api/svclive"
)

const (
	Appid     = "1030"
	SecretKey = "i88NYdbQ8YlLP8GiRTvxFLqN5nF7zz"
)

const (
	HeaderCookie = "Cookie"
)

func GenLiveSign(ts int64) string {
	return util.Md5String(fmt.Sprintf(SecretKey + fmt.Sprintf("%d", ts)))
}

func CheckLiveSignValid(ts int64, sign string) bool {
	return GenLiveSign(ts) == sign
}

func GetLiveCookie(mds map[string]string) string {
	if cookie := mds[HeaderCookie]; cookie != "" {
		return cookie
	}
	if cookie := mds[strings.ToLower(HeaderCookie)]; cookie != "" {
		return cookie
	}
	return ""
}

func GetSessionidAndUserid(mds map[string]string) (sessionId string, userid int64) {
	cookie := GetLiveCookie(mds)
	if len(cookie) == 0 {
		return "", 0
	}
	datas := strings.Split(cookie, ";")
	if len(datas) == 0 {
		return "", 0
	}

	for _, s := range datas {
		if strings.Contains(s, "sessionid") {
			sessionid := strings.Split(s, "=")
			if len(sessionid) > 1 {
				sessionId = sessionid[1]
			}
		}
		if strings.Contains(s, "userid") {
			userId := strings.Split(s, "=")
			if len(userId) > 0 {
				userid, _ = strconv.ParseInt(userId[1], 10, 64)
			}
		}
		if len(sessionId) != 0 && userid != 0 {
			break
		}
	}
	return
}

func GetUseridFromCookie(mds map[string]string) (userid int64) {
	cookie := GetLiveCookie(mds)
	if len(cookie) == 0 {
		return 0
	}
	datas := strings.Split(cookie, ";")
	if len(datas) == 0 {
		return 0
	}

	for _, s := range datas {
		if strings.Contains(s, "userid") {
			userId := strings.Split(s, "=")
			if len(userId) > 0 {
				userid, _ = strconv.ParseInt(userId[1], 10, 64)
				break
			}
		}
	}
	return
}

const (
	LiveHomeAction       string = "home"        // 直播首页
	LiveGiftAction       string = "gift"        // 礼物列表
	LiveBagAction        string = "bag"         // 用户背包
	LiveRegisterAction   string = "register"    // 注册
	LiveUpdateInfoAction string = "update_info" // 用户信息同步
	LiveUserInfoAction   string = "user_info"   // 获取直播用户信息
	LiveUserStatusAction string = "live_status" // 获取主播状态
)

func GetHostByAction(action string) string {
	if env.IsProd() {
		return getHost(action)
	}
	return getHostTest(action)
}

func getHostTest(action string) string {
	switch action {
	case LiveHomeAction,
		LiveBagAction,
		LiveRegisterAction,
		LiveUpdateInfoAction,
		LiveUserInfoAction,
		LiveUserStatusAction:
		return "https://test-soagw-live-ssl.xunlei.com"
	case LiveGiftAction:
		return "https://test-biz-live-ssl.xunlei.com"
	}
	return ""
}

func getHost(action string) string {
	switch action {
	case LiveHomeAction,
		LiveBagAction,
		LiveRegisterAction,
		LiveUpdateInfoAction,
		LiveUserInfoAction,
		LiveUserStatusAction:
		return "https://soagw-live-ssl.xunlei.com"
	case LiveGiftAction:
		return "https://biz-live-ssl.xunlei.com"
	}
	return ""
}

func GetDevOs(dt int32) string {
	switch dt {
	case int32(proto.PlatformAndroid):
		return "android"
	case int32(proto.PlatformIos):
		return "ios"
	default:
		return ""
	}
}

func GenResiterCookie(sessionid, guid, appcode, appver, os, osver string) string {
	var values []string
	values = append(values, fmt.Sprintf("appid=%s", Appid))
	values = append(values, fmt.Sprintf("guid=%s", guid))
	values = append(values, fmt.Sprintf("appcode=%s", appcode))
	values = append(values, fmt.Sprintf("appver=%s", appver))
	values = append(values, fmt.Sprintf("os=%s", os))
	values = append(values, fmt.Sprintf("osver=%s", osver))
	values = append(values, fmt.Sprintf("sessionid=%s", sessionid))
	return strings.Join(values, ";")
}

func GenHomeCookie(userid int64, guid, deviceid, channel string) string {
	var values []string
	values = append(values, fmt.Sprintf("appid=%s", Appid))
	values = append(values, fmt.Sprintf("guid=%s", guid))
	values = append(values, fmt.Sprintf("deviceid=%s", deviceid))
	values = append(values, fmt.Sprintf("channel=%s", channel))
	values = append(values, fmt.Sprintf("userid=%d", userid))
	return strings.Join(values, ";")
}

func NeedShieldGift(gtype int64, isLucky int32) bool {
	if isLucky == 1 {
		return true
	}
	switch svclive.LiveGiftType(gtype) {
	case svclive.LiveGiftType_gt_normal, //普通礼物
		svclive.LiveGiftType_gt_full_ad,    //全服公告礼物
		svclive.LiveGiftType_gt_discount,   //特价礼物
		svclive.LiveGiftType_gt_confession, //告白礼物
		svclive.LiveGiftType_gt_hot:        //热度礼物
		return false
	default:
		return true
	}
}

var filterHomeUserids = map[int64]bool{
	875170856: true,
	732085858: true,
}

func IsFilterHomeUserid(userid int64) bool {
	if _, ok := filterHomeUserids[userid]; ok {
		return true
	}
	return false
}

func GetTitle(title string) string {
	if len(title) == 0 {
		return ""
	}
	return fmt.Sprintf("<b><font color=\"#FF2EAB\">%s</font></b>", title)
}

func LiveBizHttpPost(action string, apiUrl string, headers map[string]string, timeout int64, data interface{}) (body string, err error) {
	if timeout == 0 {
		timeout = 2
	}
	st := time.Now()
	body, err = httpcli.PostJsonWithTimeout(apiUrl, headers, timeout, data)
	if err != nil {
		return
	}
	cost := time.Since(st).Milliseconds()
	metric.HistogramLabelsWithBuckets("biz_live_http", map[string]string{
		"action": action,
		"url":    apiUrl,
	}, []float64{10, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 3000, 4000}).Observe(float64(cost))
	return
}

func LiveSvcHttpPost(action string, apiUrl string, headers map[string]string, timeout int64, data interface{}) (body string, err error) {
	if timeout == 0 {
		timeout = 2
	}
	st := time.Now()
	body, err = httpcli.PostJsonWithTimeout(apiUrl, headers, timeout, data)
	if err != nil {
		return
	}
	cost := time.Since(st).Milliseconds()
	metric.HistogramLabelsWithBuckets("svc_live_http", map[string]string{
		"action": action,
		"url":    apiUrl,
	}, []float64{10, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 3000, 4000}).Observe(float64(cost))
	return
}

func LiveSvcHttpGet(action string, apiUrl string, headers map[string]string, timeout int64) (body string, err error) {
	if timeout == 0 {
		timeout = 2
	}
	st := time.Now()
	body, err = httpcli.Get(apiUrl, headers, nil)
	if err != nil {
		return
	}
	cost := time.Since(st).Milliseconds()
	metric.HistogramLabelsWithBuckets("svc_live_http", map[string]string{
		"action": action,
		"url":    apiUrl,
	}, []float64{10, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 3000, 4000}).Observe(float64(cost))
	return
}
