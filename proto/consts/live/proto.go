package live

type LiveUserLevel struct {
	Current int32  `json:"current"`
	Icon3   string `json:"icon3"`
}

type LiveUserInfo struct {
	Uuid     string        `json:"uuid"`
	Avatar   string        `json:"avatar"`
	Nickname string        `json:"nickname"`
	Level    LiveUserLevel `json:"level"`
}

type LiveUserData struct {
	UserInfo LiveUserInfo `json:"user_info"`
}

type LiveUserInfoResp struct {
	Result  int32        `json:"result"`
	Message string       `json:"message"`
	Data    LiveUserData `json:"data"`
}

// "is_lucky":1,"player_percent":20, "gtype":1,
type BagItemExt struct {
	IsLucky int `json:"is_lucky"`
	GTyppe  int `json:"gtype"`
}
