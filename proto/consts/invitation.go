package consts

const (
	InvitationDailyTypeNewUser  = 1
	InvitationDailyTypeScore    = 2
	InvitationDailyTypeWorkUser = 3

	InvitationIncomeRate = 30

	InvitationSuperiorLevelTotal = 1
	InvitationIncomeLevel1Rate   = 8
	InvitationIncomeLevel2Rate   = 0
	InvitationIncomeLevel3Rate   = 0
)

var (
	guildUsers = map[int64]int32{}
)

func IsGuildUser(userid int64) (ok bool, gid int32) {
	gid, ok = guildUsers[userid]
	return
}
