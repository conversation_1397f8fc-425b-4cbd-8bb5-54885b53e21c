package consts

import (
	"fmt"
	"strings"
	"xim/baselib/server/env"
)

const (
	ActivityTypeGiftCollect = 1 //礼物收集

	ActivityAKeyGeneralLottery  = "lottery"          //充值抽奖
	ActivityAKeyRechargeLottery = "recharge-lottery" //充值抽奖
	LotteryChanceTypeGeneral    = 0
	LotteryChanceTypeRecharge   = 1

	RechargeLotteryChanceLimit = 3

	InvitationDailyTypeUser = 1

	ActivityLoveCard = "love-card" // 恋爱助力卡

	BannerPositionMessageTop = "message_top"

	JackpotTaskResultUndo  = 0
	JackpotTaskResultDone  = 1 //已完成，但未领取奖励
	JackpotTaskResultAward = 2 //已领取奖励

	JackpotRankingTypeConsumedCoin = 1
	JackpotRankingTypeIncomeScore  = 2
)

const (
	JackpotAkey            = "jackpot-202212"
	JackpotStartTime       = 1671501600 //2022-12-20 10:00:00
	JackpotEndTime         = 1671897600 //2022-12-25 00:00:00
	JackpotRankingTimeType = "total"
	JackpotRankingTopLimit = 20
)

func FullActivityUrl(akay string) string {
	baseUrl := "https://h5-api-lianli-test.joinedchat.com/activity"
	if env.IsProd() {
		baseUrl = "https://h5-api.joinedchat.com/activity"
	}

	// 处理poster
	key := strings.Replace(akay, "poster-", "poster/", 1)
	return fmt.Sprintf("%s/%s?hideStatusBar=true", baseUrl, key)
}
