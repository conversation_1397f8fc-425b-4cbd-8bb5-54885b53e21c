// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: svcfamily.proto

package svcfamily

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	basemsgtransfer "xim/proto/api/basemsgtransfer"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MsgHandleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg  *basemsgtransfer.MsgData `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Base *common.BaseParam        `protobuf:"bytes,2,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *MsgHandleReq) Reset() {
	*x = MsgHandleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcfamily_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgHandleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgHandleReq) ProtoMessage() {}

func (x *MsgHandleReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcfamily_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgHandleReq.ProtoReflect.Descriptor instead.
func (*MsgHandleReq) Descriptor() ([]byte, []int) {
	return file_svcfamily_proto_rawDescGZIP(), []int{0}
}

func (x *MsgHandleReq) GetMsg() *basemsgtransfer.MsgData {
	if x != nil {
		return x.Msg
	}
	return nil
}

func (x *MsgHandleReq) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

type MsgHandleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base  *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Exts  map[string]string   `protobuf:"bytes,2,rep,name=exts,proto3" json:"exts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Topic string              `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *MsgHandleResp) Reset() {
	*x = MsgHandleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcfamily_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgHandleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgHandleResp) ProtoMessage() {}

func (x *MsgHandleResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcfamily_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgHandleResp.ProtoReflect.Descriptor instead.
func (*MsgHandleResp) Descriptor() ([]byte, []int) {
	return file_svcfamily_proto_rawDescGZIP(), []int{1}
}

func (x *MsgHandleResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *MsgHandleResp) GetExts() map[string]string {
	if x != nil {
		return x.Exts
	}
	return nil
}

func (x *MsgHandleResp) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

var File_svcfamily_proto protoreflect.FileDescriptor

var file_svcfamily_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x1a,
	0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64, 0x0a, 0x0c, 0x4d,
	0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x22, 0xc2, 0x01, 0x0a, 0x0d, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x04,
	0x65, 0x78, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x45, 0x78, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x04, 0x65, 0x78, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x1a, 0x37, 0x0a,
	0x09, 0x45, 0x78, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x4b, 0x0a, 0x01, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x4d,
	0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x66, 0x61, 0x6d,
	0x69, 0x6c, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x42, 0x19, 0x5a, 0x17, 0x78, 0x69, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_svcfamily_proto_rawDescOnce sync.Once
	file_svcfamily_proto_rawDescData = file_svcfamily_proto_rawDesc
)

func file_svcfamily_proto_rawDescGZIP() []byte {
	file_svcfamily_proto_rawDescOnce.Do(func() {
		file_svcfamily_proto_rawDescData = protoimpl.X.CompressGZIP(file_svcfamily_proto_rawDescData)
	})
	return file_svcfamily_proto_rawDescData
}

var file_svcfamily_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_svcfamily_proto_goTypes = []interface{}{
	(*MsgHandleReq)(nil),            // 0: vc.svcfamily.MsgHandleReq
	(*MsgHandleResp)(nil),           // 1: vc.svcfamily.MsgHandleResp
	nil,                             // 2: vc.svcfamily.MsgHandleResp.ExtsEntry
	(*basemsgtransfer.MsgData)(nil), // 3: vc.basemsgtransfer.MsgData
	(*common.BaseParam)(nil),        // 4: common.BaseParam
	(*common.SvcBaseResp)(nil),      // 5: common.SvcBaseResp
}
var file_svcfamily_proto_depIdxs = []int32{
	3, // 0: vc.svcfamily.MsgHandleReq.msg:type_name -> vc.basemsgtransfer.MsgData
	4, // 1: vc.svcfamily.MsgHandleReq.base:type_name -> common.BaseParam
	5, // 2: vc.svcfamily.MsgHandleResp.base:type_name -> common.SvcBaseResp
	2, // 3: vc.svcfamily.MsgHandleResp.exts:type_name -> vc.svcfamily.MsgHandleResp.ExtsEntry
	0, // 4: vc.svcfamily.s.MsgHandle:input_type -> vc.svcfamily.MsgHandleReq
	1, // 5: vc.svcfamily.s.MsgHandle:output_type -> vc.svcfamily.MsgHandleResp
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_svcfamily_proto_init() }
func file_svcfamily_proto_init() {
	if File_svcfamily_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_svcfamily_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgHandleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_svcfamily_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgHandleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_svcfamily_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_svcfamily_proto_goTypes,
		DependencyIndexes: file_svcfamily_proto_depIdxs,
		MessageInfos:      file_svcfamily_proto_msgTypes,
	}.Build()
	File_svcfamily_proto = out.File
	file_svcfamily_proto_rawDesc = nil
	file_svcfamily_proto_goTypes = nil
	file_svcfamily_proto_depIdxs = nil
}
