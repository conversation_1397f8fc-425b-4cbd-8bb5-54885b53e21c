syntax = "proto3";

package  vc.svcfamily;
option go_package = "xim/proto/api/svcfamily";

import "common/common.proto";
import "basemsgtransfer/basemsgtransfer.proto";

message MsgHandleReq {
  basemsgtransfer.MsgData msg = 1;
  common.BaseParam base = 2;
}

message MsgHandleResp {
  common.SvcBaseResp base = 1;
  map<string, string> exts = 2;
  string topic = 3;
}

service s {
  rpc MsgHandle(MsgHandleReq) returns  (MsgHandleResp) {}
}
