// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: basemsgtopic.proto

package basemsgtopic

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConnStatusType int32

const (
	// 占位无效
	ConnStatusType_NoneType ConnStatusType = 0
	// 长连接建立
	ConnStatusType_ConnectType ConnStatusType = 1
	// 长连接断开
	ConnStatusType_CloseType ConnStatusType = 2
	// 长连接心跳维持
	ConnStatusType_PingType ConnStatusType = 3
)

// Enum value maps for ConnStatusType.
var (
	ConnStatusType_name = map[int32]string{
		0: "NoneType",
		1: "ConnectType",
		2: "CloseType",
		3: "PingType",
	}
	ConnStatusType_value = map[string]int32{
		"NoneType":    0,
		"ConnectType": 1,
		"CloseType":   2,
		"PingType":    3,
	}
)

func (x ConnStatusType) Enum() *ConnStatusType {
	p := new(ConnStatusType)
	*p = x
	return p
}

func (x ConnStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtopic_proto_enumTypes[0].Descriptor()
}

func (ConnStatusType) Type() protoreflect.EnumType {
	return &file_basemsgtopic_proto_enumTypes[0]
}

func (x ConnStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnStatusType.Descriptor instead.
func (ConnStatusType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{0}
}

type TestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestReq) Reset() {
	*x = TestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReq) ProtoMessage() {}

func (x *TestReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReq.ProtoReflect.Descriptor instead.
func (*TestReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{0}
}

type TestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *TestRsp) Reset() {
	*x = TestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRsp) ProtoMessage() {}

func (x *TestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRsp.ProtoReflect.Descriptor instead.
func (*TestRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{1}
}

func (x *TestRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *TestRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ConnStatusReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 长连接状态上报类型，1:conn，2：disconn，close，3：ping
	Type ConnStatusType `protobuf:"varint,1,opt,name=type,proto3,enum=vc.basemsgtopic.ConnStatusType" json:"type,omitempty"`
	// 用户token，解析出用户所有信息
	UserToken string `protobuf:"bytes,2,opt,name=userToken,proto3" json:"userToken,omitempty"`
	// 长连接ID
	ConnId uint64 `protobuf:"varint,3,opt,name=connId,proto3" json:"connId,omitempty"`
	// 对应Broker IP
	BrokerIP string `protobuf:"bytes,4,opt,name=brokerIP,proto3" json:"brokerIP,omitempty"`
	// 对应Broker上报时刻的时间戳(unit: ns)
	OpTime int64 `protobuf:"varint,5,opt,name=opTime,proto3" json:"opTime,omitempty"`
}

func (x *ConnStatusReportReq) Reset() {
	*x = ConnStatusReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnStatusReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnStatusReportReq) ProtoMessage() {}

func (x *ConnStatusReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnStatusReportReq.ProtoReflect.Descriptor instead.
func (*ConnStatusReportReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{2}
}

func (x *ConnStatusReportReq) GetType() ConnStatusType {
	if x != nil {
		return x.Type
	}
	return ConnStatusType_NoneType
}

func (x *ConnStatusReportReq) GetUserToken() string {
	if x != nil {
		return x.UserToken
	}
	return ""
}

func (x *ConnStatusReportReq) GetConnId() uint64 {
	if x != nil {
		return x.ConnId
	}
	return 0
}

func (x *ConnStatusReportReq) GetBrokerIP() string {
	if x != nil {
		return x.BrokerIP
	}
	return ""
}

func (x *ConnStatusReportReq) GetOpTime() int64 {
	if x != nil {
		return x.OpTime
	}
	return 0
}

type ConnStatusReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ConnStatusReportRsp) Reset() {
	*x = ConnStatusReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnStatusReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnStatusReportRsp) ProtoMessage() {}

func (x *ConnStatusReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnStatusReportRsp.ProtoReflect.Descriptor instead.
func (*ConnStatusReportRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{3}
}

func (x *ConnStatusReportRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *ConnStatusReportRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SubscribeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，可指定该设备才订阅topic，设备为空默认用户下所有设备都订阅
	DeviceId string `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 订阅的topic列表
	Topics []string `protobuf:"bytes,4,rep,name=topics,proto3" json:"topics,omitempty"`
}

func (x *SubscribeReq) Reset() {
	*x = SubscribeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeReq) ProtoMessage() {}

func (x *SubscribeReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeReq.ProtoReflect.Descriptor instead.
func (*SubscribeReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{4}
}

func (x *SubscribeReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *SubscribeReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SubscribeReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SubscribeReq) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

type SubscribeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SubscribeRsp) Reset() {
	*x = SubscribeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRsp) ProtoMessage() {}

func (x *SubscribeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRsp.ProtoReflect.Descriptor instead.
func (*SubscribeRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{5}
}

func (x *SubscribeRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SubscribeRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UnSubscribeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，可指定该设备才取消订阅topic，设备为空默认用户下所有设备都取消订阅
	DeviceId string `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 取消订阅的topic列表
	Topics []string `protobuf:"bytes,4,rep,name=topics,proto3" json:"topics,omitempty"`
}

func (x *UnSubscribeReq) Reset() {
	*x = UnSubscribeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnSubscribeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSubscribeReq) ProtoMessage() {}

func (x *UnSubscribeReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSubscribeReq.ProtoReflect.Descriptor instead.
func (*UnSubscribeReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{6}
}

func (x *UnSubscribeReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *UnSubscribeReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnSubscribeReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UnSubscribeReq) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

type UnSubscribeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *UnSubscribeRsp) Reset() {
	*x = UnSubscribeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnSubscribeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSubscribeRsp) ProtoMessage() {}

func (x *UnSubscribeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSubscribeRsp.ProtoReflect.Descriptor instead.
func (*UnSubscribeRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{7}
}

func (x *UnSubscribeRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *UnSubscribeRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type TopicUsersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 订阅的topic
	Topic string `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *TopicUsersReq) Reset() {
	*x = TopicUsersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicUsersReq) ProtoMessage() {}

func (x *TopicUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicUsersReq.ProtoReflect.Descriptor instead.
func (*TopicUsersReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{8}
}

func (x *TopicUsersReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *TopicUsersReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type TopicUsersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// topic下所有的用户列表
	Data *TopicUsersData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TopicUsersRsp) Reset() {
	*x = TopicUsersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicUsersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicUsersRsp) ProtoMessage() {}

func (x *TopicUsersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicUsersRsp.ProtoReflect.Descriptor instead.
func (*TopicUsersRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{9}
}

func (x *TopicUsersRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *TopicUsersRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TopicUsersRsp) GetData() *TopicUsersData {
	if x != nil {
		return x.Data
	}
	return nil
}

type TopicUsersData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id列表
	UserIds []string `protobuf:"bytes,1,rep,name=userIds,proto3" json:"userIds,omitempty"`
}

func (x *TopicUsersData) Reset() {
	*x = TopicUsersData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicUsersData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicUsersData) ProtoMessage() {}

func (x *TopicUsersData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicUsersData.ProtoReflect.Descriptor instead.
func (*TopicUsersData) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{10}
}

func (x *TopicUsersData) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type TopicUsersWithConnInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// topic下的所有用户长连接信息列表
	Data *TopicUsersWithConnInfoData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TopicUsersWithConnInfoRsp) Reset() {
	*x = TopicUsersWithConnInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicUsersWithConnInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicUsersWithConnInfoRsp) ProtoMessage() {}

func (x *TopicUsersWithConnInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicUsersWithConnInfoRsp.ProtoReflect.Descriptor instead.
func (*TopicUsersWithConnInfoRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{11}
}

func (x *TopicUsersWithConnInfoRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *TopicUsersWithConnInfoRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TopicUsersWithConnInfoRsp) GetData() *TopicUsersWithConnInfoData {
	if x != nil {
		return x.Data
	}
	return nil
}

type TopicUsersWithConnInfoData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 详细的长连接信息列表
	Conns []*PlatformUserConn `protobuf:"bytes,1,rep,name=conns,proto3" json:"conns,omitempty"`
}

func (x *TopicUsersWithConnInfoData) Reset() {
	*x = TopicUsersWithConnInfoData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicUsersWithConnInfoData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicUsersWithConnInfoData) ProtoMessage() {}

func (x *TopicUsersWithConnInfoData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicUsersWithConnInfoData.ProtoReflect.Descriptor instead.
func (*TopicUsersWithConnInfoData) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{12}
}

func (x *TopicUsersWithConnInfoData) GetConns() []*PlatformUserConn {
	if x != nil {
		return x.Conns
	}
	return nil
}

type GetPlatformUserConnReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 平台用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	// 平台用户设备id，设备为空则查用户所有的长连接
	DeviceId string `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *GetPlatformUserConnReq) Reset() {
	*x = GetPlatformUserConnReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlatformUserConnReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatformUserConnReq) ProtoMessage() {}

func (x *GetPlatformUserConnReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatformUserConnReq.ProtoReflect.Descriptor instead.
func (*GetPlatformUserConnReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{13}
}

func (x *GetPlatformUserConnReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *GetPlatformUserConnReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetPlatformUserConnReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetPlatformUserConnRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 平台用户长连接数据
	Data *PlatformUserConnData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetPlatformUserConnRsp) Reset() {
	*x = GetPlatformUserConnRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlatformUserConnRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatformUserConnRsp) ProtoMessage() {}

func (x *GetPlatformUserConnRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatformUserConnRsp.ProtoReflect.Descriptor instead.
func (*GetPlatformUserConnRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{14}
}

func (x *GetPlatformUserConnRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetPlatformUserConnRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPlatformUserConnRsp) GetData() *PlatformUserConnData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PlatformUserConnData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台用户长连接列表
	Conns []*PlatformUserConn `protobuf:"bytes,1,rep,name=conns,proto3" json:"conns,omitempty"`
}

func (x *PlatformUserConnData) Reset() {
	*x = PlatformUserConnData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatformUserConnData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformUserConnData) ProtoMessage() {}

func (x *PlatformUserConnData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformUserConnData.ProtoReflect.Descriptor instead.
func (*PlatformUserConnData) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{15}
}

func (x *PlatformUserConnData) GetConns() []*PlatformUserConn {
	if x != nil {
		return x.Conns
	}
	return nil
}

type PlatformUserConn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 平台用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id
	DeviceId string `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 客户端标识id
	ClientId uint32 `protobuf:"varint,7,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// 客户端版本
	ClientVersion string `protobuf:"bytes,8,opt,name=clientVersion,proto3" json:"clientVersion,omitempty"`
	// 用户长连接所在broker的ip
	BrokerIp string `protobuf:"bytes,4,opt,name=brokerIp,proto3" json:"brokerIp,omitempty"`
	// 用户长连接唯一id
	ConnId uint64 `protobuf:"varint,5,opt,name=connId,proto3" json:"connId,omitempty"`
	// 用户长连接时间
	ConnTime int64 `protobuf:"varint,6,opt,name=connTime,proto3" json:"connTime,omitempty"`
}

func (x *PlatformUserConn) Reset() {
	*x = PlatformUserConn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatformUserConn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformUserConn) ProtoMessage() {}

func (x *PlatformUserConn) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformUserConn.ProtoReflect.Descriptor instead.
func (*PlatformUserConn) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{16}
}

func (x *PlatformUserConn) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *PlatformUserConn) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PlatformUserConn) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PlatformUserConn) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *PlatformUserConn) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *PlatformUserConn) GetBrokerIp() string {
	if x != nil {
		return x.BrokerIp
	}
	return ""
}

func (x *PlatformUserConn) GetConnId() uint64 {
	if x != nil {
		return x.ConnId
	}
	return 0
}

func (x *PlatformUserConn) GetConnTime() int64 {
	if x != nil {
		return x.ConnTime
	}
	return 0
}

type UserTopicsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，可指定该设备订阅的topic，设备为空默认查询用户下所有设备的订阅topic
	DeviceId string `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *UserTopicsReq) Reset() {
	*x = UserTopicsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserTopicsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicsReq) ProtoMessage() {}

func (x *UserTopicsReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicsReq.ProtoReflect.Descriptor instead.
func (*UserTopicsReq) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{17}
}

func (x *UserTopicsReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *UserTopicsReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserTopicsReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type UserTopicsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 用户订阅的topic列表
	Data *UserTopicsData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UserTopicsRsp) Reset() {
	*x = UserTopicsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserTopicsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicsRsp) ProtoMessage() {}

func (x *UserTopicsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicsRsp.ProtoReflect.Descriptor instead.
func (*UserTopicsRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{18}
}

func (x *UserTopicsRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *UserTopicsRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UserTopicsRsp) GetData() *UserTopicsData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserTopicsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户和设备的订阅topic列表
	List []*UserDeviceTopicsData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *UserTopicsData) Reset() {
	*x = UserTopicsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserTopicsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicsData) ProtoMessage() {}

func (x *UserTopicsData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicsData.ProtoReflect.Descriptor instead.
func (*UserTopicsData) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{19}
}

func (x *UserTopicsData) GetList() []*UserDeviceTopicsData {
	if x != nil {
		return x.List
	}
	return nil
}

type UserDeviceTopicsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接
	DeviceId string `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 订阅的topic列表
	Topics []string `protobuf:"bytes,4,rep,name=topics,proto3" json:"topics,omitempty"`
}

func (x *UserDeviceTopicsData) Reset() {
	*x = UserDeviceTopicsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtopic_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDeviceTopicsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDeviceTopicsData) ProtoMessage() {}

func (x *UserDeviceTopicsData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtopic_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDeviceTopicsData.ProtoReflect.Descriptor instead.
func (*UserDeviceTopicsData) Descriptor() ([]byte, []int) {
	return file_basemsgtopic_proto_rawDescGZIP(), []int{20}
}

func (x *UserDeviceTopicsData) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *UserDeviceTopicsData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserDeviceTopicsData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserDeviceTopicsData) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

var File_basemsgtopic_proto protoreflect.FileDescriptor

var file_basemsgtopic_proto_rawDesc = []byte{
	0x0a, 0x12, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x3b,
	0x0a, 0x07, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x13,
	0x43, 0x6f, 0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x49, 0x50, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x49, 0x50, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x47, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7a, 0x0a, 0x0c, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x22, 0x40, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7c, 0x0a, 0x0e, 0x55, 0x6e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x22, 0x42, 0x0a, 0x0e, 0x55, 0x6e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x45, 0x0a, 0x0d, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x22, 0x76, 0x0a, 0x0d, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2a, 0x0a, 0x0e, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x19, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x55, 0x0a, 0x1a, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x22, 0x6c,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x4f, 0x0a, 0x14, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x05,
	0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x52, 0x05,
	0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x10, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f,
	0x6e, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x63, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4b, 0x0a,
	0x0e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x39, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x14, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x2a,
	0x4c, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x10, 0x02, 0x12,
	0x0c, 0x0a, 0x08, 0x50, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x10, 0x03, 0x32, 0xc6, 0x06,
	0x0a, 0x01, 0x73, 0x12, 0x6c, 0x0a, 0x04, 0x54, 0x65, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22,
	0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x78, 0x6c, 0x6c,
	0x69, 0x76, 0x65, 0x6d, 0x70, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x2e, 0x6a, 0x73, 0x6f,
	0x6e, 0x12, 0x60, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x43, 0x6f,
	0x6e, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x51, 0x0a, 0x0b, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12,
	0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x2e, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x2e, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0a, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x30, 0x01, 0x12, 0x6e, 0x0a, 0x1c, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x57,
	0x69, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x30, 0x01, 0x12, 0x69, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x6e, 0x12, 0x27, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x73, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x29, 0x5a, 0x27, 0x78, 0x69, 0x6d, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x3b, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_basemsgtopic_proto_rawDescOnce sync.Once
	file_basemsgtopic_proto_rawDescData = file_basemsgtopic_proto_rawDesc
)

func file_basemsgtopic_proto_rawDescGZIP() []byte {
	file_basemsgtopic_proto_rawDescOnce.Do(func() {
		file_basemsgtopic_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgtopic_proto_rawDescData)
	})
	return file_basemsgtopic_proto_rawDescData
}

var file_basemsgtopic_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_basemsgtopic_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_basemsgtopic_proto_goTypes = []interface{}{
	(ConnStatusType)(0),                // 0: vc.basemsgtopic.ConnStatusType
	(*TestReq)(nil),                    // 1: vc.basemsgtopic.TestReq
	(*TestRsp)(nil),                    // 2: vc.basemsgtopic.TestRsp
	(*ConnStatusReportReq)(nil),        // 3: vc.basemsgtopic.ConnStatusReportReq
	(*ConnStatusReportRsp)(nil),        // 4: vc.basemsgtopic.ConnStatusReportRsp
	(*SubscribeReq)(nil),               // 5: vc.basemsgtopic.SubscribeReq
	(*SubscribeRsp)(nil),               // 6: vc.basemsgtopic.SubscribeRsp
	(*UnSubscribeReq)(nil),             // 7: vc.basemsgtopic.UnSubscribeReq
	(*UnSubscribeRsp)(nil),             // 8: vc.basemsgtopic.UnSubscribeRsp
	(*TopicUsersReq)(nil),              // 9: vc.basemsgtopic.TopicUsersReq
	(*TopicUsersRsp)(nil),              // 10: vc.basemsgtopic.TopicUsersRsp
	(*TopicUsersData)(nil),             // 11: vc.basemsgtopic.TopicUsersData
	(*TopicUsersWithConnInfoRsp)(nil),  // 12: vc.basemsgtopic.TopicUsersWithConnInfoRsp
	(*TopicUsersWithConnInfoData)(nil), // 13: vc.basemsgtopic.TopicUsersWithConnInfoData
	(*GetPlatformUserConnReq)(nil),     // 14: vc.basemsgtopic.GetPlatformUserConnReq
	(*GetPlatformUserConnRsp)(nil),     // 15: vc.basemsgtopic.GetPlatformUserConnRsp
	(*PlatformUserConnData)(nil),       // 16: vc.basemsgtopic.PlatformUserConnData
	(*PlatformUserConn)(nil),           // 17: vc.basemsgtopic.PlatformUserConn
	(*UserTopicsReq)(nil),              // 18: vc.basemsgtopic.UserTopicsReq
	(*UserTopicsRsp)(nil),              // 19: vc.basemsgtopic.UserTopicsRsp
	(*UserTopicsData)(nil),             // 20: vc.basemsgtopic.UserTopicsData
	(*UserDeviceTopicsData)(nil),       // 21: vc.basemsgtopic.UserDeviceTopicsData
}
var file_basemsgtopic_proto_depIdxs = []int32{
	0,  // 0: vc.basemsgtopic.ConnStatusReportReq.type:type_name -> vc.basemsgtopic.ConnStatusType
	11, // 1: vc.basemsgtopic.TopicUsersRsp.data:type_name -> vc.basemsgtopic.TopicUsersData
	13, // 2: vc.basemsgtopic.TopicUsersWithConnInfoRsp.data:type_name -> vc.basemsgtopic.TopicUsersWithConnInfoData
	17, // 3: vc.basemsgtopic.TopicUsersWithConnInfoData.conns:type_name -> vc.basemsgtopic.PlatformUserConn
	16, // 4: vc.basemsgtopic.GetPlatformUserConnRsp.data:type_name -> vc.basemsgtopic.PlatformUserConnData
	17, // 5: vc.basemsgtopic.PlatformUserConnData.conns:type_name -> vc.basemsgtopic.PlatformUserConn
	20, // 6: vc.basemsgtopic.UserTopicsRsp.data:type_name -> vc.basemsgtopic.UserTopicsData
	21, // 7: vc.basemsgtopic.UserTopicsData.list:type_name -> vc.basemsgtopic.UserDeviceTopicsData
	1,  // 8: vc.basemsgtopic.s.Test:input_type -> vc.basemsgtopic.TestReq
	3,  // 9: vc.basemsgtopic.s.ConnStatusReport:input_type -> vc.basemsgtopic.ConnStatusReportReq
	5,  // 10: vc.basemsgtopic.s.Subscribe:input_type -> vc.basemsgtopic.SubscribeReq
	7,  // 11: vc.basemsgtopic.s.UnSubscribe:input_type -> vc.basemsgtopic.UnSubscribeReq
	9,  // 12: vc.basemsgtopic.s.TopicUsers:input_type -> vc.basemsgtopic.TopicUsersReq
	9,  // 13: vc.basemsgtopic.s.StreamTopicUsers:input_type -> vc.basemsgtopic.TopicUsersReq
	9,  // 14: vc.basemsgtopic.s.StreamTopicUsersWithConnInfo:input_type -> vc.basemsgtopic.TopicUsersReq
	14, // 15: vc.basemsgtopic.s.GetPlatformUserConn:input_type -> vc.basemsgtopic.GetPlatformUserConnReq
	18, // 16: vc.basemsgtopic.s.UserTopics:input_type -> vc.basemsgtopic.UserTopicsReq
	2,  // 17: vc.basemsgtopic.s.Test:output_type -> vc.basemsgtopic.TestRsp
	4,  // 18: vc.basemsgtopic.s.ConnStatusReport:output_type -> vc.basemsgtopic.ConnStatusReportRsp
	6,  // 19: vc.basemsgtopic.s.Subscribe:output_type -> vc.basemsgtopic.SubscribeRsp
	8,  // 20: vc.basemsgtopic.s.UnSubscribe:output_type -> vc.basemsgtopic.UnSubscribeRsp
	10, // 21: vc.basemsgtopic.s.TopicUsers:output_type -> vc.basemsgtopic.TopicUsersRsp
	10, // 22: vc.basemsgtopic.s.StreamTopicUsers:output_type -> vc.basemsgtopic.TopicUsersRsp
	12, // 23: vc.basemsgtopic.s.StreamTopicUsersWithConnInfo:output_type -> vc.basemsgtopic.TopicUsersWithConnInfoRsp
	15, // 24: vc.basemsgtopic.s.GetPlatformUserConn:output_type -> vc.basemsgtopic.GetPlatformUserConnRsp
	19, // 25: vc.basemsgtopic.s.UserTopics:output_type -> vc.basemsgtopic.UserTopicsRsp
	17, // [17:26] is the sub-list for method output_type
	8,  // [8:17] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_basemsgtopic_proto_init() }
func file_basemsgtopic_proto_init() {
	if File_basemsgtopic_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgtopic_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnStatusReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnStatusReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnSubscribeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnSubscribeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicUsersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicUsersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicUsersData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicUsersWithConnInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicUsersWithConnInfoData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlatformUserConnReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlatformUserConnRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatformUserConnData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatformUserConn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserTopicsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserTopicsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserTopicsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtopic_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDeviceTopicsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgtopic_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgtopic_proto_goTypes,
		DependencyIndexes: file_basemsgtopic_proto_depIdxs,
		EnumInfos:         file_basemsgtopic_proto_enumTypes,
		MessageInfos:      file_basemsgtopic_proto_msgTypes,
	}.Build()
	File_basemsgtopic_proto = out.File
	file_basemsgtopic_proto_rawDesc = nil
	file_basemsgtopic_proto_goTypes = nil
	file_basemsgtopic_proto_depIdxs = nil
}
