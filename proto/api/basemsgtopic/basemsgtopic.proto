syntax = "proto3";

package   vc.basemsgtopic;
option go_package = "xim/proto/api/basemsgtopic;basemsgtopic";

import "google/api/annotations.proto";

enum ConnStatusType {
  // 占位无效
  NoneType = 0;
  // 长连接建立
  ConnectType = 1;
  // 长连接断开
  CloseType = 2;
  // 长连接心跳维持
  PingType = 3;
}

message TestReq{
}
message TestRsp{
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

message ConnStatusReportReq {
  // 长连接状态上报类型，1:conn，2：disconn，close，3：ping
  ConnStatusType type = 1;
  // 用户token，解析出用户所有信息
  string userToken = 2;
  // 长连接ID
  uint64 connId = 3;
  // 对应Broker IP
  string brokerIP = 4;
  // 对应Broker上报时刻的时间戳(unit: ns)
  int64 opTime = 5;
}
message ConnStatusReportRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

message SubscribeReq {
  //平台id
  uint64 platformId = 1;
  //用户id
  string userId = 2;
  //用户设备id，不同设备可能有不同长连接，可指定该设备才订阅topic，设备为空默认用户下所有设备都订阅
  string deviceId = 3;
  //订阅的topic列表
  repeated string topics = 4;
}
message SubscribeRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

message UnSubscribeReq {
  //平台id
  uint64 platformId = 1;
  //用户id
  string userId = 2;
  //用户设备id，不同设备可能有不同长连接，可指定该设备才取消订阅topic，设备为空默认用户下所有设备都取消订阅
  string deviceId = 3;
  //取消订阅的topic列表
  repeated string topics = 4;
}
message UnSubscribeRsp{
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

message TopicUsersReq {
  //平台id
  uint64 platformId = 1;
  //订阅的topic
  string topic = 2;
}
message TopicUsersRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
  //topic下所有的用户列表
  TopicUsersData data = 3;
}
message TopicUsersData {
  // 用户id列表
  repeated string userIds = 1;
}
message TopicUsersWithConnInfoRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
  //topic下的所有用户长连接信息列表
  TopicUsersWithConnInfoData data = 3;
}
message TopicUsersWithConnInfoData {
  //详细的长连接信息列表
  repeated PlatformUserConn conns = 1;
}

message GetPlatformUserConnReq {
  //平台id
  uint64 platformId = 1;
  //平台用户id
  string userId = 2;
  //平台用户设备id，设备为空则查用户所有的长连接
  string deviceId = 3;
}
message GetPlatformUserConnRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
  //平台用户长连接数据
  PlatformUserConnData data = 3;
}
message PlatformUserConnData {
  //平台用户长连接列表
  repeated PlatformUserConn conns = 1;
}
message PlatformUserConn {
  //平台id
  uint64 platformId = 1;
  //平台用户id
  string userId = 2;
  //用户设备id
  string deviceId = 3;
  //客户端标识id
  uint32 clientId = 7;
  //客户端版本
  string clientVersion = 8;
  //用户长连接所在broker的ip
  string brokerIp = 4;
  //用户长连接唯一id
  uint64 connId = 5;
  //用户长连接时间
  int64 connTime = 6;
}

message UserTopicsReq {
  //平台id
  uint64 platformId = 1;
  //用户id
  string userId = 2;
  //用户设备id，不同设备可能有不同长连接，可指定该设备订阅的topic，设备为空默认查询用户下所有设备的订阅topic
  string deviceId = 3;
}
message UserTopicsRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
  //用户订阅的topic列表
  UserTopicsData data = 3;
}
message UserTopicsData {
  //用户和设备的订阅topic列表
  repeated UserDeviceTopicsData list = 1;
}
message UserDeviceTopicsData {
  //平台id
  uint64 platformId = 1;
  //用户id
  string userId = 2;
  //用户设备id，不同设备可能有不同长连接
  string deviceId = 3;
  //订阅的topic列表
  repeated string topics = 4;
}

service s {
  //服务调试接口，主要是测试服务连通性
  rpc Test (TestReq) returns (TestRsp) {
    option (google.api.http) = {
      post: "/xllivemp.basemsgtopic.s/v1/Test.json"
      body: "*"
    };
  }

  // Broker长连接状态上报
  rpc ConnStatusReport (ConnStatusReportReq) returns (ConnStatusReportRsp) {}

  // 用户批量订阅主题
  rpc Subscribe (SubscribeReq) returns (SubscribeRsp) {}

  // 用户批量取消订阅主题
  rpc UnSubscribe (UnSubscribeReq) returns (UnSubscribeRsp) {}

  // 查询订阅Topic的所有用户
  rpc TopicUsers (TopicUsersReq) returns (TopicUsersRsp) {}

  // Stream方式查询订阅Topic的所有用户
  rpc StreamTopicUsers (TopicUsersReq) returns (stream TopicUsersRsp) {};

  // stream方式查询订阅Topic的所有用户，带有长连接详细信息的
  rpc StreamTopicUsersWithConnInfo (TopicUsersReq) returns (stream TopicUsersWithConnInfoRsp) {};

  // 查询平台用户的长连接信息
  rpc GetPlatformUserConn (GetPlatformUserConnReq) returns (GetPlatformUserConnRsp) {}

  // 查询用户订阅的topic列表
  rpc UserTopics (UserTopicsReq) returns (UserTopicsRsp) {}
}
