{"swagger": "2.0", "info": {"title": "basemsgtopic.proto", "version": "version not set"}, "tags": [{"name": "s"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xllivemp.basemsgtopic.s/v1/Test.json": {"post": {"summary": "服务调试接口，主要是测试服务连通性", "operationId": "s_Test", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgtopicTestRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgtopicTestReq"}}], "tags": ["s"]}}}, "definitions": {"basemsgtopicConnStatusReportRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}}}, "basemsgtopicConnStatusType": {"type": "string", "enum": ["NoneType", "ConnectType", "CloseType", "PingType"], "default": "NoneType", "title": "- NoneType: 占位无效\n - ConnectType: 长连接建立\n - CloseType: 长连接断开\n - PingType: 长连接心跳维持"}, "basemsgtopicGetPlatformUserConnRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}, "data": {"$ref": "#/definitions/basemsgtopicPlatformUserConnData", "title": "平台用户长连接数据"}}}, "basemsgtopicPlatformUserConn": {"type": "object", "properties": {"platformId": {"type": "string", "format": "uint64", "title": "平台id"}, "userId": {"type": "string", "title": "平台用户id"}, "deviceId": {"type": "string", "title": "用户设备id"}, "clientId": {"type": "integer", "format": "int64", "title": "客户端标识id"}, "clientVersion": {"type": "string", "title": "客户端版本"}, "brokerIp": {"type": "string", "title": "用户长连接所在broker的ip"}, "connId": {"type": "string", "format": "uint64", "title": "用户长连接唯一id"}, "connTime": {"type": "string", "format": "int64", "title": "用户长连接时间"}}}, "basemsgtopicPlatformUserConnData": {"type": "object", "properties": {"conns": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/basemsgtopicPlatformUserConn"}, "title": "平台用户长连接列表"}}}, "basemsgtopicSubscribeRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}}}, "basemsgtopicTestReq": {"type": "object"}, "basemsgtopicTestRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}}}, "basemsgtopicTopicUsersData": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string"}, "title": "用户id列表"}}}, "basemsgtopicTopicUsersRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}, "data": {"$ref": "#/definitions/basemsgtopicTopicUsersData", "title": "topic下所有的用户列表"}}}, "basemsgtopicTopicUsersWithConnInfoData": {"type": "object", "properties": {"conns": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/basemsgtopicPlatformUserConn"}, "title": "详细的长连接信息列表"}}}, "basemsgtopicTopicUsersWithConnInfoRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}, "data": {"$ref": "#/definitions/basemsgtopicTopicUsersWithConnInfoData", "title": "topic下的所有用户长连接信息列表"}}}, "basemsgtopicUnSubscribeRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}}}, "basemsgtopicUserDeviceTopicsData": {"type": "object", "properties": {"platformId": {"type": "string", "format": "uint64", "title": "平台id"}, "userId": {"type": "string", "title": "用户id"}, "deviceId": {"type": "string", "title": "用户设备id，不同设备可能有不同长连接"}, "topics": {"type": "array", "items": {"type": "string"}, "title": "订阅的topic列表"}}}, "basemsgtopicUserTopicsData": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/basemsgtopicUserDeviceTopicsData"}, "title": "用户和设备的订阅topic列表"}}}, "basemsgtopicUserTopicsRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}, "data": {"$ref": "#/definitions/basemsgtopicUserTopicsData", "title": "用户订阅的topic列表"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}