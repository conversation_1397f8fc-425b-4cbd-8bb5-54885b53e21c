// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: basemsgcallout.proto

package basemsgcallout

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MsgEventType int32

const (
	// 占位无效
	MsgEventType_NoneType MsgEventType = 0
	// 长连接建立
	MsgEventType_ConnectType MsgEventType = 1
	// 长连接断开
	MsgEventType_CloseType MsgEventType = 2
	// 长连接心跳维持
	MsgEventType_PingType MsgEventType = 3
	// 长连接上行消息
	MsgEventType_PublishType MsgEventType = 4
)

// Enum value maps for MsgEventType.
var (
	MsgEventType_name = map[int32]string{
		0: "NoneType",
		1: "ConnectType",
		2: "CloseType",
		3: "PingType",
		4: "PublishType",
	}
	MsgEventType_value = map[string]int32{
		"NoneType":    0,
		"ConnectType": 1,
		"CloseType":   2,
		"PingType":    3,
		"PublishType": 4,
	}
)

func (x MsgEventType) Enum() *MsgEventType {
	p := new(MsgEventType)
	*p = x
	return p
}

func (x MsgEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgcallout_proto_enumTypes[0].Descriptor()
}

func (MsgEventType) Type() protoreflect.EnumType {
	return &file_basemsgcallout_proto_enumTypes[0]
}

func (x MsgEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgEventType.Descriptor instead.
func (MsgEventType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{0}
}

type TestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestReq) Reset() {
	*x = TestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReq) ProtoMessage() {}

func (x *TestReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReq.ProtoReflect.Descriptor instead.
func (*TestReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{0}
}

type TestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *TestRsp) Reset() {
	*x = TestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRsp) ProtoMessage() {}

func (x *TestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRsp.ProtoReflect.Descriptor instead.
func (*TestRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{1}
}

func (x *TestRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *TestRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SyncMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	// 消息json包，是下面MsgData数据的json包，使用时需要解析出来
	MsgJson string `protobuf:"bytes,5,opt,name=msgJson,proto3" json:"msgJson,omitempty"`
}

func (x *SyncMsgReq) Reset() {
	*x = SyncMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMsgReq) ProtoMessage() {}

func (x *SyncMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMsgReq.ProtoReflect.Descriptor instead.
func (*SyncMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{2}
}

func (x *SyncMsgReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *SyncMsgReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *SyncMsgReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *SyncMsgReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *SyncMsgReq) GetMsgJson() string {
	if x != nil {
		return x.MsgJson
	}
	return ""
}

type MsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上行消息类型（1为长连接建立，2为长连接关闭，3为长连接ping，4为长连接上行消息）
	Type MsgEventType `protobuf:"varint,1,opt,name=type,proto3,enum=vc.basemsgcallout.MsgEventType" json:"type,omitempty"`
	// 长连接本地标识id
	ConnId uint64 `protobuf:"varint,11,opt,name=connId,proto3" json:"connId,omitempty"`
	// 客户端设备id
	DeviceId string `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId,omitempty"`
	// 消息发送时间戳(这个是长连接接收到消息的时间)
	Time int64 `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	// 消息内容，具体消息数据
	Msg string `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
	// 消息对应的topic
	Topic string `protobuf:"bytes,6,opt,name=topic,proto3" json:"topic,omitempty"`
	// 消息id
	MsgId string `protobuf:"bytes,7,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// 消息记录唯一id
	OrderId string `protobuf:"bytes,8,opt,name=orderId,proto3" json:"orderId,omitempty"`
	// 消息发送时间戳-纳秒(这个是长连接接收到消息的时间)
	NanoTime int64 `protobuf:"varint,9,opt,name=nanoTime,proto3" json:"nanoTime,omitempty"`
	// 客户端标识
	ClientId uint32 `protobuf:"varint,10,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// base info,
	Base *common.BaseParam `protobuf:"bytes,12,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *MsgData) Reset() {
	*x = MsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgData) ProtoMessage() {}

func (x *MsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgData.ProtoReflect.Descriptor instead.
func (*MsgData) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{3}
}

func (x *MsgData) GetType() MsgEventType {
	if x != nil {
		return x.Type
	}
	return MsgEventType_NoneType
}

func (x *MsgData) GetConnId() uint64 {
	if x != nil {
		return x.ConnId
	}
	return 0
}

func (x *MsgData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *MsgData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MsgData) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *MsgData) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MsgData) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *MsgData) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *MsgData) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *MsgData) GetNanoTime() int64 {
	if x != nil {
		return x.NanoTime
	}
	return 0
}

func (x *MsgData) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *MsgData) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

type SyncMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SyncMsgRsp) Reset() {
	*x = SyncMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMsgRsp) ProtoMessage() {}

func (x *SyncMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMsgRsp.ProtoReflect.Descriptor instead.
func (*SyncMsgRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{4}
}

func (x *SyncMsgRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SyncMsgRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CheckPlatformUserTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 用户token相关信息，用json包传递，json包的字段跟业务协定
	TokenData string `protobuf:"bytes,2,opt,name=tokenData,proto3" json:"tokenData,omitempty"`
}

func (x *CheckPlatformUserTokenReq) Reset() {
	*x = CheckPlatformUserTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPlatformUserTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPlatformUserTokenReq) ProtoMessage() {}

func (x *CheckPlatformUserTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPlatformUserTokenReq.ProtoReflect.Descriptor instead.
func (*CheckPlatformUserTokenReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{5}
}

func (x *CheckPlatformUserTokenReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *CheckPlatformUserTokenReq) GetTokenData() string {
	if x != nil {
		return x.TokenData
	}
	return ""
}

type CheckPlatformUserTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 检查结果
	Data *CheckTokenResult `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckPlatformUserTokenRsp) Reset() {
	*x = CheckPlatformUserTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPlatformUserTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPlatformUserTokenRsp) ProtoMessage() {}

func (x *CheckPlatformUserTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPlatformUserTokenRsp.ProtoReflect.Descriptor instead.
func (*CheckPlatformUserTokenRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{6}
}

func (x *CheckPlatformUserTokenRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *CheckPlatformUserTokenRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckPlatformUserTokenRsp) GetData() *CheckTokenResult {
	if x != nil {
		return x.Data
	}
	return nil
}

type CheckTokenResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token是否有效，正确
	IsValid bool `protobuf:"varint,1,opt,name=isValid,proto3" json:"isValid,omitempty"`
}

func (x *CheckTokenResult) Reset() {
	*x = CheckTokenResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallout_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTokenResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTokenResult) ProtoMessage() {}

func (x *CheckTokenResult) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallout_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTokenResult.ProtoReflect.Descriptor instead.
func (*CheckTokenResult) Descriptor() ([]byte, []int) {
	return file_basemsgcallout_proto_rawDescGZIP(), []int{7}
}

func (x *CheckTokenResult) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

var File_basemsgcallout_proto protoreflect.FileDescriptor

var file_basemsgcallout_proto_rawDesc = []byte{
	0x0a, 0x14, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07,
	0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x3b, 0x0a, 0x07, 0x54, 0x65, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x73, 0x67,
	0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x73, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0xd5, 0x02, 0x0a, 0x07,
	0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f,
	0x6e, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x61, 0x6e, 0x6f, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x61, 0x6e, 0x6f, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04,
	0x62, 0x61, 0x73, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04, 0x62,
	0x61, 0x73, 0x65, 0x22, 0x3e, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x73, 0x67, 0x52, 0x73,
	0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x59, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x86,
	0x01, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2c, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x69,
	0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x2a, 0x5b, 0x0a, 0x0c, 0x4d, 0x73, 0x67, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x10,
	0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x10, 0x04, 0x32, 0xef, 0x01, 0x0a, 0x01, 0x73, 0x12, 0x72, 0x0a, 0x04, 0x54, 0x65, 0x73, 0x74,
	0x12, 0x1a, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c,
	0x6c, 0x6f, 0x75, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c,
	0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x78, 0x6c, 0x6c, 0x69, 0x76, 0x65, 0x6d, 0x70, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x2e, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x76, 0x0a, 0x16,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x78, 0x69, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c,
	0x6c, 0x6f, 0x75, 0x74, 0x3b, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c,
	0x6f, 0x75, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_basemsgcallout_proto_rawDescOnce sync.Once
	file_basemsgcallout_proto_rawDescData = file_basemsgcallout_proto_rawDesc
)

func file_basemsgcallout_proto_rawDescGZIP() []byte {
	file_basemsgcallout_proto_rawDescOnce.Do(func() {
		file_basemsgcallout_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgcallout_proto_rawDescData)
	})
	return file_basemsgcallout_proto_rawDescData
}

var file_basemsgcallout_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_basemsgcallout_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_basemsgcallout_proto_goTypes = []interface{}{
	(MsgEventType)(0),                 // 0: vc.basemsgcallout.MsgEventType
	(*TestReq)(nil),                   // 1: vc.basemsgcallout.TestReq
	(*TestRsp)(nil),                   // 2: vc.basemsgcallout.TestRsp
	(*SyncMsgReq)(nil),                // 3: vc.basemsgcallout.SyncMsgReq
	(*MsgData)(nil),                   // 4: vc.basemsgcallout.MsgData
	(*SyncMsgRsp)(nil),                // 5: vc.basemsgcallout.SyncMsgRsp
	(*CheckPlatformUserTokenReq)(nil), // 6: vc.basemsgcallout.CheckPlatformUserTokenReq
	(*CheckPlatformUserTokenRsp)(nil), // 7: vc.basemsgcallout.CheckPlatformUserTokenRsp
	(*CheckTokenResult)(nil),          // 8: vc.basemsgcallout.CheckTokenResult
	(*common.BaseParam)(nil),          // 9: common.BaseParam
}
var file_basemsgcallout_proto_depIdxs = []int32{
	0, // 0: vc.basemsgcallout.MsgData.type:type_name -> vc.basemsgcallout.MsgEventType
	9, // 1: vc.basemsgcallout.MsgData.base:type_name -> common.BaseParam
	8, // 2: vc.basemsgcallout.CheckPlatformUserTokenRsp.data:type_name -> vc.basemsgcallout.CheckTokenResult
	1, // 3: vc.basemsgcallout.s.Test:input_type -> vc.basemsgcallout.TestReq
	6, // 4: vc.basemsgcallout.s.CheckPlatformUserToken:input_type -> vc.basemsgcallout.CheckPlatformUserTokenReq
	2, // 5: vc.basemsgcallout.s.Test:output_type -> vc.basemsgcallout.TestRsp
	7, // 6: vc.basemsgcallout.s.CheckPlatformUserToken:output_type -> vc.basemsgcallout.CheckPlatformUserTokenRsp
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_basemsgcallout_proto_init() }
func file_basemsgcallout_proto_init() {
	if File_basemsgcallout_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgcallout_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPlatformUserTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPlatformUserTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallout_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTokenResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgcallout_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgcallout_proto_goTypes,
		DependencyIndexes: file_basemsgcallout_proto_depIdxs,
		EnumInfos:         file_basemsgcallout_proto_enumTypes,
		MessageInfos:      file_basemsgcallout_proto_msgTypes,
	}.Build()
	File_basemsgcallout_proto = out.File
	file_basemsgcallout_proto_rawDesc = nil
	file_basemsgcallout_proto_goTypes = nil
	file_basemsgcallout_proto_depIdxs = nil
}
