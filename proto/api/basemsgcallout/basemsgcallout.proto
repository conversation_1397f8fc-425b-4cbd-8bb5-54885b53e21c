syntax = "proto3";

package   vc.basemsgcallout;
option go_package = "xim/proto/api/basemsgcallout;basemsgcallout";

import "google/api/annotations.proto";
import "common/common.proto";

message TestReq{
}
message TestRsp{
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

enum MsgEventType {
  // 占位无效
  NoneType = 0;
  // 长连接建立
  ConnectType = 1;
  // 长连接断开
  CloseType = 2;
  // 长连接心跳维持
  PingType = 3;
  // 长连接上行消息
  PublishType = 4;
}
message SyncMsgReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;
  // 消息json包，是下面MsgData数据的json包，使用时需要解析出来
  string msgJson = 5;
}
message MsgData {
  //上行消息类型（1为长连接建立，2为长连接关闭，3为长连接ping，4为长连接上行消息）
  MsgEventType type = 1;
  //长连接本地标识id
  uint64 connId = 11;
  //客户端设备id
  string deviceId = 2;
  //用户id
  string userId = 3;
  //消息发送时间戳(这个是长连接接收到消息的时间)
  int64 time = 4;
  //消息内容，具体消息数据
  string msg = 5;
  //消息对应的topic
  string topic = 6;
  //消息id
  string msgId = 7;
  //消息记录唯一id
  string orderId = 8;
  //消息发送时间戳-纳秒(这个是长连接接收到消息的时间)
  int64 nanoTime = 9;
  //客户端标识
  uint32 clientId = 10;
  // base info,
  common.BaseParam base = 12;
}
message SyncMsgRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

message CheckPlatformUserTokenReq {
  //平台id
  uint64 platformId = 1;
  //用户token相关信息，用json包传递，json包的字段跟业务协定
  string tokenData = 2;
}
message CheckPlatformUserTokenRsp {
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
  //检查结果
  CheckTokenResult data = 3;
}
message CheckTokenResult {
  //token是否有效，正确
  bool isValid = 1;
}

service s {
  //服务调试接口，主要是测试服务连通性
  rpc Test (TestReq) returns (TestRsp) {
    option (google.api.http) = {
      post: "/xllivemp.basemsgcallout.s/v1/Test.json"
      body: "*"
    };
  }

  // 获取平台用户的token校验结果
  rpc CheckPlatformUserToken (CheckPlatformUserTokenReq) returns (CheckPlatformUserTokenRsp) {}
}
