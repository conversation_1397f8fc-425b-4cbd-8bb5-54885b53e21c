// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.4
// source: basemsgtransfer.proto

package basemsgtransfer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "xim/proto/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	S_WebSocket_FullMethodName            = "/vc.basemsgtransfer.s/WebSocket"
	S_GetChanToken_FullMethodName         = "/vc.basemsgtransfer.s/GetChanToken"
	S_HandleMsg_FullMethodName            = "/vc.basemsgtransfer.s/HandleMsg"
	S_SendMsg_FullMethodName              = "/vc.basemsgtransfer.s/SendMsg"
	S_SendRealTimeMsg_FullMethodName      = "/vc.basemsgtransfer.s/SendRealTimeMsg"
	S_MulticastRealTimeMsg_FullMethodName = "/vc.basemsgtransfer.s/MulticastRealTimeMsg"
	S_SubscribeTopic_FullMethodName       = "/vc.basemsgtransfer.s/SubscribeTopic"
	S_UnSubscribeTopic_FullMethodName     = "/vc.basemsgtransfer.s/UnSubscribeTopic"
	S_GetSession_FullMethodName           = "/vc.basemsgtransfer.s/GetSession"
	S_SetSession_FullMethodName           = "/vc.basemsgtransfer.s/SetSession"
	S_GetSortedSessionList_FullMethodName = "/vc.basemsgtransfer.s/GetSortedSessionList"
	S_GetSessionInfo_FullMethodName       = "/vc.basemsgtransfer.s/GetSessionInfo"
	S_UserGroupIdList_FullMethodName      = "/vc.basemsgtransfer.s/UserGroupIdList"
	S_UserJoinGroup_FullMethodName        = "/vc.basemsgtransfer.s/UserJoinGroup"
	S_UserLeaveGroup_FullMethodName       = "/vc.basemsgtransfer.s/UserLeaveGroup"
	S_Test_FullMethodName                 = "/vc.basemsgtransfer.s/Test"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 长链接websocket
	WebSocket(ctx context.Context, in *Req, opts ...grpc.CallOption) (*Resp, error)
	// 获取长链接地址和token
	GetChanToken(ctx context.Context, in *common.BizBaseReq, opts ...grpc.CallOption) (*ChanTokenResp, error)
	HandleMsg(ctx context.Context, in *HandleMsgReq, opts ...grpc.CallOption) (*HandleMsgRsp, error)
	SendMsg(ctx context.Context, in *SendMsgReq, opts ...grpc.CallOption) (*SendMsgResp, error)
	SendRealTimeMsg(ctx context.Context, in *SendMsgReq, opts ...grpc.CallOption) (*SendMsgResp, error)
	MulticastRealTimeMsg(ctx context.Context, in *MulticastRealTimeMsgReq, opts ...grpc.CallOption) (*SendMsgResp, error)
	SubscribeTopic(ctx context.Context, in *SubscribeTopicReq, opts ...grpc.CallOption) (*SendMsgResp, error)
	UnSubscribeTopic(ctx context.Context, in *UnSubscribeTopicReq, opts ...grpc.CallOption) (*SendMsgResp, error)
	GetSession(ctx context.Context, in *GetSessionReq, opts ...grpc.CallOption) (*GetSessionResp, error)
	SetSession(ctx context.Context, in *SetSessionReq, opts ...grpc.CallOption) (*SetSessionResp, error)
	GetSortedSessionList(ctx context.Context, in *GetSortedSessionListReq, opts ...grpc.CallOption) (*GetSortedSessionListResp, error)
	GetSessionInfo(ctx context.Context, in *GetSessionInfoReq, opts ...grpc.CallOption) (*GetSessionInfoResp, error)
	UserGroupIdList(ctx context.Context, in *UserGroupIdListReq, opts ...grpc.CallOption) (*UserGroupIdListResp, error)
	UserJoinGroup(ctx context.Context, in *UserJoinGroupReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
	UserLeaveGroup(ctx context.Context, in *UserLeaveGroupReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
	Test(ctx context.Context, in *common.TestReq, opts ...grpc.CallOption) (*common.TestResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) WebSocket(ctx context.Context, in *Req, opts ...grpc.CallOption) (*Resp, error) {
	out := new(Resp)
	err := c.cc.Invoke(ctx, S_WebSocket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetChanToken(ctx context.Context, in *common.BizBaseReq, opts ...grpc.CallOption) (*ChanTokenResp, error) {
	out := new(ChanTokenResp)
	err := c.cc.Invoke(ctx, S_GetChanToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) HandleMsg(ctx context.Context, in *HandleMsgReq, opts ...grpc.CallOption) (*HandleMsgRsp, error) {
	out := new(HandleMsgRsp)
	err := c.cc.Invoke(ctx, S_HandleMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SendMsg(ctx context.Context, in *SendMsgReq, opts ...grpc.CallOption) (*SendMsgResp, error) {
	out := new(SendMsgResp)
	err := c.cc.Invoke(ctx, S_SendMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SendRealTimeMsg(ctx context.Context, in *SendMsgReq, opts ...grpc.CallOption) (*SendMsgResp, error) {
	out := new(SendMsgResp)
	err := c.cc.Invoke(ctx, S_SendRealTimeMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) MulticastRealTimeMsg(ctx context.Context, in *MulticastRealTimeMsgReq, opts ...grpc.CallOption) (*SendMsgResp, error) {
	out := new(SendMsgResp)
	err := c.cc.Invoke(ctx, S_MulticastRealTimeMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SubscribeTopic(ctx context.Context, in *SubscribeTopicReq, opts ...grpc.CallOption) (*SendMsgResp, error) {
	out := new(SendMsgResp)
	err := c.cc.Invoke(ctx, S_SubscribeTopic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UnSubscribeTopic(ctx context.Context, in *UnSubscribeTopicReq, opts ...grpc.CallOption) (*SendMsgResp, error) {
	out := new(SendMsgResp)
	err := c.cc.Invoke(ctx, S_UnSubscribeTopic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetSession(ctx context.Context, in *GetSessionReq, opts ...grpc.CallOption) (*GetSessionResp, error) {
	out := new(GetSessionResp)
	err := c.cc.Invoke(ctx, S_GetSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SetSession(ctx context.Context, in *SetSessionReq, opts ...grpc.CallOption) (*SetSessionResp, error) {
	out := new(SetSessionResp)
	err := c.cc.Invoke(ctx, S_SetSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetSortedSessionList(ctx context.Context, in *GetSortedSessionListReq, opts ...grpc.CallOption) (*GetSortedSessionListResp, error) {
	out := new(GetSortedSessionListResp)
	err := c.cc.Invoke(ctx, S_GetSortedSessionList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetSessionInfo(ctx context.Context, in *GetSessionInfoReq, opts ...grpc.CallOption) (*GetSessionInfoResp, error) {
	out := new(GetSessionInfoResp)
	err := c.cc.Invoke(ctx, S_GetSessionInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UserGroupIdList(ctx context.Context, in *UserGroupIdListReq, opts ...grpc.CallOption) (*UserGroupIdListResp, error) {
	out := new(UserGroupIdListResp)
	err := c.cc.Invoke(ctx, S_UserGroupIdList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UserJoinGroup(ctx context.Context, in *UserJoinGroupReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, S_UserJoinGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UserLeaveGroup(ctx context.Context, in *UserLeaveGroupReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, S_UserLeaveGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Test(ctx context.Context, in *common.TestReq, opts ...grpc.CallOption) (*common.TestResp, error) {
	out := new(common.TestResp)
	err := c.cc.Invoke(ctx, S_Test_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 长链接websocket
	WebSocket(context.Context, *Req) (*Resp, error)
	// 获取长链接地址和token
	GetChanToken(context.Context, *common.BizBaseReq) (*ChanTokenResp, error)
	HandleMsg(context.Context, *HandleMsgReq) (*HandleMsgRsp, error)
	SendMsg(context.Context, *SendMsgReq) (*SendMsgResp, error)
	SendRealTimeMsg(context.Context, *SendMsgReq) (*SendMsgResp, error)
	MulticastRealTimeMsg(context.Context, *MulticastRealTimeMsgReq) (*SendMsgResp, error)
	SubscribeTopic(context.Context, *SubscribeTopicReq) (*SendMsgResp, error)
	UnSubscribeTopic(context.Context, *UnSubscribeTopicReq) (*SendMsgResp, error)
	GetSession(context.Context, *GetSessionReq) (*GetSessionResp, error)
	SetSession(context.Context, *SetSessionReq) (*SetSessionResp, error)
	GetSortedSessionList(context.Context, *GetSortedSessionListReq) (*GetSortedSessionListResp, error)
	GetSessionInfo(context.Context, *GetSessionInfoReq) (*GetSessionInfoResp, error)
	UserGroupIdList(context.Context, *UserGroupIdListReq) (*UserGroupIdListResp, error)
	UserJoinGroup(context.Context, *UserJoinGroupReq) (*common.SvcCommonResp, error)
	UserLeaveGroup(context.Context, *UserLeaveGroupReq) (*common.SvcCommonResp, error)
	Test(context.Context, *common.TestReq) (*common.TestResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) WebSocket(context.Context, *Req) (*Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebSocket not implemented")
}
func (UnimplementedSServer) GetChanToken(context.Context, *common.BizBaseReq) (*ChanTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChanToken not implemented")
}
func (UnimplementedSServer) HandleMsg(context.Context, *HandleMsgReq) (*HandleMsgRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleMsg not implemented")
}
func (UnimplementedSServer) SendMsg(context.Context, *SendMsgReq) (*SendMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMsg not implemented")
}
func (UnimplementedSServer) SendRealTimeMsg(context.Context, *SendMsgReq) (*SendMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRealTimeMsg not implemented")
}
func (UnimplementedSServer) MulticastRealTimeMsg(context.Context, *MulticastRealTimeMsgReq) (*SendMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MulticastRealTimeMsg not implemented")
}
func (UnimplementedSServer) SubscribeTopic(context.Context, *SubscribeTopicReq) (*SendMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscribeTopic not implemented")
}
func (UnimplementedSServer) UnSubscribeTopic(context.Context, *UnSubscribeTopicReq) (*SendMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnSubscribeTopic not implemented")
}
func (UnimplementedSServer) GetSession(context.Context, *GetSessionReq) (*GetSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSession not implemented")
}
func (UnimplementedSServer) SetSession(context.Context, *SetSessionReq) (*SetSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSession not implemented")
}
func (UnimplementedSServer) GetSortedSessionList(context.Context, *GetSortedSessionListReq) (*GetSortedSessionListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSortedSessionList not implemented")
}
func (UnimplementedSServer) GetSessionInfo(context.Context, *GetSessionInfoReq) (*GetSessionInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessionInfo not implemented")
}
func (UnimplementedSServer) UserGroupIdList(context.Context, *UserGroupIdListReq) (*UserGroupIdListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserGroupIdList not implemented")
}
func (UnimplementedSServer) UserJoinGroup(context.Context, *UserJoinGroupReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserJoinGroup not implemented")
}
func (UnimplementedSServer) UserLeaveGroup(context.Context, *UserLeaveGroupReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLeaveGroup not implemented")
}
func (UnimplementedSServer) Test(context.Context, *common.TestReq) (*common.TestResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Test not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_WebSocket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).WebSocket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_WebSocket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).WebSocket(ctx, req.(*Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetChanToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.BizBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetChanToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetChanToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetChanToken(ctx, req.(*common.BizBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_HandleMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).HandleMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_HandleMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).HandleMsg(ctx, req.(*HandleMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SendMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_SendMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SendMsg(ctx, req.(*SendMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SendRealTimeMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SendRealTimeMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_SendRealTimeMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SendRealTimeMsg(ctx, req.(*SendMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_MulticastRealTimeMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MulticastRealTimeMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).MulticastRealTimeMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_MulticastRealTimeMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).MulticastRealTimeMsg(ctx, req.(*MulticastRealTimeMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_SubscribeTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SubscribeTopic(ctx, req.(*SubscribeTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UnSubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnSubscribeTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UnSubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UnSubscribeTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UnSubscribeTopic(ctx, req.(*UnSubscribeTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetSession(ctx, req.(*GetSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SetSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SetSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_SetSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SetSession(ctx, req.(*SetSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetSortedSessionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSortedSessionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetSortedSessionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetSortedSessionList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetSortedSessionList(ctx, req.(*GetSortedSessionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetSessionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetSessionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetSessionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetSessionInfo(ctx, req.(*GetSessionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UserGroupIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserGroupIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UserGroupIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UserGroupIdList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UserGroupIdList(ctx, req.(*UserGroupIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UserJoinGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserJoinGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UserJoinGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UserJoinGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UserJoinGroup(ctx, req.(*UserJoinGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UserLeaveGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLeaveGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UserLeaveGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UserLeaveGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UserLeaveGroup(ctx, req.(*UserLeaveGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.TestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_Test_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Test(ctx, req.(*common.TestReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.basemsgtransfer.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WebSocket",
			Handler:    _S_WebSocket_Handler,
		},
		{
			MethodName: "GetChanToken",
			Handler:    _S_GetChanToken_Handler,
		},
		{
			MethodName: "HandleMsg",
			Handler:    _S_HandleMsg_Handler,
		},
		{
			MethodName: "SendMsg",
			Handler:    _S_SendMsg_Handler,
		},
		{
			MethodName: "SendRealTimeMsg",
			Handler:    _S_SendRealTimeMsg_Handler,
		},
		{
			MethodName: "MulticastRealTimeMsg",
			Handler:    _S_MulticastRealTimeMsg_Handler,
		},
		{
			MethodName: "SubscribeTopic",
			Handler:    _S_SubscribeTopic_Handler,
		},
		{
			MethodName: "UnSubscribeTopic",
			Handler:    _S_UnSubscribeTopic_Handler,
		},
		{
			MethodName: "GetSession",
			Handler:    _S_GetSession_Handler,
		},
		{
			MethodName: "SetSession",
			Handler:    _S_SetSession_Handler,
		},
		{
			MethodName: "GetSortedSessionList",
			Handler:    _S_GetSortedSessionList_Handler,
		},
		{
			MethodName: "GetSessionInfo",
			Handler:    _S_GetSessionInfo_Handler,
		},
		{
			MethodName: "UserGroupIdList",
			Handler:    _S_UserGroupIdList_Handler,
		},
		{
			MethodName: "UserJoinGroup",
			Handler:    _S_UserJoinGroup_Handler,
		},
		{
			MethodName: "UserLeaveGroup",
			Handler:    _S_UserLeaveGroup_Handler,
		},
		{
			MethodName: "Test",
			Handler:    _S_Test_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "basemsgtransfer.proto",
}
