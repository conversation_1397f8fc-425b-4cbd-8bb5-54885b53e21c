syntax = "proto3";

package  vc.basemsgtransfer;
option go_package = "xim/proto/api/basemsgtransfer";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "basemsgcallin/basemsgcallin.proto";
import "common/common.proto";
import "google/protobuf/wrappers.proto";

message Req {
  // 可以传空,ping :心跳；pong :回复心跳；puback :puback 回包
  string topicName = 1;
  uint64 messageId = 2;
  // 消息体，json_decode 后的结构体是下面字段：payload_decode
  bytes payload = 3 [(google.api.field_behavior) = REQUIRED] ;
  uint32 ack = 4;
  // 改字段为虚拟字段，为Payload 解码后的结构体
  ReqMsgData payload_decode = 5 [json_name="payload_decode"];
  string traceId = 6; // 服务端生成，用于openTelemetry链路追踪
}

message Resp {
  // ping :心跳；pong :回复心跳；puback :puback 回包
  string topicName = 1;
  uint64 messageId = 2;
  // 消息体，json_decode 是ClientMsg
  bytes payload = 3;
  uint32 ack = 4;

  // 改字段为虚拟字段，为Payload 解码后的结构体
  RespMsgData payload_decode = 5 [json_name="payload_decode"];
  string traceId = 6; // 服务端生成，用于openTelemetry链路追踪

}

enum MsgType {
  chat = 0; //IM消息
  toast = 1; //非im消息
}




enum ChatMsgType {
  ChatMsgTypeUnknown = 0;
  ChatMsgTypePing = 1;  //上行： ping
  ChatMsgTypeSendReq = 2; // 上行：发消息
  ChatMsgTypeSendRsp = 3; // 下行：发消息ack
  ChatMsgTypeNotify = 4;    // 下行：消息下推
  ChatMsgTypeNotifyAck = 5; // 上行: 消息下推确认
  ChatMsgTypeAckSender = 6; // 下行: 转发给发送方,用于发送方统计数据
  ChatMsgTypeNewestSeq = 7; // 下行: 推送群聊会话最新seq给客户端
  ChatMsgTypePullMsgBySeqList = 8; // 上行: 通过seq同步消息
  ChatMsgTypePullMsgBySeqListRsp =9; // 下行: 通过seq同步消息
}

// 该字段为虚拟字段，为Payload 解码后的结构体
message ReqMsgData {
  // ping = 1;send_req = 2;
  int32 type = 1 [(google.api.field_behavior) = REQUIRED] ;
  // type=1解码为PingData；type=2解码为SendReqData type=5解码为  type=8解码为PullMessageBySeqsReq
  string data = 2 [(google.api.field_behavior) = REQUIRED] ;
  PingData type1Data = 3; // type=1json_decode后的消息结构体
  MsgData type2Data = 4; // type=2json_decode后消息结构体
  repeated NotifySyncData type5Data = 5;
  PullMessageBySeqsReq type8Data = 8; // type=8 json_decode后消息结构体
}
// 该字段为虚拟字段，为Payload 解码后的结构体
message RespMsgData{
  // ping = 1;send_req = 2;
  int32 type = 1;
  // type=3解码为SendRespData；type=4 解码为ReceiveData[]
  string data = 2;
  SendRespData type3Data = 3;
  repeated MsgData type4Data = 4;
  MaxSeqRespData type7Data = 5;
  PullMessageBySeqsRsp type9Data = 9;
}

message PingData{
  string userid = 1;
  int64 seq = 2;
  //是否在前台
  bool isFront = 3 [(google.api.field_behavior) = REQUIRED] ;
}

message NotifySyncData {
  int64 seq = 1;
  int64 mill_ts = 2;
}



enum SessionType {
  SessionTypeUnknown = 0;
  SessionTypeChat = 1;
  SessionTypeGroup = 2;
  SessionTypeVoiceRoom = 3;
  SessionTypeSystem = 4;
  SessionTypeMoment = 5;
}

enum MsgFromEnum {
  // 普通消息,需要Ack
  MsgFromIm = 0;
  // 实时消息，不需要客户端ack
  MsgFromRealTime = 1;
  // 普通消息,需要Ack,用来标记客户端不入库
  MsgFromIm2 = 2;
}

// 发送和接收的消息结构体，type=2或type=4，对data进行json_decode后消息结构体
message MsgData{
  // 客户端消息的唯一ID
  string localId = 1 [(google.api.field_behavior) = REQUIRED] ;
  // 服务端生成消息的唯一ID
  string msgId = 2;
  // 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
  string sessionId = 3 [(google.api.field_behavior) = REQUIRED] ;
  int64 from = 4;
  int64 to = 5 [(google.api.field_behavior) = REQUIRED] ;
  // 群聊ID
  int64 groupId = 6;
  // 0:IM消息;1:实时消息
  uint32 msgFrom = 7;
  // 1 单聊，2 群聊，3 语音房， 4 系统通知
  int32 sessionType = 8 [(google.api.field_behavior) = REQUIRED] ;
  // 消息内容类型：Text:101,Image:102,Voice:103,Video:104,Custom:110,RealtimeVoice:115,RealTimeVideo:116
  int32 contentType = 9 [(google.api.field_behavior) = REQUIRED] ;
  // 消息内容，一个json根据contentType解码到不同的结构体，见下面
  string content = 10 [(google.api.field_behavior) = REQUIRED] ;
  // 消息序号
  int64 seq = 16;
  // 发送时间
  int64 sendTime = 17;
  // 消息创建时间
  int64  createTime = 18 [(google.api.field_behavior) = REQUIRED] ;
  // 一个map,不配置默认为true。
  // key如下 history:是否推送历史消息
  // persistent：是否永久存储消息
  // offlinePush：离线是否走第三方推送
  map<string,bool> options = 19;
  // 第三方推送配置
  OfflinePushInfo offlinePushInfo =20;
  // @用户列表
  // repeated  int64  atUserIdList = 21;
  repeated AtInfo atList = 21;
  bool upgrade = 22; // 如果是 true，客户端不支持，则提示升级。 否则不显示
  map<string, string> exts = 23; // 扩展字段
  bool SysMsg = 24; //是否系统消息
  int64 receiver = 35 [(google.api.field_behavior) = REQUIRED] ; //消息接受方
}

message AtInfo {
  // @all atUserId 需要占位给0
  int64 atUserId = 1;
  string text = 2;
  string style = 3;
}

// type=3，data进行json_decode后的消息结构体
message SendRespData{
  // 客户端消息的唯一ID
  string localId = 1;
  // 服务端生成消息的唯一ID
  string msgId = 2;
  // 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
  string sessionId = 3;
  // 发送时间
  int64 sendTime = 4;
  // 消息创建时间
  int64  createTime = 5;

  int32 errCode = 6;
  string errMsg = 7;
  int32 sessionType = 8;
  int64 seq = 9;
  string orderTime = 10;
}
// type=7，data进行json_decode后的消息结构体
message MaxSeqRespData{
  // 群聊会话的最大sed
  map<string,int64> maxSeqs = 1;
}

message SeqRange{
     string sessionId = 1;
     int64 begin = 2;
     int64 end = 3;
     // 不传或传0，走客户端配置的值，>0会最多返回num条数据
     int64 num = 4;
}
 

// 同步丢弃消息
message SyncSeq {
  string sessionId = 1;
  // SeqRange 内部只要传 begin end
  repeated SeqRange seqRanges = 2;
}

// type=8，data进行json_decode后的消息结构体
message PullMessageBySeqsReq {
  int64 userID = 1;
  // 同步消息
  repeated SeqRange seqRanges = 2;
}
// type=9，data进行json_decode后的消息结构体
message PullMessageBySeqsRsp {
  // sessionId => msglist[]
  map<string,PullMsgs> msgs = 1;
}

message PullMsgs {
  repeated MsgData msgs = 1;
  // 是否结束，预留字段
  bool isEnd = 2;
}

message OfflinePushInfo{
  string title = 1;
  string desc  = 2;
  string ext = 3;
  string  IOSPushSound = 4;
  bool IOSBadgeCount  = 5;
  string signalInfo =6;
}

message TextElem {
  string content = 1;
}

message ImageElem {
  string imageUrl = 1;
  string objectId = 2;
  int64 width = 3;
  int64 height = 4;
  int64 size = 5;
}

message VoiceElem {
  string voiceUrl = 1;
  int64 dataSize = 2;
  int64 duration = 3;
  string objectId = 4;
}

message VideoElem {
  string videoUrl = 1;
  string videoType = 2;
  int64  videoSize = 3;
  int64  duration = 4;
  string objectId = 5;
}

message CustomElem{
  string data = 1;
  string description = 2;
  string extension = 3;
}

message ChanTokenRespData {
  //长连接token
  string token = 1;
  //长连接主地址
  basemsgcallin.MsgAddrData masterAddr = 2;
  //长连接备用地址
  basemsgcallin.MsgAddrData slaveAddr = 3;
  //客户端发上行消息使用的通用topic
  string generalTopic = 4;
  //payload是否进行gzip压缩
  bool gzip = 5;
  // userId
  int64 userid = 6;
}

message ChanTokenResp {
  common.SvcBaseResp base = 1;
  ChanTokenRespData data = 2;
}

message HandleMsgReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;

  string msgJson = 5;
}

message HandleMsgRsp {
  common.SvcBaseResp base = 1;
}

enum MsgEventType {
  unknown = 0;
  connected = 1;
  closed = 2;
  ping = 3;
  message = 4;
}

message UpstreamMsgData {
  //上行消息类型（1为长连接建立，2为长连接关闭，3为长连接ping，4为长连接上行消息）
  MsgEventType type = 1;
  //长连接本地标识id
  uint64 connId = 11;
  //客户端设备id
  string deviceId = 2;
  //用户id
  string userId = 3;
  //消息发送时间戳(这个是长连接接收到消息的时间)
  int64 time = 4;
  //消息内容，具体消息数据
  string msg = 5;
  //消息对应的topic
  string topic = 6;
  //消息id
  string msgId = 7;
  //消息记录唯一id
  string orderId = 8;
  //消息发送时间戳-纳秒(这个是长连接接收到消息的时间)
  int64 nanoTime = 9;
  //客户端标识
  uint32 clientId = 10;
  // base info,
  common.BaseParam base = 12;

}


message InterMsgData {
  int64 userId = 1;
  common.BaseParam base = 2;
  int32 type = 3;
  PingData type1Data = 4; // type=1json_decode后的消息结构体
  MsgData type2Data = 5; // type=2json_decode后消息结构体
  repeated NotifySyncData type5Data = 6;
  int64 timestamp = 7;
  PullMessageBySeqsReq type8Data= 8;  // type=8json_decode后消息结构体
  int64 nano_time=9;
}

message SendMsgReq {
  repeated MsgData msgs = 1;
  int64 receiver = 2;
}

message SendMsgResp {
  common.SvcBaseResp base = 1;
  repeated string msgids=2;
}

message GetSessionReq {
  string sessionId =1;
  int64 userid = 2;
  SessionInfo upsert = 3;
  int64 to = 4;
  int64 groupId = 5;
}

message GetSessionResp {
  common.SvcBaseResp base = 1;
  SessionInfo session = 2;
  bool isNew = 3;
}

message GetSortedSessionListReq{
  int64 userid = 1;
}

message SessionInfo {
  string sessionId =1;
  int32 sessionType =2;
  int64 ownUserid = 3;
  int64 peerId = 4; //会话 对方用户
  int64 groupId = 5;
  int32 recvMsgOpt = 6;
  bool isPinned = 7;
  int64 ct = 8;
  string remark = 9; //备注
}

message SessionListData{
  repeated SessionInfo sessions = 1;
}

message GetSortedSessionListResp {
  common.SvcBaseResp base = 1;
  SessionListData data = 2; 
}



message GetSessionInfoReq {
  int64 userid = 1;
  repeated string sessionIds = 2;
}

message GetSortedSessionListData{
  repeated SessionInfo sessions = 1;
}

message GetSessionInfoResp {
  common.SvcBaseResp base = 1;
  SessionListData data = 2; 
}


message SetSessionReq {
  string sessionId =1;
  int64 ownUserid = 2;
  google.protobuf.Int32Value recvMsgOpt = 3;
  google.protobuf.BoolValue isPinned = 4;
  google.protobuf.StringValue remark = 5;
}


message MulticastRealTimeMsgReq{
  repeated MsgData msgs = 1;
  string topic = 2;
}

message SubscribeTopicReq {
  int64 userid = 1;
  string topic = 2;
}



message SetSessionResp {
  common.SvcBaseResp base = 1;
}

message UnSubscribeTopicReq {
  int64 userid = 1;
  string topic = 2;
}


message UserGroupIdListReq {
  int64 userid = 1;
}

message UserGroupIdListResp {
  common.SvcBaseResp base = 1;
  repeated int64 grouid_list=2;
}

message UserJoinGroupReq {
  int64 userid = 1;
  int64 groupid =2;
  string topic = 3;
}

message UserLeaveGroupReq {
  int64 userid = 1;
  int64 groupid = 2;
  string topic = 3;
}


service s {
  // 长链接websocket
  rpc WebSocket(Req) returns(Resp){
    option (google.api.http) = {
      post:"/vc.basemsgtransfer.s/v1/websocket"
      body:"*"
    };
  }
  // 获取长链接地址和token
  rpc GetChanToken(common.BizBaseReq) returns (ChanTokenResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/get_chan_token"
      body: "*"
    };
  }

  rpc HandleMsg (HandleMsgReq) returns (HandleMsgRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/handle_msg"
      body: "*"
    };
  }
  rpc SendMsg (SendMsgReq) returns (SendMsgResp) {
    // option (google.api.http) = {
    //   post: "/melon.basemsgtransfer.s/v1/send_msg"
    //   body: "*"
    // };
  }


  rpc SendRealTimeMsg (SendMsgReq) returns (SendMsgResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/send_realtime_msg"
      body: "*"
    };
  }

  rpc MulticastRealTimeMsg (MulticastRealTimeMsgReq) returns (SendMsgResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/multicast_realtime_msg"
      body: "*"
    };
  }

  rpc SubscribeTopic (SubscribeTopicReq) returns (SendMsgResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/subscribe_topic"
      body: "*"
    };
  }

  rpc UnSubscribeTopic (UnSubscribeTopicReq) returns (SendMsgResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/unsubscribe_topic"
      body: "*"
    };
  }

  rpc GetSession (GetSessionReq) returns (GetSessionResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/get_session"
      body: "*"
    };
  }

  rpc SetSession (SetSessionReq) returns (SetSessionResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/set_session"
      body: "*"
    };
  }


  rpc GetSortedSessionList (GetSortedSessionListReq) returns (GetSortedSessionListResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/get_sorted_session"
      body: "*"
    };
  }

  rpc GetSessionInfo (GetSessionInfoReq) returns (GetSessionInfoResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/get_session_info"
      body: "*"
    };
  }

  rpc UserGroupIdList(UserGroupIdListReq) returns (UserGroupIdListResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/user_group_id_list"
      body: "*"
    };
  }

  rpc UserJoinGroup(UserJoinGroupReq) returns (common.SvcCommonResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/user_join_group"
      body: "*"
    };
  }

  rpc UserLeaveGroup(UserLeaveGroupReq) returns (common.SvcCommonResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/user_leave_group"
      body: "*"
    };
  }

  rpc Test (common.TestReq) returns (common.TestResp) {
    option (google.api.http) = {
      post: "/vc.basemsgtransfer.s/v1/test.json"
      body: "*"
    };
  }

}


// ContentType 前三位为业务大类型,三位之后为业务细分的子类型，可以按需拓展
enum ContentType {
  Invalid = 0; // 不使用
  Text = 101; // 文本
  Image = 102; // 图片
  Voice = 103; // 语音
  Video = 104; // 视频
  Gift = 105; // 礼物
  Custom = 110; // 保留字段
  RealtimeVoice = 115; // 实时语音
  RealTimeVideo = 116; // 实时视频
  RealTimeVideoCall =  11601; // 实时视频发起消息
  RealTimeVideoCallAccept =  11602; // 实时视频同意
}


message seqCacheData {
  int64 receiver = 1;
  int64 seq = 2;
}