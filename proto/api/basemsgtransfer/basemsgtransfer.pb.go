// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: basemsgtransfer.proto

package basemsgtransfer

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
	basemsgcallin "xim/proto/api/basemsgcallin"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MsgType int32

const (
	MsgType_chat  MsgType = 0 //IM消息
	MsgType_toast MsgType = 1 //非im消息
)

// Enum value maps for MsgType.
var (
	MsgType_name = map[int32]string{
		0: "chat",
		1: "toast",
	}
	MsgType_value = map[string]int32{
		"chat":  0,
		"toast": 1,
	}
)

func (x MsgType) Enum() *MsgType {
	p := new(MsgType)
	*p = x
	return p
}

func (x MsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[0].Descriptor()
}

func (MsgType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[0]
}

func (x MsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgType.Descriptor instead.
func (MsgType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{0}
}

type ChatMsgType int32

const (
	ChatMsgType_ChatMsgTypeUnknown             ChatMsgType = 0
	ChatMsgType_ChatMsgTypePing                ChatMsgType = 1 //上行： ping
	ChatMsgType_ChatMsgTypeSendReq             ChatMsgType = 2 // 上行：发消息
	ChatMsgType_ChatMsgTypeSendRsp             ChatMsgType = 3 // 下行：发消息ack
	ChatMsgType_ChatMsgTypeNotify              ChatMsgType = 4 // 下行：消息下推
	ChatMsgType_ChatMsgTypeNotifyAck           ChatMsgType = 5 // 上行: 消息下推确认
	ChatMsgType_ChatMsgTypeAckSender           ChatMsgType = 6 // 下行: 转发给发送方,用于发送方统计数据
	ChatMsgType_ChatMsgTypeNewestSeq           ChatMsgType = 7 // 下行: 推送群聊会话最新seq给客户端
	ChatMsgType_ChatMsgTypePullMsgBySeqList    ChatMsgType = 8 // 上行: 通过seq同步消息
	ChatMsgType_ChatMsgTypePullMsgBySeqListRsp ChatMsgType = 9 // 下行: 通过seq同步消息
)

// Enum value maps for ChatMsgType.
var (
	ChatMsgType_name = map[int32]string{
		0: "ChatMsgTypeUnknown",
		1: "ChatMsgTypePing",
		2: "ChatMsgTypeSendReq",
		3: "ChatMsgTypeSendRsp",
		4: "ChatMsgTypeNotify",
		5: "ChatMsgTypeNotifyAck",
		6: "ChatMsgTypeAckSender",
		7: "ChatMsgTypeNewestSeq",
		8: "ChatMsgTypePullMsgBySeqList",
		9: "ChatMsgTypePullMsgBySeqListRsp",
	}
	ChatMsgType_value = map[string]int32{
		"ChatMsgTypeUnknown":             0,
		"ChatMsgTypePing":                1,
		"ChatMsgTypeSendReq":             2,
		"ChatMsgTypeSendRsp":             3,
		"ChatMsgTypeNotify":              4,
		"ChatMsgTypeNotifyAck":           5,
		"ChatMsgTypeAckSender":           6,
		"ChatMsgTypeNewestSeq":           7,
		"ChatMsgTypePullMsgBySeqList":    8,
		"ChatMsgTypePullMsgBySeqListRsp": 9,
	}
)

func (x ChatMsgType) Enum() *ChatMsgType {
	p := new(ChatMsgType)
	*p = x
	return p
}

func (x ChatMsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatMsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[1].Descriptor()
}

func (ChatMsgType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[1]
}

func (x ChatMsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatMsgType.Descriptor instead.
func (ChatMsgType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{1}
}

type SessionType int32

const (
	SessionType_SessionTypeUnknown   SessionType = 0
	SessionType_SessionTypeChat      SessionType = 1
	SessionType_SessionTypeGroup     SessionType = 2
	SessionType_SessionTypeVoiceRoom SessionType = 3
	SessionType_SessionTypeSystem    SessionType = 4
	SessionType_SessionTypeMoment    SessionType = 5
)

// Enum value maps for SessionType.
var (
	SessionType_name = map[int32]string{
		0: "SessionTypeUnknown",
		1: "SessionTypeChat",
		2: "SessionTypeGroup",
		3: "SessionTypeVoiceRoom",
		4: "SessionTypeSystem",
		5: "SessionTypeMoment",
	}
	SessionType_value = map[string]int32{
		"SessionTypeUnknown":   0,
		"SessionTypeChat":      1,
		"SessionTypeGroup":     2,
		"SessionTypeVoiceRoom": 3,
		"SessionTypeSystem":    4,
		"SessionTypeMoment":    5,
	}
)

func (x SessionType) Enum() *SessionType {
	p := new(SessionType)
	*p = x
	return p
}

func (x SessionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SessionType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[2].Descriptor()
}

func (SessionType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[2]
}

func (x SessionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SessionType.Descriptor instead.
func (SessionType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{2}
}

type MsgFromEnum int32

const (
	// 普通消息,需要Ack
	MsgFromEnum_MsgFromIm MsgFromEnum = 0
	// 实时消息，不需要客户端ack
	MsgFromEnum_MsgFromRealTime MsgFromEnum = 1
	// 普通消息,需要Ack,用来标记客户端不入库
	MsgFromEnum_MsgFromIm2 MsgFromEnum = 2
)

// Enum value maps for MsgFromEnum.
var (
	MsgFromEnum_name = map[int32]string{
		0: "MsgFromIm",
		1: "MsgFromRealTime",
		2: "MsgFromIm2",
	}
	MsgFromEnum_value = map[string]int32{
		"MsgFromIm":       0,
		"MsgFromRealTime": 1,
		"MsgFromIm2":      2,
	}
)

func (x MsgFromEnum) Enum() *MsgFromEnum {
	p := new(MsgFromEnum)
	*p = x
	return p
}

func (x MsgFromEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgFromEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[3].Descriptor()
}

func (MsgFromEnum) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[3]
}

func (x MsgFromEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgFromEnum.Descriptor instead.
func (MsgFromEnum) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{3}
}

type MsgEventType int32

const (
	MsgEventType_unknown   MsgEventType = 0
	MsgEventType_connected MsgEventType = 1
	MsgEventType_closed    MsgEventType = 2
	MsgEventType_ping      MsgEventType = 3
	MsgEventType_message   MsgEventType = 4
)

// Enum value maps for MsgEventType.
var (
	MsgEventType_name = map[int32]string{
		0: "unknown",
		1: "connected",
		2: "closed",
		3: "ping",
		4: "message",
	}
	MsgEventType_value = map[string]int32{
		"unknown":   0,
		"connected": 1,
		"closed":    2,
		"ping":      3,
		"message":   4,
	}
)

func (x MsgEventType) Enum() *MsgEventType {
	p := new(MsgEventType)
	*p = x
	return p
}

func (x MsgEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[4].Descriptor()
}

func (MsgEventType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[4]
}

func (x MsgEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgEventType.Descriptor instead.
func (MsgEventType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{4}
}

// ContentType 前三位为业务大类型,三位之后为业务细分的子类型，可以按需拓展
type ContentType int32

const (
	ContentType_Invalid                 ContentType = 0     // 不使用
	ContentType_Text                    ContentType = 101   // 文本
	ContentType_Image                   ContentType = 102   // 图片
	ContentType_Voice                   ContentType = 103   // 语音
	ContentType_Video                   ContentType = 104   // 视频
	ContentType_Gift                    ContentType = 105   // 礼物
	ContentType_Custom                  ContentType = 110   // 保留字段
	ContentType_RealtimeVoice           ContentType = 115   // 实时语音
	ContentType_RealTimeVideo           ContentType = 116   // 实时视频
	ContentType_RealTimeVideoCall       ContentType = 11601 // 实时视频发起消息
	ContentType_RealTimeVideoCallAccept ContentType = 11602 // 实时视频同意
)

// Enum value maps for ContentType.
var (
	ContentType_name = map[int32]string{
		0:     "Invalid",
		101:   "Text",
		102:   "Image",
		103:   "Voice",
		104:   "Video",
		105:   "Gift",
		110:   "Custom",
		115:   "RealtimeVoice",
		116:   "RealTimeVideo",
		11601: "RealTimeVideoCall",
		11602: "RealTimeVideoCallAccept",
	}
	ContentType_value = map[string]int32{
		"Invalid":                 0,
		"Text":                    101,
		"Image":                   102,
		"Voice":                   103,
		"Video":                   104,
		"Gift":                    105,
		"Custom":                  110,
		"RealtimeVoice":           115,
		"RealTimeVideo":           116,
		"RealTimeVideoCall":       11601,
		"RealTimeVideoCallAccept": 11602,
	}
)

func (x ContentType) Enum() *ContentType {
	p := new(ContentType)
	*p = x
	return p
}

func (x ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[5].Descriptor()
}

func (ContentType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[5]
}

func (x ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContentType.Descriptor instead.
func (ContentType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{5}
}

type Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可以传空,ping :心跳；pong :回复心跳；puback :puback 回包
	TopicName string `protobuf:"bytes,1,opt,name=topicName,proto3" json:"topicName,omitempty"`
	MessageId uint64 `protobuf:"varint,2,opt,name=messageId,proto3" json:"messageId,omitempty"`
	// 消息体，json_decode 后的结构体是下面字段：payload_decode
	Payload []byte `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	Ack     uint32 `protobuf:"varint,4,opt,name=ack,proto3" json:"ack,omitempty"`
	// 改字段为虚拟字段，为Payload 解码后的结构体
	PayloadDecode *ReqMsgData `protobuf:"bytes,5,opt,name=payload_decode,proto3" json:"payload_decode,omitempty"`
	TraceId       string      `protobuf:"bytes,6,opt,name=traceId,proto3" json:"traceId,omitempty"` // 服务端生成，用于openTelemetry链路追踪
}

func (x *Req) Reset() {
	*x = Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Req) ProtoMessage() {}

func (x *Req) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Req.ProtoReflect.Descriptor instead.
func (*Req) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{0}
}

func (x *Req) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

func (x *Req) GetMessageId() uint64 {
	if x != nil {
		return x.MessageId
	}
	return 0
}

func (x *Req) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Req) GetAck() uint32 {
	if x != nil {
		return x.Ack
	}
	return 0
}

func (x *Req) GetPayloadDecode() *ReqMsgData {
	if x != nil {
		return x.PayloadDecode
	}
	return nil
}

func (x *Req) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

type Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ping :心跳；pong :回复心跳；puback :puback 回包
	TopicName string `protobuf:"bytes,1,opt,name=topicName,proto3" json:"topicName,omitempty"`
	MessageId uint64 `protobuf:"varint,2,opt,name=messageId,proto3" json:"messageId,omitempty"`
	// 消息体，json_decode 是ClientMsg
	Payload []byte `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	Ack     uint32 `protobuf:"varint,4,opt,name=ack,proto3" json:"ack,omitempty"`
	// 改字段为虚拟字段，为Payload 解码后的结构体
	PayloadDecode *RespMsgData `protobuf:"bytes,5,opt,name=payload_decode,proto3" json:"payload_decode,omitempty"`
	TraceId       string       `protobuf:"bytes,6,opt,name=traceId,proto3" json:"traceId,omitempty"` // 服务端生成，用于openTelemetry链路追踪
}

func (x *Resp) Reset() {
	*x = Resp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resp) ProtoMessage() {}

func (x *Resp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resp.ProtoReflect.Descriptor instead.
func (*Resp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{1}
}

func (x *Resp) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

func (x *Resp) GetMessageId() uint64 {
	if x != nil {
		return x.MessageId
	}
	return 0
}

func (x *Resp) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Resp) GetAck() uint32 {
	if x != nil {
		return x.Ack
	}
	return 0
}

func (x *Resp) GetPayloadDecode() *RespMsgData {
	if x != nil {
		return x.PayloadDecode
	}
	return nil
}

func (x *Resp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

// 该字段为虚拟字段，为Payload 解码后的结构体
type ReqMsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ping = 1;send_req = 2;
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// type=1解码为PingData；type=2解码为SendReqData type=5解码为  type=8解码为PullMessageBySeqsReq
	Data      string                `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Type1Data *PingData             `protobuf:"bytes,3,opt,name=type1Data,proto3" json:"type1Data,omitempty"` // type=1json_decode后的消息结构体
	Type2Data *MsgData              `protobuf:"bytes,4,opt,name=type2Data,proto3" json:"type2Data,omitempty"` // type=2json_decode后消息结构体
	Type5Data []*NotifySyncData     `protobuf:"bytes,5,rep,name=type5Data,proto3" json:"type5Data,omitempty"`
	Type8Data *PullMessageBySeqsReq `protobuf:"bytes,8,opt,name=type8Data,proto3" json:"type8Data,omitempty"` // type=8 json_decode后消息结构体
}

func (x *ReqMsgData) Reset() {
	*x = ReqMsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqMsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqMsgData) ProtoMessage() {}

func (x *ReqMsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqMsgData.ProtoReflect.Descriptor instead.
func (*ReqMsgData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{2}
}

func (x *ReqMsgData) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ReqMsgData) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *ReqMsgData) GetType1Data() *PingData {
	if x != nil {
		return x.Type1Data
	}
	return nil
}

func (x *ReqMsgData) GetType2Data() *MsgData {
	if x != nil {
		return x.Type2Data
	}
	return nil
}

func (x *ReqMsgData) GetType5Data() []*NotifySyncData {
	if x != nil {
		return x.Type5Data
	}
	return nil
}

func (x *ReqMsgData) GetType8Data() *PullMessageBySeqsReq {
	if x != nil {
		return x.Type8Data
	}
	return nil
}

// 该字段为虚拟字段，为Payload 解码后的结构体
type RespMsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ping = 1;send_req = 2;
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// type=3解码为SendRespData；type=4 解码为ReceiveData[]
	Data      string                `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Type3Data *SendRespData         `protobuf:"bytes,3,opt,name=type3Data,proto3" json:"type3Data,omitempty"`
	Type4Data []*MsgData            `protobuf:"bytes,4,rep,name=type4Data,proto3" json:"type4Data,omitempty"`
	Type7Data *MaxSeqRespData       `protobuf:"bytes,5,opt,name=type7Data,proto3" json:"type7Data,omitempty"`
	Type9Data *PullMessageBySeqsRsp `protobuf:"bytes,9,opt,name=type9Data,proto3" json:"type9Data,omitempty"`
}

func (x *RespMsgData) Reset() {
	*x = RespMsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RespMsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespMsgData) ProtoMessage() {}

func (x *RespMsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespMsgData.ProtoReflect.Descriptor instead.
func (*RespMsgData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{3}
}

func (x *RespMsgData) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *RespMsgData) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *RespMsgData) GetType3Data() *SendRespData {
	if x != nil {
		return x.Type3Data
	}
	return nil
}

func (x *RespMsgData) GetType4Data() []*MsgData {
	if x != nil {
		return x.Type4Data
	}
	return nil
}

func (x *RespMsgData) GetType7Data() *MaxSeqRespData {
	if x != nil {
		return x.Type7Data
	}
	return nil
}

func (x *RespMsgData) GetType9Data() *PullMessageBySeqsRsp {
	if x != nil {
		return x.Type9Data
	}
	return nil
}

type PingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid string `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Seq    int64  `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`
	// 是否在前台
	IsFront bool `protobuf:"varint,3,opt,name=isFront,proto3" json:"isFront,omitempty"`
}

func (x *PingData) Reset() {
	*x = PingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingData) ProtoMessage() {}

func (x *PingData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingData.ProtoReflect.Descriptor instead.
func (*PingData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{4}
}

func (x *PingData) GetUserid() string {
	if x != nil {
		return x.Userid
	}
	return ""
}

func (x *PingData) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *PingData) GetIsFront() bool {
	if x != nil {
		return x.IsFront
	}
	return false
}

type NotifySyncData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seq    int64 `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`
	MillTs int64 `protobuf:"varint,2,opt,name=mill_ts,json=millTs,proto3" json:"mill_ts,omitempty"`
}

func (x *NotifySyncData) Reset() {
	*x = NotifySyncData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifySyncData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifySyncData) ProtoMessage() {}

func (x *NotifySyncData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifySyncData.ProtoReflect.Descriptor instead.
func (*NotifySyncData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{5}
}

func (x *NotifySyncData) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *NotifySyncData) GetMillTs() int64 {
	if x != nil {
		return x.MillTs
	}
	return 0
}

// 发送和接收的消息结构体，type=2或type=4，对data进行json_decode后消息结构体
type MsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端消息的唯一ID
	LocalId string `protobuf:"bytes,1,opt,name=localId,proto3" json:"localId,omitempty"`
	// 服务端生成消息的唯一ID
	MsgId string `protobuf:"bytes,2,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
	SessionId string `protobuf:"bytes,3,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	From      int64  `protobuf:"varint,4,opt,name=from,proto3" json:"from,omitempty"`
	To        int64  `protobuf:"varint,5,opt,name=to,proto3" json:"to,omitempty"`
	// 群聊ID
	GroupId int64 `protobuf:"varint,6,opt,name=groupId,proto3" json:"groupId,omitempty"`
	// 0:IM消息;1:实时消息
	MsgFrom uint32 `protobuf:"varint,7,opt,name=msgFrom,proto3" json:"msgFrom,omitempty"`
	// 1 单聊，2 群聊，3 语音房， 4 系统通知
	SessionType int32 `protobuf:"varint,8,opt,name=sessionType,proto3" json:"sessionType,omitempty"`
	// 消息内容类型：Text:101,Image:102,Voice:103,Video:104,Custom:110,RealtimeVoice:115,RealTimeVideo:116
	ContentType int32 `protobuf:"varint,9,opt,name=contentType,proto3" json:"contentType,omitempty"`
	// 消息内容，一个json根据contentType解码到不同的结构体，见下面
	Content string `protobuf:"bytes,10,opt,name=content,proto3" json:"content,omitempty"`
	// 消息序号
	Seq int64 `protobuf:"varint,16,opt,name=seq,proto3" json:"seq,omitempty"`
	// 发送时间
	SendTime int64 `protobuf:"varint,17,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	// 消息创建时间
	CreateTime int64 `protobuf:"varint,18,opt,name=createTime,proto3" json:"createTime,omitempty"`
	// 一个map,不配置默认为true。
	// key如下 history:是否推送历史消息
	// persistent：是否永久存储消息
	// offlinePush：离线是否走第三方推送
	Options map[string]bool `protobuf:"bytes,19,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 第三方推送配置
	OfflinePushInfo *OfflinePushInfo `protobuf:"bytes,20,opt,name=offlinePushInfo,proto3" json:"offlinePushInfo,omitempty"`
	// @用户列表
	// repeated  int64  atUserIdList = 21;
	AtList   []*AtInfo         `protobuf:"bytes,21,rep,name=atList,proto3" json:"atList,omitempty"`
	Upgrade  bool              `protobuf:"varint,22,opt,name=upgrade,proto3" json:"upgrade,omitempty"`                                                                                  // 如果是 true，客户端不支持，则提示升级。 否则不显示
	Exts     map[string]string `protobuf:"bytes,23,rep,name=exts,proto3" json:"exts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展字段
	SysMsg   bool              `protobuf:"varint,24,opt,name=SysMsg,proto3" json:"SysMsg,omitempty"`                                                                                    //是否系统消息
	Receiver int64             `protobuf:"varint,35,opt,name=receiver,proto3" json:"receiver,omitempty"`                                                                                //消息接受方
}

func (x *MsgData) Reset() {
	*x = MsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgData) ProtoMessage() {}

func (x *MsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgData.ProtoReflect.Descriptor instead.
func (*MsgData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{6}
}

func (x *MsgData) GetLocalId() string {
	if x != nil {
		return x.LocalId
	}
	return ""
}

func (x *MsgData) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *MsgData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *MsgData) GetFrom() int64 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *MsgData) GetTo() int64 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *MsgData) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MsgData) GetMsgFrom() uint32 {
	if x != nil {
		return x.MsgFrom
	}
	return 0
}

func (x *MsgData) GetSessionType() int32 {
	if x != nil {
		return x.SessionType
	}
	return 0
}

func (x *MsgData) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *MsgData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MsgData) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *MsgData) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *MsgData) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MsgData) GetOptions() map[string]bool {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MsgData) GetOfflinePushInfo() *OfflinePushInfo {
	if x != nil {
		return x.OfflinePushInfo
	}
	return nil
}

func (x *MsgData) GetAtList() []*AtInfo {
	if x != nil {
		return x.AtList
	}
	return nil
}

func (x *MsgData) GetUpgrade() bool {
	if x != nil {
		return x.Upgrade
	}
	return false
}

func (x *MsgData) GetExts() map[string]string {
	if x != nil {
		return x.Exts
	}
	return nil
}

func (x *MsgData) GetSysMsg() bool {
	if x != nil {
		return x.SysMsg
	}
	return false
}

func (x *MsgData) GetReceiver() int64 {
	if x != nil {
		return x.Receiver
	}
	return 0
}

type AtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @all atUserId 需要占位给0
	AtUserId int64  `protobuf:"varint,1,opt,name=atUserId,proto3" json:"atUserId,omitempty"`
	Text     string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Style    string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
}

func (x *AtInfo) Reset() {
	*x = AtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtInfo) ProtoMessage() {}

func (x *AtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtInfo.ProtoReflect.Descriptor instead.
func (*AtInfo) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{7}
}

func (x *AtInfo) GetAtUserId() int64 {
	if x != nil {
		return x.AtUserId
	}
	return 0
}

func (x *AtInfo) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *AtInfo) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

// type=3，data进行json_decode后的消息结构体
type SendRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端消息的唯一ID
	LocalId string `protobuf:"bytes,1,opt,name=localId,proto3" json:"localId,omitempty"`
	// 服务端生成消息的唯一ID
	MsgId string `protobuf:"bytes,2,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
	SessionId string `protobuf:"bytes,3,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	// 发送时间
	SendTime int64 `protobuf:"varint,4,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	// 消息创建时间
	CreateTime  int64  `protobuf:"varint,5,opt,name=createTime,proto3" json:"createTime,omitempty"`
	ErrCode     int32  `protobuf:"varint,6,opt,name=errCode,proto3" json:"errCode,omitempty"`
	ErrMsg      string `protobuf:"bytes,7,opt,name=errMsg,proto3" json:"errMsg,omitempty"`
	SessionType int32  `protobuf:"varint,8,opt,name=sessionType,proto3" json:"sessionType,omitempty"`
	Seq         int64  `protobuf:"varint,9,opt,name=seq,proto3" json:"seq,omitempty"`
	OrderTime   string `protobuf:"bytes,10,opt,name=orderTime,proto3" json:"orderTime,omitempty"`
}

func (x *SendRespData) Reset() {
	*x = SendRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRespData) ProtoMessage() {}

func (x *SendRespData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRespData.ProtoReflect.Descriptor instead.
func (*SendRespData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{8}
}

func (x *SendRespData) GetLocalId() string {
	if x != nil {
		return x.LocalId
	}
	return ""
}

func (x *SendRespData) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *SendRespData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SendRespData) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *SendRespData) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SendRespData) GetErrCode() int32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

func (x *SendRespData) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *SendRespData) GetSessionType() int32 {
	if x != nil {
		return x.SessionType
	}
	return 0
}

func (x *SendRespData) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *SendRespData) GetOrderTime() string {
	if x != nil {
		return x.OrderTime
	}
	return ""
}

// type=7，data进行json_decode后的消息结构体
type MaxSeqRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 群聊会话的最大sed
	MaxSeqs map[string]int64 `protobuf:"bytes,1,rep,name=maxSeqs,proto3" json:"maxSeqs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *MaxSeqRespData) Reset() {
	*x = MaxSeqRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaxSeqRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaxSeqRespData) ProtoMessage() {}

func (x *MaxSeqRespData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaxSeqRespData.ProtoReflect.Descriptor instead.
func (*MaxSeqRespData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{9}
}

func (x *MaxSeqRespData) GetMaxSeqs() map[string]int64 {
	if x != nil {
		return x.MaxSeqs
	}
	return nil
}

type SeqRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId string `protobuf:"bytes,1,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	Begin     int64  `protobuf:"varint,2,opt,name=begin,proto3" json:"begin,omitempty"`
	End       int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	// 不传或传0，走客户端配置的值，>0会最多返回num条数据
	Num int64 `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *SeqRange) Reset() {
	*x = SeqRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqRange) ProtoMessage() {}

func (x *SeqRange) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqRange.ProtoReflect.Descriptor instead.
func (*SeqRange) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{10}
}

func (x *SeqRange) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SeqRange) GetBegin() int64 {
	if x != nil {
		return x.Begin
	}
	return 0
}

func (x *SeqRange) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *SeqRange) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

// 同步丢弃消息
type SyncSeq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId string `protobuf:"bytes,1,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	// SeqRange 内部只要传 begin end
	SeqRanges []*SeqRange `protobuf:"bytes,2,rep,name=seqRanges,proto3" json:"seqRanges,omitempty"`
}

func (x *SyncSeq) Reset() {
	*x = SyncSeq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSeq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSeq) ProtoMessage() {}

func (x *SyncSeq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSeq.ProtoReflect.Descriptor instead.
func (*SyncSeq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{11}
}

func (x *SyncSeq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SyncSeq) GetSeqRanges() []*SeqRange {
	if x != nil {
		return x.SeqRanges
	}
	return nil
}

// type=8，data进行json_decode后的消息结构体
type PullMessageBySeqsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID int64 `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
	// 同步消息
	SeqRanges []*SeqRange `protobuf:"bytes,2,rep,name=seqRanges,proto3" json:"seqRanges,omitempty"`
}

func (x *PullMessageBySeqsReq) Reset() {
	*x = PullMessageBySeqsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullMessageBySeqsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullMessageBySeqsReq) ProtoMessage() {}

func (x *PullMessageBySeqsReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullMessageBySeqsReq.ProtoReflect.Descriptor instead.
func (*PullMessageBySeqsReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{12}
}

func (x *PullMessageBySeqsReq) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *PullMessageBySeqsReq) GetSeqRanges() []*SeqRange {
	if x != nil {
		return x.SeqRanges
	}
	return nil
}

// type=9，data进行json_decode后的消息结构体
type PullMessageBySeqsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sessionId => msglist[]
	Msgs map[string]*PullMsgs `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PullMessageBySeqsRsp) Reset() {
	*x = PullMessageBySeqsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullMessageBySeqsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullMessageBySeqsRsp) ProtoMessage() {}

func (x *PullMessageBySeqsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullMessageBySeqsRsp.ProtoReflect.Descriptor instead.
func (*PullMessageBySeqsRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{13}
}

func (x *PullMessageBySeqsRsp) GetMsgs() map[string]*PullMsgs {
	if x != nil {
		return x.Msgs
	}
	return nil
}

type PullMsgs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msgs []*MsgData `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
	// 是否结束，预留字段
	IsEnd bool `protobuf:"varint,2,opt,name=isEnd,proto3" json:"isEnd,omitempty"`
}

func (x *PullMsgs) Reset() {
	*x = PullMsgs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullMsgs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullMsgs) ProtoMessage() {}

func (x *PullMsgs) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullMsgs.ProtoReflect.Descriptor instead.
func (*PullMsgs) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{14}
}

func (x *PullMsgs) GetMsgs() []*MsgData {
	if x != nil {
		return x.Msgs
	}
	return nil
}

func (x *PullMsgs) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

type OfflinePushInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title         string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Desc          string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Ext           string `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	IOSPushSound  string `protobuf:"bytes,4,opt,name=IOSPushSound,proto3" json:"IOSPushSound,omitempty"`
	IOSBadgeCount bool   `protobuf:"varint,5,opt,name=IOSBadgeCount,proto3" json:"IOSBadgeCount,omitempty"`
	SignalInfo    string `protobuf:"bytes,6,opt,name=signalInfo,proto3" json:"signalInfo,omitempty"`
}

func (x *OfflinePushInfo) Reset() {
	*x = OfflinePushInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflinePushInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflinePushInfo) ProtoMessage() {}

func (x *OfflinePushInfo) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflinePushInfo.ProtoReflect.Descriptor instead.
func (*OfflinePushInfo) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{15}
}

func (x *OfflinePushInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OfflinePushInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *OfflinePushInfo) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *OfflinePushInfo) GetIOSPushSound() string {
	if x != nil {
		return x.IOSPushSound
	}
	return ""
}

func (x *OfflinePushInfo) GetIOSBadgeCount() bool {
	if x != nil {
		return x.IOSBadgeCount
	}
	return false
}

func (x *OfflinePushInfo) GetSignalInfo() string {
	if x != nil {
		return x.SignalInfo
	}
	return ""
}

type TextElem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *TextElem) Reset() {
	*x = TextElem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextElem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextElem) ProtoMessage() {}

func (x *TextElem) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextElem.ProtoReflect.Descriptor instead.
func (*TextElem) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{16}
}

func (x *TextElem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ImageElem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl string `protobuf:"bytes,1,opt,name=imageUrl,proto3" json:"imageUrl,omitempty"`
	ObjectId string `protobuf:"bytes,2,opt,name=objectId,proto3" json:"objectId,omitempty"`
	Width    int64  `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`
	Height   int64  `protobuf:"varint,4,opt,name=height,proto3" json:"height,omitempty"`
	Size     int64  `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *ImageElem) Reset() {
	*x = ImageElem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageElem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageElem) ProtoMessage() {}

func (x *ImageElem) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageElem.ProtoReflect.Descriptor instead.
func (*ImageElem) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{17}
}

func (x *ImageElem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *ImageElem) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

func (x *ImageElem) GetWidth() int64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ImageElem) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *ImageElem) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type VoiceElem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoiceUrl string `protobuf:"bytes,1,opt,name=voiceUrl,proto3" json:"voiceUrl,omitempty"`
	DataSize int64  `protobuf:"varint,2,opt,name=dataSize,proto3" json:"dataSize,omitempty"`
	Duration int64  `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	ObjectId string `protobuf:"bytes,4,opt,name=objectId,proto3" json:"objectId,omitempty"`
}

func (x *VoiceElem) Reset() {
	*x = VoiceElem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoiceElem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoiceElem) ProtoMessage() {}

func (x *VoiceElem) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoiceElem.ProtoReflect.Descriptor instead.
func (*VoiceElem) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{18}
}

func (x *VoiceElem) GetVoiceUrl() string {
	if x != nil {
		return x.VoiceUrl
	}
	return ""
}

func (x *VoiceElem) GetDataSize() int64 {
	if x != nil {
		return x.DataSize
	}
	return 0
}

func (x *VoiceElem) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *VoiceElem) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

type VideoElem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl  string `protobuf:"bytes,1,opt,name=videoUrl,proto3" json:"videoUrl,omitempty"`
	VideoType string `protobuf:"bytes,2,opt,name=videoType,proto3" json:"videoType,omitempty"`
	VideoSize int64  `protobuf:"varint,3,opt,name=videoSize,proto3" json:"videoSize,omitempty"`
	Duration  int64  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	ObjectId  string `protobuf:"bytes,5,opt,name=objectId,proto3" json:"objectId,omitempty"`
}

func (x *VideoElem) Reset() {
	*x = VideoElem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoElem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoElem) ProtoMessage() {}

func (x *VideoElem) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoElem.ProtoReflect.Descriptor instead.
func (*VideoElem) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{19}
}

func (x *VideoElem) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *VideoElem) GetVideoType() string {
	if x != nil {
		return x.VideoType
	}
	return ""
}

func (x *VideoElem) GetVideoSize() int64 {
	if x != nil {
		return x.VideoSize
	}
	return 0
}

func (x *VideoElem) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *VideoElem) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

type CustomElem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data        string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Extension   string `protobuf:"bytes,3,opt,name=extension,proto3" json:"extension,omitempty"`
}

func (x *CustomElem) Reset() {
	*x = CustomElem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomElem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomElem) ProtoMessage() {}

func (x *CustomElem) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomElem.ProtoReflect.Descriptor instead.
func (*CustomElem) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{20}
}

func (x *CustomElem) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *CustomElem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CustomElem) GetExtension() string {
	if x != nil {
		return x.Extension
	}
	return ""
}

type ChanTokenRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 长连接token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 长连接主地址
	MasterAddr *basemsgcallin.MsgAddrData `protobuf:"bytes,2,opt,name=masterAddr,proto3" json:"masterAddr,omitempty"`
	// 长连接备用地址
	SlaveAddr *basemsgcallin.MsgAddrData `protobuf:"bytes,3,opt,name=slaveAddr,proto3" json:"slaveAddr,omitempty"`
	// 客户端发上行消息使用的通用topic
	GeneralTopic string `protobuf:"bytes,4,opt,name=generalTopic,proto3" json:"generalTopic,omitempty"`
	// payload是否进行gzip压缩
	Gzip bool `protobuf:"varint,5,opt,name=gzip,proto3" json:"gzip,omitempty"`
	// userId
	Userid int64 `protobuf:"varint,6,opt,name=userid,proto3" json:"userid,omitempty"`
}

func (x *ChanTokenRespData) Reset() {
	*x = ChanTokenRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChanTokenRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChanTokenRespData) ProtoMessage() {}

func (x *ChanTokenRespData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChanTokenRespData.ProtoReflect.Descriptor instead.
func (*ChanTokenRespData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{21}
}

func (x *ChanTokenRespData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ChanTokenRespData) GetMasterAddr() *basemsgcallin.MsgAddrData {
	if x != nil {
		return x.MasterAddr
	}
	return nil
}

func (x *ChanTokenRespData) GetSlaveAddr() *basemsgcallin.MsgAddrData {
	if x != nil {
		return x.SlaveAddr
	}
	return nil
}

func (x *ChanTokenRespData) GetGeneralTopic() string {
	if x != nil {
		return x.GeneralTopic
	}
	return ""
}

func (x *ChanTokenRespData) GetGzip() bool {
	if x != nil {
		return x.Gzip
	}
	return false
}

func (x *ChanTokenRespData) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

type ChanTokenResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data *ChanTokenRespData  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ChanTokenResp) Reset() {
	*x = ChanTokenResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChanTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChanTokenResp) ProtoMessage() {}

func (x *ChanTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChanTokenResp.ProtoReflect.Descriptor instead.
func (*ChanTokenResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{22}
}

func (x *ChanTokenResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *ChanTokenResp) GetData() *ChanTokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type HandleMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign    string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	MsgJson string `protobuf:"bytes,5,opt,name=msgJson,proto3" json:"msgJson,omitempty"`
}

func (x *HandleMsgReq) Reset() {
	*x = HandleMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleMsgReq) ProtoMessage() {}

func (x *HandleMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleMsgReq.ProtoReflect.Descriptor instead.
func (*HandleMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{23}
}

func (x *HandleMsgReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *HandleMsgReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *HandleMsgReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *HandleMsgReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *HandleMsgReq) GetMsgJson() string {
	if x != nil {
		return x.MsgJson
	}
	return ""
}

type HandleMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *HandleMsgRsp) Reset() {
	*x = HandleMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleMsgRsp) ProtoMessage() {}

func (x *HandleMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleMsgRsp.ProtoReflect.Descriptor instead.
func (*HandleMsgRsp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{24}
}

func (x *HandleMsgRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type UpstreamMsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上行消息类型（1为长连接建立，2为长连接关闭，3为长连接ping，4为长连接上行消息）
	Type MsgEventType `protobuf:"varint,1,opt,name=type,proto3,enum=vc.basemsgtransfer.MsgEventType" json:"type,omitempty"`
	// 长连接本地标识id
	ConnId uint64 `protobuf:"varint,11,opt,name=connId,proto3" json:"connId,omitempty"`
	// 客户端设备id
	DeviceId string `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId,omitempty"`
	// 消息发送时间戳(这个是长连接接收到消息的时间)
	Time int64 `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	// 消息内容，具体消息数据
	Msg string `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
	// 消息对应的topic
	Topic string `protobuf:"bytes,6,opt,name=topic,proto3" json:"topic,omitempty"`
	// 消息id
	MsgId string `protobuf:"bytes,7,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// 消息记录唯一id
	OrderId string `protobuf:"bytes,8,opt,name=orderId,proto3" json:"orderId,omitempty"`
	// 消息发送时间戳-纳秒(这个是长连接接收到消息的时间)
	NanoTime int64 `protobuf:"varint,9,opt,name=nanoTime,proto3" json:"nanoTime,omitempty"`
	// 客户端标识
	ClientId uint32 `protobuf:"varint,10,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// base info,
	Base *common.BaseParam `protobuf:"bytes,12,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *UpstreamMsgData) Reset() {
	*x = UpstreamMsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpstreamMsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpstreamMsgData) ProtoMessage() {}

func (x *UpstreamMsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpstreamMsgData.ProtoReflect.Descriptor instead.
func (*UpstreamMsgData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{25}
}

func (x *UpstreamMsgData) GetType() MsgEventType {
	if x != nil {
		return x.Type
	}
	return MsgEventType_unknown
}

func (x *UpstreamMsgData) GetConnId() uint64 {
	if x != nil {
		return x.ConnId
	}
	return 0
}

func (x *UpstreamMsgData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UpstreamMsgData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpstreamMsgData) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *UpstreamMsgData) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpstreamMsgData) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *UpstreamMsgData) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *UpstreamMsgData) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *UpstreamMsgData) GetNanoTime() int64 {
	if x != nil {
		return x.NanoTime
	}
	return 0
}

func (x *UpstreamMsgData) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *UpstreamMsgData) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

type InterMsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Base      *common.BaseParam     `protobuf:"bytes,2,opt,name=base,proto3" json:"base,omitempty"`
	Type      int32                 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Type1Data *PingData             `protobuf:"bytes,4,opt,name=type1Data,proto3" json:"type1Data,omitempty"` // type=1json_decode后的消息结构体
	Type2Data *MsgData              `protobuf:"bytes,5,opt,name=type2Data,proto3" json:"type2Data,omitempty"` // type=2json_decode后消息结构体
	Type5Data []*NotifySyncData     `protobuf:"bytes,6,rep,name=type5Data,proto3" json:"type5Data,omitempty"`
	Timestamp int64                 `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Type8Data *PullMessageBySeqsReq `protobuf:"bytes,8,opt,name=type8Data,proto3" json:"type8Data,omitempty"` // type=8json_decode后消息结构体
	NanoTime  int64                 `protobuf:"varint,9,opt,name=nano_time,json=nanoTime,proto3" json:"nano_time,omitempty"`
}

func (x *InterMsgData) Reset() {
	*x = InterMsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterMsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterMsgData) ProtoMessage() {}

func (x *InterMsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterMsgData.ProtoReflect.Descriptor instead.
func (*InterMsgData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{26}
}

func (x *InterMsgData) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *InterMsgData) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *InterMsgData) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *InterMsgData) GetType1Data() *PingData {
	if x != nil {
		return x.Type1Data
	}
	return nil
}

func (x *InterMsgData) GetType2Data() *MsgData {
	if x != nil {
		return x.Type2Data
	}
	return nil
}

func (x *InterMsgData) GetType5Data() []*NotifySyncData {
	if x != nil {
		return x.Type5Data
	}
	return nil
}

func (x *InterMsgData) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *InterMsgData) GetType8Data() *PullMessageBySeqsReq {
	if x != nil {
		return x.Type8Data
	}
	return nil
}

func (x *InterMsgData) GetNanoTime() int64 {
	if x != nil {
		return x.NanoTime
	}
	return 0
}

type SendMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msgs     []*MsgData `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
	Receiver int64      `protobuf:"varint,2,opt,name=receiver,proto3" json:"receiver,omitempty"`
}

func (x *SendMsgReq) Reset() {
	*x = SendMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgReq) ProtoMessage() {}

func (x *SendMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgReq.ProtoReflect.Descriptor instead.
func (*SendMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{27}
}

func (x *SendMsgReq) GetMsgs() []*MsgData {
	if x != nil {
		return x.Msgs
	}
	return nil
}

func (x *SendMsgReq) GetReceiver() int64 {
	if x != nil {
		return x.Receiver
	}
	return 0
}

type SendMsgResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base   *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Msgids []string            `protobuf:"bytes,2,rep,name=msgids,proto3" json:"msgids,omitempty"`
}

func (x *SendMsgResp) Reset() {
	*x = SendMsgResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgResp) ProtoMessage() {}

func (x *SendMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgResp.ProtoReflect.Descriptor instead.
func (*SendMsgResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{28}
}

func (x *SendMsgResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *SendMsgResp) GetMsgids() []string {
	if x != nil {
		return x.Msgids
	}
	return nil
}

type GetSessionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId string       `protobuf:"bytes,1,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	Userid    int64        `protobuf:"varint,2,opt,name=userid,proto3" json:"userid,omitempty"`
	Upsert    *SessionInfo `protobuf:"bytes,3,opt,name=upsert,proto3" json:"upsert,omitempty"`
	To        int64        `protobuf:"varint,4,opt,name=to,proto3" json:"to,omitempty"`
	GroupId   int64        `protobuf:"varint,5,opt,name=groupId,proto3" json:"groupId,omitempty"`
}

func (x *GetSessionReq) Reset() {
	*x = GetSessionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionReq) ProtoMessage() {}

func (x *GetSessionReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionReq.ProtoReflect.Descriptor instead.
func (*GetSessionReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{29}
}

func (x *GetSessionReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *GetSessionReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *GetSessionReq) GetUpsert() *SessionInfo {
	if x != nil {
		return x.Upsert
	}
	return nil
}

func (x *GetSessionReq) GetTo() int64 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *GetSessionReq) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

type GetSessionResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base    *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Session *SessionInfo        `protobuf:"bytes,2,opt,name=session,proto3" json:"session,omitempty"`
	IsNew   bool                `protobuf:"varint,3,opt,name=isNew,proto3" json:"isNew,omitempty"`
}

func (x *GetSessionResp) Reset() {
	*x = GetSessionResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionResp) ProtoMessage() {}

func (x *GetSessionResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionResp.ProtoReflect.Descriptor instead.
func (*GetSessionResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{30}
}

func (x *GetSessionResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetSessionResp) GetSession() *SessionInfo {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *GetSessionResp) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

type GetSortedSessionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid int64 `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
}

func (x *GetSortedSessionListReq) Reset() {
	*x = GetSortedSessionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSortedSessionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSortedSessionListReq) ProtoMessage() {}

func (x *GetSortedSessionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSortedSessionListReq.ProtoReflect.Descriptor instead.
func (*GetSortedSessionListReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{31}
}

func (x *GetSortedSessionListReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

type SessionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId   string `protobuf:"bytes,1,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	SessionType int32  `protobuf:"varint,2,opt,name=sessionType,proto3" json:"sessionType,omitempty"`
	OwnUserid   int64  `protobuf:"varint,3,opt,name=ownUserid,proto3" json:"ownUserid,omitempty"`
	PeerId      int64  `protobuf:"varint,4,opt,name=peerId,proto3" json:"peerId,omitempty"` //会话 对方用户
	GroupId     int64  `protobuf:"varint,5,opt,name=groupId,proto3" json:"groupId,omitempty"`
	RecvMsgOpt  int32  `protobuf:"varint,6,opt,name=recvMsgOpt,proto3" json:"recvMsgOpt,omitempty"`
	IsPinned    bool   `protobuf:"varint,7,opt,name=isPinned,proto3" json:"isPinned,omitempty"`
	Ct          int64  `protobuf:"varint,8,opt,name=ct,proto3" json:"ct,omitempty"`
	Remark      string `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark,omitempty"` //备注
}

func (x *SessionInfo) Reset() {
	*x = SessionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionInfo) ProtoMessage() {}

func (x *SessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionInfo.ProtoReflect.Descriptor instead.
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{32}
}

func (x *SessionInfo) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SessionInfo) GetSessionType() int32 {
	if x != nil {
		return x.SessionType
	}
	return 0
}

func (x *SessionInfo) GetOwnUserid() int64 {
	if x != nil {
		return x.OwnUserid
	}
	return 0
}

func (x *SessionInfo) GetPeerId() int64 {
	if x != nil {
		return x.PeerId
	}
	return 0
}

func (x *SessionInfo) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *SessionInfo) GetRecvMsgOpt() int32 {
	if x != nil {
		return x.RecvMsgOpt
	}
	return 0
}

func (x *SessionInfo) GetIsPinned() bool {
	if x != nil {
		return x.IsPinned
	}
	return false
}

func (x *SessionInfo) GetCt() int64 {
	if x != nil {
		return x.Ct
	}
	return 0
}

func (x *SessionInfo) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type SessionListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sessions []*SessionInfo `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *SessionListData) Reset() {
	*x = SessionListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionListData) ProtoMessage() {}

func (x *SessionListData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionListData.ProtoReflect.Descriptor instead.
func (*SessionListData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{33}
}

func (x *SessionListData) GetSessions() []*SessionInfo {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type GetSortedSessionListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data *SessionListData    `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetSortedSessionListResp) Reset() {
	*x = GetSortedSessionListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSortedSessionListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSortedSessionListResp) ProtoMessage() {}

func (x *GetSortedSessionListResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSortedSessionListResp.ProtoReflect.Descriptor instead.
func (*GetSortedSessionListResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{34}
}

func (x *GetSortedSessionListResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetSortedSessionListResp) GetData() *SessionListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetSessionInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid     int64    `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	SessionIds []string `protobuf:"bytes,2,rep,name=sessionIds,proto3" json:"sessionIds,omitempty"`
}

func (x *GetSessionInfoReq) Reset() {
	*x = GetSessionInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionInfoReq) ProtoMessage() {}

func (x *GetSessionInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionInfoReq.ProtoReflect.Descriptor instead.
func (*GetSessionInfoReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{35}
}

func (x *GetSessionInfoReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *GetSessionInfoReq) GetSessionIds() []string {
	if x != nil {
		return x.SessionIds
	}
	return nil
}

type GetSortedSessionListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sessions []*SessionInfo `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *GetSortedSessionListData) Reset() {
	*x = GetSortedSessionListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSortedSessionListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSortedSessionListData) ProtoMessage() {}

func (x *GetSortedSessionListData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSortedSessionListData.ProtoReflect.Descriptor instead.
func (*GetSortedSessionListData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{36}
}

func (x *GetSortedSessionListData) GetSessions() []*SessionInfo {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type GetSessionInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data *SessionListData    `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetSessionInfoResp) Reset() {
	*x = GetSessionInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionInfoResp) ProtoMessage() {}

func (x *GetSessionInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionInfoResp.ProtoReflect.Descriptor instead.
func (*GetSessionInfoResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{37}
}

func (x *GetSessionInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetSessionInfoResp) GetData() *SessionListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SetSessionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId  string                  `protobuf:"bytes,1,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	OwnUserid  int64                   `protobuf:"varint,2,opt,name=ownUserid,proto3" json:"ownUserid,omitempty"`
	RecvMsgOpt *wrapperspb.Int32Value  `protobuf:"bytes,3,opt,name=recvMsgOpt,proto3" json:"recvMsgOpt,omitempty"`
	IsPinned   *wrapperspb.BoolValue   `protobuf:"bytes,4,opt,name=isPinned,proto3" json:"isPinned,omitempty"`
	Remark     *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *SetSessionReq) Reset() {
	*x = SetSessionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSessionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSessionReq) ProtoMessage() {}

func (x *SetSessionReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSessionReq.ProtoReflect.Descriptor instead.
func (*SetSessionReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{38}
}

func (x *SetSessionReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SetSessionReq) GetOwnUserid() int64 {
	if x != nil {
		return x.OwnUserid
	}
	return 0
}

func (x *SetSessionReq) GetRecvMsgOpt() *wrapperspb.Int32Value {
	if x != nil {
		return x.RecvMsgOpt
	}
	return nil
}

func (x *SetSessionReq) GetIsPinned() *wrapperspb.BoolValue {
	if x != nil {
		return x.IsPinned
	}
	return nil
}

func (x *SetSessionReq) GetRemark() *wrapperspb.StringValue {
	if x != nil {
		return x.Remark
	}
	return nil
}

type MulticastRealTimeMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msgs  []*MsgData `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
	Topic string     `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *MulticastRealTimeMsgReq) Reset() {
	*x = MulticastRealTimeMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MulticastRealTimeMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MulticastRealTimeMsgReq) ProtoMessage() {}

func (x *MulticastRealTimeMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MulticastRealTimeMsgReq.ProtoReflect.Descriptor instead.
func (*MulticastRealTimeMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{39}
}

func (x *MulticastRealTimeMsgReq) GetMsgs() []*MsgData {
	if x != nil {
		return x.Msgs
	}
	return nil
}

func (x *MulticastRealTimeMsgReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type SubscribeTopicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid int64  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Topic  string `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *SubscribeTopicReq) Reset() {
	*x = SubscribeTopicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeTopicReq) ProtoMessage() {}

func (x *SubscribeTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeTopicReq.ProtoReflect.Descriptor instead.
func (*SubscribeTopicReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{40}
}

func (x *SubscribeTopicReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *SubscribeTopicReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type SetSessionResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *SetSessionResp) Reset() {
	*x = SetSessionResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSessionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSessionResp) ProtoMessage() {}

func (x *SetSessionResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSessionResp.ProtoReflect.Descriptor instead.
func (*SetSessionResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{41}
}

func (x *SetSessionResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type UnSubscribeTopicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid int64  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Topic  string `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *UnSubscribeTopicReq) Reset() {
	*x = UnSubscribeTopicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnSubscribeTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSubscribeTopicReq) ProtoMessage() {}

func (x *UnSubscribeTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSubscribeTopicReq.ProtoReflect.Descriptor instead.
func (*UnSubscribeTopicReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{42}
}

func (x *UnSubscribeTopicReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *UnSubscribeTopicReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type UserGroupIdListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid int64 `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
}

func (x *UserGroupIdListReq) Reset() {
	*x = UserGroupIdListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserGroupIdListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGroupIdListReq) ProtoMessage() {}

func (x *UserGroupIdListReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGroupIdListReq.ProtoReflect.Descriptor instead.
func (*UserGroupIdListReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{43}
}

func (x *UserGroupIdListReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

type UserGroupIdListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base       *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	GrouidList []int64             `protobuf:"varint,2,rep,packed,name=grouid_list,json=grouidList,proto3" json:"grouid_list,omitempty"`
}

func (x *UserGroupIdListResp) Reset() {
	*x = UserGroupIdListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserGroupIdListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGroupIdListResp) ProtoMessage() {}

func (x *UserGroupIdListResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGroupIdListResp.ProtoReflect.Descriptor instead.
func (*UserGroupIdListResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{44}
}

func (x *UserGroupIdListResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *UserGroupIdListResp) GetGrouidList() []int64 {
	if x != nil {
		return x.GrouidList
	}
	return nil
}

type UserJoinGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid  int64  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Groupid int64  `protobuf:"varint,2,opt,name=groupid,proto3" json:"groupid,omitempty"`
	Topic   string `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *UserJoinGroupReq) Reset() {
	*x = UserJoinGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserJoinGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserJoinGroupReq) ProtoMessage() {}

func (x *UserJoinGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserJoinGroupReq.ProtoReflect.Descriptor instead.
func (*UserJoinGroupReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{45}
}

func (x *UserJoinGroupReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *UserJoinGroupReq) GetGroupid() int64 {
	if x != nil {
		return x.Groupid
	}
	return 0
}

func (x *UserJoinGroupReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type UserLeaveGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid  int64  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Groupid int64  `protobuf:"varint,2,opt,name=groupid,proto3" json:"groupid,omitempty"`
	Topic   string `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *UserLeaveGroupReq) Reset() {
	*x = UserLeaveGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLeaveGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLeaveGroupReq) ProtoMessage() {}

func (x *UserLeaveGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLeaveGroupReq.ProtoReflect.Descriptor instead.
func (*UserLeaveGroupReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{46}
}

func (x *UserLeaveGroupReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *UserLeaveGroupReq) GetGroupid() int64 {
	if x != nil {
		return x.Groupid
	}
	return 0
}

func (x *UserLeaveGroupReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type SeqCacheData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Receiver int64 `protobuf:"varint,1,opt,name=receiver,proto3" json:"receiver,omitempty"`
	Seq      int64 `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`
}

func (x *SeqCacheData) Reset() {
	*x = SeqCacheData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqCacheData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqCacheData) ProtoMessage() {}

func (x *SeqCacheData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqCacheData.ProtoReflect.Descriptor instead.
func (*SeqCacheData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{47}
}

func (x *SeqCacheData) GetReceiver() int64 {
	if x != nil {
		return x.Receiver
	}
	return 0
}

func (x *SeqCacheData) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

var File_basemsgtransfer_proto protoreflect.FileDescriptor

var file_basemsgtransfer_proto_rawDesc = []byte{
	0x0a, 0x15, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xd4, 0x01, 0x0a, 0x03, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x46, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0xd1, 0x01, 0x0a, 0x04, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x47, 0x0a, 0x0e, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x73, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x64, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0xbf, 0x02,
	0x0a, 0x0a, 0x52, 0x65, 0x71, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a,
	0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x31, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x09, 0x74, 0x79, 0x70, 0x65, 0x31, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x79,
	0x70, 0x65, 0x32, 0x44, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65,
	0x32, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x35, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79,
	0x70, 0x65, 0x35, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x38,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x50, 0x75, 0x6c, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x53, 0x65, 0x71,
	0x73, 0x52, 0x65, 0x71, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x38, 0x44, 0x61, 0x74, 0x61, 0x22,
	0xba, 0x02, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x70, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x33,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79,
	0x70, 0x65, 0x33, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x34,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x34, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x37, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x61, 0x78, 0x53, 0x65,
	0x71, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x37,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x39, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x6c,
	0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x53, 0x65, 0x71, 0x73, 0x52, 0x73,
	0x70, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x39, 0x44, 0x61, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x08,
	0x50, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x07, 0x69, 0x73, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x69, 0x73, 0x46, 0x72, 0x6f, 0x6e,
	0x74, 0x22, 0x3b, 0x0a, 0x0e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x53, 0x79, 0x6e, 0x63, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x69, 0x6c, 0x6c, 0x54, 0x73, 0x22, 0xc8,
	0x06, 0x0a, 0x07, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x07, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x07, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x13, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x12,
	0x25, 0x0a, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x65, 0x71, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x42, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x0f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x32, 0x0a, 0x06, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x15, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x41, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06,
	0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x12, 0x39, 0x0a, 0x04, 0x65, 0x78, 0x74, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x78, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x53,
	0x79, 0x73, 0x4d, 0x73, 0x67, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x53, 0x79, 0x73,
	0x4d, 0x73, 0x67, 0x12, 0x1f, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x1a, 0x3a, 0x0a, 0x0c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x37, 0x0a, 0x09, 0x45, 0x78, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4e, 0x0a, 0x06, 0x41, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x22, 0x9c, 0x02, 0x0a, 0x0c, 0x53, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x0e, 0x4d, 0x61, 0x78,
	0x53, 0x65, 0x71, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x07, 0x6d,
	0x61, 0x78, 0x53, 0x65, 0x71, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x71, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x71, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d,
	0x61, 0x78, 0x53, 0x65, 0x71, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x71,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x62, 0x0a, 0x08, 0x53, 0x65, 0x71, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x62, 0x65, 0x67,
	0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x65, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x63, 0x0a, 0x07, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65,
	0x71, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x3a, 0x0a, 0x09, 0x73, 0x65, 0x71, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x71, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x09, 0x73, 0x65, 0x71, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0x6a, 0x0a, 0x14, 0x50,
	0x75, 0x6c, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x53, 0x65, 0x71, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x3a, 0x0a, 0x09, 0x73,
	0x65, 0x71, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x71, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x73, 0x65,
	0x71, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0xb5, 0x01, 0x0a, 0x14, 0x50, 0x75, 0x6c, 0x6c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x53, 0x65, 0x71, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x46, 0x0a, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x6c, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42,
	0x79, 0x53, 0x65, 0x71, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x73, 0x67, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x1a, 0x55, 0x0a, 0x09, 0x4d, 0x73, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x6c, 0x6c,
	0x4d, 0x73, 0x67, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x51, 0x0a, 0x08, 0x50, 0x75, 0x6c, 0x6c, 0x4d, 0x73, 0x67, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x6d,
	0x73, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d,
	0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x73, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x45,
	0x6e, 0x64, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65,
	0x78, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x4f, 0x53, 0x50, 0x75, 0x73, 0x68, 0x53, 0x6f, 0x75,
	0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x4f, 0x53, 0x50, 0x75, 0x73,
	0x68, 0x53, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x49, 0x4f, 0x53, 0x42, 0x61, 0x64,
	0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x49,
	0x4f, 0x53, 0x42, 0x61, 0x64, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x24, 0x0a, 0x08,
	0x54, 0x65, 0x78, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x22, 0x85, 0x01, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x7b, 0x0a, 0x09, 0x56, 0x6f,
	0x69, 0x63, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x09, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x45, 0x6c, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72,
	0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x45,
	0x6c, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf5, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x3d, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x41, 0x64,
	0x64, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x12, 0x3b, 0x0a, 0x09, 0x73, 0x6c, 0x61, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x41, 0x64, 0x64, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x73, 0x6c, 0x61, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12,
	0x22, 0x0a, 0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x7a, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x04, 0x67, 0x7a, 0x69, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x22,
	0x73, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x96, 0x01, 0x0a, 0x0c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d,
	0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73,
	0x69, 0x67, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x73, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x37, 0x0a,
	0x0c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a,
	0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0xde, 0x02, 0x0a, 0x0f, 0x55, 0x70, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73,
	0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x61, 0x6e, 0x6f,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x61, 0x6e, 0x6f,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x9d, 0x03, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x74,
	0x79, 0x70, 0x65, 0x31, 0x44, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79,
	0x70, 0x65, 0x31, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x32,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x32, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x35, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x35,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x46, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x38, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x6c, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x53, 0x65, 0x71, 0x73, 0x52, 0x65, 0x71, 0x52,
	0x09, 0x74, 0x79, 0x70, 0x65, 0x38, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x61,
	0x6e, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e,
	0x61, 0x6e, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x59, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x22, 0x4e, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x73,
	0x67, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x73, 0x67, 0x69,
	0x64, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x75, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x75, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x74, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x8a, 0x01,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x07, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x22, 0x31, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x22, 0x81, 0x02,
	0x0a, 0x0b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x65, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x65, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x63, 0x76, 0x4d, 0x73, 0x67, 0x4f, 0x70, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x76, 0x4d, 0x73, 0x67, 0x4f, 0x70, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x22, 0x4e, 0x0a, 0x0f, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x7c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a,
	0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x4b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x57, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x76, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62,
	0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04,
	0x62, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xf6, 0x01,
	0x0a, 0x0d, 0x53, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x6f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x72,
	0x65, 0x63, 0x76, 0x4d, 0x73, 0x67, 0x4f, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x72, 0x65,
	0x63, 0x76, 0x4d, 0x73, 0x67, 0x4f, 0x70, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x69, 0x73, 0x50, 0x69,
	0x6e, 0x6e, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64,
	0x12, 0x34, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x60, 0x0a, 0x17, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x2f, 0x0a, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x73,
	0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x41, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x39, 0x0a, 0x0e, 0x53,
	0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a,
	0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x13, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x2c, 0x0a, 0x12, 0x55,
	0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x22, 0x5f, 0x0a, 0x13, 0x55, 0x73, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f,
	0x75, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a,
	0x67, 0x72, 0x6f, 0x75, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x5a, 0x0a, 0x10, 0x55, 0x73,
	0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x5b, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x22, 0x3c, 0x0a, 0x0c, 0x73, 0x65, 0x71, 0x43, 0x61, 0x63, 0x68, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65,
	0x71, 0x2a, 0x1e, 0x0a, 0x07, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x63, 0x68, 0x61, 0x74, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x74, 0x6f, 0x61, 0x73, 0x74, 0x10,
	0x01, 0x2a, 0x94, 0x02, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x61,
	0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x73, 0x70, 0x10, 0x03, 0x12, 0x15,
	0x0a, 0x11, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x41, 0x63, 0x6b, 0x10, 0x05, 0x12,
	0x18, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x41, 0x63,
	0x6b, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x68, 0x61,
	0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x65, 0x77, 0x65, 0x73, 0x74, 0x53, 0x65,
	0x71, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x4d, 0x73, 0x67, 0x42, 0x79, 0x53, 0x65, 0x71, 0x4c, 0x69,
	0x73, 0x74, 0x10, 0x08, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x4d, 0x73, 0x67, 0x42, 0x79, 0x53, 0x65, 0x71, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x10, 0x09, 0x2a, 0x98, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x52,
	0x6f, 0x6f, 0x6d, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x10, 0x05, 0x2a, 0x41, 0x0a, 0x0b, 0x4d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x49, 0x6d, 0x10,
	0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x61, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x73, 0x67, 0x46, 0x72, 0x6f,
	0x6d, 0x49, 0x6d, 0x32, 0x10, 0x02, 0x2a, 0x4d, 0x0a, 0x0c, 0x4d, 0x73, 0x67, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x10, 0x04, 0x2a, 0xb7, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x65, 0x78, 0x74, 0x10, 0x65, 0x12, 0x09, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x66, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x6f, 0x69, 0x63, 0x65,
	0x10, 0x67, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x10, 0x68, 0x12, 0x08, 0x0a,
	0x04, 0x47, 0x69, 0x66, 0x74, 0x10, 0x69, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x10, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x56,
	0x6f, 0x69, 0x63, 0x65, 0x10, 0x73, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x10, 0x74, 0x12, 0x16, 0x0a, 0x11, 0x52, 0x65, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x10, 0xd1,
	0x5a, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x10, 0xd2, 0x5a, 0x32,
	0xe5, 0x10, 0x0a, 0x01, 0x73, 0x12, 0x6d, 0x0a, 0x09, 0x57, 0x65, 0x62, 0x53, 0x6f, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x17, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a,
	0x22, 0x22, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65, 0x62, 0x73, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x79, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x69,
	0x7a, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x32, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x7f, 0x0a, 0x09, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x20, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x20,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70,
	0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x3a, 0x01, 0x2a, 0x22, 0x23, 0x2f, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x6d, 0x73, 0x67,
	0x12, 0x4c, 0x0a, 0x07, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x12, 0x1e, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x89,
	0x01, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x67, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a,
	0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x12, 0xa0, 0x01, 0x0a, 0x14, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x67, 0x12, 0x2b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61,
	0x73, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x3a, 0x01, 0x2a, 0x22, 0x2f, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x12, 0x8d, 0x01,
	0x0a, 0x0e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x12, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d,
	0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x93, 0x01,
	0x0a, 0x10, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x12, 0x27, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x35, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x12, 0x84, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x65, 0x74, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x84, 0x01, 0x0a, 0x0a, 0x53,
	0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0xa9, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01, 0x2a,
	0x22, 0x2b, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x73,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x95, 0x01,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x9a, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x27, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x30, 0x3a, 0x01, 0x2a, 0x22, 0x2b, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4a, 0x6f,
	0x69, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6a, 0x6f, 0x69, 0x6e,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x84, 0x01, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x4c,
	0x65, 0x61, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a,
	0x01, 0x2a, 0x22, 0x29, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x58, 0x0a,
	0x04, 0x54, 0x65, 0x73, 0x74, 0x12, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27,
	0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65,
	0x73, 0x74, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x42, 0x1f, 0x5a, 0x1d, 0x78, 0x69, 0x6d, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_basemsgtransfer_proto_rawDescOnce sync.Once
	file_basemsgtransfer_proto_rawDescData = file_basemsgtransfer_proto_rawDesc
)

func file_basemsgtransfer_proto_rawDescGZIP() []byte {
	file_basemsgtransfer_proto_rawDescOnce.Do(func() {
		file_basemsgtransfer_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgtransfer_proto_rawDescData)
	})
	return file_basemsgtransfer_proto_rawDescData
}

var file_basemsgtransfer_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_basemsgtransfer_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_basemsgtransfer_proto_goTypes = []interface{}{
	(MsgType)(0),                      // 0: vc.basemsgtransfer.MsgType
	(ChatMsgType)(0),                  // 1: vc.basemsgtransfer.ChatMsgType
	(SessionType)(0),                  // 2: vc.basemsgtransfer.SessionType
	(MsgFromEnum)(0),                  // 3: vc.basemsgtransfer.MsgFromEnum
	(MsgEventType)(0),                 // 4: vc.basemsgtransfer.MsgEventType
	(ContentType)(0),                  // 5: vc.basemsgtransfer.ContentType
	(*Req)(nil),                       // 6: vc.basemsgtransfer.Req
	(*Resp)(nil),                      // 7: vc.basemsgtransfer.Resp
	(*ReqMsgData)(nil),                // 8: vc.basemsgtransfer.ReqMsgData
	(*RespMsgData)(nil),               // 9: vc.basemsgtransfer.RespMsgData
	(*PingData)(nil),                  // 10: vc.basemsgtransfer.PingData
	(*NotifySyncData)(nil),            // 11: vc.basemsgtransfer.NotifySyncData
	(*MsgData)(nil),                   // 12: vc.basemsgtransfer.MsgData
	(*AtInfo)(nil),                    // 13: vc.basemsgtransfer.AtInfo
	(*SendRespData)(nil),              // 14: vc.basemsgtransfer.SendRespData
	(*MaxSeqRespData)(nil),            // 15: vc.basemsgtransfer.MaxSeqRespData
	(*SeqRange)(nil),                  // 16: vc.basemsgtransfer.SeqRange
	(*SyncSeq)(nil),                   // 17: vc.basemsgtransfer.SyncSeq
	(*PullMessageBySeqsReq)(nil),      // 18: vc.basemsgtransfer.PullMessageBySeqsReq
	(*PullMessageBySeqsRsp)(nil),      // 19: vc.basemsgtransfer.PullMessageBySeqsRsp
	(*PullMsgs)(nil),                  // 20: vc.basemsgtransfer.PullMsgs
	(*OfflinePushInfo)(nil),           // 21: vc.basemsgtransfer.OfflinePushInfo
	(*TextElem)(nil),                  // 22: vc.basemsgtransfer.TextElem
	(*ImageElem)(nil),                 // 23: vc.basemsgtransfer.ImageElem
	(*VoiceElem)(nil),                 // 24: vc.basemsgtransfer.VoiceElem
	(*VideoElem)(nil),                 // 25: vc.basemsgtransfer.VideoElem
	(*CustomElem)(nil),                // 26: vc.basemsgtransfer.CustomElem
	(*ChanTokenRespData)(nil),         // 27: vc.basemsgtransfer.ChanTokenRespData
	(*ChanTokenResp)(nil),             // 28: vc.basemsgtransfer.ChanTokenResp
	(*HandleMsgReq)(nil),              // 29: vc.basemsgtransfer.HandleMsgReq
	(*HandleMsgRsp)(nil),              // 30: vc.basemsgtransfer.HandleMsgRsp
	(*UpstreamMsgData)(nil),           // 31: vc.basemsgtransfer.UpstreamMsgData
	(*InterMsgData)(nil),              // 32: vc.basemsgtransfer.InterMsgData
	(*SendMsgReq)(nil),                // 33: vc.basemsgtransfer.SendMsgReq
	(*SendMsgResp)(nil),               // 34: vc.basemsgtransfer.SendMsgResp
	(*GetSessionReq)(nil),             // 35: vc.basemsgtransfer.GetSessionReq
	(*GetSessionResp)(nil),            // 36: vc.basemsgtransfer.GetSessionResp
	(*GetSortedSessionListReq)(nil),   // 37: vc.basemsgtransfer.GetSortedSessionListReq
	(*SessionInfo)(nil),               // 38: vc.basemsgtransfer.SessionInfo
	(*SessionListData)(nil),           // 39: vc.basemsgtransfer.SessionListData
	(*GetSortedSessionListResp)(nil),  // 40: vc.basemsgtransfer.GetSortedSessionListResp
	(*GetSessionInfoReq)(nil),         // 41: vc.basemsgtransfer.GetSessionInfoReq
	(*GetSortedSessionListData)(nil),  // 42: vc.basemsgtransfer.GetSortedSessionListData
	(*GetSessionInfoResp)(nil),        // 43: vc.basemsgtransfer.GetSessionInfoResp
	(*SetSessionReq)(nil),             // 44: vc.basemsgtransfer.SetSessionReq
	(*MulticastRealTimeMsgReq)(nil),   // 45: vc.basemsgtransfer.MulticastRealTimeMsgReq
	(*SubscribeTopicReq)(nil),         // 46: vc.basemsgtransfer.SubscribeTopicReq
	(*SetSessionResp)(nil),            // 47: vc.basemsgtransfer.SetSessionResp
	(*UnSubscribeTopicReq)(nil),       // 48: vc.basemsgtransfer.UnSubscribeTopicReq
	(*UserGroupIdListReq)(nil),        // 49: vc.basemsgtransfer.UserGroupIdListReq
	(*UserGroupIdListResp)(nil),       // 50: vc.basemsgtransfer.UserGroupIdListResp
	(*UserJoinGroupReq)(nil),          // 51: vc.basemsgtransfer.UserJoinGroupReq
	(*UserLeaveGroupReq)(nil),         // 52: vc.basemsgtransfer.UserLeaveGroupReq
	(*SeqCacheData)(nil),              // 53: vc.basemsgtransfer.seqCacheData
	nil,                               // 54: vc.basemsgtransfer.MsgData.OptionsEntry
	nil,                               // 55: vc.basemsgtransfer.MsgData.ExtsEntry
	nil,                               // 56: vc.basemsgtransfer.MaxSeqRespData.MaxSeqsEntry
	nil,                               // 57: vc.basemsgtransfer.PullMessageBySeqsRsp.MsgsEntry
	(*basemsgcallin.MsgAddrData)(nil), // 58: vc.basemsgcallin.MsgAddrData
	(*common.SvcBaseResp)(nil),        // 59: common.SvcBaseResp
	(*common.BaseParam)(nil),          // 60: common.BaseParam
	(*wrapperspb.Int32Value)(nil),     // 61: google.protobuf.Int32Value
	(*wrapperspb.BoolValue)(nil),      // 62: google.protobuf.BoolValue
	(*wrapperspb.StringValue)(nil),    // 63: google.protobuf.StringValue
	(*common.BizBaseReq)(nil),         // 64: common.BizBaseReq
	(*common.TestReq)(nil),            // 65: common.TestReq
	(*common.SvcCommonResp)(nil),      // 66: common.SvcCommonResp
	(*common.TestResp)(nil),           // 67: common.TestResp
}
var file_basemsgtransfer_proto_depIdxs = []int32{
	8,  // 0: vc.basemsgtransfer.Req.payload_decode:type_name -> vc.basemsgtransfer.ReqMsgData
	9,  // 1: vc.basemsgtransfer.Resp.payload_decode:type_name -> vc.basemsgtransfer.RespMsgData
	10, // 2: vc.basemsgtransfer.ReqMsgData.type1Data:type_name -> vc.basemsgtransfer.PingData
	12, // 3: vc.basemsgtransfer.ReqMsgData.type2Data:type_name -> vc.basemsgtransfer.MsgData
	11, // 4: vc.basemsgtransfer.ReqMsgData.type5Data:type_name -> vc.basemsgtransfer.NotifySyncData
	18, // 5: vc.basemsgtransfer.ReqMsgData.type8Data:type_name -> vc.basemsgtransfer.PullMessageBySeqsReq
	14, // 6: vc.basemsgtransfer.RespMsgData.type3Data:type_name -> vc.basemsgtransfer.SendRespData
	12, // 7: vc.basemsgtransfer.RespMsgData.type4Data:type_name -> vc.basemsgtransfer.MsgData
	15, // 8: vc.basemsgtransfer.RespMsgData.type7Data:type_name -> vc.basemsgtransfer.MaxSeqRespData
	19, // 9: vc.basemsgtransfer.RespMsgData.type9Data:type_name -> vc.basemsgtransfer.PullMessageBySeqsRsp
	54, // 10: vc.basemsgtransfer.MsgData.options:type_name -> vc.basemsgtransfer.MsgData.OptionsEntry
	21, // 11: vc.basemsgtransfer.MsgData.offlinePushInfo:type_name -> vc.basemsgtransfer.OfflinePushInfo
	13, // 12: vc.basemsgtransfer.MsgData.atList:type_name -> vc.basemsgtransfer.AtInfo
	55, // 13: vc.basemsgtransfer.MsgData.exts:type_name -> vc.basemsgtransfer.MsgData.ExtsEntry
	56, // 14: vc.basemsgtransfer.MaxSeqRespData.maxSeqs:type_name -> vc.basemsgtransfer.MaxSeqRespData.MaxSeqsEntry
	16, // 15: vc.basemsgtransfer.SyncSeq.seqRanges:type_name -> vc.basemsgtransfer.SeqRange
	16, // 16: vc.basemsgtransfer.PullMessageBySeqsReq.seqRanges:type_name -> vc.basemsgtransfer.SeqRange
	57, // 17: vc.basemsgtransfer.PullMessageBySeqsRsp.msgs:type_name -> vc.basemsgtransfer.PullMessageBySeqsRsp.MsgsEntry
	12, // 18: vc.basemsgtransfer.PullMsgs.msgs:type_name -> vc.basemsgtransfer.MsgData
	58, // 19: vc.basemsgtransfer.ChanTokenRespData.masterAddr:type_name -> vc.basemsgcallin.MsgAddrData
	58, // 20: vc.basemsgtransfer.ChanTokenRespData.slaveAddr:type_name -> vc.basemsgcallin.MsgAddrData
	59, // 21: vc.basemsgtransfer.ChanTokenResp.base:type_name -> common.SvcBaseResp
	27, // 22: vc.basemsgtransfer.ChanTokenResp.data:type_name -> vc.basemsgtransfer.ChanTokenRespData
	59, // 23: vc.basemsgtransfer.HandleMsgRsp.base:type_name -> common.SvcBaseResp
	4,  // 24: vc.basemsgtransfer.UpstreamMsgData.type:type_name -> vc.basemsgtransfer.MsgEventType
	60, // 25: vc.basemsgtransfer.UpstreamMsgData.base:type_name -> common.BaseParam
	60, // 26: vc.basemsgtransfer.InterMsgData.base:type_name -> common.BaseParam
	10, // 27: vc.basemsgtransfer.InterMsgData.type1Data:type_name -> vc.basemsgtransfer.PingData
	12, // 28: vc.basemsgtransfer.InterMsgData.type2Data:type_name -> vc.basemsgtransfer.MsgData
	11, // 29: vc.basemsgtransfer.InterMsgData.type5Data:type_name -> vc.basemsgtransfer.NotifySyncData
	18, // 30: vc.basemsgtransfer.InterMsgData.type8Data:type_name -> vc.basemsgtransfer.PullMessageBySeqsReq
	12, // 31: vc.basemsgtransfer.SendMsgReq.msgs:type_name -> vc.basemsgtransfer.MsgData
	59, // 32: vc.basemsgtransfer.SendMsgResp.base:type_name -> common.SvcBaseResp
	38, // 33: vc.basemsgtransfer.GetSessionReq.upsert:type_name -> vc.basemsgtransfer.SessionInfo
	59, // 34: vc.basemsgtransfer.GetSessionResp.base:type_name -> common.SvcBaseResp
	38, // 35: vc.basemsgtransfer.GetSessionResp.session:type_name -> vc.basemsgtransfer.SessionInfo
	38, // 36: vc.basemsgtransfer.SessionListData.sessions:type_name -> vc.basemsgtransfer.SessionInfo
	59, // 37: vc.basemsgtransfer.GetSortedSessionListResp.base:type_name -> common.SvcBaseResp
	39, // 38: vc.basemsgtransfer.GetSortedSessionListResp.data:type_name -> vc.basemsgtransfer.SessionListData
	38, // 39: vc.basemsgtransfer.GetSortedSessionListData.sessions:type_name -> vc.basemsgtransfer.SessionInfo
	59, // 40: vc.basemsgtransfer.GetSessionInfoResp.base:type_name -> common.SvcBaseResp
	39, // 41: vc.basemsgtransfer.GetSessionInfoResp.data:type_name -> vc.basemsgtransfer.SessionListData
	61, // 42: vc.basemsgtransfer.SetSessionReq.recvMsgOpt:type_name -> google.protobuf.Int32Value
	62, // 43: vc.basemsgtransfer.SetSessionReq.isPinned:type_name -> google.protobuf.BoolValue
	63, // 44: vc.basemsgtransfer.SetSessionReq.remark:type_name -> google.protobuf.StringValue
	12, // 45: vc.basemsgtransfer.MulticastRealTimeMsgReq.msgs:type_name -> vc.basemsgtransfer.MsgData
	59, // 46: vc.basemsgtransfer.SetSessionResp.base:type_name -> common.SvcBaseResp
	59, // 47: vc.basemsgtransfer.UserGroupIdListResp.base:type_name -> common.SvcBaseResp
	20, // 48: vc.basemsgtransfer.PullMessageBySeqsRsp.MsgsEntry.value:type_name -> vc.basemsgtransfer.PullMsgs
	6,  // 49: vc.basemsgtransfer.s.WebSocket:input_type -> vc.basemsgtransfer.Req
	64, // 50: vc.basemsgtransfer.s.GetChanToken:input_type -> common.BizBaseReq
	29, // 51: vc.basemsgtransfer.s.HandleMsg:input_type -> vc.basemsgtransfer.HandleMsgReq
	33, // 52: vc.basemsgtransfer.s.SendMsg:input_type -> vc.basemsgtransfer.SendMsgReq
	33, // 53: vc.basemsgtransfer.s.SendRealTimeMsg:input_type -> vc.basemsgtransfer.SendMsgReq
	45, // 54: vc.basemsgtransfer.s.MulticastRealTimeMsg:input_type -> vc.basemsgtransfer.MulticastRealTimeMsgReq
	46, // 55: vc.basemsgtransfer.s.SubscribeTopic:input_type -> vc.basemsgtransfer.SubscribeTopicReq
	48, // 56: vc.basemsgtransfer.s.UnSubscribeTopic:input_type -> vc.basemsgtransfer.UnSubscribeTopicReq
	35, // 57: vc.basemsgtransfer.s.GetSession:input_type -> vc.basemsgtransfer.GetSessionReq
	44, // 58: vc.basemsgtransfer.s.SetSession:input_type -> vc.basemsgtransfer.SetSessionReq
	37, // 59: vc.basemsgtransfer.s.GetSortedSessionList:input_type -> vc.basemsgtransfer.GetSortedSessionListReq
	41, // 60: vc.basemsgtransfer.s.GetSessionInfo:input_type -> vc.basemsgtransfer.GetSessionInfoReq
	49, // 61: vc.basemsgtransfer.s.UserGroupIdList:input_type -> vc.basemsgtransfer.UserGroupIdListReq
	51, // 62: vc.basemsgtransfer.s.UserJoinGroup:input_type -> vc.basemsgtransfer.UserJoinGroupReq
	52, // 63: vc.basemsgtransfer.s.UserLeaveGroup:input_type -> vc.basemsgtransfer.UserLeaveGroupReq
	65, // 64: vc.basemsgtransfer.s.Test:input_type -> common.TestReq
	7,  // 65: vc.basemsgtransfer.s.WebSocket:output_type -> vc.basemsgtransfer.Resp
	28, // 66: vc.basemsgtransfer.s.GetChanToken:output_type -> vc.basemsgtransfer.ChanTokenResp
	30, // 67: vc.basemsgtransfer.s.HandleMsg:output_type -> vc.basemsgtransfer.HandleMsgRsp
	34, // 68: vc.basemsgtransfer.s.SendMsg:output_type -> vc.basemsgtransfer.SendMsgResp
	34, // 69: vc.basemsgtransfer.s.SendRealTimeMsg:output_type -> vc.basemsgtransfer.SendMsgResp
	34, // 70: vc.basemsgtransfer.s.MulticastRealTimeMsg:output_type -> vc.basemsgtransfer.SendMsgResp
	34, // 71: vc.basemsgtransfer.s.SubscribeTopic:output_type -> vc.basemsgtransfer.SendMsgResp
	34, // 72: vc.basemsgtransfer.s.UnSubscribeTopic:output_type -> vc.basemsgtransfer.SendMsgResp
	36, // 73: vc.basemsgtransfer.s.GetSession:output_type -> vc.basemsgtransfer.GetSessionResp
	47, // 74: vc.basemsgtransfer.s.SetSession:output_type -> vc.basemsgtransfer.SetSessionResp
	40, // 75: vc.basemsgtransfer.s.GetSortedSessionList:output_type -> vc.basemsgtransfer.GetSortedSessionListResp
	43, // 76: vc.basemsgtransfer.s.GetSessionInfo:output_type -> vc.basemsgtransfer.GetSessionInfoResp
	50, // 77: vc.basemsgtransfer.s.UserGroupIdList:output_type -> vc.basemsgtransfer.UserGroupIdListResp
	66, // 78: vc.basemsgtransfer.s.UserJoinGroup:output_type -> common.SvcCommonResp
	66, // 79: vc.basemsgtransfer.s.UserLeaveGroup:output_type -> common.SvcCommonResp
	67, // 80: vc.basemsgtransfer.s.Test:output_type -> common.TestResp
	65, // [65:81] is the sub-list for method output_type
	49, // [49:65] is the sub-list for method input_type
	49, // [49:49] is the sub-list for extension type_name
	49, // [49:49] is the sub-list for extension extendee
	0,  // [0:49] is the sub-list for field type_name
}

func init() { file_basemsgtransfer_proto_init() }
func file_basemsgtransfer_proto_init() {
	if File_basemsgtransfer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgtransfer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqMsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RespMsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifySyncData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaxSeqRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSeq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullMessageBySeqsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullMessageBySeqsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullMsgs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflinePushInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextElem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageElem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoiceElem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoElem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomElem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChanTokenRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChanTokenResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpstreamMsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterMsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSortedSessionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSortedSessionListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSortedSessionListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSessionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MulticastRealTimeMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeTopicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSessionResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnSubscribeTopicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserGroupIdListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserGroupIdListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserJoinGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLeaveGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqCacheData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgtransfer_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgtransfer_proto_goTypes,
		DependencyIndexes: file_basemsgtransfer_proto_depIdxs,
		EnumInfos:         file_basemsgtransfer_proto_enumTypes,
		MessageInfos:      file_basemsgtransfer_proto_msgTypes,
	}.Build()
	File_basemsgtransfer_proto = out.File
	file_basemsgtransfer_proto_rawDesc = nil
	file_basemsgtransfer_proto_goTypes = nil
	file_basemsgtransfer_proto_depIdxs = nil
}
