    set -v
    echo $1
    echo $(dirname $1)

main() {
    CURRENT_DIR=$(pwd)
    echo "CURRENT_DIR=${CURRENT_DIR}"
    mkdir -p ".dependencies"  && cd .dependencies
    if [ ! -d "third-party" ]; then
      echo "Cloning third-party repository..." && \
      git clone https://maoyifeng:<EMAIL>/vcproject/third-party.git third-party || { echo "Failed to clone third-party repository"; exit 1; }; \
    else 
      echo "Third-party repository already exists"; \
    fi
    cd ${CURRENT_DIR}
    case $1 in
        clean)
            for i in $(ls -F | grep '/$' | sed 's#/##g'); do
                echo $i
                cd $i
                rm -rf *.pb.go *.pb *.json *.pb.gw.go
                cd ..
            done
            ;;

        all)
            for i in $(ls -F | grep '/$' | grep -v admin | sed 's#/##g'); do
                echo $i
                cd $i
                rm -rf *.pb.go *.pb
                for j in $(ls *.proto); do
                    generateFile "$j" "${j%.*}"
                done
                cd ..
            done
            ;;

        *)
            cd $1
            for i in $(ls *.proto); do
                generateFile "$i" "${i%.*}"
            done
            cd ..
            ;;

    esac
}
  
  generateFile() {
    protoc -I . -I ../ -I ${CURRENT_DIR}/.dependencies/third-party/protobuf/googleapis  \
            --go_out . --go_opt paths=source_relative  \
            --go-grpc_out . --go-grpc_opt=require_unimplemented_servers=false --go-grpc_opt paths=source_relative \
            --include_imports  --include_source_info --descriptor_set_out $2.pb  \
            $1

      protoc -I . -I ../ -I ${CURRENT_DIR}/.dependencies/third-party/protobuf/googleapis \
        --grpc-gateway_out .  \
        --grpc-gateway_opt logtostderr=true \
        --grpc-gateway_opt paths=source_relative \
        --grpc-gateway_opt generate_unbound_methods=true \
        $1
      protoc -I . -I ../ -I ${CURRENT_DIR}/.dependencies/third-party/protobuf/googleapis  \
        --openapiv2_out ./ \
        --openapiv2_opt logtostderr=true \
        --openapiv2_opt json_names_for_fields=true \
          $1
  }


  main "$@"