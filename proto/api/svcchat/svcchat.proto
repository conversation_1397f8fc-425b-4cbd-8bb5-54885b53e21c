syntax = "proto3";

package  vc.svcchat;
option go_package = "xim/proto/api/svcchat";

import "common/common.proto";
import "basemsgtransfer/basemsgtransfer.proto";

message MsgHandleReq {
  basemsgtransfer.MsgData msg = 1;
  common.BaseParam base = 2;
}

message MsgHandleResp {
  common.SvcBaseResp base = 1;
  map<string, string> exts = 2;
}

service s {
  rpc SetUserOffline(common.SvcUseridReq) returns (common.SvcCommonResp) {}
  rpc MsgHandle(MsgHandleReq) returns  (MsgHandleResp) {}
}
