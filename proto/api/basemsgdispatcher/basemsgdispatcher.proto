syntax = "proto3";

package   vc.basemsgdispatcher;
option go_package = "xim/proto/api/basemsgdispatcher;basemsgdispatcher";

import "google/api/annotations.proto";

enum PublishTargetType {
    // 占位无效
    NoneType = 0;
    // 目标为平台用户
    UserType = 1;
    // 目标为平台topic下的所有用户
    TopicType = 2;
    // 目标为平台下所有用户
    AllType = 3;
    // 目标为平台topic下，符合限制条件的用户
    TopicLimitType = 4;
    // 目标为平台下，符合限制条件的用户
    AllLimitType = 5;
}
enum PublishTargetClientLimitType {
    // 占位无效
    NilType = 0;
    // 放行
    PassType = 1;
    // 屏蔽
    BlockType = 2;
}

message TestReq{
}
message TestRsp{
    //结果码
    int32 result =1;
    //结果说明
    string message = 2;
}

message ClientLimit {
    //客户端限制类型，1为放行，2为屏蔽
    PublishTargetClientLimitType limitType = 1;
    //客户端标识id
    uint32 clientId = 2;
    //客户端版本比较类型，分{>, >=, <, <=, =}，空字符串或其他字符串的默认为不区分版本
    string versionCompare = 3;
    //客户端版本比较值
    string clientVersion = 4;
}
message PublishMsgReq {
    //要发布的内容
    string payload = 1;
    //发布的目标类型，1为平台用户，2为平台topic下的用户，3为平台所有用户，4为平台topic下符合限制条件的用户，5为平台下符合限制条件的用户
    PublishTargetType type = 2;
    //平台id（用于所有目标类型）
    uint64 platformId = 3;
    //用户id（用于目标类型为1）
    string userId = 4;
    //用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备（用于目标类型为1）
    string deviceId = 5;
    //排除，不发布的用户设备id名单（用于目标类型为1）
    repeated string excludeDeviceIds = 10;
    //发布的topic（用于目标类型为2，4）
    string topic = 6;
    //特定需要发布的用户id名单（用于目标类型为4，5）
    repeated string includeUserIds = 7;
    //排除，不发布的用户id名单（用于目标类型为4，5）
    repeated string excludeUserIds = 8;
    //不在includeUserIds内的长连接，有一定概率可以发布（用于目标类型为4，5）
    uint32 broadcastRate = 9;
    //需要限制的客户端配置列表（用于目标类型为1，4，5）
    repeated ClientLimit limitList = 11;
}
message PublishMsgRsp {
    //结果码
    int32 result = 1;
    //结果说明
    string message = 2;
}

service s {
    //服务调试接口，主要是测试服务连通性
    rpc Test (TestReq) returns (TestRsp) {
        option (google.api.http) = {
            post: "/xllivemp.basemsgdispatcher.s/v1/Test.json"
            body: "*"
       };
    }

    rpc PublishMsg (PublishMsgReq) returns (PublishMsgRsp) {}
}
