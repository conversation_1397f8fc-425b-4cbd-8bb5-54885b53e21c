// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: basemsgdispatcher.proto

package basemsgdispatcher

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublishTargetType int32

const (
	// 占位无效
	PublishTargetType_NoneType PublishTargetType = 0
	// 目标为平台用户
	PublishTargetType_UserType PublishTargetType = 1
	// 目标为平台topic下的所有用户
	PublishTargetType_TopicType PublishTargetType = 2
	// 目标为平台下所有用户
	PublishTargetType_AllType PublishTargetType = 3
	// 目标为平台topic下，符合限制条件的用户
	PublishTargetType_TopicLimitType PublishTargetType = 4
	// 目标为平台下，符合限制条件的用户
	PublishTargetType_AllLimitType PublishTargetType = 5
)

// Enum value maps for PublishTargetType.
var (
	PublishTargetType_name = map[int32]string{
		0: "NoneType",
		1: "UserType",
		2: "TopicType",
		3: "AllType",
		4: "TopicLimitType",
		5: "AllLimitType",
	}
	PublishTargetType_value = map[string]int32{
		"NoneType":       0,
		"UserType":       1,
		"TopicType":      2,
		"AllType":        3,
		"TopicLimitType": 4,
		"AllLimitType":   5,
	}
)

func (x PublishTargetType) Enum() *PublishTargetType {
	p := new(PublishTargetType)
	*p = x
	return p
}

func (x PublishTargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PublishTargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgdispatcher_proto_enumTypes[0].Descriptor()
}

func (PublishTargetType) Type() protoreflect.EnumType {
	return &file_basemsgdispatcher_proto_enumTypes[0]
}

func (x PublishTargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PublishTargetType.Descriptor instead.
func (PublishTargetType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{0}
}

type PublishTargetClientLimitType int32

const (
	// 占位无效
	PublishTargetClientLimitType_NilType PublishTargetClientLimitType = 0
	// 放行
	PublishTargetClientLimitType_PassType PublishTargetClientLimitType = 1
	// 屏蔽
	PublishTargetClientLimitType_BlockType PublishTargetClientLimitType = 2
)

// Enum value maps for PublishTargetClientLimitType.
var (
	PublishTargetClientLimitType_name = map[int32]string{
		0: "NilType",
		1: "PassType",
		2: "BlockType",
	}
	PublishTargetClientLimitType_value = map[string]int32{
		"NilType":   0,
		"PassType":  1,
		"BlockType": 2,
	}
)

func (x PublishTargetClientLimitType) Enum() *PublishTargetClientLimitType {
	p := new(PublishTargetClientLimitType)
	*p = x
	return p
}

func (x PublishTargetClientLimitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PublishTargetClientLimitType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgdispatcher_proto_enumTypes[1].Descriptor()
}

func (PublishTargetClientLimitType) Type() protoreflect.EnumType {
	return &file_basemsgdispatcher_proto_enumTypes[1]
}

func (x PublishTargetClientLimitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PublishTargetClientLimitType.Descriptor instead.
func (PublishTargetClientLimitType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{1}
}

type TestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestReq) Reset() {
	*x = TestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgdispatcher_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReq) ProtoMessage() {}

func (x *TestReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgdispatcher_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReq.ProtoReflect.Descriptor instead.
func (*TestReq) Descriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{0}
}

type TestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *TestRsp) Reset() {
	*x = TestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgdispatcher_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRsp) ProtoMessage() {}

func (x *TestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgdispatcher_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRsp.ProtoReflect.Descriptor instead.
func (*TestRsp) Descriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{1}
}

func (x *TestRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *TestRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ClientLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端限制类型，1为放行，2为屏蔽
	LimitType PublishTargetClientLimitType `protobuf:"varint,1,opt,name=limitType,proto3,enum=vc.basemsgdispatcher.PublishTargetClientLimitType" json:"limitType,omitempty"`
	// 客户端标识id
	ClientId uint32 `protobuf:"varint,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// 客户端版本比较类型，分{>, >=, <, <=, =}，空字符串或其他字符串的默认为不区分版本
	VersionCompare string `protobuf:"bytes,3,opt,name=versionCompare,proto3" json:"versionCompare,omitempty"`
	// 客户端版本比较值
	ClientVersion string `protobuf:"bytes,4,opt,name=clientVersion,proto3" json:"clientVersion,omitempty"`
}

func (x *ClientLimit) Reset() {
	*x = ClientLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgdispatcher_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientLimit) ProtoMessage() {}

func (x *ClientLimit) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgdispatcher_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientLimit.ProtoReflect.Descriptor instead.
func (*ClientLimit) Descriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{2}
}

func (x *ClientLimit) GetLimitType() PublishTargetClientLimitType {
	if x != nil {
		return x.LimitType
	}
	return PublishTargetClientLimitType_NilType
}

func (x *ClientLimit) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ClientLimit) GetVersionCompare() string {
	if x != nil {
		return x.VersionCompare
	}
	return ""
}

func (x *ClientLimit) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

type PublishMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 要发布的内容
	Payload string `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
	// 发布的目标类型，1为平台用户，2为平台topic下的用户，3为平台所有用户，4为平台topic下符合限制条件的用户，5为平台下符合限制条件的用户
	Type PublishTargetType `protobuf:"varint,2,opt,name=type,proto3,enum=vc.basemsgdispatcher.PublishTargetType" json:"type,omitempty"`
	// 平台id（用于所有目标类型）
	PlatformId uint64 `protobuf:"varint,3,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 用户id（用于目标类型为1）
	UserId string `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备（用于目标类型为1）
	DeviceId string `protobuf:"bytes,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 排除，不发布的用户设备id名单（用于目标类型为1）
	ExcludeDeviceIds []string `protobuf:"bytes,10,rep,name=excludeDeviceIds,proto3" json:"excludeDeviceIds,omitempty"`
	// 发布的topic（用于目标类型为2，4）
	Topic string `protobuf:"bytes,6,opt,name=topic,proto3" json:"topic,omitempty"`
	// 特定需要发布的用户id名单（用于目标类型为4，5）
	IncludeUserIds []string `protobuf:"bytes,7,rep,name=includeUserIds,proto3" json:"includeUserIds,omitempty"`
	// 排除，不发布的用户id名单（用于目标类型为4，5）
	ExcludeUserIds []string `protobuf:"bytes,8,rep,name=excludeUserIds,proto3" json:"excludeUserIds,omitempty"`
	// 不在includeUserIds内的长连接，有一定概率可以发布（用于目标类型为4，5）
	BroadcastRate uint32 `protobuf:"varint,9,opt,name=broadcastRate,proto3" json:"broadcastRate,omitempty"`
	// 需要限制的客户端配置列表（用于目标类型为1，4，5）
	LimitList []*ClientLimit `protobuf:"bytes,11,rep,name=limitList,proto3" json:"limitList,omitempty"`
}

func (x *PublishMsgReq) Reset() {
	*x = PublishMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgdispatcher_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishMsgReq) ProtoMessage() {}

func (x *PublishMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgdispatcher_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishMsgReq.ProtoReflect.Descriptor instead.
func (*PublishMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{3}
}

func (x *PublishMsgReq) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *PublishMsgReq) GetType() PublishTargetType {
	if x != nil {
		return x.Type
	}
	return PublishTargetType_NoneType
}

func (x *PublishMsgReq) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *PublishMsgReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PublishMsgReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PublishMsgReq) GetExcludeDeviceIds() []string {
	if x != nil {
		return x.ExcludeDeviceIds
	}
	return nil
}

func (x *PublishMsgReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *PublishMsgReq) GetIncludeUserIds() []string {
	if x != nil {
		return x.IncludeUserIds
	}
	return nil
}

func (x *PublishMsgReq) GetExcludeUserIds() []string {
	if x != nil {
		return x.ExcludeUserIds
	}
	return nil
}

func (x *PublishMsgReq) GetBroadcastRate() uint32 {
	if x != nil {
		return x.BroadcastRate
	}
	return 0
}

func (x *PublishMsgReq) GetLimitList() []*ClientLimit {
	if x != nil {
		return x.LimitList
	}
	return nil
}

type PublishMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PublishMsgRsp) Reset() {
	*x = PublishMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgdispatcher_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishMsgRsp) ProtoMessage() {}

func (x *PublishMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgdispatcher_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishMsgRsp.ProtoReflect.Descriptor instead.
func (*PublishMsgRsp) Descriptor() ([]byte, []int) {
	return file_basemsgdispatcher_proto_rawDescGZIP(), []int{4}
}

func (x *PublishMsgRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *PublishMsgRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_basemsgdispatcher_proto protoreflect.FileDescriptor

var file_basemsgdispatcher_proto_rawDesc = []byte{
	0x0a, 0x17, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a,
	0x07, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x3b, 0x0a, 0x07, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x0b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x50, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xb3, 0x03, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x67,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x3b, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x12, 0x24, 0x0a, 0x0d, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x09, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x41, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x71, 0x0a, 0x11, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x41,
	0x6c, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x10, 0x05, 0x2a, 0x48, 0x0a,
	0x1c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x4e, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x61,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x10, 0x02, 0x32, 0xda, 0x01, 0x0a, 0x01, 0x73, 0x12, 0x7b, 0x0a,
	0x04, 0x54, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a,
	0x2f, 0x78, 0x6c, 0x6c, 0x69, 0x76, 0x65, 0x6d, 0x70, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x54, 0x65, 0x73, 0x74, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x0a, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x12, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x42, 0x33, 0x5a, 0x31, 0x78, 0x69, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x3b, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_basemsgdispatcher_proto_rawDescOnce sync.Once
	file_basemsgdispatcher_proto_rawDescData = file_basemsgdispatcher_proto_rawDesc
)

func file_basemsgdispatcher_proto_rawDescGZIP() []byte {
	file_basemsgdispatcher_proto_rawDescOnce.Do(func() {
		file_basemsgdispatcher_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgdispatcher_proto_rawDescData)
	})
	return file_basemsgdispatcher_proto_rawDescData
}

var file_basemsgdispatcher_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_basemsgdispatcher_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_basemsgdispatcher_proto_goTypes = []interface{}{
	(PublishTargetType)(0),            // 0: vc.basemsgdispatcher.PublishTargetType
	(PublishTargetClientLimitType)(0), // 1: vc.basemsgdispatcher.PublishTargetClientLimitType
	(*TestReq)(nil),                   // 2: vc.basemsgdispatcher.TestReq
	(*TestRsp)(nil),                   // 3: vc.basemsgdispatcher.TestRsp
	(*ClientLimit)(nil),               // 4: vc.basemsgdispatcher.ClientLimit
	(*PublishMsgReq)(nil),             // 5: vc.basemsgdispatcher.PublishMsgReq
	(*PublishMsgRsp)(nil),             // 6: vc.basemsgdispatcher.PublishMsgRsp
}
var file_basemsgdispatcher_proto_depIdxs = []int32{
	1, // 0: vc.basemsgdispatcher.ClientLimit.limitType:type_name -> vc.basemsgdispatcher.PublishTargetClientLimitType
	0, // 1: vc.basemsgdispatcher.PublishMsgReq.type:type_name -> vc.basemsgdispatcher.PublishTargetType
	4, // 2: vc.basemsgdispatcher.PublishMsgReq.limitList:type_name -> vc.basemsgdispatcher.ClientLimit
	2, // 3: vc.basemsgdispatcher.s.Test:input_type -> vc.basemsgdispatcher.TestReq
	5, // 4: vc.basemsgdispatcher.s.PublishMsg:input_type -> vc.basemsgdispatcher.PublishMsgReq
	3, // 5: vc.basemsgdispatcher.s.Test:output_type -> vc.basemsgdispatcher.TestRsp
	6, // 6: vc.basemsgdispatcher.s.PublishMsg:output_type -> vc.basemsgdispatcher.PublishMsgRsp
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_basemsgdispatcher_proto_init() }
func file_basemsgdispatcher_proto_init() {
	if File_basemsgdispatcher_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgdispatcher_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgdispatcher_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgdispatcher_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgdispatcher_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgdispatcher_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgdispatcher_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgdispatcher_proto_goTypes,
		DependencyIndexes: file_basemsgdispatcher_proto_depIdxs,
		EnumInfos:         file_basemsgdispatcher_proto_enumTypes,
		MessageInfos:      file_basemsgdispatcher_proto_msgTypes,
	}.Build()
	File_basemsgdispatcher_proto = out.File
	file_basemsgdispatcher_proto_rawDesc = nil
	file_basemsgdispatcher_proto_goTypes = nil
	file_basemsgdispatcher_proto_depIdxs = nil
}
