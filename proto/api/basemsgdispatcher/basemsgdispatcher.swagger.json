{"swagger": "2.0", "info": {"title": "basemsgdispatcher.proto", "version": "version not set"}, "tags": [{"name": "s"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xllivemp.basemsgdispatcher.s/v1/Test.json": {"post": {"summary": "服务调试接口，主要是测试服务连通性", "operationId": "s_Test", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgdispatcherTestRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgdispatcherTestReq"}}], "tags": ["s"]}}}, "definitions": {"basemsgdispatcherClientLimit": {"type": "object", "properties": {"limitType": {"$ref": "#/definitions/basemsgdispatcherPublishTargetClientLimitType", "title": "客户端限制类型，1为放行，2为屏蔽"}, "clientId": {"type": "integer", "format": "int64", "title": "客户端标识id"}, "versionCompare": {"type": "string", "title": "客户端版本比较类型，分{>, >=, <, <=, =}，空字符串或其他字符串的默认为不区分版本"}, "clientVersion": {"type": "string", "title": "客户端版本比较值"}}}, "basemsgdispatcherPublishMsgRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}}}, "basemsgdispatcherPublishTargetClientLimitType": {"type": "string", "enum": ["NilType", "PassType", "BlockType"], "default": "NilType", "title": "- NilType: 占位无效\n - PassType: 放行\n - BlockType: 屏蔽"}, "basemsgdispatcherPublishTargetType": {"type": "string", "enum": ["NoneType", "UserType", "TopicType", "AllType", "TopicLimitType", "AllLimitType"], "default": "NoneType", "title": "- NoneType: 占位无效\n - UserType: 目标为平台用户\n - TopicType: 目标为平台topic下的所有用户\n - AllType: 目标为平台下所有用户\n - TopicLimitType: 目标为平台topic下，符合限制条件的用户\n - AllLimitType: 目标为平台下，符合限制条件的用户"}, "basemsgdispatcherTestReq": {"type": "object"}, "basemsgdispatcherTestRsp": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32", "title": "结果码"}, "message": {"type": "string", "title": "结果说明"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}