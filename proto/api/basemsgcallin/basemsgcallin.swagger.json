{"swagger": "2.0", "info": {"title": "basemsgcallin.proto", "version": "version not set"}, "tags": [{"name": "s"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/vc.basemsgcallin.s/v1/GetMsgConnToken.json": {"post": {"summary": "平台业务获取长连接token和连接域名信息接口", "operationId": "s_GetMsgConnToken", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgcallinGetMsgConnTokenRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgcallinGetMsgConnTokenReq"}}], "tags": ["s"]}}, "/vc.basemsgcallin.s/v1/PublishMsg.json": {"post": {"summary": "平台业务下发消息接口", "operationId": "s_PublishMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgcallinPublishMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgcallinPublishMsgReq"}}], "tags": ["s"]}}, "/vc.basemsgcallin.s/v1/SubscribeTopic.json": {"post": {"summary": "平台业务用户订阅topic接口", "operationId": "s_SubscribeTopic", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgcallinSubscribeTopicRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgcallinSubscribeTopicReq"}}], "tags": ["s"]}}, "/vc.basemsgcallin.s/v1/Test.json": {"post": {"summary": "服务调试接口，主要是测试服务连通性", "operationId": "s_Test", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/vcbasemsgcallinTestRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/vcbasemsgcallinTestReq"}}], "tags": ["s"]}}, "/vc.basemsgcallin.s/v1/UnSubscribeTopic.json": {"post": {"summary": "平台业务用户取消订阅topic接口", "operationId": "s_UnSubscribeTopic", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgcallinUnSubscribeTopicRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgcallinUnSubscribeTopicReq"}}], "tags": ["s"]}}, "/vc.basemsgcallin.s/v1/UsersTopics.json": {"post": {"summary": "平台业务批量查询用户订阅的topic信息接口", "operationId": "s_UsersTopics", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/basemsgcallinUsersTopicsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/basemsgcallinUsersTopicsReq"}}], "tags": ["s"]}}}, "definitions": {"basemsgcallinGetMsgConnTokenReq": {"type": "object", "properties": {"platformId": {"type": "string", "title": "平台id"}, "timeStamp": {"type": "string", "title": "签名unix时间戳"}, "nonceStr": {"type": "string", "title": "签名随机串"}, "sign": {"type": "string", "title": "签名串，用于接口认证"}, "userId": {"type": "string", "title": "用户id"}, "deviceId": {"type": "string", "title": "用户设备id"}, "extendData": {"type": "string", "title": "扩展数据的json包，提供用户设备的一些补充信息，可以补充clientId，clientVersion，scheme等字段，比如{\"clientVersion\":\"1.1.0\"}"}}}, "basemsgcallinGetMsgConnTokenRsp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}, "data": {"$ref": "#/definitions/basemsgcallinMsgTokenData", "title": "长连接token数据，包含长连接地址端口"}}}, "basemsgcallinMsgAddrData": {"type": "object", "properties": {"host": {"type": "string", "title": "长连接域名或ip"}, "port": {"type": "integer", "format": "int32", "title": "长连接端口"}}}, "basemsgcallinMsgTokenData": {"type": "object", "properties": {"token": {"type": "string", "title": "长连接token"}, "masterAddr": {"$ref": "#/definitions/basemsgcallinMsgAddrData", "title": "长连接主地址"}, "slaveAddr": {"$ref": "#/definitions/basemsgcallinMsgAddrData", "title": "长连接备用地址"}, "generalTopic": {"type": "string", "title": "客户端发上行消息使用的通用topic"}, "kickTopic": {"type": "string", "title": "客户端长连接被踢时下发的消息topic"}}}, "basemsgcallinPublishMsgReq": {"type": "object", "properties": {"platformId": {"type": "string", "title": "平台id"}, "timeStamp": {"type": "string", "title": "签名unix时间戳"}, "nonceStr": {"type": "string", "title": "签名随机串"}, "sign": {"type": "string", "title": "签名串，用于接口认证"}, "payload": {"type": "string", "title": "发布的具体消息"}, "type": {"type": "string", "title": "发布目标类型，1为平台用户，2为平台topic下的用户，4为平台topic下符合限制条件的用户"}, "targetJson": {"type": "string", "title": "发布的目标信息，是下面PublishTargetInfo数据的json包形式，使用时解析出来"}}}, "basemsgcallinPublishMsgRsp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}}}, "basemsgcallinSubscribeTopicReq": {"type": "object", "properties": {"platformId": {"type": "string", "title": "平台id"}, "timeStamp": {"type": "string", "title": "签名unix时间戳"}, "nonceStr": {"type": "string", "title": "签名随机串"}, "sign": {"type": "string", "title": "签名串，用于接口认证"}, "userId": {"type": "string", "title": "用户id"}, "deviceId": {"type": "string", "title": "用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备"}, "topics": {"type": "string", "title": "需要订阅的topic列表，是topic数组的json包，比如'[\"t1\",\"t2\"]'"}}}, "basemsgcallinSubscribeTopicRsp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}}}, "basemsgcallinUnSubscribeTopicReq": {"type": "object", "properties": {"platformId": {"type": "string", "title": "平台id"}, "timeStamp": {"type": "string", "title": "签名unix时间戳"}, "nonceStr": {"type": "string", "title": "签名随机串"}, "sign": {"type": "string", "title": "签名串，用于接口认证"}, "userId": {"type": "string", "title": "用户id"}, "deviceId": {"type": "string", "title": "用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备"}, "topics": {"type": "string", "title": "需要取消订阅的topic列表，是topic数组的json包，比如'[\"t1\",\"t2\"]'"}}}, "basemsgcallinUnSubscribeTopicRsp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}}}, "basemsgcallinUsersTopicsData": {"type": "object", "properties": {"userTopics": {"type": "object", "additionalProperties": {"$ref": "#/definitions/basemsgtopicUserTopicsData"}, "title": "用户id和对应的订阅信息"}}}, "basemsgcallinUsersTopicsReq": {"type": "object", "properties": {"platformId": {"type": "string", "title": "平台id"}, "timeStamp": {"type": "string", "title": "签名unix时间戳"}, "nonceStr": {"type": "string", "title": "签名随机串"}, "sign": {"type": "string", "title": "签名串，用于接口认证"}, "userDevices": {"type": "string", "title": "查询的用户id和对应设备，设备id为空则查询用户的所有设备，是UserDevice结构数组json包"}}}, "basemsgcallinUsersTopicsRsp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}, "data": {"$ref": "#/definitions/basemsgcallinUsersTopicsData", "title": "查询的用户topic数据"}}}, "basemsgtopicUserDeviceTopicsData": {"type": "object", "properties": {"platformId": {"type": "string", "format": "uint64", "title": "平台id"}, "userId": {"type": "string", "title": "用户id"}, "deviceId": {"type": "string", "title": "用户设备id，不同设备可能有不同长连接"}, "topics": {"type": "array", "items": {"type": "string"}, "title": "订阅的topic列表"}}}, "basemsgtopicUserTopicsData": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/basemsgtopicUserDeviceTopicsData"}, "title": "用户和设备的订阅topic列表"}}}, "commonSvcBaseResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}, "msg": {"type": "string", "title": "错误信息"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\nExample 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\nExample 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "vcbasemsgcallinTestReq": {"type": "object"}, "vcbasemsgcallinTestRsp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}}}}}