syntax = "proto3";

package   vc.basemsgcallin;
option go_package = "xim/proto/api/basemsgcallin;basemsgcallin";

import "google/api/annotations.proto";
import "basemsgtopic/basemsgtopic.proto";
import "common/common.proto";

message TestReq{
}
message TestRsp{
  common.SvcBaseResp base = 1;
}

message GetMsgConnTokenReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;
  //用户id
  string userId = 5;
  //用户设备id
  string deviceId = 6;
  //扩展数据的json包，提供用户设备的一些补充信息，可以补充clientId，clientVersion，scheme等字段，比如{"clientVersion":"1.1.0"}
  string extendData = 7;
}
message GetMsgConnTokenRsp {
  common.SvcBaseResp base = 1;
  //长连接token数据，包含长连接地址端口
  MsgTokenData data = 2;
}
message MsgTokenData {
  //长连接token
  string token = 1;
  //长连接主地址
  MsgAddrData masterAddr = 2;
  //长连接备用地址
  MsgAddrData slaveAddr = 3;
  //客户端发上行消息使用的通用topic
  string generalTopic = 4;
  //客户端长连接被踢时下发的消息topic
  string kickTopic = 5;
}
message MsgAddrData {
  //长连接域名或ip
  string host = 1;
  //长连接端口
  int32 port = 2;
}

message PublishMsgReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;
  // 发布的具体消息
  string payload = 5;
  // 发布目标类型，1为平台用户，2为平台topic下的用户，4为平台topic下符合限制条件的用户
  string type = 6;
  // 发布的目标信息，是下面PublishTargetInfo数据的json包形式，使用时解析出来
  string targetJson = 7;
}
message PublishTargetInfo {
  //用户id（用于目标类型为1）
  string userId = 1;
  //用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备（用于目标类型为1）
  string deviceId = 2;
  //排除，不发布的用户设备id名单（用于目标类型为1）
  repeated string excludeDeviceIds = 3;
  //发布的topic（用于目标类型为2，4）
  string topic = 4;
  //特定需要发布的用户id名单（用于目标类型为4）
  repeated string includeUserIds = 5;
  //排除，不发布的用户id名单（用于目标类型为4）
  repeated string excludeUserIds = 6;
  //不在includeUserIds内的长连接，有一定概率可以发布（用于目标类型为4）
  uint32 broadcastRate = 7;
  //需要限制的客户端配置列表（用于目标类型为1，4）
  repeated ClientLimit limitList = 8;
}
message ClientLimit {
  //客户端限制类型，1为放行，2为屏蔽
  int32 limitType = 1;
  //客户端标识id
  uint32 clientId = 2;
  //客户端版本比较类型，分{>, >=, <, <=, =}，空字符串或其他字符串的默认为不区分版本
  string versionCompare = 3;
  //客户端版本比较值
  string clientVersion = 4;
}
message PublishMsgRsp {
   common.SvcBaseResp base = 1;
}

message SubscribeTopicReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;
  //用户id
  string userId = 5;
  //用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备
  string deviceId = 6;
  //需要订阅的topic列表，是topic数组的json包，比如'["t1","t2"]'
  string topics = 7;
}
message SubscribeTopicRsp {
   common.SvcBaseResp base = 1;
}

message UnSubscribeTopicReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;
  //用户id
  string userId = 5;
  //用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备
  string deviceId = 6;
  //需要取消订阅的topic列表，是topic数组的json包，比如'["t1","t2"]'
  string topics = 7;
}
message UnSubscribeTopicRsp {
  common.SvcBaseResp base = 1;
}

message UsersTopicsReq {
  // 平台id
  string platformId = 1;
  // 签名unix时间戳
  string timeStamp = 2;
  // 签名随机串
  string nonceStr = 3;
  // 签名串，用于接口认证
  string sign = 4;
  //查询的用户id和对应设备，设备id为空则查询用户的所有设备，是UserDevice结构数组json包
  string userDevices = 5;
}
message UserDevice {
  //用户id
  string userId = 1;
  //用户设备id，设备id为空则查询用户的所有设备
  string deviceId = 2;
}
message UsersTopicsRsp {
  common.SvcBaseResp base = 1;
  //查询的用户topic数据
  UsersTopicsData data = 2;
}
message UsersTopicsData {
  //用户id和对应的订阅信息
  map<string, basemsgtopic.UserTopicsData> userTopics = 1;
}

service s {
  //服务调试接口，主要是测试服务连通性
  rpc Test (TestReq) returns (TestRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgcallin.s/v1/Test.json"
      body: "*"
    };
  }

  //平台业务获取长连接token和连接域名信息接口
  rpc GetMsgConnToken (GetMsgConnTokenReq) returns (GetMsgConnTokenRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgcallin.s/v1/GetMsgConnToken.json"
      body: "*"
    };
  }

  //平台业务下发消息接口
  rpc PublishMsg (PublishMsgReq) returns (PublishMsgRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgcallin.s/v1/PublishMsg.json"
      body: "*"
    };
  }

  //平台业务用户订阅topic接口
  rpc SubscribeTopic (SubscribeTopicReq) returns (SubscribeTopicRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgcallin.s/v1/SubscribeTopic.json"
      body: "*"
    };
  }

  //平台业务用户取消订阅topic接口
  rpc UnSubscribeTopic (UnSubscribeTopicReq) returns (UnSubscribeTopicRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgcallin.s/v1/UnSubscribeTopic.json"
      body: "*"
    };
  }

  //平台业务批量查询用户订阅的topic信息接口
  rpc UsersTopics (UsersTopicsReq) returns (UsersTopicsRsp) {
    option (google.api.http) = {
      post: "/vc.basemsgcallin.s/v1/UsersTopics.json"
      body: "*"
    };
  }
}
