// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: basemsgcallin.proto

package basemsgcallin

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	basemsgtopic "xim/proto/api/basemsgtopic"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestReq) Reset() {
	*x = TestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReq) ProtoMessage() {}

func (x *TestReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReq.ProtoReflect.Descriptor instead.
func (*TestReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{0}
}

type TestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *TestRsp) Reset() {
	*x = TestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRsp) ProtoMessage() {}

func (x *TestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRsp.ProtoReflect.Descriptor instead.
func (*TestRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{1}
}

func (x *TestRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type GetMsgConnTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id
	DeviceId string `protobuf:"bytes,6,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 扩展数据的json包，提供用户设备的一些补充信息，可以补充clientId，clientVersion，scheme等字段，比如{"clientVersion":"1.1.0"}
	ExtendData string `protobuf:"bytes,7,opt,name=extendData,proto3" json:"extendData,omitempty"`
}

func (x *GetMsgConnTokenReq) Reset() {
	*x = GetMsgConnTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMsgConnTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMsgConnTokenReq) ProtoMessage() {}

func (x *GetMsgConnTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMsgConnTokenReq.ProtoReflect.Descriptor instead.
func (*GetMsgConnTokenReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{2}
}

func (x *GetMsgConnTokenReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *GetMsgConnTokenReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *GetMsgConnTokenReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *GetMsgConnTokenReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *GetMsgConnTokenReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetMsgConnTokenReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetMsgConnTokenReq) GetExtendData() string {
	if x != nil {
		return x.ExtendData
	}
	return ""
}

type GetMsgConnTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	// 长连接token数据，包含长连接地址端口
	Data *MsgTokenData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMsgConnTokenRsp) Reset() {
	*x = GetMsgConnTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMsgConnTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMsgConnTokenRsp) ProtoMessage() {}

func (x *GetMsgConnTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMsgConnTokenRsp.ProtoReflect.Descriptor instead.
func (*GetMsgConnTokenRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{3}
}

func (x *GetMsgConnTokenRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetMsgConnTokenRsp) GetData() *MsgTokenData {
	if x != nil {
		return x.Data
	}
	return nil
}

type MsgTokenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 长连接token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 长连接主地址
	MasterAddr *MsgAddrData `protobuf:"bytes,2,opt,name=masterAddr,proto3" json:"masterAddr,omitempty"`
	// 长连接备用地址
	SlaveAddr *MsgAddrData `protobuf:"bytes,3,opt,name=slaveAddr,proto3" json:"slaveAddr,omitempty"`
	// 客户端发上行消息使用的通用topic
	GeneralTopic string `protobuf:"bytes,4,opt,name=generalTopic,proto3" json:"generalTopic,omitempty"`
	// 客户端长连接被踢时下发的消息topic
	KickTopic string `protobuf:"bytes,5,opt,name=kickTopic,proto3" json:"kickTopic,omitempty"`
}

func (x *MsgTokenData) Reset() {
	*x = MsgTokenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgTokenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgTokenData) ProtoMessage() {}

func (x *MsgTokenData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgTokenData.ProtoReflect.Descriptor instead.
func (*MsgTokenData) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{4}
}

func (x *MsgTokenData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *MsgTokenData) GetMasterAddr() *MsgAddrData {
	if x != nil {
		return x.MasterAddr
	}
	return nil
}

func (x *MsgTokenData) GetSlaveAddr() *MsgAddrData {
	if x != nil {
		return x.SlaveAddr
	}
	return nil
}

func (x *MsgTokenData) GetGeneralTopic() string {
	if x != nil {
		return x.GeneralTopic
	}
	return ""
}

func (x *MsgTokenData) GetKickTopic() string {
	if x != nil {
		return x.KickTopic
	}
	return ""
}

type MsgAddrData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 长连接域名或ip
	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	// 长连接端口
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *MsgAddrData) Reset() {
	*x = MsgAddrData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgAddrData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgAddrData) ProtoMessage() {}

func (x *MsgAddrData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgAddrData.ProtoReflect.Descriptor instead.
func (*MsgAddrData) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{5}
}

func (x *MsgAddrData) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *MsgAddrData) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

type PublishMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	// 发布的具体消息
	Payload string `protobuf:"bytes,5,opt,name=payload,proto3" json:"payload,omitempty"`
	// 发布目标类型，1为平台用户，2为平台topic下的用户，4为平台topic下符合限制条件的用户
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// 发布的目标信息，是下面PublishTargetInfo数据的json包形式，使用时解析出来
	TargetJson string `protobuf:"bytes,7,opt,name=targetJson,proto3" json:"targetJson,omitempty"`
}

func (x *PublishMsgReq) Reset() {
	*x = PublishMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishMsgReq) ProtoMessage() {}

func (x *PublishMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishMsgReq.ProtoReflect.Descriptor instead.
func (*PublishMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{6}
}

func (x *PublishMsgReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *PublishMsgReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *PublishMsgReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *PublishMsgReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *PublishMsgReq) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *PublishMsgReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PublishMsgReq) GetTargetJson() string {
	if x != nil {
		return x.TargetJson
	}
	return ""
}

type PublishTargetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id（用于目标类型为1）
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备（用于目标类型为1）
	DeviceId string `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 排除，不发布的用户设备id名单（用于目标类型为1）
	ExcludeDeviceIds []string `protobuf:"bytes,3,rep,name=excludeDeviceIds,proto3" json:"excludeDeviceIds,omitempty"`
	// 发布的topic（用于目标类型为2，4）
	Topic string `protobuf:"bytes,4,opt,name=topic,proto3" json:"topic,omitempty"`
	// 特定需要发布的用户id名单（用于目标类型为4）
	IncludeUserIds []string `protobuf:"bytes,5,rep,name=includeUserIds,proto3" json:"includeUserIds,omitempty"`
	// 排除，不发布的用户id名单（用于目标类型为4）
	ExcludeUserIds []string `protobuf:"bytes,6,rep,name=excludeUserIds,proto3" json:"excludeUserIds,omitempty"`
	// 不在includeUserIds内的长连接，有一定概率可以发布（用于目标类型为4）
	BroadcastRate uint32 `protobuf:"varint,7,opt,name=broadcastRate,proto3" json:"broadcastRate,omitempty"`
	// 需要限制的客户端配置列表（用于目标类型为1，4）
	LimitList []*ClientLimit `protobuf:"bytes,8,rep,name=limitList,proto3" json:"limitList,omitempty"`
}

func (x *PublishTargetInfo) Reset() {
	*x = PublishTargetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishTargetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishTargetInfo) ProtoMessage() {}

func (x *PublishTargetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishTargetInfo.ProtoReflect.Descriptor instead.
func (*PublishTargetInfo) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{7}
}

func (x *PublishTargetInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PublishTargetInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PublishTargetInfo) GetExcludeDeviceIds() []string {
	if x != nil {
		return x.ExcludeDeviceIds
	}
	return nil
}

func (x *PublishTargetInfo) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *PublishTargetInfo) GetIncludeUserIds() []string {
	if x != nil {
		return x.IncludeUserIds
	}
	return nil
}

func (x *PublishTargetInfo) GetExcludeUserIds() []string {
	if x != nil {
		return x.ExcludeUserIds
	}
	return nil
}

func (x *PublishTargetInfo) GetBroadcastRate() uint32 {
	if x != nil {
		return x.BroadcastRate
	}
	return 0
}

func (x *PublishTargetInfo) GetLimitList() []*ClientLimit {
	if x != nil {
		return x.LimitList
	}
	return nil
}

type ClientLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端限制类型，1为放行，2为屏蔽
	LimitType int32 `protobuf:"varint,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
	// 客户端标识id
	ClientId uint32 `protobuf:"varint,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// 客户端版本比较类型，分{>, >=, <, <=, =}，空字符串或其他字符串的默认为不区分版本
	VersionCompare string `protobuf:"bytes,3,opt,name=versionCompare,proto3" json:"versionCompare,omitempty"`
	// 客户端版本比较值
	ClientVersion string `protobuf:"bytes,4,opt,name=clientVersion,proto3" json:"clientVersion,omitempty"`
}

func (x *ClientLimit) Reset() {
	*x = ClientLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientLimit) ProtoMessage() {}

func (x *ClientLimit) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientLimit.ProtoReflect.Descriptor instead.
func (*ClientLimit) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{8}
}

func (x *ClientLimit) GetLimitType() int32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *ClientLimit) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ClientLimit) GetVersionCompare() string {
	if x != nil {
		return x.VersionCompare
	}
	return ""
}

func (x *ClientLimit) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

type PublishMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *PublishMsgRsp) Reset() {
	*x = PublishMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishMsgRsp) ProtoMessage() {}

func (x *PublishMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishMsgRsp.ProtoReflect.Descriptor instead.
func (*PublishMsgRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{9}
}

func (x *PublishMsgRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type SubscribeTopicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备
	DeviceId string `protobuf:"bytes,6,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 需要订阅的topic列表，是topic数组的json包，比如'["t1","t2"]'
	Topics string `protobuf:"bytes,7,opt,name=topics,proto3" json:"topics,omitempty"`
}

func (x *SubscribeTopicReq) Reset() {
	*x = SubscribeTopicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeTopicReq) ProtoMessage() {}

func (x *SubscribeTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeTopicReq.ProtoReflect.Descriptor instead.
func (*SubscribeTopicReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{10}
}

func (x *SubscribeTopicReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *SubscribeTopicReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *SubscribeTopicReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *SubscribeTopicReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *SubscribeTopicReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SubscribeTopicReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SubscribeTopicReq) GetTopics() string {
	if x != nil {
		return x.Topics
	}
	return ""
}

type SubscribeTopicRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *SubscribeTopicRsp) Reset() {
	*x = SubscribeTopicRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeTopicRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeTopicRsp) ProtoMessage() {}

func (x *SubscribeTopicRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeTopicRsp.ProtoReflect.Descriptor instead.
func (*SubscribeTopicRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{11}
}

func (x *SubscribeTopicRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type UnSubscribeTopicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，不同设备可能有不同长连接，设备为空默认用户下所有设备
	DeviceId string `protobuf:"bytes,6,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 需要取消订阅的topic列表，是topic数组的json包，比如'["t1","t2"]'
	Topics string `protobuf:"bytes,7,opt,name=topics,proto3" json:"topics,omitempty"`
}

func (x *UnSubscribeTopicReq) Reset() {
	*x = UnSubscribeTopicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnSubscribeTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSubscribeTopicReq) ProtoMessage() {}

func (x *UnSubscribeTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSubscribeTopicReq.ProtoReflect.Descriptor instead.
func (*UnSubscribeTopicReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{12}
}

func (x *UnSubscribeTopicReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *UnSubscribeTopicReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *UnSubscribeTopicReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *UnSubscribeTopicReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *UnSubscribeTopicReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnSubscribeTopicReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UnSubscribeTopicReq) GetTopics() string {
	if x != nil {
		return x.Topics
	}
	return ""
}

type UnSubscribeTopicRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *UnSubscribeTopicRsp) Reset() {
	*x = UnSubscribeTopicRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnSubscribeTopicRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSubscribeTopicRsp) ProtoMessage() {}

func (x *UnSubscribeTopicRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSubscribeTopicRsp.ProtoReflect.Descriptor instead.
func (*UnSubscribeTopicRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{13}
}

func (x *UnSubscribeTopicRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type UsersTopicsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 平台id
	PlatformId string `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 签名unix时间戳
	TimeStamp string `protobuf:"bytes,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	// 签名随机串
	NonceStr string `protobuf:"bytes,3,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	// 签名串，用于接口认证
	Sign string `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	// 查询的用户id和对应设备，设备id为空则查询用户的所有设备，是UserDevice结构数组json包
	UserDevices string `protobuf:"bytes,5,opt,name=userDevices,proto3" json:"userDevices,omitempty"`
}

func (x *UsersTopicsReq) Reset() {
	*x = UsersTopicsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersTopicsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersTopicsReq) ProtoMessage() {}

func (x *UsersTopicsReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersTopicsReq.ProtoReflect.Descriptor instead.
func (*UsersTopicsReq) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{14}
}

func (x *UsersTopicsReq) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *UsersTopicsReq) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *UsersTopicsReq) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *UsersTopicsReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *UsersTopicsReq) GetUserDevices() string {
	if x != nil {
		return x.UserDevices
	}
	return ""
}

type UserDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	// 用户设备id，设备id为空则查询用户的所有设备
	DeviceId string `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *UserDevice) Reset() {
	*x = UserDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDevice) ProtoMessage() {}

func (x *UserDevice) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDevice.ProtoReflect.Descriptor instead.
func (*UserDevice) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{15}
}

func (x *UserDevice) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserDevice) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type UsersTopicsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	// 查询的用户topic数据
	Data *UsersTopicsData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UsersTopicsRsp) Reset() {
	*x = UsersTopicsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersTopicsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersTopicsRsp) ProtoMessage() {}

func (x *UsersTopicsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersTopicsRsp.ProtoReflect.Descriptor instead.
func (*UsersTopicsRsp) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{16}
}

func (x *UsersTopicsRsp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *UsersTopicsRsp) GetData() *UsersTopicsData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UsersTopicsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id和对应的订阅信息
	UserTopics map[string]*basemsgtopic.UserTopicsData `protobuf:"bytes,1,rep,name=userTopics,proto3" json:"userTopics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UsersTopicsData) Reset() {
	*x = UsersTopicsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgcallin_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersTopicsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersTopicsData) ProtoMessage() {}

func (x *UsersTopicsData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgcallin_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersTopicsData.ProtoReflect.Descriptor instead.
func (*UsersTopicsData) Descriptor() ([]byte, []int) {
	return file_basemsgcallin_proto_rawDescGZIP(), []int{17}
}

func (x *UsersTopicsData) GetUserTopics() map[string]*basemsgtopic.UserTopicsData {
	if x != nil {
		return x.UserTopics
	}
	return nil
}

var File_basemsgcallin_proto protoreflect.FileDescriptor

var file_basemsgcallin_proto_rawDesc = []byte{
	0x0a, 0x13, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07, 0x54,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x32, 0x0a, 0x07, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0xd6, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x71, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e,
	0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xe2, 0x01, 0x0a, 0x0c, 0x4d, 0x73, 0x67, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3d, 0x0a,
	0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x41, 0x64, 0x64, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x3b, 0x0a, 0x09,
	0x73, 0x6c, 0x61, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x41, 0x64, 0x64, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09,
	0x73, 0x6c, 0x61, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x6c, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x1c, 0x0a,
	0x09, 0x6b, 0x69, 0x63, 0x6b, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6b, 0x69, 0x63, 0x6b, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x35, 0x0a, 0x0b, 0x4d,
	0x73, 0x67, 0x41, 0x64, 0x64, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x22, 0xcb, 0x01, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73,
	0x67, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69,
	0x67, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4a, 0x73, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4a, 0x73, 0x6f, 0x6e,
	0x22, 0xbc, 0x02, 0x0a, 0x11, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x26, 0x0a, 0x0e,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x95, 0x01, 0x0a, 0x0b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x38, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x22, 0xcd, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x22, 0x3c, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76,
	0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22,
	0xcf, 0x01, 0x0a, 0x13, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x22, 0x3e, 0x0a, 0x13, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x22, 0xa0, 0x01, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69,
	0x67, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x22, 0x40, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x73, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xc4, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x51, 0x0a, 0x0a,
	0x75, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x1a,
	0x5e, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32,
	0xc2, 0x06, 0x0a, 0x01, 0x73, 0x12, 0x69, 0x0a, 0x04, 0x54, 0x65, 0x73, 0x74, 0x12, 0x19, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20,
	0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x2e, 0x6a, 0x73, 0x6f, 0x6e,
	0x12, 0x95, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x6e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x6f,
	0x6e, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70,
	0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01, 0x2a, 0x22, 0x2b, 0x2f, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x47, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x81, 0x01, 0x0a, 0x0a, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x12, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x91, 0x01, 0x0a,
	0x0e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12,
	0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x73, 0x70, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x6a, 0x73, 0x6f, 0x6e,
	0x12, 0x99, 0x01, 0x0a, 0x10, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e,
	0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x52, 0x73, 0x70, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x22, 0x2c,
	0x2f, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x55, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x85, 0x01, 0x0a,
	0x0b, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x20, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x52, 0x73, 0x70,
	0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x76, 0x63,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x2e, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x55, 0x73, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x2e,
	0x6a, 0x73, 0x6f, 0x6e, 0x42, 0x2b, 0x5a, 0x29, 0x78, 0x69, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x3b, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x63, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_basemsgcallin_proto_rawDescOnce sync.Once
	file_basemsgcallin_proto_rawDescData = file_basemsgcallin_proto_rawDesc
)

func file_basemsgcallin_proto_rawDescGZIP() []byte {
	file_basemsgcallin_proto_rawDescOnce.Do(func() {
		file_basemsgcallin_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgcallin_proto_rawDescData)
	})
	return file_basemsgcallin_proto_rawDescData
}

var file_basemsgcallin_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_basemsgcallin_proto_goTypes = []interface{}{
	(*TestReq)(nil),                     // 0: vc.basemsgcallin.TestReq
	(*TestRsp)(nil),                     // 1: vc.basemsgcallin.TestRsp
	(*GetMsgConnTokenReq)(nil),          // 2: vc.basemsgcallin.GetMsgConnTokenReq
	(*GetMsgConnTokenRsp)(nil),          // 3: vc.basemsgcallin.GetMsgConnTokenRsp
	(*MsgTokenData)(nil),                // 4: vc.basemsgcallin.MsgTokenData
	(*MsgAddrData)(nil),                 // 5: vc.basemsgcallin.MsgAddrData
	(*PublishMsgReq)(nil),               // 6: vc.basemsgcallin.PublishMsgReq
	(*PublishTargetInfo)(nil),           // 7: vc.basemsgcallin.PublishTargetInfo
	(*ClientLimit)(nil),                 // 8: vc.basemsgcallin.ClientLimit
	(*PublishMsgRsp)(nil),               // 9: vc.basemsgcallin.PublishMsgRsp
	(*SubscribeTopicReq)(nil),           // 10: vc.basemsgcallin.SubscribeTopicReq
	(*SubscribeTopicRsp)(nil),           // 11: vc.basemsgcallin.SubscribeTopicRsp
	(*UnSubscribeTopicReq)(nil),         // 12: vc.basemsgcallin.UnSubscribeTopicReq
	(*UnSubscribeTopicRsp)(nil),         // 13: vc.basemsgcallin.UnSubscribeTopicRsp
	(*UsersTopicsReq)(nil),              // 14: vc.basemsgcallin.UsersTopicsReq
	(*UserDevice)(nil),                  // 15: vc.basemsgcallin.UserDevice
	(*UsersTopicsRsp)(nil),              // 16: vc.basemsgcallin.UsersTopicsRsp
	(*UsersTopicsData)(nil),             // 17: vc.basemsgcallin.UsersTopicsData
	nil,                                 // 18: vc.basemsgcallin.UsersTopicsData.UserTopicsEntry
	(*common.SvcBaseResp)(nil),          // 19: common.SvcBaseResp
	(*basemsgtopic.UserTopicsData)(nil), // 20: vc.basemsgtopic.UserTopicsData
}
var file_basemsgcallin_proto_depIdxs = []int32{
	19, // 0: vc.basemsgcallin.TestRsp.base:type_name -> common.SvcBaseResp
	19, // 1: vc.basemsgcallin.GetMsgConnTokenRsp.base:type_name -> common.SvcBaseResp
	4,  // 2: vc.basemsgcallin.GetMsgConnTokenRsp.data:type_name -> vc.basemsgcallin.MsgTokenData
	5,  // 3: vc.basemsgcallin.MsgTokenData.masterAddr:type_name -> vc.basemsgcallin.MsgAddrData
	5,  // 4: vc.basemsgcallin.MsgTokenData.slaveAddr:type_name -> vc.basemsgcallin.MsgAddrData
	8,  // 5: vc.basemsgcallin.PublishTargetInfo.limitList:type_name -> vc.basemsgcallin.ClientLimit
	19, // 6: vc.basemsgcallin.PublishMsgRsp.base:type_name -> common.SvcBaseResp
	19, // 7: vc.basemsgcallin.SubscribeTopicRsp.base:type_name -> common.SvcBaseResp
	19, // 8: vc.basemsgcallin.UnSubscribeTopicRsp.base:type_name -> common.SvcBaseResp
	19, // 9: vc.basemsgcallin.UsersTopicsRsp.base:type_name -> common.SvcBaseResp
	17, // 10: vc.basemsgcallin.UsersTopicsRsp.data:type_name -> vc.basemsgcallin.UsersTopicsData
	18, // 11: vc.basemsgcallin.UsersTopicsData.userTopics:type_name -> vc.basemsgcallin.UsersTopicsData.UserTopicsEntry
	20, // 12: vc.basemsgcallin.UsersTopicsData.UserTopicsEntry.value:type_name -> vc.basemsgtopic.UserTopicsData
	0,  // 13: vc.basemsgcallin.s.Test:input_type -> vc.basemsgcallin.TestReq
	2,  // 14: vc.basemsgcallin.s.GetMsgConnToken:input_type -> vc.basemsgcallin.GetMsgConnTokenReq
	6,  // 15: vc.basemsgcallin.s.PublishMsg:input_type -> vc.basemsgcallin.PublishMsgReq
	10, // 16: vc.basemsgcallin.s.SubscribeTopic:input_type -> vc.basemsgcallin.SubscribeTopicReq
	12, // 17: vc.basemsgcallin.s.UnSubscribeTopic:input_type -> vc.basemsgcallin.UnSubscribeTopicReq
	14, // 18: vc.basemsgcallin.s.UsersTopics:input_type -> vc.basemsgcallin.UsersTopicsReq
	1,  // 19: vc.basemsgcallin.s.Test:output_type -> vc.basemsgcallin.TestRsp
	3,  // 20: vc.basemsgcallin.s.GetMsgConnToken:output_type -> vc.basemsgcallin.GetMsgConnTokenRsp
	9,  // 21: vc.basemsgcallin.s.PublishMsg:output_type -> vc.basemsgcallin.PublishMsgRsp
	11, // 22: vc.basemsgcallin.s.SubscribeTopic:output_type -> vc.basemsgcallin.SubscribeTopicRsp
	13, // 23: vc.basemsgcallin.s.UnSubscribeTopic:output_type -> vc.basemsgcallin.UnSubscribeTopicRsp
	16, // 24: vc.basemsgcallin.s.UsersTopics:output_type -> vc.basemsgcallin.UsersTopicsRsp
	19, // [19:25] is the sub-list for method output_type
	13, // [13:19] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_basemsgcallin_proto_init() }
func file_basemsgcallin_proto_init() {
	if File_basemsgcallin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgcallin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMsgConnTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMsgConnTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgTokenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgAddrData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishTargetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeTopicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeTopicRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnSubscribeTopicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnSubscribeTopicRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersTopicsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersTopicsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgcallin_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersTopicsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgcallin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgcallin_proto_goTypes,
		DependencyIndexes: file_basemsgcallin_proto_depIdxs,
		MessageInfos:      file_basemsgcallin_proto_msgTypes,
	}.Build()
	File_basemsgcallin_proto = out.File
	file_basemsgcallin_proto_rawDesc = nil
	file_basemsgcallin_proto_goTypes = nil
	file_basemsgcallin_proto_depIdxs = nil
}
