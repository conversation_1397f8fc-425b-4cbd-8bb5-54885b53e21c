syntax = "proto3";

package vc.basemsgbroker;
option go_package = "xim/proto/api/basemsgbroker";

import "google/api/annotations.proto";
import "common/common.proto";

enum MsgEventType {
  // 占位无效
  NoneType = 0;
  // 长连接建立
  ConnectType = 1;
  // 长连接断开
  CloseType = 2;
  // 长连接心跳维持
  PingType = 3;
  // 长连接上行消息
  PublishType = 4;
}

message TestReq{
}
message TestRsp{
  //结果码
  int32 result = 1;
  //结果说明
  string message = 2;
}

message PublishReq {
  //发送目标id数组
  repeated uint64 connIds = 1;
  // 消息主题
  string topic = 2;
  // 消息内容
  string message = 3;
}
message PublishRsp {
  //结果码
  int32 result = 1;
  //结果详情
  string message = 2;
}

message KickReq {
  // 下线的目标id
  uint64 connId = 1;
  // 消息主题
  string topic = 2;
  // 消息内容
  string message = 3;
}
message KickRsp {
  //结果码
  int32 result = 1;
  //结果详情
  string message = 2;
}

message MsgEvent {
  //上行消息类型
  MsgEventType type = 1;
  //平台id
  uint64 platformId = 2;
  //客户端id
  uint32 clientId = 3;
  //客户端设备id
  string deviceId = 4;
  //用户id
  string userId = 5;
  //长连接id
  uint64 connId = 6;
  // 长连接broker的ip
  string linkIp = 7;
  //消息发送时间戳
  int64 time = 8;
  //消息内容，具体消息数据
  string msg = 9;
  //消息topic
  string topic = 10;
  //消息id
  string msgId = 11;
  //消息发送时间戳-纳秒
  int64 nanoTime = 12;
  // base info,
  common.BaseParam base = 13;
  // 是否审核
  bool review = 14;
}

service s {
  //服务调试接口，调试没问题后可去掉
  rpc Test (TestReq) returns (TestRsp) {
    option (google.api.http) = {
      post: "/xllivemp.basemsgbroker.s/v1/Test.json"
      body: "*"
    };
  }

  // 内部发布消息到用户客户端端
  rpc Publish(PublishReq) returns (PublishRsp) {}
  // 内部下线与客户端的长连接
  rpc Kick(KickReq) returns (KickRsp) {}
}
