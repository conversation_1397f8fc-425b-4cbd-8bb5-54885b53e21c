// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.4
// source: basemsgbroker.proto

package basemsgbroker

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	S_Test_FullMethodName    = "/vc.basemsgbroker.s/Test"
	S_Publish_FullMethodName = "/vc.basemsgbroker.s/Publish"
	S_Kick_FullMethodName    = "/vc.basemsgbroker.s/Kick"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 服务调试接口，调试没问题后可去掉
	Test(ctx context.Context, in *TestReq, opts ...grpc.CallOption) (*TestRsp, error)
	// 内部发布消息到用户客户端端
	Publish(ctx context.Context, in *PublishReq, opts ...grpc.CallOption) (*PublishRsp, error)
	// 内部下线与客户端的长连接
	Kick(ctx context.Context, in *KickReq, opts ...grpc.CallOption) (*KickRsp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) Test(ctx context.Context, in *TestReq, opts ...grpc.CallOption) (*TestRsp, error) {
	out := new(TestRsp)
	err := c.cc.Invoke(ctx, S_Test_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Publish(ctx context.Context, in *PublishReq, opts ...grpc.CallOption) (*PublishRsp, error) {
	out := new(PublishRsp)
	err := c.cc.Invoke(ctx, S_Publish_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Kick(ctx context.Context, in *KickReq, opts ...grpc.CallOption) (*KickRsp, error) {
	out := new(KickRsp)
	err := c.cc.Invoke(ctx, S_Kick_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 服务调试接口，调试没问题后可去掉
	Test(context.Context, *TestReq) (*TestRsp, error)
	// 内部发布消息到用户客户端端
	Publish(context.Context, *PublishReq) (*PublishRsp, error)
	// 内部下线与客户端的长连接
	Kick(context.Context, *KickReq) (*KickRsp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) Test(context.Context, *TestReq) (*TestRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Test not implemented")
}
func (UnimplementedSServer) Publish(context.Context, *PublishReq) (*PublishRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Publish not implemented")
}
func (UnimplementedSServer) Kick(context.Context, *KickReq) (*KickRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Kick not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_Test_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Test(ctx, req.(*TestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Publish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Publish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_Publish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Publish(ctx, req.(*PublishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Kick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Kick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_Kick_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Kick(ctx, req.(*KickReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.basemsgbroker.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Test",
			Handler:    _S_Test_Handler,
		},
		{
			MethodName: "Publish",
			Handler:    _S_Publish_Handler,
		},
		{
			MethodName: "Kick",
			Handler:    _S_Kick_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "basemsgbroker.proto",
}
