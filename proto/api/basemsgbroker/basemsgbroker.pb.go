// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: basemsgbroker.proto

package basemsgbroker

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MsgEventType int32

const (
	// 占位无效
	MsgEventType_NoneType MsgEventType = 0
	// 长连接建立
	MsgEventType_ConnectType MsgEventType = 1
	// 长连接断开
	MsgEventType_CloseType MsgEventType = 2
	// 长连接心跳维持
	MsgEventType_PingType MsgEventType = 3
	// 长连接上行消息
	MsgEventType_PublishType MsgEventType = 4
)

// Enum value maps for MsgEventType.
var (
	MsgEventType_name = map[int32]string{
		0: "NoneType",
		1: "ConnectType",
		2: "CloseType",
		3: "PingType",
		4: "PublishType",
	}
	MsgEventType_value = map[string]int32{
		"NoneType":    0,
		"ConnectType": 1,
		"CloseType":   2,
		"PingType":    3,
		"PublishType": 4,
	}
)

func (x MsgEventType) Enum() *MsgEventType {
	p := new(MsgEventType)
	*p = x
	return p
}

func (x MsgEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgbroker_proto_enumTypes[0].Descriptor()
}

func (MsgEventType) Type() protoreflect.EnumType {
	return &file_basemsgbroker_proto_enumTypes[0]
}

func (x MsgEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgEventType.Descriptor instead.
func (MsgEventType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{0}
}

type TestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TestReq) Reset() {
	*x = TestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReq) ProtoMessage() {}

func (x *TestReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReq.ProtoReflect.Descriptor instead.
func (*TestReq) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{0}
}

type TestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果说明
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *TestRsp) Reset() {
	*x = TestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRsp) ProtoMessage() {}

func (x *TestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRsp.ProtoReflect.Descriptor instead.
func (*TestRsp) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{1}
}

func (x *TestRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *TestRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type PublishReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发送目标id数组
	ConnIds []uint64 `protobuf:"varint,1,rep,packed,name=connIds,proto3" json:"connIds,omitempty"`
	// 消息主题
	Topic string `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	// 消息内容
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PublishReq) Reset() {
	*x = PublishReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishReq) ProtoMessage() {}

func (x *PublishReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishReq.ProtoReflect.Descriptor instead.
func (*PublishReq) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{2}
}

func (x *PublishReq) GetConnIds() []uint64 {
	if x != nil {
		return x.ConnIds
	}
	return nil
}

func (x *PublishReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *PublishReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type PublishRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果详情
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PublishRsp) Reset() {
	*x = PublishRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishRsp) ProtoMessage() {}

func (x *PublishRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishRsp.ProtoReflect.Descriptor instead.
func (*PublishRsp) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{3}
}

func (x *PublishRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *PublishRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type KickReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 下线的目标id
	ConnId uint64 `protobuf:"varint,1,opt,name=connId,proto3" json:"connId,omitempty"`
	// 消息主题
	Topic string `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	// 消息内容
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *KickReq) Reset() {
	*x = KickReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KickReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickReq) ProtoMessage() {}

func (x *KickReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickReq.ProtoReflect.Descriptor instead.
func (*KickReq) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{4}
}

func (x *KickReq) GetConnId() uint64 {
	if x != nil {
		return x.ConnId
	}
	return 0
}

func (x *KickReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *KickReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type KickRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果码
	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// 结果详情
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *KickRsp) Reset() {
	*x = KickRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KickRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickRsp) ProtoMessage() {}

func (x *KickRsp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickRsp.ProtoReflect.Descriptor instead.
func (*KickRsp) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{5}
}

func (x *KickRsp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *KickRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MsgEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上行消息类型
	Type MsgEventType `protobuf:"varint,1,opt,name=type,proto3,enum=vc.basemsgbroker.MsgEventType" json:"type,omitempty"`
	// 平台id
	PlatformId uint64 `protobuf:"varint,2,opt,name=platformId,proto3" json:"platformId,omitempty"`
	// 客户端id
	ClientId uint32 `protobuf:"varint,3,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// 客户端设备id
	DeviceId string `protobuf:"bytes,4,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	// 用户id
	UserId string `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId,omitempty"`
	// 长连接id
	ConnId uint64 `protobuf:"varint,6,opt,name=connId,proto3" json:"connId,omitempty"`
	// 长连接broker的ip
	LinkIp string `protobuf:"bytes,7,opt,name=linkIp,proto3" json:"linkIp,omitempty"`
	// 消息发送时间戳
	Time int64 `protobuf:"varint,8,opt,name=time,proto3" json:"time,omitempty"`
	// 消息内容，具体消息数据
	Msg string `protobuf:"bytes,9,opt,name=msg,proto3" json:"msg,omitempty"`
	// 消息topic
	Topic string `protobuf:"bytes,10,opt,name=topic,proto3" json:"topic,omitempty"`
	// 消息id
	MsgId string `protobuf:"bytes,11,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// 消息发送时间戳-纳秒
	NanoTime int64 `protobuf:"varint,12,opt,name=nanoTime,proto3" json:"nanoTime,omitempty"`
	// base info,
	Base *common.BaseParam `protobuf:"bytes,13,opt,name=base,proto3" json:"base,omitempty"`
	// 是否审核
	Review bool `protobuf:"varint,14,opt,name=review,proto3" json:"review,omitempty"`
}

func (x *MsgEvent) Reset() {
	*x = MsgEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgbroker_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgEvent) ProtoMessage() {}

func (x *MsgEvent) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgbroker_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgEvent.ProtoReflect.Descriptor instead.
func (*MsgEvent) Descriptor() ([]byte, []int) {
	return file_basemsgbroker_proto_rawDescGZIP(), []int{6}
}

func (x *MsgEvent) GetType() MsgEventType {
	if x != nil {
		return x.Type
	}
	return MsgEventType_NoneType
}

func (x *MsgEvent) GetPlatformId() uint64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *MsgEvent) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *MsgEvent) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *MsgEvent) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MsgEvent) GetConnId() uint64 {
	if x != nil {
		return x.ConnId
	}
	return 0
}

func (x *MsgEvent) GetLinkIp() string {
	if x != nil {
		return x.LinkIp
	}
	return ""
}

func (x *MsgEvent) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *MsgEvent) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MsgEvent) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *MsgEvent) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *MsgEvent) GetNanoTime() int64 {
	if x != nil {
		return x.NanoTime
	}
	return 0
}

func (x *MsgEvent) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *MsgEvent) GetReview() bool {
	if x != nil {
		return x.Review
	}
	return false
}

var File_basemsgbroker_proto protoreflect.FileDescriptor

var file_basemsgbroker_proto_rawDesc = []byte{
	0x0a, 0x13, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73,
	0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07, 0x54, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x3b, 0x0a, 0x07, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x56, 0x0a, 0x0a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3e, 0x0a, 0x0a, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x51, 0x0a, 0x07, 0x4b, 0x69,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3b, 0x0a,
	0x07, 0x4b, 0x69, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x8b, 0x03, 0x0a, 0x08, 0x4d,
	0x73, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x6f, 0x6e, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x6e, 0x61, 0x6e, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6e, 0x61, 0x6e, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2a, 0x5b, 0x0a, 0x0c, 0x4d, 0x73, 0x67, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x10, 0x04, 0x32, 0xfd, 0x01, 0x0a, 0x01, 0x73, 0x12, 0x6f, 0x0a, 0x04, 0x54,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67,
	0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65,
	0x72, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x78, 0x6c, 0x6c, 0x69, 0x76, 0x65, 0x6d, 0x70, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x2e, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x07,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x6d, 0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d,
	0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3e, 0x0a, 0x04, 0x4b, 0x69, 0x63, 0x6b, 0x12, 0x19, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72,
	0x2e, 0x4b, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x2e, 0x4b, 0x69, 0x63, 0x6b,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x1d, 0x5a, 0x1b, 0x78, 0x69, 0x6d, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x62, 0x72,
	0x6f, 0x6b, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_basemsgbroker_proto_rawDescOnce sync.Once
	file_basemsgbroker_proto_rawDescData = file_basemsgbroker_proto_rawDesc
)

func file_basemsgbroker_proto_rawDescGZIP() []byte {
	file_basemsgbroker_proto_rawDescOnce.Do(func() {
		file_basemsgbroker_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgbroker_proto_rawDescData)
	})
	return file_basemsgbroker_proto_rawDescData
}

var file_basemsgbroker_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_basemsgbroker_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_basemsgbroker_proto_goTypes = []interface{}{
	(MsgEventType)(0),        // 0: vc.basemsgbroker.MsgEventType
	(*TestReq)(nil),          // 1: vc.basemsgbroker.TestReq
	(*TestRsp)(nil),          // 2: vc.basemsgbroker.TestRsp
	(*PublishReq)(nil),       // 3: vc.basemsgbroker.PublishReq
	(*PublishRsp)(nil),       // 4: vc.basemsgbroker.PublishRsp
	(*KickReq)(nil),          // 5: vc.basemsgbroker.KickReq
	(*KickRsp)(nil),          // 6: vc.basemsgbroker.KickRsp
	(*MsgEvent)(nil),         // 7: vc.basemsgbroker.MsgEvent
	(*common.BaseParam)(nil), // 8: common.BaseParam
}
var file_basemsgbroker_proto_depIdxs = []int32{
	0, // 0: vc.basemsgbroker.MsgEvent.type:type_name -> vc.basemsgbroker.MsgEventType
	8, // 1: vc.basemsgbroker.MsgEvent.base:type_name -> common.BaseParam
	1, // 2: vc.basemsgbroker.s.Test:input_type -> vc.basemsgbroker.TestReq
	3, // 3: vc.basemsgbroker.s.Publish:input_type -> vc.basemsgbroker.PublishReq
	5, // 4: vc.basemsgbroker.s.Kick:input_type -> vc.basemsgbroker.KickReq
	2, // 5: vc.basemsgbroker.s.Test:output_type -> vc.basemsgbroker.TestRsp
	4, // 6: vc.basemsgbroker.s.Publish:output_type -> vc.basemsgbroker.PublishRsp
	6, // 7: vc.basemsgbroker.s.Kick:output_type -> vc.basemsgbroker.KickRsp
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_basemsgbroker_proto_init() }
func file_basemsgbroker_proto_init() {
	if File_basemsgbroker_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgbroker_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgbroker_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgbroker_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgbroker_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgbroker_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KickReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgbroker_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KickRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgbroker_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgbroker_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgbroker_proto_goTypes,
		DependencyIndexes: file_basemsgbroker_proto_depIdxs,
		EnumInfos:         file_basemsgbroker_proto_enumTypes,
		MessageInfos:      file_basemsgbroker_proto_msgTypes,
	}.Build()
	File_basemsgbroker_proto = out.File
	file_basemsgbroker_proto_rawDesc = nil
	file_basemsgbroker_proto_goTypes = nil
	file_basemsgbroker_proto_depIdxs = nil
}
