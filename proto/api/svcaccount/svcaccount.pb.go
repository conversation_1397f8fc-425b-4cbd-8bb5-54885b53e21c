// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: svcaccount.proto

package svcaccount

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OnConnectType int32

const (
	OnConnectType_invalid      OnConnectType = 0
	OnConnectType_CONNECTED    OnConnectType = 1
	OnConnectType_PING         OnConnectType = 2
	OnConnectType_DISCONNECTED OnConnectType = 3
)

// Enum value maps for OnConnectType.
var (
	OnConnectType_name = map[int32]string{
		0: "invalid",
		1: "CONNECTED",
		2: "PING",
		3: "DISCONNECTED",
	}
	OnConnectType_value = map[string]int32{
		"invalid":      0,
		"CONNECTED":    1,
		"PING":         2,
		"DISCONNECTED": 3,
	}
)

func (x OnConnectType) Enum() *OnConnectType {
	p := new(OnConnectType)
	*p = x
	return p
}

func (x OnConnectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnConnectType) Descriptor() protoreflect.EnumDescriptor {
	return file_svcaccount_proto_enumTypes[0].Descriptor()
}

func (OnConnectType) Type() protoreflect.EnumType {
	return &file_svcaccount_proto_enumTypes[0]
}

func (x OnConnectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnConnectType.Descriptor instead.
func (OnConnectType) EnumDescriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{0}
}

type AccountOnlineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data          *common.BaseParam `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Userid        int64             `protobuf:"varint,2,opt,name=userid,proto3" json:"userid,omitempty"`
	State         bool              `protobuf:"varint,3,opt,name=state,proto3" json:"state,omitempty"`
	Heartbeat     bool              `protobuf:"varint,4,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"`
	NeedResp      bool              `protobuf:"varint,5,opt,name=need_resp,json=needResp,proto3" json:"need_resp,omitempty"`
	Front         bool              `protobuf:"varint,6,opt,name=front,proto3" json:"front,omitempty"`
	OnConnectType OnConnectType     `protobuf:"varint,7,opt,name=on_connect_type,json=onConnectType,proto3,enum=vc.svcaccount.OnConnectType" json:"on_connect_type,omitempty"`
}

func (x *AccountOnlineReq) Reset() {
	*x = AccountOnlineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcaccount_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountOnlineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountOnlineReq) ProtoMessage() {}

func (x *AccountOnlineReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountOnlineReq.ProtoReflect.Descriptor instead.
func (*AccountOnlineReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{0}
}

func (x *AccountOnlineReq) GetData() *common.BaseParam {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AccountOnlineReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *AccountOnlineReq) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

func (x *AccountOnlineReq) GetHeartbeat() bool {
	if x != nil {
		return x.Heartbeat
	}
	return false
}

func (x *AccountOnlineReq) GetNeedResp() bool {
	if x != nil {
		return x.NeedResp
	}
	return false
}

func (x *AccountOnlineReq) GetFront() bool {
	if x != nil {
		return x.Front
	}
	return false
}

func (x *AccountOnlineReq) GetOnConnectType() OnConnectType {
	if x != nil {
		return x.OnConnectType
	}
	return OnConnectType_invalid
}

type AccountOnlineResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *AccountOnlineResp) Reset() {
	*x = AccountOnlineResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcaccount_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountOnlineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountOnlineResp) ProtoMessage() {}

func (x *AccountOnlineResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountOnlineResp.ProtoReflect.Descriptor instead.
func (*AccountOnlineResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{1}
}

func (x *AccountOnlineResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

var File_svcaccount_proto protoreflect.FileDescriptor

var file_svcaccount_proto_rawDesc = []byte{
	0x0a, 0x10, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfe, 0x01, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x6e, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x12, 0x44, 0x0a, 0x0f, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4f, 0x6e, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3c, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04,
	0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52,
	0x04, 0x62, 0x61, 0x73, 0x65, 0x2a, 0x47, 0x0a, 0x0d, 0x4f, 0x6e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c,
	0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x32, 0x59,
	0x0a, 0x01, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x1a, 0x5a, 0x18, 0x78, 0x69, 0x6d,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x76, 0x63, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_svcaccount_proto_rawDescOnce sync.Once
	file_svcaccount_proto_rawDescData = file_svcaccount_proto_rawDesc
)

func file_svcaccount_proto_rawDescGZIP() []byte {
	file_svcaccount_proto_rawDescOnce.Do(func() {
		file_svcaccount_proto_rawDescData = protoimpl.X.CompressGZIP(file_svcaccount_proto_rawDescData)
	})
	return file_svcaccount_proto_rawDescData
}

var file_svcaccount_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_svcaccount_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_svcaccount_proto_goTypes = []interface{}{
	(OnConnectType)(0),         // 0: vc.svcaccount.OnConnectType
	(*AccountOnlineReq)(nil),   // 1: vc.svcaccount.AccountOnlineReq
	(*AccountOnlineResp)(nil),  // 2: vc.svcaccount.AccountOnlineResp
	(*common.BaseParam)(nil),   // 3: common.BaseParam
	(*common.SvcBaseResp)(nil), // 4: common.SvcBaseResp
}
var file_svcaccount_proto_depIdxs = []int32{
	3, // 0: vc.svcaccount.AccountOnlineReq.data:type_name -> common.BaseParam
	0, // 1: vc.svcaccount.AccountOnlineReq.on_connect_type:type_name -> vc.svcaccount.OnConnectType
	4, // 2: vc.svcaccount.AccountOnlineResp.base:type_name -> common.SvcBaseResp
	1, // 3: vc.svcaccount.s.OnlineLimiter:input_type -> vc.svcaccount.AccountOnlineReq
	2, // 4: vc.svcaccount.s.OnlineLimiter:output_type -> vc.svcaccount.AccountOnlineResp
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_svcaccount_proto_init() }
func file_svcaccount_proto_init() {
	if File_svcaccount_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_svcaccount_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountOnlineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_svcaccount_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountOnlineResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_svcaccount_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_svcaccount_proto_goTypes,
		DependencyIndexes: file_svcaccount_proto_depIdxs,
		EnumInfos:         file_svcaccount_proto_enumTypes,
		MessageInfos:      file_svcaccount_proto_msgTypes,
	}.Build()
	File_svcaccount_proto = out.File
	file_svcaccount_proto_rawDesc = nil
	file_svcaccount_proto_goTypes = nil
	file_svcaccount_proto_depIdxs = nil
}
