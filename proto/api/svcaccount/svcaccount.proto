syntax = "proto3";

package  vc.svcaccount;
option go_package = "xim/proto/api/svcaccount";

import "common/common.proto";

enum OnConnectType {
  invalid = 0;
  CONNECTED = 1;
  PING = 2;
  DISCONNECTED = 3;
}

message AccountOnlineReq {
  common.BaseParam data = 1;
  int64 userid = 2;
  bool state = 3;
  bool heartbeat = 4;
  bool need_resp = 5;
  bool front = 6;
  OnConnectType  on_connect_type = 7;
}

message AccountOnlineResp {
  common.SvcBaseResp base = 1;
}

service s {
    rpc OnlineLimiter(AccountOnlineReq) returns (AccountOnlineResp) {}
}
