// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 审核结果
type AuditResult int32

const (
	// 默认，不使用
	AuditResult_unknown AuditResult = 0
	// 通过
	AuditResult_pass AuditResult = 1
	// 拒绝
	AuditResult_reject AuditResult = 2
	// 审核中
	AuditResult_review AuditResult = 3
	// 伪发送 (命中了拒绝并且一级标签是广告)
	AuditResult_fake_send AuditResult = 4
)

// Enum value maps for AuditResult.
var (
	AuditResult_name = map[int32]string{
		0: "unknown",
		1: "pass",
		2: "reject",
		3: "review",
		4: "fake_send",
	}
	AuditResult_value = map[string]int32{
		"unknown":   0,
		"pass":      1,
		"reject":    2,
		"review":    3,
		"fake_send": 4,
	}
)

func (x AuditResult) Enum() *AuditResult {
	p := new(AuditResult)
	*p = x
	return p
}

func (x AuditResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditResult) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[0].Descriptor()
}

func (AuditResult) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[0]
}

func (x AuditResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditResult.Descriptor instead.
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

type AuditSubResult int32

const (
	// 默认，不使用
	AuditSubResult_audit_sub_unknown AuditSubResult = 0
	// 联系方式拦截 男用户亲密的等级过低
	AuditSubResult_audit_sub_male_lianxifangshi AuditSubResult = 1
)

// Enum value maps for AuditSubResult.
var (
	AuditSubResult_name = map[int32]string{
		0: "audit_sub_unknown",
		1: "audit_sub_male_lianxifangshi",
	}
	AuditSubResult_value = map[string]int32{
		"audit_sub_unknown":            0,
		"audit_sub_male_lianxifangshi": 1,
	}
)

func (x AuditSubResult) Enum() *AuditSubResult {
	p := new(AuditSubResult)
	*p = x
	return p
}

func (x AuditSubResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditSubResult) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[1].Descriptor()
}

func (AuditSubResult) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[1]
}

func (x AuditSubResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditSubResult.Descriptor instead.
func (AuditSubResult) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

// shield 结果
type ShieldResult int32

const (
	ShieldResult_shield_unknown ShieldResult = 0 // 默认，不使用
	ShieldResult_shield_pass    ShieldResult = 1 //通过
	ShieldResult_shield_reject  ShieldResult = 2 //拒绝
	ShieldResult_shield_review  ShieldResult = 3 //
)

// Enum value maps for ShieldResult.
var (
	ShieldResult_name = map[int32]string{
		0: "shield_unknown",
		1: "shield_pass",
		2: "shield_reject",
		3: "shield_review",
	}
	ShieldResult_value = map[string]int32{
		"shield_unknown": 0,
		"shield_pass":    1,
		"shield_reject":  2,
		"shield_review":  3,
	}
)

func (x ShieldResult) Enum() *ShieldResult {
	p := new(ShieldResult)
	*p = x
	return p
}

func (x ShieldResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShieldResult) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[2].Descriptor()
}

func (ShieldResult) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[2]
}

func (x ShieldResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShieldResult.Descriptor instead.
func (ShieldResult) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

type IdNameItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // xxx/ddd
	Subid string `protobuf:"bytes,3,opt,name=subid,proto3" json:"subid,omitempty"`
}

func (x *IdNameItem) Reset() {
	*x = IdNameItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdNameItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdNameItem) ProtoMessage() {}

func (x *IdNameItem) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdNameItem.ProtoReflect.Descriptor instead.
func (*IdNameItem) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *IdNameItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IdNameItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IdNameItem) GetSubid() string {
	if x != nil {
		return x.Subid
	}
	return ""
}

type TestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	I    uint32 `protobuf:"varint,1,opt,name=i,proto3" json:"i,omitempty"`
	Data string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TestReq) Reset() {
	*x = TestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReq) ProtoMessage() {}

func (x *TestReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReq.ProtoReflect.Descriptor instead.
func (*TestReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *TestReq) GetI() uint32 {
	if x != nil {
		return x.I
	}
	return 0
}

func (x *TestReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type TestResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	I    uint32 `protobuf:"varint,1,opt,name=i,proto3" json:"i,omitempty"`
	Data string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TestResp) Reset() {
	*x = TestResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestResp) ProtoMessage() {}

func (x *TestResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestResp.ProtoReflect.Descriptor instead.
func (*TestResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *TestResp) GetI() uint32 {
	if x != nil {
		return x.I
	}
	return 0
}

func (x *TestResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type SvcBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SvcBaseReq) Reset() {
	*x = SvcBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SvcBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SvcBaseReq) ProtoMessage() {}

func (x *SvcBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SvcBaseReq.ProtoReflect.Descriptor instead.
func (*SvcBaseReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

type SvcBaseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 错误信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SvcBaseResp) Reset() {
	*x = SvcBaseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SvcBaseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SvcBaseResp) ProtoMessage() {}

func (x *SvcBaseResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SvcBaseResp.ProtoReflect.Descriptor instead.
func (*SvcBaseResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{4}
}

func (x *SvcBaseResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SvcBaseResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SvcCommonResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *SvcCommonResp) Reset() {
	*x = SvcCommonResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SvcCommonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SvcCommonResp) ProtoMessage() {}

func (x *SvcCommonResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SvcCommonResp.ProtoReflect.Descriptor instead.
func (*SvcCommonResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{5}
}

func (x *SvcCommonResp) GetBase() *SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type SvcUseridReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid    int64  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	LastIndex string `protobuf:"bytes,2,opt,name=last_index,json=lastIndex,proto3" json:"last_index,omitempty"`
}

func (x *SvcUseridReq) Reset() {
	*x = SvcUseridReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SvcUseridReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SvcUseridReq) ProtoMessage() {}

func (x *SvcUseridReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SvcUseridReq.ProtoReflect.Descriptor instead.
func (*SvcUseridReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{6}
}

func (x *SvcUseridReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *SvcUseridReq) GetLastIndex() string {
	if x != nil {
		return x.LastIndex
	}
	return ""
}

type SvcUseridsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userids []int64 `protobuf:"varint,1,rep,packed,name=userids,proto3" json:"userids,omitempty"`
}

func (x *SvcUseridsReq) Reset() {
	*x = SvcUseridsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SvcUseridsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SvcUseridsReq) ProtoMessage() {}

func (x *SvcUseridsReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SvcUseridsReq.ProtoReflect.Descriptor instead.
func (*SvcUseridsReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{7}
}

func (x *SvcUseridsReq) GetUserids() []int64 {
	if x != nil {
		return x.Userids
	}
	return nil
}

type SvcUserPageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Userid    int64  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Offset    int32  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit     int32  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	LastIndex string `protobuf:"bytes,4,opt,name=last_index,json=lastIndex,proto3" json:"last_index,omitempty"`
}

func (x *SvcUserPageReq) Reset() {
	*x = SvcUserPageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SvcUserPageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SvcUserPageReq) ProtoMessage() {}

func (x *SvcUserPageReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SvcUserPageReq.ProtoReflect.Descriptor instead.
func (*SvcUserPageReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{8}
}

func (x *SvcUserPageReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *SvcUserPageReq) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SvcUserPageReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SvcUserPageReq) GetLastIndex() string {
	if x != nil {
		return x.LastIndex
	}
	return ""
}

type BizBaseParamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *BaseParam `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *BizBaseParamReq) Reset() {
	*x = BizBaseParamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BizBaseParamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizBaseParamReq) ProtoMessage() {}

func (x *BizBaseParamReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizBaseParamReq.ProtoReflect.Descriptor instead.
func (*BizBaseParamReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{9}
}

func (x *BizBaseParamReq) GetBase() *BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

type BizBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base   *BaseParam `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	UserId int64      `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *BizBaseReq) Reset() {
	*x = BizBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BizBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizBaseReq) ProtoMessage() {}

func (x *BizBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizBaseReq.ProtoReflect.Descriptor instead.
func (*BizBaseReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{10}
}

func (x *BizBaseReq) GetBase() *BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *BizBaseReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type BizPageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *BaseParam `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	// 分页偏移量
	Offset int32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
}

func (x *BizPageReq) Reset() {
	*x = BizPageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BizPageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizPageReq) ProtoMessage() {}

func (x *BizPageReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizPageReq.ProtoReflect.Descriptor instead.
func (*BizPageReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{11}
}

func (x *BizPageReq) GetBase() *BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *BizPageReq) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type BizBaseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 错误信息
	Msg  string     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *anypb.Any `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BizBaseResp) Reset() {
	*x = BizBaseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BizBaseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizBaseResp) ProtoMessage() {}

func (x *BizBaseResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizBaseResp.ProtoReflect.Descriptor instead.
func (*BizBaseResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{12}
}

func (x *BizBaseResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BizBaseResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BizBaseResp) GetData() *anypb.Any {
	if x != nil {
		return x.Data
	}
	return nil
}

type BaseParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app 包名/域名
	App string `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	// app_ver 版本号
	Av string `protobuf:"bytes,2,opt,name=av,proto3" json:"av,omitempty"`
	// dev_type 操作系统 1=android, 2=ios, 3=web
	Dt int32 `protobuf:"varint,3,opt,name=dt,proto3" json:"dt,omitempty"`
	// dev_id 设备标识ID，唯一
	Did string `protobuf:"bytes,4,opt,name=did,proto3" json:"did,omitempty"`
	// net_type 网络类型 1: Wi-Fi 2: 2G或3G 3: 4G 4: 其他
	Nt int32 `protobuf:"varint,5,opt,name=nt,proto3" json:"nt,omitempty"`
	// channel 安装渠道
	Ch string `protobuf:"bytes,6,opt,name=ch,proto3" json:"ch,omitempty"`
	// model 机型
	Md string `protobuf:"bytes,7,opt,name=md,proto3" json:"md,omitempty"`
	// os 安卓系统版本/IOS系统版本/UA
	Os string `protobuf:"bytes,8,opt,name=os,proto3" json:"os,omitempty"`
	// timestamp 时间戳
	Ts int64 `protobuf:"varint,9,opt,name=ts,proto3" json:"ts,omitempty"`
	// IP
	Ip string `protobuf:"bytes,10,opt,name=ip,proto3" json:"ip,omitempty"`
	// imei
	Imei string `protobuf:"bytes,11,opt,name=imei,proto3" json:"imei,omitempty"`
	// oaid
	Oaid string `protobuf:"bytes,12,opt,name=oaid,proto3" json:"oaid,omitempty"`
	// brand 品牌
	Bd string `protobuf:"bytes,13,opt,name=bd,proto3" json:"bd,omitempty"`
	// iOS广告标识符
	Idfa string `protobuf:"bytes,14,opt,name=idfa,proto3" json:"idfa,omitempty"`
	// vpn 1开始，0关闭
	Vpn string `protobuf:"bytes,15,opt,name=vpn,proto3" json:"vpn,omitempty"`
	// traceid
	Traceid string `protobuf:"bytes,16,opt,name=traceid,proto3" json:"traceid,omitempty"`
}

func (x *BaseParam) Reset() {
	*x = BaseParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseParam) ProtoMessage() {}

func (x *BaseParam) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseParam.ProtoReflect.Descriptor instead.
func (*BaseParam) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{13}
}

func (x *BaseParam) GetApp() string {
	if x != nil {
		return x.App
	}
	return ""
}

func (x *BaseParam) GetAv() string {
	if x != nil {
		return x.Av
	}
	return ""
}

func (x *BaseParam) GetDt() int32 {
	if x != nil {
		return x.Dt
	}
	return 0
}

func (x *BaseParam) GetDid() string {
	if x != nil {
		return x.Did
	}
	return ""
}

func (x *BaseParam) GetNt() int32 {
	if x != nil {
		return x.Nt
	}
	return 0
}

func (x *BaseParam) GetCh() string {
	if x != nil {
		return x.Ch
	}
	return ""
}

func (x *BaseParam) GetMd() string {
	if x != nil {
		return x.Md
	}
	return ""
}

func (x *BaseParam) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BaseParam) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *BaseParam) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BaseParam) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BaseParam) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BaseParam) GetBd() string {
	if x != nil {
		return x.Bd
	}
	return ""
}

func (x *BaseParam) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BaseParam) GetVpn() string {
	if x != nil {
		return x.Vpn
	}
	return ""
}

func (x *BaseParam) GetTraceid() string {
	if x != nil {
		return x.Traceid
	}
	return ""
}

type UserLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat  float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng  float64 `protobuf:"fixed64,2,opt,name=lng,proto3" json:"lng,omitempty"`
	Addr string  `protobuf:"bytes,3,opt,name=addr,proto3" json:"addr,omitempty"`
}

func (x *UserLocation) Reset() {
	*x = UserLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLocation) ProtoMessage() {}

func (x *UserLocation) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLocation.ProtoReflect.Descriptor instead.
func (*UserLocation) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{14}
}

func (x *UserLocation) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *UserLocation) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

func (x *UserLocation) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

type GiftStyle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 唯一ID：{giftid}_{amount}
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// 展示类型
	ShowType int32 `protobuf:"varint,2,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	// 特效资源链接
	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	// 黑白样式
	Portrait int32 `protobuf:"varint,4,opt,name=portrait,proto3" json:"portrait,omitempty"`
	// 彩色样式
	Landscape int32 `protobuf:"varint,5,opt,name=landscape,proto3" json:"landscape,omitempty"`
}

func (x *GiftStyle) Reset() {
	*x = GiftStyle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftStyle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftStyle) ProtoMessage() {}

func (x *GiftStyle) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftStyle.ProtoReflect.Descriptor instead.
func (*GiftStyle) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{15}
}

func (x *GiftStyle) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *GiftStyle) GetShowType() int32 {
	if x != nil {
		return x.ShowType
	}
	return 0
}

func (x *GiftStyle) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GiftStyle) GetPortrait() int32 {
	if x != nil {
		return x.Portrait
	}
	return 0
}

func (x *GiftStyle) GetLandscape() int32 {
	if x != nil {
		return x.Landscape
	}
	return 0
}

var File_common_proto protoreflect.FileDescriptor

var file_common_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x46, 0x0a, 0x0a, 0x49, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x75, 0x62, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x75, 0x62, 0x69, 0x64, 0x22, 0x2b, 0x0a, 0x07, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x0c, 0x0a, 0x01, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x01, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2c, 0x0a, 0x08, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x69,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x0c, 0x0a, 0x0a, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x22, 0x33, 0x0a, 0x0b, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x38, 0x0a, 0x0d, 0x53, 0x76, 0x63, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x22, 0x45, 0x0a, 0x0c, 0x53, 0x76, 0x63, 0x55, 0x73, 0x65, 0x72, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x29, 0x0a, 0x0d, 0x53, 0x76, 0x63, 0x55,
	0x73, 0x65, 0x72, 0x69, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x69, 0x64, 0x73, 0x22, 0x75, 0x0a, 0x0e, 0x53, 0x76, 0x63, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x38, 0x0a, 0x0f, 0x42, 0x69,
	0x7a, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a,
	0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04,
	0x62, 0x61, 0x73, 0x65, 0x22, 0x4b, 0x0a, 0x0a, 0x42, 0x69, 0x7a, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x4b, 0x0a, 0x0a, 0x42, 0x69, 0x7a, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x25, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x5d,
	0x0a, 0x0b, 0x42, 0x69, 0x7a, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa7, 0x02,
	0x0a, 0x09, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x61,
	0x70, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x61, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x61, 0x76, 0x12, 0x0e, 0x0a,
	0x02, 0x64, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x64, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x64, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x69, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x68, 0x12,
	0x0e, 0x0a, 0x02, 0x6d, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6d, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x6d, 0x65, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x62, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x70, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x69, 0x64, 0x22, 0x46, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x61,
	0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x22,
	0x86, 0x01, 0x0a, 0x09, 0x47, 0x69, 0x66, 0x74, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6f, 0x72, 0x74, 0x72, 0x61, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x6f, 0x72, 0x74, 0x72, 0x61, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x61,
	0x6e, 0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c,
	0x61, 0x6e, 0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2a, 0x4b, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0b, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x70, 0x61, 0x73, 0x73, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x66, 0x61, 0x6b, 0x65, 0x5f, 0x73,
	0x65, 0x6e, 0x64, 0x10, 0x04, 0x2a, 0x49, 0x0a, 0x0e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x75,
	0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x15, 0x0a, 0x11, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x20,
	0x0a, 0x1c, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x61, 0x6c, 0x65,
	0x5f, 0x6c, 0x69, 0x61, 0x6e, 0x78, 0x69, 0x66, 0x61, 0x6e, 0x67, 0x73, 0x68, 0x69, 0x10, 0x01,
	0x2a, 0x59, 0x0a, 0x0c, 0x53, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x12, 0x0a, 0x0e, 0x73, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x73, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x73, 0x68, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x73, 0x68, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x10, 0x03, 0x42, 0x1d, 0x5a, 0x1b, 0x78,
	0x69, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData = file_common_proto_rawDesc
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_proto_rawDescData)
	})
	return file_common_proto_rawDescData
}

var file_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_common_proto_goTypes = []interface{}{
	(AuditResult)(0),        // 0: common.AuditResult
	(AuditSubResult)(0),     // 1: common.AuditSubResult
	(ShieldResult)(0),       // 2: common.ShieldResult
	(*IdNameItem)(nil),      // 3: common.IdNameItem
	(*TestReq)(nil),         // 4: common.TestReq
	(*TestResp)(nil),        // 5: common.TestResp
	(*SvcBaseReq)(nil),      // 6: common.SvcBaseReq
	(*SvcBaseResp)(nil),     // 7: common.SvcBaseResp
	(*SvcCommonResp)(nil),   // 8: common.SvcCommonResp
	(*SvcUseridReq)(nil),    // 9: common.SvcUseridReq
	(*SvcUseridsReq)(nil),   // 10: common.SvcUseridsReq
	(*SvcUserPageReq)(nil),  // 11: common.SvcUserPageReq
	(*BizBaseParamReq)(nil), // 12: common.BizBaseParamReq
	(*BizBaseReq)(nil),      // 13: common.BizBaseReq
	(*BizPageReq)(nil),      // 14: common.BizPageReq
	(*BizBaseResp)(nil),     // 15: common.BizBaseResp
	(*BaseParam)(nil),       // 16: common.BaseParam
	(*UserLocation)(nil),    // 17: common.UserLocation
	(*GiftStyle)(nil),       // 18: common.GiftStyle
	(*anypb.Any)(nil),       // 19: google.protobuf.Any
}
var file_common_proto_depIdxs = []int32{
	7,  // 0: common.SvcCommonResp.base:type_name -> common.SvcBaseResp
	16, // 1: common.BizBaseParamReq.base:type_name -> common.BaseParam
	16, // 2: common.BizBaseReq.base:type_name -> common.BaseParam
	16, // 3: common.BizPageReq.base:type_name -> common.BaseParam
	19, // 4: common.BizBaseResp.data:type_name -> google.protobuf.Any
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdNameItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SvcBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SvcBaseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SvcCommonResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SvcUseridReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SvcUseridsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SvcUserPageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BizBaseParamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BizBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BizPageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BizBaseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftStyle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		EnumInfos:         file_common_proto_enumTypes,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_rawDesc = nil
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
