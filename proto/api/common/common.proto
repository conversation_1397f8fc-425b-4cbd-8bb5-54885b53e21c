syntax = "proto3";

package common;
option go_package = "xim/proto/api/common;common";

import "google/protobuf/any.proto";

message IdNameItem {
  string id = 1;
  string name = 2; // xxx/ddd
  string subid = 3;
}

message TestReq{
  uint32 i = 1;
  string data = 2;
}

message TestResp{
  uint32 i = 1;
  string data = 2;
}

message SvcBaseReq {

}

message SvcBaseResp {
  //错误码
  int32 code = 1;
  //错误信息
  string msg = 2;
}

message SvcCommonResp {
  common.SvcBaseResp base = 1;
}

message SvcUseridReq {
  int64 userid = 1;
  string last_index = 2;
}

message SvcUseridsReq {
  repeated int64 userids = 1;
}

message SvcUserPageReq {
  int64 userid = 1;
  int32 offset = 2;
  int32 limit = 3;
  string last_index = 4;
}

message BizBaseParamReq {
  BaseParam base = 1;
}

message BizBaseReq {
  BaseParam base = 1;
  int64 userId = 2;
}

message BizPageReq {
  BaseParam base = 1;
  //分页偏移量
  int32 offset = 2;
}

message BizBaseResp {
  //错误码
  int32 code = 1;
  //错误信息
  string msg = 2;
  google.protobuf.Any data = 3;
}

message BaseParam {
  //app 包名/域名
  string app = 1;
  //app_ver 版本号
  string av = 2;
  //dev_type 操作系统 1=android, 2=ios, 3=web
  int32 dt = 3;
  //dev_id 设备标识ID，唯一
  string did = 4;
  //net_type 网络类型 1: Wi-Fi 2: 2G或3G 3: 4G 4: 其他
  int32 nt = 5;
  //channel 安装渠道
  string ch = 6;
  //model 机型
  string md = 7;
  //os 安卓系统版本/IOS系统版本/UA
  string os = 8;
  //timestamp 时间戳
  int64 ts = 9;
  //IP
  string ip = 10;
  // imei
  string imei = 11;
  // oaid
  string oaid = 12;
  //brand 品牌
  string bd = 13;
  //iOS广告标识符
  string idfa = 14;
  //vpn 1开始，0关闭
  string vpn = 15;
  // traceid
  string traceid = 16;
}

message UserLocation {
  double lat = 1;
  double lng = 2;
  string addr = 3;
}

message GiftStyle {
  //唯一ID：{giftid}_{amount}
  string key = 1;
  //展示类型
  int32 show_type = 2;
  //特效资源链接
  string url = 3;
  //黑白样式
  int32 portrait = 4;
  //彩色样式
  int32 landscape = 5;
}

// 审核结果
enum AuditResult {
  // 默认，不使用
  unknown = 0;
  // 通过
  pass = 1;
  // 拒绝
  reject = 2;
  // 审核中
  review = 3;
  // 伪发送 (命中了拒绝并且一级标签是广告)
  fake_send = 4;
}

enum AuditSubResult {
    // 默认，不使用
    audit_sub_unknown = 0;
    // 联系方式拦截 男用户亲密的等级过低
    audit_sub_male_lianxifangshi = 1;
}

// shield 结果
enum ShieldResult {
  shield_unknown = 0; // 默认，不使用
  shield_pass = 1; //通过
  shield_reject=2; //拒绝
  shield_review=3; //
}

