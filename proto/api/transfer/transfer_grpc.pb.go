// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.4
// source: transfer.proto

package transfer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	svcchat "xim/proto/api/svcchat"
	svcfamily "xim/proto/api/svcfamily"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	S_MsgHandle_FullMethodName       = "/vc.transfer.s/MsgHandle"
	S_FamilyMsgHandle_FullMethodName = "/vc.transfer.s/FamilyMsgHandle"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 1v1 chat
	MsgHandle(ctx context.Context, in *svcchat.MsgHandleReq, opts ...grpc.CallOption) (*svcchat.MsgHandleResp, error)
	// // family chat
	FamilyMsgHandle(ctx context.Context, in *svcfamily.MsgHandleReq, opts ...grpc.CallOption) (*svcfamily.MsgHandleResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) MsgHandle(ctx context.Context, in *svcchat.MsgHandleReq, opts ...grpc.CallOption) (*svcchat.MsgHandleResp, error) {
	out := new(svcchat.MsgHandleResp)
	err := c.cc.Invoke(ctx, S_MsgHandle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) FamilyMsgHandle(ctx context.Context, in *svcfamily.MsgHandleReq, opts ...grpc.CallOption) (*svcfamily.MsgHandleResp, error) {
	out := new(svcfamily.MsgHandleResp)
	err := c.cc.Invoke(ctx, S_FamilyMsgHandle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 1v1 chat
	MsgHandle(context.Context, *svcchat.MsgHandleReq) (*svcchat.MsgHandleResp, error)
	// // family chat
	FamilyMsgHandle(context.Context, *svcfamily.MsgHandleReq) (*svcfamily.MsgHandleResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) MsgHandle(context.Context, *svcchat.MsgHandleReq) (*svcchat.MsgHandleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MsgHandle not implemented")
}
func (UnimplementedSServer) FamilyMsgHandle(context.Context, *svcfamily.MsgHandleReq) (*svcfamily.MsgHandleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FamilyMsgHandle not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_MsgHandle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcchat.MsgHandleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).MsgHandle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_MsgHandle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).MsgHandle(ctx, req.(*svcchat.MsgHandleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_FamilyMsgHandle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcfamily.MsgHandleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).FamilyMsgHandle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_FamilyMsgHandle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).FamilyMsgHandle(ctx, req.(*svcfamily.MsgHandleReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.transfer.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MsgHandle",
			Handler:    _S_MsgHandle_Handler,
		},
		{
			MethodName: "FamilyMsgHandle",
			Handler:    _S_FamilyMsgHandle_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "transfer.proto",
}
