// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: transfer.proto

package transfer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	svcchat "xim/proto/api/svcchat"
	svcfamily "xim/proto/api/svcfamily"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_transfer_proto protoreflect.FileDescriptor

var file_transfer_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0b, 0x76, 0x63, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x1a, 0x15, 0x73,
	0x76, 0x63, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x73, 0x76, 0x63, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2f,
	0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x95, 0x01, 0x0a, 0x01, 0x73, 0x12, 0x42, 0x0a, 0x09, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x12, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0f, 0x46, 0x61, 0x6d,
	0x69, 0x6c, 0x79, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x18, 0x5a, 0x16, 0x78, 0x69, 0x6d, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_transfer_proto_goTypes = []interface{}{
	(*svcchat.MsgHandleReq)(nil),    // 0: vc.svcchat.MsgHandleReq
	(*svcfamily.MsgHandleReq)(nil),  // 1: vc.svcfamily.MsgHandleReq
	(*svcchat.MsgHandleResp)(nil),   // 2: vc.svcchat.MsgHandleResp
	(*svcfamily.MsgHandleResp)(nil), // 3: vc.svcfamily.MsgHandleResp
}
var file_transfer_proto_depIdxs = []int32{
	0, // 0: vc.transfer.s.MsgHandle:input_type -> vc.svcchat.MsgHandleReq
	1, // 1: vc.transfer.s.FamilyMsgHandle:input_type -> vc.svcfamily.MsgHandleReq
	2, // 2: vc.transfer.s.MsgHandle:output_type -> vc.svcchat.MsgHandleResp
	3, // 3: vc.transfer.s.FamilyMsgHandle:output_type -> vc.svcfamily.MsgHandleResp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_transfer_proto_init() }
func file_transfer_proto_init() {
	if File_transfer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_transfer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_transfer_proto_goTypes,
		DependencyIndexes: file_transfer_proto_depIdxs,
	}.Build()
	File_transfer_proto = out.File
	file_transfer_proto_rawDesc = nil
	file_transfer_proto_goTypes = nil
	file_transfer_proto_depIdxs = nil
}
