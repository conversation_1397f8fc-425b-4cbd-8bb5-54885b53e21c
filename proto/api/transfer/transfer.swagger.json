{"swagger": "2.0", "info": {"title": "transfer.proto", "version": "version not set"}, "tags": [{"name": "s"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"basemsgtransferAtInfo": {"type": "object", "properties": {"atUserId": {"type": "string", "format": "int64", "title": "@all atUserId 需要占位给0"}, "text": {"type": "string"}, "style": {"type": "string"}}}, "basemsgtransferCustomElem": {"type": "object", "properties": {"data": {"type": "string"}, "description": {"type": "string"}, "extension": {"type": "string"}}}, "basemsgtransferImageElem": {"type": "object", "properties": {"imageUrl": {"type": "string"}, "objectId": {"type": "string"}, "width": {"type": "string", "format": "int64"}, "height": {"type": "string", "format": "int64"}, "size": {"type": "string", "format": "int64"}}}, "basemsgtransferMsgData": {"type": "object", "properties": {"localId": {"type": "string", "title": "客户端消息的唯一ID"}, "msgId": {"type": "string", "title": "服务端生成消息的唯一ID"}, "sessionId": {"type": "string", "title": "会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx"}, "from": {"type": "string", "format": "int64"}, "to": {"type": "string", "format": "int64"}, "groupId": {"type": "string", "format": "int64", "title": "群聊ID"}, "msgFrom": {"type": "integer", "format": "int64", "title": "0:IM消息;1:实时消息"}, "sessionType": {"type": "integer", "format": "int32", "title": "1 单聊，2 群聊，3 语音房， 4 系统通知"}, "contentType": {"type": "integer", "format": "int32", "title": "消息内容类型：Text:101,Image:102,Voice:103,Video:104,Custom:110,RealtimeVoice:115,RealTimeVideo:116"}, "content": {"type": "string", "title": "消息内容，一个json根据contentType解码到不同的结构体，见下面"}, "seq": {"type": "string", "format": "int64", "title": "消息序号"}, "sendTime": {"type": "string", "format": "int64", "title": "发送时间"}, "createTime": {"type": "string", "format": "int64", "title": "消息创建时间"}, "options": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "一个map,不配置默认为true。\nkey如下 history:是否推送历史消息\npersistent：是否永久存储消息\nofflinePush：离线是否走第三方推送"}, "offlinePushInfo": {"$ref": "#/definitions/basemsgtransferOfflinePushInfo", "title": "第三方推送配置"}, "atList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/basemsgtransferAtInfo"}, "title": "@用户列表\nrepeated  int64  atUserIdList = 21;"}, "upgrade": {"type": "boolean", "title": "如果是 true，客户端不支持，则提示升级。 否则不显示"}, "exts": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展字段"}, "contentTypeTextData": {"$ref": "#/definitions/basemsgtransferTextElem", "title": "contentType为101时候content解码的结构体"}, "contentTypeImageData": {"$ref": "#/definitions/basemsgtransferImageElem", "title": "contentType为102时候content解码的结构体"}, "contentTypeVoiceData": {"$ref": "#/definitions/basemsgtransferVoiceElem", "title": "contentType为103时候content解码的结构体"}, "contentTypeVideoData": {"$ref": "#/definitions/basemsgtransferVideoElem", "title": "contentType为104时候content解码的结构体"}, "contentTypeCustomData": {"$ref": "#/definitions/basemsgtransferCustomElem", "title": "contentType为110时候content解码的结构体"}, "SysMsg": {"type": "boolean", "title": "是否系统消息"}, "receiver": {"type": "string", "format": "int64", "title": "消息接受方"}, "isBot": {"type": "boolean", "title": "是否是bot消息"}}, "title": "发送和接收的消息结构体，type=2或type=4，对data进行json_decode后消息结构体", "required": ["localId", "sessionId", "to", "sessionType", "contentType", "content", "createTime", "receiver"]}, "basemsgtransferOfflinePushInfo": {"type": "object", "properties": {"title": {"type": "string"}, "desc": {"type": "string"}, "ext": {"type": "string"}, "IOSPushSound": {"type": "string"}, "IOSBadgeCount": {"type": "boolean"}, "signalInfo": {"type": "string"}}}, "basemsgtransferTextElem": {"type": "object", "properties": {"content": {"type": "string"}}}, "basemsgtransferVideoElem": {"type": "object", "properties": {"videoUrl": {"type": "string"}, "videoType": {"type": "string"}, "videoSize": {"type": "string", "format": "int64"}, "duration": {"type": "string", "format": "int64"}, "objectId": {"type": "string"}}}, "basemsgtransferVoiceElem": {"type": "object", "properties": {"voiceUrl": {"type": "string"}, "dataSize": {"type": "string", "format": "int64"}, "duration": {"type": "string", "format": "int64"}, "objectId": {"type": "string"}}}, "commonBaseParam": {"type": "object", "properties": {"app": {"type": "string", "title": "app 包名/域名"}, "av": {"type": "string", "title": "app_ver 版本号"}, "dt": {"type": "integer", "format": "int32", "title": "dev_type 操作系统 1=android, 2=ios, 3=web"}, "did": {"type": "string", "title": "dev_id 设备标识ID，唯一"}, "nt": {"type": "integer", "format": "int32", "title": "net_type 网络类型 1: Wi-Fi 2: 2G或3G 3: 4G 4: 其他"}, "ch": {"type": "string", "title": "channel 安装渠道"}, "md": {"type": "string", "title": "model 机型"}, "os": {"type": "string", "title": "os 安卓系统版本/IOS系统版本/UA"}, "ts": {"type": "string", "format": "int64", "title": "timestamp 时间戳"}, "loc": {"$ref": "#/definitions/commonUserLocation", "title": "location 地理位置"}, "ip": {"type": "string", "title": "IP"}, "aid": {"type": "string", "title": "安卓id 原文"}, "imei": {"type": "string", "title": "imei"}, "oaid": {"type": "string", "title": "oaid"}, "bd": {"type": "string", "title": "brand 品牌"}, "idfa": {"type": "string", "title": "iOS广告标识符"}, "sign": {"type": "string", "title": "接口参数签名"}, "smid": {"type": "string", "title": "数美设备id"}, "ua": {"type": "string", "title": "user agent"}, "lg": {"type": "string", "title": "语言"}, "cc": {"type": "string", "title": "国家"}, "tz": {"type": "string", "title": "时区"}, "vpn": {"type": "string", "title": "vpn 1开始，0关闭"}, "muuid": {"type": "string", "title": "muuid"}, "adjustId": {"type": "string", "title": "adjust_id"}, "gpsAdid": {"type": "string", "title": "gps_adid"}, "idfv": {"type": "string", "title": "idfv"}, "region": {"type": "integer", "format": "int32", "title": "region 1:southeast asia 2: latin america"}, "country": {"type": "string", "title": "country 注册国家码"}}}, "commonSvcBaseResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}, "msg": {"type": "string", "title": "错误信息"}}}, "commonUserLocation": {"type": "object", "properties": {"lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "addr": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\nExample 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\nExample 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "vcsvcchatMsgHandleResp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}, "exts": {"type": "object", "additionalProperties": {"type": "string"}}}}, "vcsvcfamilyMsgHandleResp": {"type": "object", "properties": {"base": {"$ref": "#/definitions/commonSvcBaseResp"}, "exts": {"type": "object", "additionalProperties": {"type": "string"}}, "topic": {"type": "string"}}}}}