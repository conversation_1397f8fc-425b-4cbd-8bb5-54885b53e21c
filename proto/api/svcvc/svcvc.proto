syntax = "proto3";

package  vc.svcvc;
option go_package = "xim/proto/api/svcvc";

import "common/common.proto";
import "basemsgtransfer/basemsgtransfer.proto";

message AudioMsgReq {
    common.BaseParam base = 1;
    basemsgtransfer.MsgData msg = 2;
 }
 
 message AudioMsgResp {
     common.SvcBaseResp base = 1;
     map<string, string> exts = 2;
 }
 
service s {
     rpc HandleAudioMsg(AudioMsgReq) returns (AudioMsgResp) {}
} 
