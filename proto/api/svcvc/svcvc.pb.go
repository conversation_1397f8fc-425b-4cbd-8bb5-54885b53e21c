// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: svcvc.proto

package svcvc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	basemsgtransfer "xim/proto/api/basemsgtransfer"
	common "xim/proto/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AudioMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.BaseParam        `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Msg  *basemsgtransfer.MsgData `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *AudioMsgReq) Reset() {
	*x = AudioMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcvc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudioMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioMsgReq) ProtoMessage() {}

func (x *AudioMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcvc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioMsgReq.ProtoReflect.Descriptor instead.
func (*AudioMsgReq) Descriptor() ([]byte, []int) {
	return file_svcvc_proto_rawDescGZIP(), []int{0}
}

func (x *AudioMsgReq) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AudioMsgReq) GetMsg() *basemsgtransfer.MsgData {
	if x != nil {
		return x.Msg
	}
	return nil
}

type AudioMsgResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Exts map[string]string   `protobuf:"bytes,2,rep,name=exts,proto3" json:"exts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AudioMsgResp) Reset() {
	*x = AudioMsgResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcvc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudioMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioMsgResp) ProtoMessage() {}

func (x *AudioMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcvc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioMsgResp.ProtoReflect.Descriptor instead.
func (*AudioMsgResp) Descriptor() ([]byte, []int) {
	return file_svcvc_proto_rawDescGZIP(), []int{1}
}

func (x *AudioMsgResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AudioMsgResp) GetExts() map[string]string {
	if x != nil {
		return x.Exts
	}
	return nil
}

var File_svcvc_proto protoreflect.FileDescriptor

var file_svcvc_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x73, 0x76, 0x63, 0x76, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x76, 0x63, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x63, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xa6, 0x01, 0x0a, 0x0c, 0x41, 0x75, 0x64,
	0x69, 0x6f, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x65, 0x78, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x76, 0x63, 0x2e, 0x41, 0x75, 0x64, 0x69,
	0x6f, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x45, 0x78, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x65, 0x78, 0x74, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x78, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x32, 0x46, 0x0a, 0x01, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x41, 0x75, 0x64, 0x69, 0x6f, 0x4d, 0x73, 0x67, 0x12, 0x15, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x76, 0x63, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a,
	0x16, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x76, 0x63, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x6f,
	0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x15, 0x5a, 0x13, 0x78, 0x69, 0x6d,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x76, 0x63, 0x76, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_svcvc_proto_rawDescOnce sync.Once
	file_svcvc_proto_rawDescData = file_svcvc_proto_rawDesc
)

func file_svcvc_proto_rawDescGZIP() []byte {
	file_svcvc_proto_rawDescOnce.Do(func() {
		file_svcvc_proto_rawDescData = protoimpl.X.CompressGZIP(file_svcvc_proto_rawDescData)
	})
	return file_svcvc_proto_rawDescData
}

var file_svcvc_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_svcvc_proto_goTypes = []interface{}{
	(*AudioMsgReq)(nil),             // 0: vc.svcvc.AudioMsgReq
	(*AudioMsgResp)(nil),            // 1: vc.svcvc.AudioMsgResp
	nil,                             // 2: vc.svcvc.AudioMsgResp.ExtsEntry
	(*common.BaseParam)(nil),        // 3: common.BaseParam
	(*basemsgtransfer.MsgData)(nil), // 4: vc.basemsgtransfer.MsgData
	(*common.SvcBaseResp)(nil),      // 5: common.SvcBaseResp
}
var file_svcvc_proto_depIdxs = []int32{
	3, // 0: vc.svcvc.AudioMsgReq.base:type_name -> common.BaseParam
	4, // 1: vc.svcvc.AudioMsgReq.msg:type_name -> vc.basemsgtransfer.MsgData
	5, // 2: vc.svcvc.AudioMsgResp.base:type_name -> common.SvcBaseResp
	2, // 3: vc.svcvc.AudioMsgResp.exts:type_name -> vc.svcvc.AudioMsgResp.ExtsEntry
	0, // 4: vc.svcvc.s.HandleAudioMsg:input_type -> vc.svcvc.AudioMsgReq
	1, // 5: vc.svcvc.s.HandleAudioMsg:output_type -> vc.svcvc.AudioMsgResp
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_svcvc_proto_init() }
func file_svcvc_proto_init() {
	if File_svcvc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_svcvc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudioMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_svcvc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudioMsgResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_svcvc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_svcvc_proto_goTypes,
		DependencyIndexes: file_svcvc_proto_depIdxs,
		MessageInfos:      file_svcvc_proto_msgTypes,
	}.Build()
	File_svcvc_proto = out.File
	file_svcvc_proto_rawDesc = nil
	file_svcvc_proto_goTypes = nil
	file_svcvc_proto_depIdxs = nil
}
