package model

import (
	"fmt"
	"xim/baselib/server/env"
	"xim/baselib/util"
)

type HistoryMsgModel struct {
	Id        int64  `json:"id" xorm:"id" gorm:"primaryKey;column:id"`
	SessionId string `json:"sessionId" xorm:"sessionId" gorm:"sessionId"`
	MsgData   string `json:"msgData" xorm:"msgData" gorm:"msgData"`
	Ct        int64  `json:"ct" xorm:"ct" gorm:"ct"`
}

func (HistoryMsgModel) TableName(sessionId string) string {
	if env.IsProd() {
		return fmt.Sprintf("history_msg_%03d", util.GetHashCode(sessionId)%100)
	}
	return "history_msg"
}
