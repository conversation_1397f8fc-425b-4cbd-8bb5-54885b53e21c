package model

import "xim/proto/api/basemsgtransfer"

const (
	SingleGocGroupMsgNum = 100
	MsgTableName         = "msg_group"
)

type MsgDataModel struct {
	Receiver    int64           `bson:"receiver"`
	LocalId     string          `bson:"localId"`
	MsgId       string          `bson:"msgId"`
	GroupID     int64           `bson:"groupId,omitempty"`
	SessionType int32           `bson:"sessionType"` // 1 单聊，2 群聊，3 语音房， 4 系统通知
	SessionId   string          `bson:"sessionId"`   // 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
	From        int64           `bson:"from"`
	To          int64           `bson:"to"`
	MsgFrom     uint32          `bson:"msgFrom"`
	ContentType int32           `bson:"contentType"`
	Content     string          `bson:"content"`
	Seq         int64           `bson:"seq"`
	SendTime    int64           `bson:"sendTime"`
	CreateTime  int64           `bson:"createTime"`
	Options     map[string]bool `bson:"options,omitempty"`
	// AtUserIdList []int64           `bson:"atUserIdList,omitempty"`
	AtList  []*basemsgtransfer.AtInfo `bson:"atList,omitempty"`
	Exts    map[string]string         `bson:"exts,omitempty"`
	WaitAck int32                     `bson:"waitAck,omitempty"`
}

// type Msg1v1Model struct {
// 	Receiver int64         `bson:"receiver"`
// 	Seq      int64         `bson:"seq"`
// 	Msg      *MsgDataModel `bson:"msg"`
// }

// type MsgInfoModel struct {
// 	Msg *MsgDataModel `bson:"msg"`
// }

type GroupMsgModel struct {
	GroupId int64           `bson:"groupId"`
	DocID   int64           `bson:"docId"`
	Msgs    []*MsgDataModel `bson:"msgs"`
}

func (GroupMsgModel) TableName() string {
	return MsgTableName
}

func GetDocID(seq int64) int64 {
	return (seq - 1) / SingleGocGroupMsgNum

}

func GetMsgIndex(seq int64) int64 {
	return (seq - 1) % SingleGocGroupMsgNum
}
