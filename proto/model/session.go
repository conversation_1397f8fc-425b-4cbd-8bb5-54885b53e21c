package model

type SessionModel struct {
	SessionId   string `bson:"sessionId"`
	SessionType int32  `bson:"sessionType"`
	OwnUserid   int64  `bson:"ownUserid"`
	PeerId      int64  `bson:"peerId"`
	GroupId     int64  `bson:"groupId"`
	RecvMsgOpt  int32  `bson:"recvMsgOpt"`
	IsPinned    bool   `bson:"isPinned"`
	Ct          int64  `bson:"ct"`
	MsgUt       int64  `bson:"msgUt"`
	Remark      string `bson:"remark"`
}
