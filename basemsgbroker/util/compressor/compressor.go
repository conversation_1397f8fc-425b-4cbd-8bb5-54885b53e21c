package compressor

import (
	"bytes"
	"io/ioutil"

	"github.com/klauspost/compress/flate"
	"github.com/klauspost/compress/gzip"
)

type Compressor interface {
	Compress([]byte) ([]byte, error)
	Uncompress([]byte) ([]byte, error)
	String() string
}

var (
	Gziper   Compressor
	Deflater Compressor
)

func init() {
	Gziper = GzipCompressor{}
	Deflater = DeflateCompressor{}
}

type GzipCompressor struct{}

func (c GzipCompressor) Compress(i []byte) (o []byte, err error) {
	var buf bytes.Buffer
	w := gzip.NewWriter(&buf)
	defer w.Close()

	_, err = w.Write(i)
	if err != nil {
		return
	}

	err = w.Flush()
	if err != nil {
		return
	}

	o = buf.Bytes()
	return
}

func (c GzipCompressor) Uncompress(i []byte) (o []byte, err error) {
	r, err := gzip.NewReader(bytes.NewReader(i))
	if err != nil {
		return
	}

	defer r.Close()
	o, _ = ioutil.ReadAll(r)

	return
}

func (c GzipCompressor) String() string {
	return "gzip"
}

type DeflateCompressor struct{}

func (c DeflateCompressor) Compress(i []byte) (o []byte, err error) {
	var buf bytes.Buffer
	w, err := flate.NewWriter(&buf, 6)
	if err != nil {
		return
	}

	defer w.Close()
	_, err = w.Write(i)
	if err != nil {
		return
	}

	w.Flush()

	o = buf.Bytes()
	return
}

func (c DeflateCompressor) Uncompress(i []byte) (o []byte, err error) {
	r := flate.NewReader(bytes.NewReader(i))
	defer r.Close()
	o, _ = ioutil.ReadAll(r)
	return
}

func (c DeflateCompressor) String() string {
	return "deflate"
}
