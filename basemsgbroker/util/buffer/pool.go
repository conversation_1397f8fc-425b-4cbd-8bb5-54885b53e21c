package buffer

import (
	"sync"
	"sync/atomic"
)

const (
	maxBufferLength = 685536
)

// Buffer buffer
type Buffer struct {
	buf   []byte
	count int32
}

func newBuffer() *Buffer {
	return &Buffer{buf: make([]byte, maxBufferLength), count: 1}
}

// Refer buffer引用次数+1
func (b *Buffer) Refer() {
	atomic.AddInt32(&b.count, 1)
}

// Derefer buffer引用次数-1
func (b *Buffer) Derefer() {
	count := atomic.AddInt32(&b.count, -1)
	if count <= 0 {
		bufferPool.Put(b)
	}
}

// Slice buffer切片
func (b *Buffer) Slice(start, end int) []byte {
	return b.buf[start:end]
}

// Split 分割buffer
func (b *Buffer) Split(n int) ([]byte, []byte) {
	return b.buf[:n], b.buf[n:]
}

var bufferPool = sync.Pool{
	New: func() interface{} {
		return newBuffer()
	},
}

// New 从pool中获取一个buffer
func New() *Buffer {
	return bufferPool.Get().(*Buffer)
}
