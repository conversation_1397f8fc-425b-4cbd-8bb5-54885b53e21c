package util

import "sync"

// StopChannel 该类用于控制协程停止
// 满足可重入，channel安全访问
type StopChannel struct {
	ch     chan int
	isStop bool
	locker sync.Mutex
}

// NewStopChannel 构造函数
func NewStopChannel() *StopChannel {
	sc := &StopChannel{
		ch: make(chan int, 1),
	}
	sc.isStop = false
	return sc
}

// Close 销毁函数
func (c *StopChannel) Close() {
	c.locker.Lock()
	defer c.locker.Unlock()
	select {
	case _, ok := <-c.ch:
		if !ok {
			return
		}
	default:
	}
	close(c.ch)
}

// Stop 停止命令
func (c *StopChannel) Stop() {
	c.locker.Lock()
	defer c.locker.Unlock()

	// 确保未被关闭
	select {
	case _, ok := <-c.ch:
		if !ok {
			return
		}
	default:
	}

	// 确保不会阻塞
	select {
	case c.ch <- 1:
	default:
	}
}

// IsStop 是否停止
func (c *StopChannel) IsStop() bool {
	if c.isStop == true {
		return c.isStop
	}
	select {
	case <-c.ch:
		c.isStop = true
		return true
	default:
		return false
	}
}
