package util

/**
通用方法定义
*/

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"xim/common/header"

	"github.com/hashicorp/go-uuid"
	jsoniter "github.com/json-iterator/go"
	"github.com/json-iterator/go/extra"
	"golang.org/x/net/context"
)

var (
	Json  = jsoniter.ConfigCompatibleWithStandardLibrary
	msgId uint32
)

func init() {
	extra.RegisterFuzzyDecoders()
}

// 字符串时间（格式 YYYY-MM-DD HH:mm:ss）转 Unix 时间戳
func ParseUnixTimestamp(srcTime string) int64 {
	if srcTime == "0000-00-00 00:00:00" || srcTime == "0" {
		return int64(0)
	}
	timeLayout := "2006-01-02 15:04:05"                          //转化所需模板
	loc, _ := time.LoadLocation("Local")                         //重要：获取时区
	theTime, _ := time.ParseInLocation(timeLayout, srcTime, loc) //使用模板在对应时区转化为time.time类型
	return int64(theTime.Unix())
}

// Unix 时间戳转为字符串（格式 YYYY-MM-DD HH:mm:ss）
func FormatUnixTimestamp(ts int64) string {
	timeLayout := "2006-01-02 15:04:05"
	t := time.Unix(ts, 0)
	return t.Format(timeLayout)
}

// 字符串和整形相互转换
func ParseInt64(s string) (int64, error) {
	value, err := strconv.ParseInt(s, 10, 64)
	return value, err
}
func ParseUint64(s string) (uint64, error) {
	value, err := strconv.ParseUint(s, 10, 64)
	return uint64(value), err
}
func ParseInt32(s string) (int32, error) {
	value, err := strconv.ParseInt(s, 10, 32)
	return int32(value), err
}
func ParseUint32(s string) (uint32, error) {
	value, err := strconv.ParseUint(s, 10, 32)
	return uint32(value), err
}
func ParseInt(s string) (int, error) {
	value, err := strconv.ParseUint(s, 10, 32)
	return int(value), err
}

func FormatInt64(i int64) string {
	value := strconv.FormatInt(i, 10)
	return value
}
func FormatUint64(i uint64) string {
	value := strconv.FormatUint(i, 10)
	return value
}
func FormatInt32(i int32) string {
	value := strconv.FormatInt(int64(i), 10)
	return value
}
func FormatUint32(i uint32) string {
	value := strconv.FormatUint(uint64(i), 10)
	return value
}

// 获取key的cookie值
func GetCookieValue(ctx context.Context, cookleKey string) string {
	headerData := header.NewHeader(ctx)
	return headerData.GetCookie(cookleKey)
}

func GetMsgId() uint16 {
	cur := atomic.AddUint32(&msgId, 1)
	res := uint16(cur % (math.MaxUint16 + 1))
	return res
}

func XcEncodeCode(codest XcCodeSt) (code string, err error) {
	bs, _ := Json.Marshal(codest)
	bs, err = XcEncrypt(bs)
	if err != nil {
		return
	}

	code = hex.EncodeToString(bs)
	return
}
func XcDecodeCode(code string) (codest XcCodeSt, err error) {
	bs, err := hex.DecodeString(code)
	if err != nil {
		return
	}

	bs, err = XcDecrypt(bs)
	if err != nil {
		return
	}

	err = Json.Unmarshal(bs, &codest)
	return
}
func XcEncrypt(origData []byte) ([]byte, error) {
	block, err := aes.NewCipher(xcCodeEncryptKey)
	if err != nil {
		return nil, err
	}

	//AES分组长度为128位，所以blockSize=16，单位字节
	blockSize := block.BlockSize()
	origData = XcPKCS5Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, xcCodeEncryptKey[:blockSize]) //初始向量的长度必须等于块block的长度16字节
	crypted := make([]byte, len(origData))
	blockMode.CryptBlocks(crypted, origData)
	return crypted, nil
}
func XcDecrypt(crypted []byte) ([]byte, error) {
	block, err := aes.NewCipher(xcCodeEncryptKey)
	if err != nil {
		return nil, err
	}

	//AES分组长度为128位，所以blockSize=16，单位字节
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, xcCodeEncryptKey[:blockSize]) //初始向量的长度必须等于块block的长度16字节
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = XcPKCS5UnPadding(origData)
	return origData, nil
}

// 填充明文
func XcPKCS5Padding(plaintext []byte, blockSize int) []byte {
	padding := blockSize - len(plaintext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(plaintext, padtext...)
}

// 去除填充数据
func XcPKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

func ClientIP(r *http.Request) string {
	clientIP := r.Header.Get("X-Forwarded-For")
	if len(clientIP) == 0 {
		clientIP = r.Header.Get("X-Real-IP")
	}

	if len(clientIP) == 0 {
		ipS := strings.Split(r.RemoteAddr, ":")
		if len(ipS) > 0 {
			if ipS[0] != "[" {
				return ipS[0]
			}
		}
		return "127.0.0.1"
	} else {
		idx := strings.Index(clientIP, ",")
		if idx > 0 {
			clientIP = clientIP[:idx]
		}
	}

	return clientIP
}

// 生成消息id
func Genmsgid() string {
	s, _ := uuid.GenerateUUID()
	return fmt.Sprintf("%x", md5.Sum([]byte(s)))[:12]
}

func GetXCSessionId(deviceId, userId string) string {
	return fmt.Sprintf("%s@%s", deviceId, userId)
}
