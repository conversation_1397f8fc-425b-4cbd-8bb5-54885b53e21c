package message

import (
	"encoding/json"
	"fmt"

	"xim/basemsgbroker/util/mqtt"
)

// Message 推送消息
type Message struct {
	mqtt.Message
	Closing bool //发送成功后关闭连接
}

type XcWsMsgType int8

const (
	// 长连接初始化消息
	WsMsgTypeInit XcWsMsgType = 1
	// 长连接初始化消息响应
	WsMsgTypeInitResp XcWsMsgType = 2
	// 业务消息
	WsMsgTypeBiz XcWsMsgType = 3
	// ack消息
	WsMsgTypeAck XcWsMsgType = 101
	// echo消息，用于测试
	WsMsgTypeEcho XcWsMsgType = 102
)

func (m XcWsMsgType) isvalid() bool {
	return m == WsMsgTypeInit ||
		m == WsMsgTypeInitResp ||
		m == WsMsgTypeBiz ||
		m == WsMsgTypeAck ||
		m == WsMsgTypeEcho
}
func (m XcWsMsgType) musthavedata() bool {
	return m == WsMsgTypeInit
}

// 小川科技的长连接消息结构
type XcWsMsg struct {
	Id        string          `json:"id"`            //消息id
	Type      XcWsMsgType     `json:"t"`             //消息类型
	Ack       int8            `json:"ack,omitempty"` //是否要回ack
	ChannelId string          `json:"cid,omitempty"` //频道名
	Data      json.RawMessage `json:"d,omitempty"`   // data字段为上下游业务方的协议字段，对于liverpool是透明的
}

func (m *XcWsMsg) Validate() error {
	if m == nil {
		return fmt.Errorf("XcWsMsg is null")
	}

	if !m.Type.isvalid() {
		return fmt.Errorf("unknown XcWsMsg type: %v", m.Type)
	}

	if m.Type.musthavedata() && len(m.Data) == 0 {
		return fmt.Errorf("XcWsMsg type %v must have data", m.Type)
	}

	switch m.Type {
	case WsMsgTypeBiz:
		if m.ChannelId == "" {
			return fmt.Errorf("empty chanelid for bizmsg")
		}
	}

	return nil
}

// 小川科技的长连接初始化消息结构
type XcWsInitMsgData struct {
	// 历史原因导致sessionid和session_id两个字段，服务器都兼容，这里sessionid等同于用户id+设备deviceid
	Sessionid       string `json:"sessionid"`
	SessionId       string `json:"session_id"`
	ContentEncoding string `json:"content_encoding,omitempty"`
	AcceptEncoding  string `json:"accept_encoding,omitempty"`
	PingInterval    int    `json:"ping_interval"`
	Code            string `json:"code"`
}
