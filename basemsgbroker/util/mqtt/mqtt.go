package mqtt

// MQTT协议定义：http://docs.oasis-open.org/mqtt/mqtt/v3.1.1/os/mqtt-v3.1.1-os.html

// MQTT Control Packet types
const (
	TypeReserved = uint8(iota)
	TypeConnect
	TypeConnack
	TypePublish
	TypePuback
	TypePubrec
	TypePubrel
	TypePubcomp
	TypeSubscribe
	TypeSuback
	TypeUnsubscribe
	TypeUnsuback
	TypePingreq
	TypePingresp
	TypeDisconnect
)

// Header publish package flag
type Header struct {
	Type   uint8
	DUP    bool
	QoS    uint8
	Retain bool
	Length uint32
}

// Connect MQTT connect packet
// After a Network Connection is established by a Client to a Server, the first packet sent from the Client to the Server MUST be a CONNECT packet [MQTT-3.1.0-1].
// A Client can only send the CONNECT packet once over a Network Connection.
// The Server MUST process a second CONNECT packet sent from a Client as a Protocol Error and close the Network Connection [MQTT-3.1.0-2].
// The Payload contains one or more encoded fields. They specify a unique Client identifier for the Client, a Will Topic, Will Payload, User Name and Password.
// All but the Client identifier can be omitted and their presence is determined based on flags in the Variable Header.
type Connect struct {
	Header
	// variable header
	ProtoName    []byte
	Version      uint8
	UsernameFlag bool
	PasswordFlag bool
	WillRetain   bool
	WillQoS      uint8
	WillFlag     bool
	CleanStart   bool
	KeepAlive    uint16
	// payload
	ClientID    []byte
	WillTopic   []byte
	WillMessage []byte
	Username    []byte
	Password    []byte
}

// Type 类型
func (c *Connect) Type() uint8 {
	return TypeConnect
}
func (c *Connect) TypeName() string {
	return "TypeConnect"
}

// connack 错误码
const (
	ConnackUnacceptable          = 0x01
	ConnackIdentifierRejected    = 0x02
	ConnackServerUnavailable     = 0x03
	ConnackBadUserNameOrPassword = 0x04
	ConnackNotAuthorized         = 0x05
)

// Connack MQTT connack packet
// The CONNACK packet is the packet sent by the Server in response to a CONNECT packet received from a Client.
// The Server MUST send a CONNACK with a 0x00 (Success) Reason Code before sending any Packet other than AUTH [MQTT-3.2.0-1].
// The Server MUST NOT send more than one CONNACK in a Network Connection [MQTT-3.2.0-2].
// If the Client does not receive a CONNACK packet from the Server within a reasonable amount of time, the Client SHOULD close the Network Connection.
// A "reasonable" amount of time depends on the type of application and the communications infrastructure.
type Connack struct {
	Header
	// variable header
	AckFlag    uint8
	ReasonCode uint8
}

// Type 类型
func (c *Connack) Type() uint8 {
	return TypeConnack
}
func (c *Connack) TypeName() string {
	return "TypeConnack"
}
// Publish MQTT publish packet
// A PUBLISH packet is sent from a Client to a Server or from a Server to a Client to transport an Application Message.
type Publish struct {
	Header
	// variable header
	Topic     []byte
	MessageID uint16
	// payload
	Payload []byte
}

// Type 类型
func (p *Publish) Type() uint8 {
	return TypePublish
}
func (p *Publish) TypeName() string {
	return "TypePublish"
}

// Puback MQTT puback packet
// A PUBACK packet is the response to a PUBLISH packet with QoS 1.
type Puback struct {
	Header
	// variable header
	MessageID  uint16
	ReasonCode uint8
}

// Type 类型
func (p *Puback) Type() uint8 {
	return TypePuback
}
func (p *Puback) TypeName() string {
	return "TypePuback"
}

// Pubrec MQTT pubrec packet
// A PUBREC packet is the response to a PUBLISH packet with QoS 2.
// It is the second packet of the QoS 2 protocol exchange.
type Pubrec struct {
	Header
	MessageID  uint16
	ReasonCode uint8
}

// Type 类型
func (p *Pubrec) Type() uint8 {
	return TypePubrec
}
func (p *Pubrec) TypeName() string {
	return "TypePubrec"
}

// Pubrel MQTT pubrel packet
// A PUBREL packet is the response to a PUBREC packet.
// It is the third packet of the QoS 2 protocol exchange.
type Pubrel struct {
	Header
	// variable header
	MessageID  uint16
	ReasonCode uint8
}

// Type 类型
func (p *Pubrel) Type() uint8 {
	return TypePubrel
}
func (p *Pubrel) TypeName() string {
	return "TypePubrel"
}

// Pubcomp MQTT pubcomp packet
// The PUBCOMP packet is the response to a PUBREL packet.
// It is the fourth and final packet of the QoS 2 protocol exchange.
type Pubcomp struct {
	Header
	// variable header
	MessageID  uint16
	ReasonCode uint8
}

// Type 类型
func (p *Pubcomp) Type() uint8 {
	return TypePubcomp
}
func (p *Pubcomp) TypeName() string {
	return "TypePubcomp"
}

// Subscribe MQTT subscribe packet
// The SUBSCRIBE packet is sent from the Client to the Server to create one or more Subscriptions.
// Each Subscription registers a Client’s interest in one or more Topics.
// The Server sends PUBLISH packets to the Client to forward Application Messages that were published to Topics that match these Subscriptions.
// The SUBSCRIBE packet also specifies (for each Subscription) the maximum QoS with which the Server can send Application Messages to the Client.
type Subscribe struct {
	Header
	// variable header
	MessageID uint16
	// payload
	Subscriptions []TopicFilter
}

// TopicFilter unsubscribe和subscribe的topic
type TopicFilter struct {
	Topic []byte
	Qos   uint8
}

// Type 类型
func (s *Subscribe) Type() uint8 {
	return TypeSubscribe
}
func (s *Subscribe) TypeName() string {
	return "TypeSubscribe"
}

// Suback MQTT suback packet
// A SUBACK packet is sent by the Server to the Client to confirm receipt and processing of a SUBSCRIBE packet.
// A SUBACK packet contains a list of Reason Codes, that specify the maximum QoS level that was granted or the error which was found for each Subscription that was requested by the SUBSCRIBE.
type Suback struct {
	Header
	// variable header
	MessageID uint16
	// payload
	ReasonCodes []uint8
}

// Type 类型
func (s *Suback) Type() uint8 {
	return TypeSuback
}
func (s *Suback) TypeName() string {
	return "TypeSuback"
}

// Unsubscribe MQTT unsubscribe packet
// An UNSUBSCRIBE packet is sent by the Client to the Server, to unsubscribe from topics.
type Unsubscribe struct {
	Header
	// variable header
	MessageID uint16
	// payload
	Topics [][]byte
}

// Type 类型
func (u *Unsubscribe) Type() uint8 {
	return TypeUnsubscribe
}
func (u *Unsubscribe) TypeName() string {
	return "TypeUnsubscribe"
}

// Unsuback MQTT unsuback packet
// The UNSUBACK packet is sent by the Server to the Client to confirm receipt of an UNSUBSCRIBE packet.
type Unsuback struct {
	Header
	// variable header
	MessageID uint16
	// payload
	ReasonCodes []uint8
}

// Type 类型
func (u *Unsuback) Type() uint8 {
	return TypeUnsuback
}
func (u *Unsuback) TypeName() string {
	return "TypeUnsuback"
}

// Pingreq MQTT pingreq packet
// The PINGREQ packet is sent from a Client to the Server. It can be used to:
//  ·Indicate to the Server that the Client is alive in the absence of any other MQTT Control Packets being sent from the Client to the Server.
//  ·Request that the Server responds to confirm that it is alive.
//  ·Exercise the network to indicate that the Network Connection is active.
// This packet is used in Keep Alive processing.
type Pingreq struct {
}

// Type 类型
func (p *Pingreq) Type() uint8 {
	return TypePingreq
}
func (p *Pingreq) TypeName() string {
	return "TypePingreq"
}

// Pingresp MQTT pingresp packet
// A PINGRESP Packet is sent by the Server to the Client in response to a PINGREQ packet. It indicates that the Server is alive.
// This packet is used in Keep Alive processing.
type Pingresp struct {
}

// Type 类型
func (p *Pingresp) Type() uint8 {
	return TypePingresp
}
func (p *Pingresp) TypeName() string {
	return "TypePingresp"
}

// Disconnect MQTT disconnect packet
// The DISCONNECT packet is the final MQTT Control Packet sent from the Client or the Server.
// It indicates the reason why the Network Connection is being closed.
// The Client or Server MAY send a DISCONNECT packet before closing the Network Connection.
// If the Network Connection is closed without the Client first sending a DISCONNECT packet with Reason Code 0x00 (Normal disconnection) and the Connection has a Will Message, the Will Message is published.
type Disconnect struct {
	Header
	// variable header
	ReasonCode uint8
}

// Type 类型
func (d *Disconnect) Type() uint8 {
	return TypeDisconnect
}
func (d *Disconnect) TypeName() string {
	return "TypeDisconnect"
}

// Message MQTT消息
type Message interface {
	Type() uint8
	TypeName() string
}
