package util

import (
	"fmt"
	"sync"
)

// 错误码
const (
	RetOK                 = 0
	RetInvalidParam       = 180100 // 参数错误
	RetClientTimeout      = 180101 // 客户端超时
	RetWsMessageTypeErr   = 180102 // websocket消息类型错误
	RetMessageTooLarge    = 180103 // 消息过长
	RetMessageBadPacket   = 180104 // 消息结构错误
	RetMqttMessageTypeErr = 180105 // mqtt消息类型错误
	RetSessionClosed      = 180106 // session已关闭
	RetSessionChannelFull = 180107 // session推送channel已满
	RetSessionUncheck     = 180108 // session未校验
	RetPublishMessageErr  = 180109 // 发布下行消息失败
	RetKickMessageErr     = 180110 // 发布下线消息失败
	RetUserTokenError     = 180111 // 用户客户端token参数错误
)

var errorMsg = map[int32]string{
	RetOK:                 "success",
	RetInvalidParam:       "parameter error",
	RetClientTimeout:      "client Timeout",
	RetWsMessageTypeErr:   "websocket wrong message type",
	RetMessageTooLarge:    "message is too long",
	RetMessageBadPacket:   "Wrong message structure",
	RetMqttMessageTypeErr: "mqtt Wrong message type",
	RetSessionClosed:      "session closed",
	RetSessionChannelFull: "session push channel full",
	RetSessionUncheck:     "session unchecked",
	RetPublishMessageErr:  "failed to publish downlink message",
	RetKickMessageErr:     "failed to post downline message",
	RetUserTokenError:     "wrong user token parameter",
}

var errors sync.Map

func init() {
	for s, m := range errorMsg {
		errors.Store(s, &ConnError{Errno: s, Err: m})
	}
}

// 长连接服务错误类型
type ConnError struct {
	Errno int32
	Err   string
}

// New 创建错误
func NewError(code int32, msg ...string) (err error) {
	if len(msg) > 0 {
		err = &ConnError{Errno: code, Err: msg[0]}
		return
	}
	ce, _ := errors.LoadOrStore(code, &ConnError{Errno: code, Err: "unknown error"})
	err = ce.(*ConnError)
	return
}

// Error 错误描述
func (e *ConnError) Error() string {
	return fmt.Sprintf("[error-%d]%s", e.Errno, e.Err)
}
