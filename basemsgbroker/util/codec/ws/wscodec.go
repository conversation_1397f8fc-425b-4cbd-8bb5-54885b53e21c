package ws

import (
	"fmt"
	"io"

	log "xim/baselib/logger"

	"xim/basemsgbroker/util"
	"xim/basemsgbroker/util/codec"
	"xim/basemsgbroker/util/message"
	"xim/basemsgbroker/util/mqtt"
)

type WsCodec struct{}

type BizWsMsg struct {
	Topic   string `json:"topicName"`
	MsgID   uint64 `json:"messageId"`
	Payload string `json:"payload"`
	Ack     uint8  `json:"ack"`
}

// Decode webscoekt解码, 全部解析转换为Publish消息
func (c *WsCodec) Decode(r codec.Reader) (mqtt.Message, int, error) {
	data, err := io.ReadAll(r)
	if err != nil {
		return nil, 0, err
	}
	if len(data) < 1 {
		return nil, 0, nil
	}
	log.Debugf("decoded wsmsg: %s", string(data))
	bizMsg := BizWsMsg{}
	err = util.Json.Unmarshal(data, &bizMsg)
	if err != nil {
		return nil, len(data), fmt.Errorf("unmarshal %s error %v", string(data), err)
	}
	var qos uint8
	if bizMsg.Ack > 0 {
		qos = 1
	}
	msg := &mqtt.Publish{
		Header:    mqtt.Header{QoS: qos},
		Topic:     []byte(bizMsg.Topic),
		MessageID: uint16(bizMsg.MsgID),
		Payload:   []byte(bizMsg.Payload),
	}
	return msg, len(data), nil
}

// Encode webscoekt编码
func (c *WsCodec) Encode(w codec.Writer, message *message.Message) (int, error) {
	pubMsg, ok := message.Message.(*mqtt.Publish)
	//修复消息类型不是mqtt.Publish的bug
	if !ok {
		return 0, nil
	}
	var ack uint8
	if pubMsg.QoS > 0 {
		ack = 1
	}
	bizMsg := BizWsMsg{
		Topic:   string(pubMsg.Topic),
		MsgID:   uint64(pubMsg.MessageID),
		Payload: string(pubMsg.Payload),
		Ack:     ack,
	}
	data, err := util.Json.Marshal(bizMsg)
	if err != nil {
		return 0, err
	}
	log.Debugf("encode wsmsg: %s", string(data))
	return w.Write(data)
}
