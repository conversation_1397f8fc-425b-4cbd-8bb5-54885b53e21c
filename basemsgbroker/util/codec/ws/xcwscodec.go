package ws

import (
	"io/ioutil"

	log "xim/baselib/logger"
	"xim/basemsgbroker/util/codec"
	"xim/basemsgbroker/util/compressor"
	"xim/basemsgbroker/util/message"
	"xim/basemsgbroker/util/mqtt"
)

const (
	MsgWrite_Direct = "direct" //消息的发送给方式为直接发送，不需要任何编码，即使编码器有设置
)

type XcWsCodec struct {
	Compressor compressor.Compressor
}

func (c *XcWsCodec) Decode(r codec.Reader) (mqtt.Message, int, error) {
	msgdata, err := ioutil.ReadAll(r)
	if err != nil {
		return nil, 0, err
	}
	if len(msgdata) == 0 {
		return nil, 0, nil
	}
	datalen := len(msgdata)
	if c.Compressor != nil {
		msgdata, err = c.Compressor.Uncompress(msgdata)
		if err != nil {
			// 如果有解压器，但是解压失败，也不返回错误，直接忽略此消息数据
			return nil, datalen, nil
		}
	}
	log.Debugf("decode xcwsmsg: %s", string(msgdata))
	msg := &mqtt.Publish{
		Topic:   []byte{},
		Payload: msgdata, //主要是json包数据
	}
	return msg, datalen, nil
}

func (c *XcWsCodec) Encode(w codec.Writer, message *message.Message) (int, error) {
	pubMsg, ok := message.Message.(*mqtt.Publish)
	//修复消息类型不是mqtt.Publish的bug
	if !ok {
		return 0, nil
	}
	data := pubMsg.Payload //主要是json包数据
	topic := ""
	if len(pubMsg.Topic) > 0 {
		topic = string(pubMsg.Topic)
	}
	var err error
	log.Debugf("encode xcwsmsg(json): %s, (byte): %x", string(data), data)
	//下发的数据，默认根据compressor判断是否压缩，除非要求直接发送
	if topic != MsgWrite_Direct && c.Compressor != nil {
		data, err = c.Compressor.Compress(data)
		if err != nil {
			return 0, err
		}
	}
	return w.Write(data)
}
