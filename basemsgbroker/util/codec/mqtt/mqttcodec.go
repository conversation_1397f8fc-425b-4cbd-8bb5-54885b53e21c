package mqtt

import (
	"io"

	log "xim/baselib/logger"

	"xim/basemsgbroker/util"
	"xim/basemsgbroker/util/buffer"
	"xim/basemsgbroker/util/codec"
	"xim/basemsgbroker/util/message"
	"xim/basemsgbroker/util/mqtt"
)

const (
	maxHeaderSize  = 6      // max MQTT header size
	maxMessageSize = 685536 // max MQTT message size
)

// Codec 标准mqtt编解码
type MqttCodec struct {
}

// Decode mqtt解码
func (c *MqttCodec) Decode(r codec.Reader) (mqtt.Message, int, error) {
	header, headLen, err := decodeHeader(r)
	if err != nil {
		return nil, headLen, err
	}

	data := make([]byte, header.Length)
	_, err = io.ReadFull(r, data)
	if err != nil {
		return nil, headLen, err
	}

	log.Debugf("decode mqtt msg type: %d", header.Type)

	var msg mqtt.Message
	switch header.Type {
	case mqtt.TypeConnect:
		msg, err = decodeConnect(data, header)
	case mqtt.TypeConnack:
		msg, err = decodeConnack(data, header)
	case mqtt.TypePublish:
		msg, err = decodePublish(data, header)
	case mqtt.TypePuback:
		msg, err = decodePuback(data, header)
	case mqtt.TypePubrec:
		msg, err = decodePubrec(data, header)
	case mqtt.TypePubrel:
		msg, err = decodePubrel(data, header)
	case mqtt.TypePubcomp:
		msg, err = decodePubcomp(data, header)
	case mqtt.TypeSubscribe:
		msg, err = decodeSubscribe(data, header)
	case mqtt.TypeSuback:
		msg, err = decodeSuback(data, header)
	case mqtt.TypeUnsubscribe:
		msg, err = decodeUnsubscribe(data, header)
	case mqtt.TypeUnsuback:
		msg, err = decodeUnsuback(data, header)
	case mqtt.TypePingreq:
		msg, err = decodePingreq(data, header)
	case mqtt.TypePingresp:
		msg, err = decodePingresp(data, header)
	case mqtt.TypeDisconnect:
		msg, err = decodeDisconnect(data, header)
	default:
		return nil, (headLen + len(data)), util.NewError(util.RetMqttMessageTypeErr)
	}

	return msg, (headLen + len(data)), err
}

// Encode mqtt编码
func (c *MqttCodec) Encode(w codec.Writer, message *message.Message) (int, error) {
	buf := buffer.New()
	defer buf.Derefer()

	var (
		n   int
		err error
	)
	mqttMsg := message.Message
	log.Debugf("encode mqtt msg type: %d", mqttMsg.Type())
	switch mqttMsg.Type() {
	case mqtt.TypeConnect:
		n, err = encodeConnect(w, mqttMsg.(*mqtt.Connect), buf)
	case mqtt.TypeConnack:
		n, err = encodeConnack(w, mqttMsg.(*mqtt.Connack), buf)
	case mqtt.TypePublish:
		n, err = encodePublish(w, mqttMsg.(*mqtt.Publish), buf)
	case mqtt.TypePuback:
		n, err = encodePuback(w, mqttMsg.(*mqtt.Puback), buf)
	case mqtt.TypePubrec:
		n, err = encodePubrec(w, mqttMsg.(*mqtt.Pubrec), buf)
	case mqtt.TypePubrel:
		n, err = encodePubrel(w, mqttMsg.(*mqtt.Pubrel), buf)
	case mqtt.TypePubcomp:
		n, err = encodePubcomp(w, mqttMsg.(*mqtt.Pubcomp), buf)
	case mqtt.TypeSubscribe:
		n, err = encodeSubscribe(w, mqttMsg.(*mqtt.Subscribe), buf)
	case mqtt.TypeSuback:
		n, err = encodeSuback(w, mqttMsg.(*mqtt.Suback), buf)
	case mqtt.TypeUnsubscribe:
		n, err = encodeUnsubscribe(w, mqttMsg.(*mqtt.Unsubscribe), buf)
	case mqtt.TypeUnsuback:
		n, err = encodeUnsuback(w, mqttMsg.(*mqtt.Unsuback), buf)
	case mqtt.TypePingreq:
		n, err = encodePingreq(w, mqttMsg.(*mqtt.Pingreq), buf)
	case mqtt.TypePingresp:
		n, err = encodePingresp(w, mqttMsg.(*mqtt.Pingresp), buf)
	case mqtt.TypeDisconnect:
		n, err = encodeDisconnect(w, mqttMsg.(*mqtt.Disconnect), buf)
	default:
		return 0, util.NewError(util.RetMqttMessageTypeErr)
	}
	return n, err
}

func writeString(buf, v []byte) int {
	size := len(v)
	writeUint16(buf, uint16(size))
	copy(buf[2:], v)
	return 2 + size
}

func writeStringWithoutSize(buf, v []byte) int {
	size := len(v)
	copy(buf, v)
	return size
}

func writeUint16(buf []byte, v uint16) int {
	buf[0] = byte((v & 0xff00) >> 8)
	buf[1] = byte(v & 0x00ff)
	return 2
}

func writeUint8(buf []byte, v uint8) int {
	buf[0] = v
	return 1
}

func readString(b []byte, pos *uint32) ([]byte, error) {
	l := readUint16(b, pos)
	if uint32(l)+*pos > uint32(len(b)) {
		return nil, util.NewError(util.RetMessageBadPacket)
	}
	v := []byte(string(b[*pos : uint32(l)+*pos]))
	*pos += uint32(l)
	return v, nil
}

func readUint16(b []byte, pos *uint32) uint16 {
	var b0 uint16
	if len(b) > int(*pos) {
		b0 = uint16(b[*pos])
	}
	var b1 uint16
	if len(b) > int(*pos+1) {
		b1 = uint16(b[*pos+1])
	}
	*pos += 2
	return (b0 << 8) + b1
}

func readUint8(b []byte, pos *uint32) uint8 {
	var b0 uint8
	if len(b) > int(*pos) {
		b0 = b[*pos]
	}
	*pos++
	return b0
}

func boolToUInt8(v bool) uint8 {
	if v {
		return 0x1
	}
	return 0x0
}

// http://docs.oasis-open.org/mqtt/mqtt/v3.1.1/os/mqtt-v3.1.1-os.html#_Toc398718023
func encodeLength(bodyLength uint32) (uint8, uint32) {
	if bodyLength == 0 {
		return 1, 0
	}
	var (
		numBytes uint8
		bitField uint32
	)
	for bodyLength > 0 {
		bitField <<= 8
		dig := uint8(bodyLength % 128)
		bodyLength /= 128
		if bodyLength > 0 {
			dig = dig | 0x80
		}
		bitField |= uint32(dig)
		numBytes++
	}
	return numBytes, bitField
}

// 解码header
func decodeHeader(r codec.Reader) (mqtt.Header, int, error) {
	firstByte, err := r.ReadByte()
	if err != nil {
		return mqtt.Header{}, 0, err
	}

	header := mqtt.Header{
		Type:   (firstByte & 0xf0) >> 4,
		DUP:    firstByte&0x08 > 0,
		QoS:    firstByte & 0x06 >> 1,
		Retain: firstByte&0x01 > 0,
	}

	multiplier := uint32(1)
	digit := byte(0x80)
	var length int = 1
	for (digit & 0x80) != 0 {
		b, err := r.ReadByte()
		length++
		if err != nil {
			return mqtt.Header{}, 0, err
		}
		digit = b
		header.Length += uint32(digit&0x7f) * multiplier
		if multiplier > 0x200000 { // 128*128*128 The maximum number of bytes in the Variable Byte Integer field is four.
			return mqtt.Header{}, 0, util.NewError(util.RetMessageBadPacket)
		}
		multiplier *= 128
	}
	if header.Length > maxMessageSize {
		return mqtt.Header{}, 0, util.NewError(util.RetMessageTooLarge)
	}

	return header, length, nil
}

// 编码header
func encodeHeader(buf []byte, h *mqtt.Header) int {
	var firstByte byte
	firstByte |= h.Type << 4
	firstByte |= boolToUInt8(h.DUP) << 3
	firstByte |= h.QoS << 1
	firstByte |= boolToUInt8(h.Retain)

	numBytes, bitField := encodeLength(h.Length)
	pos := maxHeaderSize - numBytes - 1
	buf[pos] = firstByte
	for i := pos + 1; i < maxHeaderSize; i++ {
		buf[i] = byte(bitField >> ((numBytes - 1) * 8))
		numBytes--
	}

	return int(pos)
}

// 解码connect消息
func decodeConnect(data []byte, header mqtt.Header) (c *mqtt.Connect, err error) {
	c = &mqtt.Connect{Header: header}
	var pos = uint32(0)
	c.ProtoName, err = readString(data, &pos)
	if err != nil {
		return
	}
	c.Version = readUint8(data, &pos)
	flags := readUint8(data, &pos)
	c.UsernameFlag = flags&(1<<7) > 0
	c.PasswordFlag = flags&(1<<6) > 0
	c.WillRetain = flags&(1<<5) > 0
	c.WillQoS = (flags & (1 << 4)) + (flags & (1 << 3))
	c.WillFlag = flags&(1<<2) > 0
	c.CleanStart = flags&(1<<1) > 0
	c.KeepAlive = readUint16(data, &pos)
	c.ClientID, err = readString(data, &pos)
	if err != nil {
		return
	}
	if c.WillFlag {
		if c.WillTopic, err = readString(data, &pos); err != nil {
			return
		}
		if c.WillMessage, err = readString(data, &pos); err != nil {
			return
		}
	}
	if c.UsernameFlag {
		if c.Username, err = readString(data, &pos); err != nil {
			return
		}
	}
	if c.PasswordFlag {
		if c.Password, err = readString(data, &pos); err != nil {
			return
		}
	}
	return
}

// 编码connect消息
func encodeConnect(w io.Writer, c *mqtt.Connect, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)

	pos := writeString(left, c.ProtoName)
	pos += writeUint8(left[pos:], c.Version)

	var flags uint8
	flags |= boolToUInt8(c.UsernameFlag) << 7
	flags |= boolToUInt8(c.PasswordFlag) << 6
	flags |= boolToUInt8(c.WillRetain) << 5
	flags |= c.WillQoS << 3
	flags |= boolToUInt8(c.WillFlag) << 2
	flags |= boolToUInt8(c.CleanStart) << 1

	pos += writeUint8(left[pos:], flags)
	pos += writeUint16(left[pos:], c.KeepAlive)
	pos += writeString(left[pos:], c.ClientID)

	if c.WillFlag {
		pos += writeString(left[pos:], c.WillTopic)
		pos += writeString(left[pos:], c.WillMessage)
	}
	if c.UsernameFlag {
		pos += writeString(left[pos:], c.Username)
	}
	if c.PasswordFlag {
		pos += writeString(left[pos:], c.Password)
	}

	c.Header.Type = c.Type()
	c.Header.Length = uint32(pos)
	start := encodeHeader(head, &c.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码connack消息
func decodeConnack(data []byte, header mqtt.Header) (c *mqtt.Connack, err error) {
	c = &mqtt.Connack{Header: header}
	pos := uint32(0)
	c.AckFlag = readUint8(data, &pos)
	c.ReasonCode = readUint8(data, &pos)
	return
}

// 编码connack消息
func encodeConnack(w io.Writer, c *mqtt.Connack, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint8(left, c.AckFlag)
	pos += writeUint8(left[pos:], c.ReasonCode)
	c.Header.Type = c.Type()
	c.Header.Length = uint32(pos)
	start := encodeHeader(head, &c.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码publish消息
func decodePublish(data []byte, header mqtt.Header) (p *mqtt.Publish, err error) {
	p = &mqtt.Publish{Header: header}
	var pos = uint32(0)
	p.Topic, err = readString(data, &pos)
	if err != nil {
		return
	}
	if header.QoS > 0 {
		p.MessageID = readUint16(data, &pos)
	}
	p.Payload = data[pos:]
	return
}

// 编码publish消息
func encodePublish(w io.Writer, p *mqtt.Publish, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)

	pos := writeString(left, p.Topic)
	if p.Header.QoS > 0 {
		pos += writeUint16(left[pos:], p.MessageID)
	}
	pos += writeStringWithoutSize(left[pos:], p.Payload)
	p.Header.Type = p.Type()
	p.Header.Length = uint32(pos)
	start := encodeHeader(head, &p.Header)

	if maxHeaderSize+pos > maxMessageSize {
		return 0, util.NewError(util.RetMessageTooLarge)
	}

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码puback消息
func decodePuback(data []byte, header mqtt.Header) (p *mqtt.Puback, err error) {
	p = &mqtt.Puback{Header: header}
	pos := uint32(0)
	p.MessageID = readUint16(data, &pos)
	p.ReasonCode = readUint8(data, &pos)
	return
}

// 编码connect消息
func encodePuback(w io.Writer, p *mqtt.Puback, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)

	pos := writeUint16(left, p.MessageID)
	pos += writeUint8(left[pos:], p.ReasonCode)
	p.Header.Type = p.Type()
	p.Header.Length = uint32(pos)
	start := encodeHeader(head, &p.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码pubrec消息
func decodePubrec(data []byte, header mqtt.Header) (p *mqtt.Pubrec, err error) {
	p = &mqtt.Pubrec{Header: header}
	pos := uint32(0)
	p.MessageID = readUint16(data, &pos)
	p.ReasonCode = readUint8(data, &pos)
	return
}

// 编码pubrec消息
func encodePubrec(w io.Writer, p *mqtt.Pubrec, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, p.MessageID)
	pos += writeUint8(left[pos:], p.ReasonCode)
	p.Header.Type = p.Type()
	p.Header.Length = uint32(pos)
	start := encodeHeader(head, &p.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码pubrel消息
func decodePubrel(data []byte, header mqtt.Header) (p *mqtt.Pubrel, err error) {
	p = &mqtt.Pubrel{Header: header}
	pos := uint32(0)
	p.MessageID = readUint16(data, &pos)
	p.ReasonCode = readUint8(data, &pos)
	return
}

// 编码pubrel消息
func encodePubrel(w io.Writer, p *mqtt.Pubrel, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, p.MessageID)
	pos += writeUint8(left[pos:], p.ReasonCode)
	p.Header.Type = p.Type()
	p.Header.Length = uint32(pos)
	start := encodeHeader(head, &p.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码pubcomp消息
func decodePubcomp(data []byte, header mqtt.Header) (p *mqtt.Pubcomp, err error) {
	p = &mqtt.Pubcomp{Header: header}
	pos := uint32(0)
	p.MessageID = readUint16(data, &pos)
	p.ReasonCode = readUint8(data, &pos)
	return
}

// 编码pubcomp消息
func encodePubcomp(w io.Writer, p *mqtt.Pubcomp, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, p.MessageID)
	pos += writeUint8(left[pos:], p.ReasonCode)
	p.Header.Type = p.Type()
	p.Header.Length = uint32(pos)
	start := encodeHeader(head, &p.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码subscribe消息
func decodeSubscribe(data []byte, header mqtt.Header) (s *mqtt.Subscribe, err error) {
	s = &mqtt.Subscribe{Header: header}
	var pos = uint32(0)
	s.MessageID = readUint16(data, &pos)
	maxlen := uint32(len(data))
	for pos < maxlen {
		var t mqtt.TopicFilter
		t.Topic, err = readString(data, &pos)
		if err != nil {
			return
		}
		t.Qos = readUint8(data, &pos)
		s.Subscriptions = append(s.Subscriptions, t)
	}
	return
}

// 编码subscribe消息
func encodeSubscribe(w io.Writer, s *mqtt.Subscribe, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, s.MessageID)
	for _, t := range s.Subscriptions {
		pos += writeString(left[pos:], t.Topic)
		pos += writeUint8(left[pos:], t.Qos)
	}
	s.Header.Type = s.Type()
	s.Header.Length = uint32(pos)
	start := encodeHeader(head, &s.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码suback消息
func decodeSuback(data []byte, header mqtt.Header) (s *mqtt.Suback, err error) {
	s = &mqtt.Suback{Header: header}
	pos := uint32(0)
	s.MessageID = readUint16(data, &pos)
	maxlen := uint32(len(data))
	for pos < maxlen {
		qos := readUint8(data, &pos)
		s.ReasonCodes = append(s.ReasonCodes, qos)
	}
	return
}

// 编码suback消息
func encodeSuback(w io.Writer, s *mqtt.Suback, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, s.MessageID)
	for _, c := range s.ReasonCodes {
		pos += writeUint8(left[pos:], c)
	}
	s.Header.Type = s.Type()
	s.Header.Length = uint32(pos)
	start := encodeHeader(head, &s.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码unsubscribe消息
func decodeUnsubscribe(data []byte, header mqtt.Header) (u *mqtt.Unsubscribe, err error) {
	u = &mqtt.Unsubscribe{Header: header}
	var pos = uint32(0)
	u.MessageID = readUint16(data, &pos)
	maxlen := uint32(len(data))
	for pos < maxlen {
		var topic []byte
		topic, err = readString(data, &pos)
		if err != nil {
			return
		}
		u.Topics = append(u.Topics, topic)
	}
	return
}

// 编码unsubscribe消息
func encodeUnsubscribe(w io.Writer, u *mqtt.Unsubscribe, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, u.MessageID)
	for _, t := range u.Topics {
		pos += writeString(left[pos:], t)
	}
	u.Header.Type = u.Type()
	u.Header.Length = uint32(pos)
	start := encodeHeader(head, &u.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码unsuback消息
func decodeUnsuback(data []byte, header mqtt.Header) (u *mqtt.Unsuback, err error) {
	u = &mqtt.Unsuback{Header: header}
	pos := uint32(0)
	u.MessageID = readUint16(data, &pos)
	maxlen := uint32(len(data))
	for pos < maxlen {
		qos := readUint8(data, &pos)
		u.ReasonCodes = append(u.ReasonCodes, qos)
	}
	return
}

// 编码unsuback消息
func encodeUnsuback(w io.Writer, u *mqtt.Unsuback, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint16(left, u.MessageID)
	for _, c := range u.ReasonCodes {
		pos += writeUint8(left[pos:], c)
	}
	u.Header.Type = u.Type()
	u.Header.Length = uint32(pos)
	start := encodeHeader(head, &u.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}

// 解码pingreq消息
func decodePingreq(_ []byte, _ mqtt.Header) (p *mqtt.Pingreq, err error) {
	return &mqtt.Pingreq{}, nil
}

// 编码pingreq消息
func encodePingreq(w io.Writer, p *mqtt.Pingreq, buf *buffer.Buffer) (int, error) {
	return w.Write([]byte{0xc0, 0x0})
}

// 解码pingresp消息
func decodePingresp(_ []byte, _ mqtt.Header) (p *mqtt.Pingresp, err error) {
	return &mqtt.Pingresp{}, nil
}

// 编码pingresp消息
func encodePingresp(w io.Writer, p *mqtt.Pingresp, buf *buffer.Buffer) (int, error) {
	return w.Write([]byte{0xd0, 0x0})
}

// 解码disconnect消息
func decodeDisconnect(data []byte, header mqtt.Header) (d *mqtt.Disconnect, err error) {
	d = &mqtt.Disconnect{Header: header}
	pos := uint32(0)
	if len(data) < 1 {
		return
	}
	d.ReasonCode = readUint8(data, &pos)
	return
}

// 编码disconnect消息
func encodeDisconnect(w io.Writer, d *mqtt.Disconnect, buf *buffer.Buffer) (int, error) {
	head, left := buf.Split(maxHeaderSize)
	pos := writeUint8(left, d.ReasonCode)
	d.Header.Type = d.Type()
	d.Header.Length = uint32(pos)
	start := encodeHeader(head, &d.Header)

	return w.Write(buf.Slice(start, maxHeaderSize+pos))
}
