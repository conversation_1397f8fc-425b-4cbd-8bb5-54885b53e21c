FROM registry.xunlei.cn/vcproject/proto_build:test AS proto_build

WORKDIR /proto

ARG GITLAB_TOKEN
RUN git config --global url."https://oauth2:${GITLAB_TOKEN}@new-gitlab.xunlei.cn".insteadOf "https://new-gitlab.xunlei.cn"

RUN <NAME_EMAIL>:vcproject/third-party.git

ADD ../proto/api /proto/api
RUN ls /proto/third-party


# Stage 1: Build the Go application
FROM golang:1.22-bullseye AS builder

RUN apt-get install -y  git gcc g++ libc-dev curl
# Set the Current Working Directory inside the container
WORKDIR /app


# 设置 GitLab private token
ARG GITLAB_TOKEN
RUN git config --global url."https://oauth2:${GITLAB_TOKEN}@new-gitlab.xunlei.cn".insteadOf "https://new-gitlab.xunlei.cn"
RUN git config --global url."https://oauth2:${GITLAB_TOKEN}@gitlab.xunlei.cn".insteadOf "https://gitlab.xunlei.cn"

# 设置 GOPROXY
ENV GOPROXY=https://goproxy.cn,direct


# Copy the source from the current directory to the Working Directory inside the container
COPY . .
# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download


# Build the Go app
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64  go build -o vc.basemsgbroker.s ./

Add deploy/proto.pb /proto/proto_descriptor.pb


# get the pb descriptor
#RUN curl --header "${GITLAB_TOKEN}" \
#     "https://gitlab.changyinlive.com/api/v4/projects/603/repository/files/auth%2Fv1%2Fauth.pb/raw?ref=jacob_umine_20241105" \
#     --output auth.pb

# Stage 2: Create a small image
FROM debian:bullseye-slim as production



# Set the timezone to Singapore time
RUN apt-get update && apt-get install -y tzdata && \
	    ln -fs /usr/share/zoneinfo/Asia/Singapore /etc/localtime && \
	    dpkg-reconfigure -f noninteractive tzdata && \
	    apt-get clean && rm -rf /var/lib/apt/lists/*
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

RUN mkdir -p /opt/vc/basemsgbroker
# Copy the Pre-built binary file from the previous stage
COPY --from=builder /app/vc.basemsgbroker.s /opt/vc/basemsgbroker/vc.basemsgbroker.s


WORKDIR /opt/vc/basemsgbroker

ENTRYPOINT ["/opt/vc/basemsgbroker/vc.basemsgbroker.s"]


FROM alpine:3.20 as initdata
WORKDIR /initdata


COPY --from=builder /proto/proto_descriptor.pb /initdata/proto_descriptor.pb
