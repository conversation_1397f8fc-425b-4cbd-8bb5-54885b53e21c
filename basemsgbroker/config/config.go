package config

import (
	"flag"
	"os"

	log "xim/baselib/logger"

	"github.com/jinzhu/configor"
	cache "xim/common/cache_redis"
)

type KafkaConfig struct {
	Addrs         []string
	Producetopic  string
	Writers       int
	Consumertopic string
	Readers       int
}

type JaegerConfig struct {
	ServiceName string `yaml:"service_name"`
	Endpoint    string `yaml:"endpoint"`
	Enable      bool   `default:"false" yaml:"enable"`
	Environment string `yaml:"environment"`
}

// Config 服务配置
type Config struct {
	Addr            string `default:"0.0.0.0:8080"`
	Logger          string `default:"deploy/logger.xml"`
	Redis           cache.RedisConfig
	Kafka           KafkaConfig
	Jaeger          JaegerConfig
	CmTcpAddr       string `default:"0.0.0.0:8080"`
	CmWebsocketAddr string `default:"0.0.0.0:8081"`
	PpWebsocketAddr string `default:"0.0.0.0:9722"`
	PodIP           string
	PlatformKey     map[uint64]string `yaml:"platform_key"`
	Env             string
}

// NewConfig 读取配置
func NewConfig() Config {
	configPath := flag.String("basemsg_config", "deploy/config.yml", "configuration file")
	flag.Parse()

	var conf Config
	if err := configor.Load(&conf, *configPath); err != nil {
		log.Panicf("load config error: %v", err)
		os.Exit(1)
	}
	conf.PodIP = os.Getenv("SELF_POD_IP")

	return conf
}
