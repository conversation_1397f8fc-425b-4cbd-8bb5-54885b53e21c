package stat

import (
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"time"
)

//自定义prometheus监控指标
var (
	podIp = ""
	//长连接数量监控指标，区分平台和podip
	connNumGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "xllivemp",
			Subsystem: "base",
			Name: "platform_msg_conn_num",
			Help: "The conn num of platform",
		},
		[]string{"platform", "pod_ip"},
	)
	//下行消息发送数量监控，区分平台
	publishMsgTotalCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xllivemp",
			Subsystem: "base",
			Name: "platform_msg_published_total",
			Help: "The msg num of platform published",
		},
		[]string{"platform"},
	)
	//下行消息发送成功数量监控，区分平台，成功以收到puback为准
	publishMsgSuccCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xllivemp",
			Subsystem: "base",
			Name: "platform_msg_published_success_total",
			Help: "The msg num of platform published success",
		},
		[]string{"platform"},
	)
	//下行消息耗时数据监控，区分平台
	publishLatencyHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "xllivemp",
			Subsystem: "base",
			Name: "platform_msg_publish_handling_seconds",
			Help: "The msg publish latency seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"platform"},
	)
	//上行消息流量数据监控，区分平台
	msgReceiveBytesCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xllivemp",
			Subsystem: "base",
			Name: "platform_msg_receive_bytes_total",
			Help: "The msg bytes of platform receive from app client",
		},
		[]string{"platform"},
	)
	//下行消息流量数据监控，区分平台
	msgPublishBytesCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xllivemp",
			Subsystem: "base",
			Name: "platform_msg_publish_bytes_total",
			Help: "The msg bytes of platform publish to app client",
		},
		[]string{"platform"},
	)
)

func ConnMetric(pIp string) []prometheus.Collector {
	podIp = pIp
	return []prometheus.Collector{
		connNumGauge,
		publishMsgTotalCounter, publishMsgSuccCounter, publishLatencyHistogram,
		msgReceiveBytesCounter, msgPublishBytesCounter,
	}
}

func ConnNumSet(platformId uint64, num uint64) {
	if platformId <= 0 || podIp == "" {
		return
	}
	lvs := []string{fmt.Sprint(platformId), podIp}
	connNumGauge.WithLabelValues(lvs...).Set(float64(num))
}

func PublishMsgTotalAdd(platformId uint64, incr int) {
	if platformId <= 0 {
		return
	}
	lvs := []string{fmt.Sprint(platformId)}
	publishMsgTotalCounter.WithLabelValues(lvs...).Add(float64(incr))
}

func PublishMsgSuccAdd(platformId uint64, incr int) {
	if platformId <= 0 {
		return
	}
	lvs := []string{fmt.Sprint(platformId)}
	publishMsgSuccCounter.WithLabelValues(lvs...).Add(float64(incr))
}

func PublishMsgLatencySecond(platformId uint64, duration float64) {
	if platformId <= 0 {
		return
	}
	lvs := []string{fmt.Sprint(platformId)}
	publishLatencyHistogram.WithLabelValues(lvs...).Observe(duration)
}

func MsgReceiveBytesTotalAdd(platformId uint64, bytes int) {
	if platformId <= 0 || bytes <= 0 {
		return
	}
	lvs := []string{fmt.Sprint(platformId)}
	msgReceiveBytesCounter.WithLabelValues(lvs...).Add(float64(bytes))
}

func MsgPublishBytesTotalAdd(platformId uint64, bytes int) {
	if platformId <= 0 || bytes <= 0 {
		return
	}
	lvs := []string{fmt.Sprint(platformId)}
	msgPublishBytesCounter.WithLabelValues(lvs...).Add(float64(bytes))
}

// Latency 计算耗时，单位秒
func Latency(t time.Time) float64 {
	return float64(time.Now().Sub(t).Nanoseconds()) / (1000 * 1000 * 1000)
}
