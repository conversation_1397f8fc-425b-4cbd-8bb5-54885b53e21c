package main

import (
	"context"
	"net"
	"net/http"
	_ "net/http/pprof"
	"runtime"
	"time"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/server/env"
	"xim/baselib/server/interceptor"
	"xim/baselib/trace"
	"xim/basemsgbroker/config"
	"xim/basemsgbroker/model/broker"
	"xim/basemsgbroker/server"
	"xim/basemsgbroker/stat"
	pb_basemsgbroker "xim/proto/api/basemsgbroker"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"

	"google.golang.org/grpc"
)

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU())
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	go func() {
		http.ListenAndServe("0.0.0.0:8888", nil)
	}()

	conf := config.NewConfig()
	logger.Infof("load config %+v", conf)

	ctx := context.Background()

	if conf.Jaeger.Enable {
		prv, err := trace.NewProvider(ctx, trace.ProviderConfig{
			Endpoint:       conf.Jaeger.Endpoint, // Jaeger
			ServiceName:    conf.Jaeger.ServiceName,
			ServiceVersion: "v0.0.1",
			Environment:    conf.Jaeger.Environment,
		})
		if err != nil {
			logger.Fatalf("Failed to create trace provider: %v", err)
		}
		defer prv.Close(ctx)
	}

	// 创建gRPC服务
	svr := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			interceptor.RecoverWithRespFunc(func(stack string) interface{} {
				msg := "internal server error"
				if !env.IsProd() {
					msg = stack
				}
				return &common.BizBaseResp{
					Code: errcode.ErrorInternal.Code,
					Msg:  msg,
				}
			}),
			interceptor.ServerLog,
			metric.UnaryServerInterceptor(),
		),
		trace.GRPCServerStatsHandler(),
	)
	//注册Prometheus
	metric.InitPrometheus(svr, "")
	metric.MustRegister(stat.ConnMetric(conf.PodIP)...)

	//初始化grpc服务
	basemsgbrokerServer, err := server.NewBasemsgbrokerServer(&conf)
	if err != nil {
		logger.Errorf("New server err:%v", err)
		return
	}
	// 注册gRPC服务实现
	pb_basemsgbroker.RegisterSServer(svr, basemsgbrokerServer)
	// 导出grpc监控数据
	//env.ServePromHttp(svr, stat.ConnMetric(conf.PodIP)...)

	// 初始化消息长连接服务
	msgServer := broker.NewServer(&conf)

	var addr = "0.0.0.0:9000"
	// 启动grpc服务，监听grpc端口
	ls, err := net.Listen("tcp", addr)
	if err != nil {
		logger.Errorf("Listen error: %v\n", err)
		return
	}
	logger.Infof("Started Application at %v, listen on %v", time.Now().Format("January 2, 2006 at 3:04pm (MST)"), addr)
	// 启动grpc和消息tcp服务监听，有一个处理需要开协程执行
	go msgServer.Serve(conf.PodIP)
	svr.Serve(ls)
}
