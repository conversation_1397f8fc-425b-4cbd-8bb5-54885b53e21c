Basemsgbroker service
====================

## Build
<NAME_EMAIL>:xllivemp/basemsgbroker.git
make

### 安装开发工具

    make, go, protoc

### 获取源代码

使用 https（帐号密码认证）

    export GIT_TERMINAL_PROMPT=1

使用 git（SSH key 认证）

    git config --global url."********************:".insteadOf "https://gitlab.xunlei.cn"

拉取代码并切到 dev 分支

    <NAME_EMAIL>:xllivemp/basemsgbroker.git
    cd basemsgbroker
    git checkout dev

### 编译

    make
