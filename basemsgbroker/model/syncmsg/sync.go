package syncmsg

import (
	"context"
	"encoding/json"
	"strconv"
	"time"
	"xim/basemsgbroker/model/grpccli"
	"xim/proto/api/basemsgtransfer"

	log "xim/baselib/logger"
	baseutil "xim/baselib/util"
	"xim/common/signature"
	"xim/proto/api/basemsgbroker"
)

func SyncMsgToPlatform(ctx context.Context, msgEvent *basemsgbroker.MsgEvent) error {
	orderId := baseutil.UUID()
	msgData := &basemsgtransfer.UpstreamMsgData{
		Type:     basemsgtransfer.MsgEventType(msgEvent.Type),
		ConnId:   msgEvent.ConnId,
		DeviceId: msgEvent.DeviceId,
		UserId:   msgEvent.UserId,
		Time:     msgEvent.Time,
		Msg:      msgEvent.Msg,
		Topic:    msgEvent.Topic,
		MsgId:    msgEvent.MsgId,
		OrderId:  orderId,
		NanoTime: msgEvent.NanoTime,
		ClientId: msgEvent.ClientId,
		Base:     msgEvent.Base,
	}
	msgJson, _ := json.Marshal(msgData)
	// 封装同步给平台业务方的数据
	req := &basemsgtransfer.HandleMsgReq{
		PlatformId: strconv.FormatUint(msgEvent.PlatformId, 10),
		TimeStamp:  strconv.FormatInt(time.Now().Unix(), 10),
		NonceStr:   baseutil.GetRandomString(10),
		MsgJson:    string(msgJson),
	}
	//设置签名
	signTool := signature.NewSignature("OsProject", 2*time.Minute)
	signTool.SetUserReflect(true)
	sign, err := signTool.Sign(req)
	if err != nil {
		log.Errorf("syncMsgToPlatform sign params %+v error %v", req, err)
		return err
	}
	req.Sign = sign
	return grpccli.HandleMessage(ctx, req)
}
