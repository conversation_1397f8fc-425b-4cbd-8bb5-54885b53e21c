package syncmsg

import (
	"sync"
)

var (
	clients = &Clients{
		clients: map[string]*CallbackClient{},
		mutex:   sync.RWMutex{},
	}
)

type Clients struct {
	clients map[string]*CallbackClient
	mutex   sync.RWMutex
}

// NewClients 创建grpc客户端
func NewClients() error {
	return nil
}

func NewCallbackClient(serviceName string) (*CallbackClient, error) {
	clients.mutex.RLock()
	client, found := clients.clients[serviceName]
	clients.mutex.RUnlock()
	if found {
		return client, nil
	} else {
		newC, err := newCallbackClient(serviceName)
		if err != nil {
			return nil, err
		}
		clients.mutex.Lock()
		defer clients.mutex.Unlock()
		clients.clients[serviceName] = newC
		return newC, nil
	}
}
