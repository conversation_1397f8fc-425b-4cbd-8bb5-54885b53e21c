package syncmsg

import (
	"context"
	"fmt"
	"xim/baselib/grpccli"
	log "xim/baselib/logger"
	"xim/proto/api/basemsgcallout"

	"google.golang.org/grpc"
)

type CallbackClient struct {
	serviceName string
	grpcConnect *grpc.ClientConn
}

// 注意：serviceName如果不是在xllivemp名下，需要在soa.yml文件配置host
func newCallbackClient(serviceName string) (*CallbackClient, error) {
	clientConn, err := grpccli.NewLocalConn()
	if err != nil {
		log.Errorf("newCallbackClient serviceName %s error %v", serviceName, err)
		return nil, err
	}
	return &CallbackClient{serviceName: serviceName, grpcConnect: clientConn}, nil
}

// 注意：需要有serviceName，method；参数签名要注意是否有字段为空
func (cc *CallbackClient) SyncMsgCallback(ctx context.Context, method string,
	req *basemsgcallout.SyncMsgReq, opts ...grpc.CallOption) (*basemsgcallout.SyncMsgRsp, error) {
	out := new(basemsgcallout.SyncMsgRsp)
	method = fmt.Sprintf("/%s/%s", cc.serviceName, method)
	err := cc.grpcConnect.Invoke(ctx, method, req, out, opts...)
	if err != nil {
		log.Infof("SyncMsgCallback method %s req %+v res %+v error %v", method, req, out, err)
		return nil, err
	}
	return out, nil
}
