package basedata

import (
	"sync"

	"xim/basemsgbroker/config"
)

var (
	platformData *PlatformDataObj
)

func NewPlatformBaseData(conf *config.Config) (err error) {
	env = conf.Addr
	platformData = &PlatformDataObj{
		mutex:   sync.RWMutex{},
		platMap: map[uint64]*PlatformData{},
	}
	for platformId, key := range conf.PlatformKey {
		platformData.platMap[platformId] = &PlatformData{
			PlatformId:  platformId,
			Name:        "",
			Description: "",
			SecretKey:   key,
			DingWebhook: "",
			Host:        "",
		}
	}
	return
}

func GetPlatformDataObj() *PlatformDataObj {
	return platformData
}

func GetCallbackUrlObj() *CallbackUrlObj {
	// 这里确认已经不再使用了，改调用处太复杂了、先修改这里
	return nil
}
