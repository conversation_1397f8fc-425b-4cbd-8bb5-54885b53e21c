package basedata

import (
	"sync"

	log "xim/baselib/logger"
)

type CallbackUrlObj struct {
	mutex  sync.RWMutex
	urlMap map[uint32]map[string]string
}

func (p *CallbackUrlObj) GetCallbackUrlData(platformId uint32, callKey string) (callUrl string, err error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	log.Infof("GetCallbackUrlData  UrlMap:%+v", p.urlMap)

	var callUrls = make(map[string]string, len(callKey))
	var ok bool
	if callUrls, ok = p.urlMap[platformId]; !ok {
		return
	}
	if callUrl, ok = callUrls[callKey]; !ok {
		return
	}
	return
}
