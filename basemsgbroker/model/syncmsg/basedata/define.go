package basedata

/**
常量和结构体定义
*/

var (
	env string
)

const (
	TIMESTART = "2006-01-02 15:04:05"
	DATESTART = "2006-01-02"

	Environment_Dev = "dev"
	Environment_Pro = "pro"

	CallbackType_Grpc     = 0
	CallbackType_HttpPost = 1
	CallbackType_HttpGet  = 2

	CallbackUrlName_CheckLogin = "checklogin"
)

var (
	CallbackUrlNames = []string{CallbackUrlName_CheckLogin}

	//todo 这里先写死平台业务的callback接口
	//测试环境的回调配置
	DevPlatformMsgCallback = map[uint64]*CallbackConfig{
		4: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "vc.basemsgtransfer.s",
			Method:       "HandleMsg",
		},
		1004: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "vc.bizmsgchan.sdev",
			Method:       "HandleMsg",
		},
		3: &CallbackConfig{
			CallbackType: CallbackType_HttpPost,
			CallUrl:      "http://testapi.ippzone.net/mp/sync_channel_msg",
		},
		5: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "milive.svdmsghub.s",
			Method:       "SyncMsg",
		},
		1: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "xllive.msglogic.s",
			Method:       "SyncMpMsg",
		},
		7: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "onebyone.bizmsgchan.s",
			Method:       "HandleMsg",
		},
	}
	//线上环境的回调配置
	ProPlatformMsgCallback = map[uint64]*CallbackConfig{
		4: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "vc.basemsgtransfer.s",
			Method:       "HandleMsg",
		},
		3: &CallbackConfig{
			CallbackType: CallbackType_HttpPost,
			CallUrl:      "http://api.ippzone.net/mp/sync_channel_msg",
		},
		5: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "milive.svdmsghub.s",
			Method:       "SyncMsg",
		},
		1: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "xllive.msglogic.s",
			Method:       "SyncMpMsg",
		},
		7: &CallbackConfig{
			CallbackType: CallbackType_Grpc,
			Service:      "onebyone.bizmsgchan.s",
			Method:       "HandleMsg",
		},
	}
)

type CallbackConfig struct {
	CallbackType int32
	Service      string
	Method       string
	CallUrl      string
}

func GetPlatformMsgCallback(envParam string, platformId uint64) *CallbackConfig {
	if envParam == "" {
		envParam = env
	}
	if envParam == Environment_Pro {
		return ProPlatformMsgCallback[platformId]
	}
	return DevPlatformMsgCallback[platformId]
}
