package basedata

import (
	"sync"
)

type PlatformData struct {
	// 平台Id
	PlatformId uint64 `protobuf:"varint,1,opt,name=platformId" json:"platformId,omitempty"`
	// 平台名称
	Name string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// 平台描述
	Description string `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`
	// 平台密钥key
	SecretKey string `protobuf:"bytes,4,opt,name=secretKey" json:"secretKey,omitempty"`
	// 钉钉告警webhook
	DingWebhook string `protobuf:"bytes,5,opt,name=dingWebhook" json:"dingWebhook,omitempty"`
	// 平台域名host
	Host string `protobuf:"bytes,6,opt,name=Host" json:"Host,omitempty"`
}

type PlatformDataObj struct {
	mutex   sync.RWMutex
	platMap map[uint64]*PlatformData
}

func (p *PlatformDataObj) GetPlatformData(platformId uint64) (platformData *PlatformData, err error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	if _, ok := p.platMap[platformId]; ok {
		platformData = p.platMap[platformId]
		return
	}
	return nil, nil
}

// 验证签名
//func (p *PlatformDataObj) CheckSignature(platformId uint32, currenv string, params interface{}, duration time.Duration) (isOk bool, err error) {
//	if currenv != "online" {
//		isOk = true
//		return
//	}
//	var platformData *PlatformData
//	platformData, err = p.GetPlatformData(platformId)
//	if platformData == nil {
//		err = errors.New(fmt.Sprintf("CheckSignature get PlatformData platformId:%d err", platformId))
//		return
//	}
//	signTool := signature.NewSignature(platformData.SecretKey, duration)
//	signTool.SetUserReflect(true)
//	if isOk, err = signTool.Verify(params); !isOk || err != nil {
//		log.Errorf("CheckSignature params:%+v signature Verify err:%+v", params, err)
//		isOk = false
//		return
//	}
//	log.Infof("CheckSignature platformId:%d, params:%+v ok", platformId, params)
//	isOk = true
//	return
//}
//
// 签名
//func (p *PlatformDataObj) Sign(platformId uint32, params interface{}, duration time.Duration) (sign string, err error) {
//	platformData, err := p.GetPlatformData(platformId)
//	if platformData == nil {
//		err = errors.New(fmt.Sprintf("get PlatformData platformId:%d err", platformId))
//		return
//	}
//	signTool := signature.NewSignature(platformData.SecretKey, duration)
//	signTool.SetUserReflect(true)
//	sign, err = signTool.Sign(params)
//	if err != nil {
//		log.Errorf("Sign sign %+v error %v", params, err)
//		return "", err
//	}
//	return sign, nil
//}
