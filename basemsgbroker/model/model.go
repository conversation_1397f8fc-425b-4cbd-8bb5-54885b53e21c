package model

import (
	"xim/basemsgbroker/config"
	"xim/basemsgbroker/model/grpccli"
	"xim/basemsgbroker/model/redis"
	"xim/basemsgbroker/model/syncmsg"
	"xim/basemsgbroker/model/syncmsg/basedata"
)

// BasemsgbrokerModel 数据模型层
// 封装与业务逻辑相关的数据以及对数据的处理方法，包括内部数据模型、数据库、缓存、消息通道、grpc服务、PHP服务等等
// 按来源、按业务将 Model 分割成不同的文件以利维护
type BasemsgbrokerModel struct {
}

// NewBasemsgbrokerModel 创建basemsgbroker model
func NewBasemsgbrokerModel(conf *config.Config) error {
	if err := basedata.NewPlatformBaseData(conf); err != nil {
		return err
	}
	if err := redis.NewRedis(conf.Redis); err != nil {
		return err
	}
	//if err := kafka.NewProducer(conf.Kafka); err != nil {
	//	return err
	//}
	if err := syncmsg.NewClients(); err != nil {
		return err
	}

	if err := grpccli.NewClients(); err != nil {
		return err
	}
	return nil
}
