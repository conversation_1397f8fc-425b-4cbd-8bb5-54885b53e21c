package broker

import "time"

// 长连接服务参数
type serverOption struct {
	writeTimeout    time.Duration //写超时
	readTimeout     time.Duration //读超时
	writeBufferSize int           //写缓存大小
	readBufferSize  int           //读缓存大小

	cmTcpAddr string //通用tcp监听地址
	cmWsAddr  string //通用websocket监听地址
	ppWsAddr  string //皮皮直播websocket监听地址
}

// 默认参数
var defaultServerOption = serverOption{
	writeTimeout:    500 * time.Millisecond,
	readTimeout:     200 * time.Second,
	writeBufferSize: 16 * 1024,
	readBufferSize:  16 * 1024,
}

func newServerOption(opts ...Option) serverOption {
	serverOpt := defaultServerOption
	for _, opt := range opts {
		opt(&serverOpt)
	}
	return serverOpt
}

type Option func(*serverOption)

func CmTcpListenerAddress(addr string) Option {
	return func(option *serverOption) {
		option.cmTcpAddr = addr
	}
}

func CmWebsocketListenerAddress(addr string) Option {
	return func(option *serverOption) {
		option.cmWsAddr = addr
	}
}

func PpWebsocketListenerAddress(addr string) Option {
	return func(option *serverOption) {
		option.ppWsAddr = addr
	}
}

func WriteTimeout(writeTimeout time.Duration) Option {
	return func(option *serverOption) {
		option.writeTimeout = writeTimeout
	}
}

func ReadTimeout(readTimeout time.Duration) Option {
	return func(option *serverOption) {
		option.readTimeout = readTimeout
	}
}

func WriteBufferSize(size int) Option {
	return func(option *serverOption) {
		option.writeBufferSize = size
	}
}

func ReadBufferSize(size int) Option {
	return func(option *serverOption) {
		option.readBufferSize = size
	}
}
