package broker

import (
	"io"
	"net"
	"net/http"
	"time"

	log "xim/baselib/logger"
	"xim/baselib/util"

	"github.com/gorilla/websocket"
)

type WebsocketServer struct {
	listener net.Listener
	upgrader *websocket.Upgrader
	acceptor Acceptor
}

// 初始化websocket服务
func NewWebsocketServer(a Acceptor) *WebsocketServer {
	s := &WebsocketServer{
		upgrader: &websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true }, //不检查Origin
		},
		acceptor: a,
	}
	return s
}

// websocket请求的ticker放在GET请求参数中，需解析出来
func (s *WebsocketServer) getTicker(r *http.Request) string {
	ticker := r.URL.Query().Get("ticker")
	return ticker
}

// 实现handler接口，将请求转为websocket长连接
func (s *WebsocketServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 检查是否是ws协议
	log.Infof("websocket conn reqmethod %s uri %s header %v", r.Method, r.RequestURI, util.JsonStr(r.Header))

	if !websocket.IsWebSocketUpgrade(r) {
		log.Errorf("websocket req is not websocket protocol")
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	// 将http请求升级为websocket-conn
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		// Upgrade失败时，内部会发送相应的http错误，不再需要发送，此处只记录错误日志即可
		log.Errorf("websocket req Upgrade error %v", err)
		return
	}
	wsconn := newWebsocketConn(conn)
	session := NewSession(ConnType_Websocket, wsconn)
	// 手动获取websocket的ticker参数，并提前设置到session中
	token := s.getTicker(r)
	log.Infof("websocket conn token %v", token)
	session.SetToken(s.getTicker(r))
	go s.acceptor.OnAccept(session)
}

func (s *WebsocketServer) Serve(l net.Listener) error {
	s.listener = l
	return http.Serve(l, s)
}

// 停止websocket监听服务
func (s *WebsocketServer) Stop() {
	if s.listener != nil {
		s.listener.Close()
	}
}

// 将websocket的conn封装成net.conn，跟其他tcp-conn保持统一
type websocketConn struct {
	conn   *websocket.Conn
	reader io.Reader
}

func newWebsocketConn(conn *websocket.Conn) *websocketConn {
	return &websocketConn{conn: conn}
}

// Read reads data from the connection.
func (c *websocketConn) Read(b []byte) (int, error) {
	if c.reader == nil {
		var (
			pos         int
			messageType int
			reader      io.Reader
			err         error
		)
		for {
			messageType, reader, err = c.conn.NextReader()
			if err != nil {
				return pos, err
			}
			if messageType != websocket.BinaryMessage && messageType != websocket.TextMessage {
				continue
			}
			c.reader = reader
			break
		}
	}
	pos, err := c.reader.Read(b)
	if err != nil {
		if err == io.EOF {
			c.reader = nil
			// err不要重置为nil, 使用ioutil.ReadAll时, 内部的buffer.ReadFrom遇到EOF会自动重置为nil
			// err = nil
		}
	}
	return pos, err
}

// Write writes data to the connection.
func (c *websocketConn) Write(b []byte) (n int, err error) {
	if len(b) <= 0 {
		return
	}
	if err = c.conn.WriteMessage(websocket.BinaryMessage, b); err != nil {
		return
	}
	n = len(b)
	return
}

// Close closes the connection.
func (c *websocketConn) Close() error {
	return c.conn.Close()
}

// LocalAddr returns the local network address.
func (c *websocketConn) LocalAddr() net.Addr {
	return c.conn.LocalAddr()
}

// RemoteAddr returns the xllive network address.
func (c *websocketConn) RemoteAddr() net.Addr {
	return c.conn.RemoteAddr()
}

// SetDeadline sets the read and write deadlines associated
// with the connection. It is equivalent to calling both
// SetReadDeadline and SetWriteDeadline.
func (c *websocketConn) SetDeadline(t time.Time) (err error) {
	if err = c.conn.SetReadDeadline(t); err == nil {
		err = c.conn.SetWriteDeadline(t)
	}
	return
}

// SetReadDeadline sets the deadline for future Read calls
// and any currently-blocked Read call.
// A zero value for t means Read will not time out.
func (c *websocketConn) SetReadDeadline(t time.Time) error {
	return c.conn.SetReadDeadline(t)
}

// SetWriteDeadline sets the deadline for future Write calls
// and any currently-blocked Write call.
func (c *websocketConn) SetWriteDeadline(t time.Time) error {
	return c.conn.SetWriteDeadline(t)
}
