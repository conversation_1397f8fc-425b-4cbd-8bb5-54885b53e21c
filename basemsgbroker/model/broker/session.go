package broker

import (
	"encoding/json"
	"net"
	"sync"
	"sync/atomic"
	"time"
	mqtt2 "xim/basemsgbroker/util/mqtt"
	"xim/proto/api/basemsgtransfer"

	log "xim/baselib/logger"
	"xim/basemsgbroker/stat"
	"xim/basemsgbroker/util"
	"xim/basemsgbroker/util/codec"
	"xim/basemsgbroker/util/codec/mqtt"
	"xim/basemsgbroker/util/codec/ws"
	"xim/basemsgbroker/util/message"
	"xim/common/msg_ticker"
	"xim/proto/api/common"
)

// 长连接类型
const (
	ConnType_Websocket   = 1 //websocket长连接
	ConnType_TCP         = 2 //tcp长连接，mqtt协议
	ConnType_XcWebsocket = 3 //小川的websocket长连接
)

// 长连接本地映射
var (
	uniqueConnId  uint64          //生成本地长连接唯一id
	sessionHelper *sessionManager //本地长连接映射：id=>conn
)

func init() {
	sessionHelper = &sessionManager{}
	go CronLogSessionNum()
}

// 外部获取connid对应的session
func GetSession(connid uint64) *Session {
	return sessionHelper.load(connid)
}

func CronLogSessionNum() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			GetSessionNum()
		}
	}
}
func GetSessionNum() {
	platformNum := make(map[uint64]uint64, 0)
	sessionHelper.sessions.Range(func(k, v interface{}) bool {
		if s, ok := v.(*Session); ok && s != nil {
			platformNum[s.GetPlatformId()]++
		}
		return true
	})
	for platformId, n := range platformNum {
		log.Debugf("the platform %d num %d", platformId, n)
		stat.ConnNumSet(platformId, n)
	}
}

type sessionManager struct {
	sessions sync.Map
}

// 删除session在本地的映射
func (s *sessionManager) delete(session *Session) {
	s.sessions.Delete(session.GetConnId())
}

// 添加session到本地的映射
func (s *sessionManager) store(session *Session) {
	s.sessions.Store(session.GetConnId(), session)
}

// 获取connid对应的session
func (s *sessionManager) load(connid uint64) *Session {
	val, ok := s.sessions.Load(connid)
	if !ok {
		return nil
	}
	return val.(*Session)
}

type Session struct {
	connid     uint64                //长连接id
	isCheck    bool                  //长连接ticker是否已校验
	token      string                //客户端长连接token
	base       *common.BaseParam     // base 参数
	userId     string                //用户id
	platformId uint64                //平台id
	deviceId   string                //设备id
	clientId   uint32                //客户端id
	clientVer  string                //客户端版本
	clientIp   string                //客户端ip
	stopCh     *util.StopChannel     //长连接是否停止状态
	isDisconn  bool                  //是否已经断开连接
	ctype      int                   //长连接类型
	conn       net.Conn              //长连接
	pushCh     chan *message.Message //消息队列，以mqtt消息协议为主
	codec      codec.Codec           //消息编解码器
	mutex      sync.RWMutex
}

func NewSession(connType int, conn net.Conn) *Session {
	session := &Session{
		connid: atomic.AddUint64(&uniqueConnId, 1),
		stopCh: util.NewStopChannel(),
		ctype:  connType,
		conn:   conn,
		pushCh: make(chan *message.Message, 1024),
		mutex:  sync.RWMutex{},
	}
	if connType == ConnType_Websocket {
		session.codec = &ws.WsCodec{}
	} else if connType == ConnType_XcWebsocket {
		session.codec = &ws.XcWsCodec{}
	} else {
		session.codec = &mqtt.MqttCodec{}
	}
	sessionHelper.store(session)
	return session
}

func (s *Session) GetConnId() uint64 {
	return s.connid
}
func (s *Session) GetCtype() int {
	return s.ctype
}
func (s *Session) SetChecked() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.isCheck = true
}
func (s *Session) IsChecked() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isCheck
}
func (s *Session) SetDisconned() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.isDisconn = true
}
func (s *Session) IsDisconned() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isDisconn
}
func (s *Session) SetToken(token string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.token = token
}
func (s *Session) GetToken() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.token
}

func (s *Session) SetBase(base *common.BaseParam) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.base = base
}
func (s *Session) GetBase() *common.BaseParam {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.base
}

func (s *Session) SetUserInfo(info *msg_ticker.Ticket) {
	if info == nil {
		return
	}
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.userId = info.UserId
	s.platformId = info.PlatformId
	s.deviceId = info.DeviceId
	s.clientId = info.ExtendData.ClientId
	s.clientVer = info.ExtendData.ClientVersion
}
func (s *Session) GetUserId() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.userId
}
func (s *Session) GetPlatformId() uint64 {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.platformId
}
func (s *Session) GetDeviceId() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.deviceId
}
func (s *Session) GetClientId() uint32 {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.clientId
}
func (s *Session) GetClientVersion() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.clientVer
}
func (s *Session) SetClientIp(ip string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.clientIp = ip
}
func (s *Session) GetClientIp() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.clientIp
}
func (s *Session) UpdateCodec(c codec.Codec) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.codec = c
}
func (s *Session) GetCodec() codec.Codec {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.codec
}

// 关闭用户的长连接
func (s *Session) Close() {
	s.stopCh.Close()
	close(s.pushCh)
	s.conn.Close()
	sessionHelper.delete(s)
}

// 停止用户的长连接
func (s *Session) Stop() {
	s.stopCh.Stop()
}
func (s *Session) IsStop() bool {
	return s.stopCh.IsStop()
}

// 将消息写到队列，等待消费发送到长连接
func (s *Session) Write(msg *message.Message) error {
	if s.IsStop() {
		return util.NewError(util.RetSessionClosed)
	}
	select {
	case s.pushCh <- msg:
		return nil
	default:
		return util.NewError(util.RetSessionChannelFull)
	}
}

func (s *Session) FilterReviewMsg(msg *message.Message) (isPass bool, reviewMsg *message.Message, err error) {
	if msg.Message.Type() == mqtt2.TypePublish {
		msgPublish, ok := msg.Message.(*mqtt2.Publish)
		if ok && msgPublish != nil {
			var msgData basemsgtransfer.RespMsgData
			_err := json.Unmarshal(msgPublish.Payload, &msgData)
			if _err != nil {
				log.Errorf("review Websocket unmarshal msg %s error %v", string(msgPublish.Payload), _err)
				err = _err
				return
			}
			if msgData.Type == int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify) {
				var notifyMsgData []*basemsgtransfer.MsgData
				filteredNotifyMsgData := make([]*basemsgtransfer.MsgData, 0)
				_err2 := json.Unmarshal([]byte(msgData.Data), &notifyMsgData)
				if _err2 != nil {
					log.Errorf("review Websocket unmarshal msg %s error %v", msgData.Data, _err2)
					err = _err2
					return
				}
				for _, notifyMsg := range notifyMsgData {
					// 小秘书消息过滤
					if notifyMsg.From == 1 {
						log.Infof("review msg filter from Userid from %d,msg: %+v", notifyMsg.From, notifyMsg)
						continue
					}
					if notifyMsg.ContentType == 107 ||
						notifyMsg.ContentType == 108 ||
						notifyMsg.ContentType == 115108 ||
						notifyMsg.ContentType == 115109 ||
						notifyMsg.ContentType == 116108 ||
						notifyMsg.ContentType == 116109 ||
						notifyMsg.ContentType == 106050 ||
						notifyMsg.ContentType == 106060 ||
						notifyMsg.ContentType == 106070 ||
						notifyMsg.ContentType == 106071 ||
						notifyMsg.ContentType == 106072 ||
						notifyMsg.ContentType == 106073 ||
						notifyMsg.ContentType == 11703 ||
						notifyMsg.ContentType == 11704 ||
						notifyMsg.ContentType == 11705 ||
						notifyMsg.ContentType == 11706 ||
						notifyMsg.ContentType == 11707 ||
						notifyMsg.ContentType == 118001 ||
						notifyMsg.ContentType == 118002 ||
						notifyMsg.ContentType == 118003 ||
						notifyMsg.ContentType == 120100 ||
						notifyMsg.ContentType == 120101 ||
						notifyMsg.ContentType == 120102 ||
						notifyMsg.ContentType == 106085 || // 红包飘屏
						notifyMsg.ContentType == 10802 || // 下发视频卡 msg_from 0
						notifyMsg.ContentType == 10803 || // 视频卡状态变更 msg_from 2
						notifyMsg.ContentType == 120103 {
						log.Infof("review msg filter ContentType %d,msg: %+v", notifyMsg.ContentType, notifyMsg)
						continue
					}
					filteredNotifyMsgData = append(filteredNotifyMsgData, notifyMsg)
				}
				if len(filteredNotifyMsgData) == 0 {
					isPass = false
					return
				}
				filteredNotifyMsgDataStr, _ := util.Json.MarshalToString(filteredNotifyMsgData)
				msgData.Data = filteredNotifyMsgDataStr
				payloadStr, _ := util.Json.MarshalToString(msgData)
				reviewMsg = &message.Message{
					Message: &mqtt2.Publish{
						Topic:     msgPublish.Topic,
						MessageID: msgPublish.MessageID,
						Payload:   []byte(payloadStr),
					},
				}
				isPass = true
				return
			}
		}
	}
	return true, nil, nil
}

// 从消息队列消费，获取消息发送到长连接，真正下发到客户端
func (s *Session) writeToConn(writeTimeout time.Duration) {
	defer func() {
		err := recover()
		if err != nil {
			log.Errorf("session %+v write panic %v", s, err)
		}
	}()
	//调用由外部tcp服务异步调用
	for msg := range s.pushCh {
		if s.IsStop() {
			log.Errorf("session %+v stop, close write", s)
			time.Sleep(500 * time.Millisecond)
			return
		}
		s.conn.SetWriteDeadline(time.Now().Add(writeTimeout))
		bytes, err := s.GetCodec().Encode(s.conn, msg)
		go reportMsgPublishBytes(s.GetPlatformId(), bytes) //上报下行消息流量到监控系统
		if err != nil {
			//发布消息失败后，主动停止服务端session，客户端被动关闭长连接
			log.Errorf("session %+v write msg %v to conn error %v", s, msg, err)
			s.Stop()
			return
		}
		if msg.Closing {
			//发布下线消息或失败消息给客户端后，就退出写协程，并stop长连接，客户端被动关闭长连接
			log.Infof("session %+v write msg %v close writeConn", s, msg)
			s.Stop()
			return
		}
	}
}

func reportMsgPublishBytes(platformId uint64, bytes int) {
	stat.MsgPublishBytesTotalAdd(platformId, bytes)
}
