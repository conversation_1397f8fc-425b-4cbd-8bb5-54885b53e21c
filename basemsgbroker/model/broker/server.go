package broker

import (
	"bufio"
	"net"
	"time"

	log "xim/baselib/logger"
	"xim/basemsgbroker/config"
	"xim/basemsgbroker/stat"
	"xim/basemsgbroker/util/mqtt"
	"xim/common/msg_ticker"
)

const (
	ppPlatformId = 3 //todo 皮皮直播平台id写死
)

type Acceptor interface {
	OnAccept(*Session)
}

// 汇总tcp/websocket服务
type Server struct {
	option        serverOption
	brokerService TcpService
	//cmTcpServer       *TcpServer
	cmWebsocketServer *WebsocketServer
	//ppWebsocketServer *XcWebsocketServer
}

func NewServer(conf *config.Config) *Server {
	s := &Server{
		option: newServerOption(
			//CmTcpListenerAddress(conf.CmTcpAddr),
			CmWebsocketListenerAddress(conf.CmWebsocketAddr),
			//PpWebsocketListenerAddress(conf.PpWebsocketAddr),
		),
		brokerService: NewBroker(conf),
	}
	return s
}

func (s *Server) SetBrokerService(bs TcpService) {
	s.brokerService = bs
}

// 长连接启动汇总
func (s *Server) Serve(podIp string) {
	// if err := s.serveTcp(); err != nil {
	// 	panic(err)
	// }
	if err := s.serveWebsocket(); err != nil {
		panic(err)
	}
	// if err := s.servePpWebsocket(podIp); err != nil {
	// 	panic(err)
	// }
}

// 开启tcp监听，接收并处理请求
//func (s *Server) serveTcp() error {
// cmListener, err := net.Listen("tcp", s.option.cmTcpAddr)
// if err != nil {
// 	log.Errorf("TCP listen %s error %v", s.option.cmTcpAddr, err)
// 	return err
// }
// s.cmTcpServer = NewTcpServer(s)
// log.Infof("TCP server start at: %v", cmListener.Addr())
// go s.cmTcpServer.Serve(cmListener)
//	return nil
//}

// 开启websocket监听，接收并处理请求
func (s *Server) serveWebsocket() error {
	cmListener, err := net.Listen("tcp", s.option.cmWsAddr)
	if err != nil {
		log.Errorf("Websocket listen %s error: %v", s.option.cmWsAddr, err)
		return err
	}
	s.cmWebsocketServer = NewWebsocketServer(s)
	log.Infof("Websocket server start at: %v", cmListener.Addr())
	go s.cmWebsocketServer.Serve(cmListener)
	return nil
}

// 开启皮皮直播的websocket监听，接收并处理请求
//func (s *Server) servePpWebsocket(podIp string) error {
// ppListener, err := net.Listen("tcp", s.option.ppWsAddr)
// if err != nil {
// 	log.Errorf("PpWebsocket listen %s error: %v", s.option.ppWsAddr, err)
// 	return err
// }
// s.ppWebsocketServer = NewXcWebsocketServer(s, ppPlatformId, s.option.readTimeout, s.option.writeTimeout, podIp)
// log.Infof("PpWebsocket server start at: %v", ppListener.Addr())
// go s.ppWebsocketServer.Serve(ppListener)
// 	return nil
// }

func (s *Server) Stop() {
	// if s.cmTcpServer != nil {
	// 	s.cmTcpServer.Stop()
	// }
	if s.cmWebsocketServer != nil {
		s.cmWebsocketServer.Stop()
	}
	// if s.ppWebsocketServer != nil {
	// 	s.ppWebsocketServer.Stop()
	// }
}

func (s *Server) OnAccept(session *Session) {
	s.brokerService.OnConnect(session) //连接建立处理

	defer func() {
		s.brokerService.OnDisconnect(session) //服务端主动断开连接
		session.Close()                       //关闭并删除session
	}()

	// 处理写消息给用户客户端
	go s.writeRoutine(session)

	// 处理用户客户端发送的消息
	s.readRoutine(session)
}

func (s *Server) writeRoutine(session *Session) {
	session.writeToConn(s.option.writeTimeout)
}

func (s *Server) readRoutine(session *Session) {
	defer func() {
		err := recover()
		if err != nil {
			log.Errorf("session %+v read panic %v", session, err)
		}
	}()
	var conn = session.conn
	reader := bufio.NewReaderSize(conn, s.option.readBufferSize)
	for {
		if session.IsStop() {
			log.Errorf("Session %+v stop, close read", session)
			time.Sleep(500 * time.Millisecond)
			return
		}
		if reader.Size() < 1 {
			continue
		}
		conn.SetReadDeadline(time.Now().Add(s.option.readTimeout))
		msg, bytes, err := session.GetCodec().Decode(reader)
		go reportMsgReceiveBytes(session, bytes, msg) //上报上行消息流量到监控系统
		if err != nil {
			log.Errorf("Read session %+v error %v, close conn", session, err)
			session.Stop()
			return
		}
		if msg == nil {
			continue
		}
		if err := s.brokerService.OnReceive(session, msg); err != nil {
			log.Errorf("OnReceive session:%+v err:%v, conn close.", session, err)
			session.Stop()
			return
		}
	}
}

func reportMsgReceiveBytes(session *Session, bytes int, msg mqtt.Message) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("report panic %v", r)
		}
	}()
	var platformId uint64
	if session != nil && session.GetPlatformId() > 0 {
		platformId = session.GetPlatformId()
	} else if msg != nil && msg.Type() == mqtt.TypeConnect {
		connMsg := msg.(*mqtt.Connect)
		if len(connMsg.Username) > 0 {
			ticket := msg_ticker.Ticket{}
			err := ticket.Decode(connMsg.Username)
			if err == nil {
				platformId = ticket.PlatformId
			}
		}
	}
	if platformId == 0 {
		return
	}
	stat.MsgReceiveBytesTotalAdd(platformId, bytes)
}
