package broker

import (
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"time"

	log "xim/baselib/logger"
	"xim/basemsgbroker/model/grpccli"
	"xim/basemsgbroker/model/kafka"
	"xim/basemsgbroker/util"
	"xim/common/msg_ticker"
	"xim/proto/api/basemsgbroker"
	"xim/proto/api/basemsgtopic"

	"github.com/gorilla/websocket"
)

/**
此websocket服务是小川科技的通用型websocket，简称xcwebsocket
*/

const (
	pingPeriod = 60 //ping的时间周期，单位为秒
)

type XcWebsocketServer struct {
	listener     net.Listener
	upgrader     *websocket.Upgrader
	acceptor     Acceptor
	platformId   uint64
	readTimeout  time.Duration
	writeTimeout time.Duration
	podIp        string
}

// 初始化websocket服务
func NewXcWebsocketServer(a Acceptor, platformId uint64, readTimeout, writeTimeout time.Duration, podIp string) *XcWebsocketServer {
	s := &XcWebsocketServer{
		upgrader: &websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true }, //不检查Origin
		},
		acceptor:     a,
		platformId:   platformId,
		readTimeout:  readTimeout,
		writeTimeout: writeTimeout,
		podIp:        podIp,
	}
	return s
}

// 实现handler接口，处理websocket长连接
func (s *XcWebsocketServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	//检查是否是ws协议
	if !websocket.IsWebSocketUpgrade(r) {
		log.Errorf("req is not websocket protocol")
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	//获取参数
	params, err := s.getRequestParams(r)
	if err != nil {
		log.Errorf("parse request %s error %v", r.RequestURI, err)
		rsp := map[string]interface{}{
			"ret": -1,
			"msg": "Parameter request error",
		}
		bs, _ := util.Json.Marshal(rsp)
		w.Write(bs)
		return
	}
	deviceid := params.DevId
	userid := util.FormatInt64(params.Mid)
	clientId := params.CliId

	//长连接本地权限校验
	needCheckPermission := true
	if params.Code != "" {
		codest, err := util.XcDecodeCode(params.Code)
		if err == nil && codest.Mid == params.Mid && time.Now().Unix()-codest.Ts < 7200 && params.DevId != "" {
			needCheckPermission = false
		}
	}
	//长连接第三方权限校验
	if needCheckPermission {
		tokenInfo := map[string]interface{}{
			"deviceid": deviceid,
			"token":    params.Token,
			"mid":      userid,
		}
		isValid, err := grpccli.CheckPlatformUserToken(s.platformId, tokenInfo)
		if err != nil {
			log.Errorf("check permission %+v error %v", params, err)
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		if !isValid {
			log.Errorf("bad token %s", r.RequestURI)
			rsp := map[string]interface{}{
				"ret": util.Error_XC_APP_TOKEN_ERROR,
				"msg": "Incorrect authorization code, please login again",
			}
			bs, _ := util.Json.Marshal(rsp)
			w.Write(bs)
			return
		}
	}

	// 将http请求升级为websocket-conn
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		// Upgrade失败时，内部会发送相应的http错误，不再需要发送，此处只记录错误日志即可
		log.Errorf("req Upgrade error %v", err)
		return
	}

	//为长连接生成中台识别的token
	extendData := msg_ticker.TicketExtendData{
		ClientId:      params.CliId,
		ClientVersion: params.CliVer,
	}
	extendDataJson, _ := util.Json.MarshalToString(extendData)
	ticker := msg_ticker.Ticket{
		UserId:         userid,
		PlatformId:     s.platformId,
		DeviceId:       deviceid,
		ExtendDataJson: extendDataJson,
	}
	ticker.EncodeToToken()
	mpToken := ticker.Token
	clientIp := util.ClientIP(r)

	//封装conn到session中
	wsconn := newXcWebsocketConn(conn)
	session := NewSession(ConnType_XcWebsocket, wsconn)
	//将生成的中台token设置到session中，方便后续中台识别判断
	session.SetToken(mpToken)
	session.SetClientIp(clientIp)

	//小川的websocket需要处理ping类型消息
	conn.SetPingHandler(func(message string) error {
		//延长读消息的过期期限
		err := conn.SetReadDeadline(time.Now().Add(s.readTimeout))
		if err != nil {
			return err
		}
		//上报ping心跳到topic服务
		go grpccli.ConnStatusReport(basemsgtopic.ConnStatusType_PingType, session.GetToken(), session.GetConnId(), s.podIp)
		// 上报ping心跳到kakfa
		msgEvent := &basemsgbroker.MsgEvent{
			Type:       basemsgbroker.MsgEventType_PingType,
			PlatformId: s.platformId,
			ClientId:   clientId,
			DeviceId:   deviceid,
			UserId:     userid,
			ConnId:     session.GetConnId(),
			LinkIp:     s.podIp,
			NanoTime:   time.Now().UnixNano(),
			Time:       time.Now().Unix(),
			Base:       session.GetBase(),
		}
		kafka.SendMsgEvent(msgEvent)
		//回pong消息给客户端
		err = conn.WriteControl(websocket.PongMessage, []byte(message), time.Now().Add(s.writeTimeout))
		if err == websocket.ErrCloseSent {
			return nil
		} else if e, ok := err.(net.Error); ok && e.Temporary() {
			return nil
		}
		return err
	})

	// 处理连接，断开连接，读上行消息，写下行消息
	go s.acceptor.OnAccept(session)
}

// 开启监听，并设置handler处理长连接
func (s *XcWebsocketServer) Serve(l net.Listener) error {
	s.listener = l
	return http.Serve(l, s)
}

// 停止监听
func (s *XcWebsocketServer) Stop() {
	if s.listener != nil {
		s.listener.Close()
	}
}

// websocket的请求参数
type requestParams struct {
	Mid    int64  `json:"h_m"`   //用户id
	DevId  string `json:"h_did"` //设备id
	Token  string `json:"token"` //第三方身份校验token
	Code   string `json:"code"`  //本地校验token
	CliId  uint32 `json:"h_dt"`  //客户端类型id
	CliVer string `json:"h_av"`  //客户端版本
}

func parseReqForm(values url.Values) (*requestParams, error) {
	firstem := func(ss []string) string {
		if len(ss) == 0 {
			return ""
		}
		return ss[0]
	}

	r := &requestParams{}
	v := reflect.ValueOf(r)
	t := v.Elem().Type()
	for i := 0; i < t.NumField(); i++ {
		tag := t.Field(i).Tag.Get("json")
		s := firstem(values[tag])
		if s == "" {
			continue
		}

		kind := v.Elem().Field(i).Type().Kind()
		switch kind {
		case reflect.String:
			v.Elem().Field(i).SetString(s)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			n, err := strconv.ParseInt(s, 10, 64)
			if err != nil {
				err = fmt.Errorf("param %s is not number", tag)
				return nil, err
			}
			v.Elem().Field(i).SetInt(n)
		default:
			log.Warnf("param %s is %s, not support", tag, kind.String())
		}
	}

	return r, nil
}

// 获取websocket的请求参数
func (s *XcWebsocketServer) getRequestParams(r *http.Request) (*requestParams, error) {
	err := r.ParseForm()
	if err != nil {
		return nil, err
	}

	params, err := parseReqForm(r.Form)
	if err != nil {
		return nil, err
	}
	return params, nil
}

// 将websocket的conn封装成net.conn，跟其他tcp-conn保持统一
type xcWebsocketConn struct {
	conn   *websocket.Conn
	reader io.Reader
}

func newXcWebsocketConn(conn *websocket.Conn) *xcWebsocketConn {
	return &xcWebsocketConn{conn: conn}
}

// Read reads data from the connection.
func (c *xcWebsocketConn) Read(b []byte) (int, error) {
	if c.reader == nil {
		var (
			pos         int
			messageType int
			reader      io.Reader
			err         error
		)
		for {
			messageType, reader, err = c.conn.NextReader()
			if err != nil {
				return pos, err
			}
			if messageType != websocket.BinaryMessage && messageType != websocket.TextMessage {
				continue
			}
			c.reader = reader
			break
		}
	}
	pos, err := c.reader.Read(b)
	if err != nil {
		if err == io.EOF {
			c.reader = nil
			// err不要重置为nil, 使用ioutil.ReadAll时, 内部的buffer.ReadFrom遇到EOF会自动重置为nil
			// err = nil
		}
	}
	return pos, err
}

// Write writes data to the connection.
func (c *xcWebsocketConn) Write(b []byte) (n int, err error) {
	if len(b) <= 0 {
		return
	}

	w, err := c.conn.NextWriter(websocket.BinaryMessage)
	if err != nil {
		return
	}

	n, err = w.Write(b)
	if err != nil {
		return
	}

	err = w.Close()
	return n, err
}

// Close closes the connection.
func (c *xcWebsocketConn) Close() error {
	return c.conn.Close()
}

// LocalAddr returns the local network address.
func (c *xcWebsocketConn) LocalAddr() net.Addr {
	return c.conn.LocalAddr()
}

// RemoteAddr returns the xllive network address.
func (c *xcWebsocketConn) RemoteAddr() net.Addr {
	return c.conn.RemoteAddr()
}

// SetDeadline sets the read and write deadlines associated
// with the connection. It is equivalent to calling both
// SetReadDeadline and SetWriteDeadline.
func (c *xcWebsocketConn) SetDeadline(t time.Time) (err error) {
	if err = c.conn.SetReadDeadline(t); err == nil {
		err = c.conn.SetWriteDeadline(t)
	}
	return
}

// SetReadDeadline sets the deadline for future Read calls
// and any currently-blocked Read call.
// A zero value for t means Read will not time out.
func (c *xcWebsocketConn) SetReadDeadline(t time.Time) error {
	return c.conn.SetReadDeadline(t)
}

// SetWriteDeadline sets the deadline for future Write calls
// and any currently-blocked Write call.
func (c *xcWebsocketConn) SetWriteDeadline(t time.Time) error {
	return c.conn.SetWriteDeadline(t)
}
