package broker

import (
	log "xim/baselib/logger"
	"xim/basemsgbroker/config"
	"xim/basemsgbroker/util/message"
	"xim/basemsgbroker/util/mqtt"
)

// grpc内部请求处理
type GrpcService interface {
	//发布消息
	OnPublish([]uint64, *message.Message) error
}

// tcp请求处理
type TcpService interface {
	//建立连接
	OnConnect(*Session)
	//断开连接
	OnDisconnect(*Session)
	//接收消息并处理
	OnReceive(*Session, mqtt.Message) error
}

type Broker struct {
	handler Handler
}

func NewBroker(conf *config.Config) *Broker {
	b := &Broker{
		handler: newCmHandler(conf.PodIP),
	}
	return b
}

// 设置消息处理接口handler
func (b *Broker) SetHandler(h Handler) {
	b.handler = h
}

// 发布消息
func (b *Broker) OnPublish(connIds []uint64, msg *message.Message) error {
	return b.handler.Publish(connIds, msg)
}

// 连接建立处理
func (b *Broker) OnConnect(session *Session) {
	log.Debugf("session %+v", session)

	//mqtt在连接初始还没解析出token，因此mqtt连接不会往下执行，下面代码主要兼容ws，将其转成mqtt统一处理
	//mqtt连接实际上是在OnReceive函数处理，先解析消息，识别是conn消息再连接
	if session.GetToken() != "" {
		b.handler.Handle(session, &mqtt.Connect{Username: []byte(session.GetToken())})
	}
}

// 连接断开处理
func (b *Broker) OnDisconnect(session *Session) {
	log.Debugf("session %+v", session)

	// 客户端可能不会发送disconnect消息，服务端主动disconnect
	if session.GetToken() != "" {
		b.handler.Handle(session, &mqtt.Disconnect{})
	}
}

// 消息接收处理
func (b *Broker) OnReceive(session *Session, msg mqtt.Message) error {
	log.Debugf("session %+v, msg %+v", session, msg)
	return b.handler.Handle(session, msg)
}
