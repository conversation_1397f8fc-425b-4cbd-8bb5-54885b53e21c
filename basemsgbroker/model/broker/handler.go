package broker

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"xim/baselib/logger"
	"xim/baselib/server/env"
	baselibUtil "xim/baselib/util"
	"xim/basemsgbroker/model/grpccli"
	"xim/basemsgbroker/model/kafka"
	"xim/basemsgbroker/model/redis"
	"xim/basemsgbroker/stat"
	"xim/basemsgbroker/util"
	"xim/basemsgbroker/util/codec/ws"
	"xim/basemsgbroker/util/compressor"
	"xim/basemsgbroker/util/message"
	"xim/basemsgbroker/util/mqtt"
	"xim/common/msg_ticker"
	"xim/proto/api/basemsgbroker"
	"xim/proto/api/basemsgtopic"
)

// 消息具体逻辑处理接口
type Handler interface {
	//处理用户上行消息
	Handle(*Session, mqtt.Message) error
	//发布下行消息
	Publish([]uint64, *message.Message) error
}

// 通用的消息处理
type cmHandler struct {
	podIp string
}

func newCmHandler(podIp string) *cmHandler {
	h := &cmHandler{
		podIp: podIp,
	}
	return h
}

// 处理上行消息
func (h *cmHandler) Handle(session *Session, msg mqtt.Message) error {
	var (
		ack *message.Message
		err error
	)
	logger.Infof("Handle conntype %d connid %d usertoken %s msgtype %s",
		session.GetCtype(), session.GetConnId(), session.GetToken(), msg.TypeName())
	// 根据消息类型处理，websocket的text和binary消息都统一转为publish类型
	switch msg.Type() {
	case mqtt.TypeConnect:
		ack, err = h.onConnect(session, msg.(*mqtt.Connect))
	case mqtt.TypePublish:
		ack, err = h.onPublish(session, msg.(*mqtt.Publish))
	case mqtt.TypePingreq:
		ack, err = h.onPingreq(session, msg.(*mqtt.Pingreq))
	case mqtt.TypeDisconnect:
		ack, err = h.onDisconnect(session, msg.(*mqtt.Disconnect))
	case mqtt.TypeSubscribe:
		ack, err = h.onSubscribe(session, msg.(*mqtt.Subscribe))
	case mqtt.TypeUnsubscribe:
		ack, err = h.onUnsubscribe(session, msg.(*mqtt.Unsubscribe))
	case mqtt.TypePuback:
		h.onPuback(session, msg.(*mqtt.Puback)) //接收到ack包，不需要再返回ack
	}
	// 处理失败就直接返回，
	if err != nil {
		return fmt.Errorf("Handle session %+v msg %+v error %v", session, msg, err)
	}
	// 如果有ack回包，则发送给用户客户端
	if ack != nil {
		if err = session.Write(ack); err != nil {
			return err
		}
	}
	return nil
}

func (h *cmHandler) onConnect(session *Session, msg *mqtt.Connect) (*message.Message, error) {
	ack := &mqtt.Connack{ReasonCode: 0}
	ret := &message.Message{Message: ack, Closing: true}
	// 校验token
	token := msg.Username
	session.SetToken(string(token))
	if len(token) == 0 {
		logger.Infof("onConnect ConnackBadUserNameOrPassword")
		ack.ReasonCode = mqtt.ConnackBadUserNameOrPassword
		return ret, nil
	}
	ticker := msg_ticker.Ticket{}
	err := ticker.Decode(token)
	if err != nil {
		logger.Infof("onConnect ConnackBadUserNameOrPassword")
		ack.ReasonCode = mqtt.ConnackBadUserNameOrPassword
		return ret, nil
	}
	if !ticker.Allow() {
		logger.Infof("onConnect ConnackBadUserNameOrPassword")
		ack.ReasonCode = mqtt.ConnackBadUserNameOrPassword
		return ret, nil
	}
	session.SetBase(ticker.ExtendData.BaseParam)
	if ticker.ExtendData.BaseParam != nil {
		logger.Debugf("ticker.ExtendData.BaseParam:%+v", ticker.ExtendData.BaseParam)
	}

	//新增同一用户同一设备长连接建立频率限制
	userDeviceLockKey := redis.GetPlatformUserDeviceidKey(ticker.PlatformId, ticker.UserId, ticker.DeviceId)
	err = redis.GetLock(userDeviceLockKey, time.Second)
	if err != nil {
		logger.Errorf("onConnect ticker %+v conn lock error %v", ticker, err)
		ack.ReasonCode = mqtt.ConnackUnacceptable
		return ret, nil
	}
	defer redis.DelLock(userDeviceLockKey)

	session.SetChecked()
	session.SetUserInfo(&ticker)
	ret.Closing = false

	//上报conn到topic服务，同步上报，确保连接状态是第一个上报的消息
	grpccli.ConnStatusReport(basemsgtopic.ConnStatusType_ConnectType, session.GetToken(), session.GetConnId(), h.podIp)

	//上报conn到kafka，同步上报，确保连接状态是第一个上报的消息
	msgEvent := &basemsgbroker.MsgEvent{
		Type:       basemsgbroker.MsgEventType_ConnectType,
		PlatformId: session.GetPlatformId(),
		ClientId:   session.GetClientId(),
		DeviceId:   session.GetDeviceId(),
		UserId:     session.GetUserId(),
		ConnId:     session.GetConnId(),
		LinkIp:     h.podIp,
		Time:       time.Now().Unix(),
		Msg:        "",
		Topic:      "",
		MsgId:      "",
		NanoTime:   time.Now().UnixNano(),
		Base:       ticker.ExtendData.BaseParam,
	}
	kafka.SendMsgEvent(msgEvent)

	if session.GetCtype() == ConnType_Websocket || session.GetCtype() == ConnType_XcWebsocket {
		// websocket不需要回复
		return nil, nil
	}
	return ret, nil
}
func (h *cmHandler) onPublish(session *Session, msg *mqtt.Publish) (*message.Message, error) {
	if env.IsTest() {
		logger.Debugf("onPublish sessionType=%+v msg.Topic=%+v msg.Qos=%+v msg.MessageID=%+v", session.GetCtype(), string(msg.Topic), msg.QoS, msg.MessageID)
	}
	if session.GetToken() == "" {
		logger.Warn("onPublish token empty")
		return nil, util.NewError(util.RetUserTokenError)
	}
	if !session.IsChecked() {
		logger.Warn("onPublish not checked")
		return nil, util.NewError(util.RetSessionUncheck)
	}
	if session.IsStop() {
		logger.Warn("onPublish stopped")
		return nil, util.NewError(util.RetSessionClosed)
	}

	//所有publish消息都充当ping心跳消息，需要作为ping心跳同步到topic服务
	go grpccli.ConnStatusReport(basemsgtopic.ConnStatusType_PingType, session.GetToken(), session.GetConnId(), h.podIp)
	ret := &message.Message{}
	msgId := util.FormatUint32(uint32(msg.MessageID))
	if session.GetCtype() == ConnType_Websocket {
		// websocket消息
		if string(msg.Topic) == "ping" {
			// 如果本身是ping心跳消息，需按ping逻辑处理，不走publish逻辑

			//上报websocket的ping到kafka
			msgEvent := &basemsgbroker.MsgEvent{
				Type:       basemsgbroker.MsgEventType_PingType,
				PlatformId: session.GetPlatformId(),
				ClientId:   session.GetClientId(),
				DeviceId:   session.GetDeviceId(),
				UserId:     session.GetUserId(),
				ConnId:     session.GetConnId(),
				LinkIp:     h.podIp,
				Time:       time.Now().Unix(),
				Msg:        string(msg.Payload),
				Topic:      "",
				MsgId:      "",
				NanoTime:   time.Now().UnixNano(),
				Base:       session.GetBase(),
			}
			kafka.SendMsgEvent(msgEvent)
			//回pong的ack包给客户端
			ack := &mqtt.Publish{
				Topic:     []byte("pong"),
				MessageID: msg.MessageID,
				Payload:   msg.Payload,
			}
			ret.Message = ack
			return ret, nil
		} else if string(msg.Topic) == "puback" {
			//如果消息是下行消息的ack回包，直接执行下列逻辑

			h.onPuback(session, &mqtt.Puback{MessageID: msg.MessageID})
			return nil, nil
		} else if msg.QoS > 0 {
			//如果上行消息需要回ack包，设置好回包等待下发

			ack := &mqtt.Publish{
				Topic:     []byte("puback"),
				MessageID: msg.MessageID,
				Payload:   []byte{},
			}
			ret.Message = ack
		} else {
			ret = nil
		}
	} else if session.GetCtype() == ConnType_XcWebsocket {
		msgdata := &message.XcWsMsg{}
		err := json.Unmarshal(msg.Payload, msgdata)
		if err != nil {
			logger.Errorf("xcwebsocket unmarshal msg %s error %v", string(msg.Payload), err)
			return nil, nil
		}
		err = msgdata.Validate() //检查消息数据是否符合规范
		if err != nil {
			logger.Errorf("xcwebsocket receive invalid msg %+v", msgdata)
			return nil, nil
		}
		//目前收到ack包，不处理，直接返回
		if msgdata.Type == message.WsMsgTypeAck {
			return nil, nil
		}
		if msgdata.Ack == 1 {
			//发ack包,也是发binary消息
			ackdata := message.XcWsMsg{
				Id:   msgdata.Id,
				Type: message.WsMsgTypeAck,
			}
			ackpayload, _ := json.Marshal(ackdata)
			ack := &mqtt.Publish{Payload: ackpayload}
			ret.Message = ack
		} else {
			ret = nil
		}
		switch msgdata.Type {
		case message.WsMsgTypeInit, message.WsMsgTypeInitResp:
			err = xcWebsocketInit(session, msgdata)
			if err != nil {
				logger.Errorf("xcwebsocket session %+v init error %v", session, err)
			}
			return ret, err
		case message.WsMsgTypeEcho:
			//消息写回客户端，下行消息是XcWsMsg的json包
			echoMsg := &mqtt.Publish{Payload: msg.Payload}
			session.Write(&message.Message{Message: echoMsg})
			return ret, nil
		case message.WsMsgTypeBiz:
			msg.Payload = msgdata.Data
			msg.Topic = []byte(msgdata.ChannelId)
			msgId = msgdata.Id
		}

	} else {
		//mqtt消息
		if msg.QoS > 0 {
			// 如果qos大于0，需回ack包
			ack := &mqtt.Puback{
				MessageID: msg.MessageID,
			}
			ret.Message = ack
		} else {
			// 如果qos为0，不用会ack包
			ret = nil
		}
	}

	//上报publish到kafka
	msgEvent := &basemsgbroker.MsgEvent{
		Type:       basemsgbroker.MsgEventType_PublishType,
		PlatformId: session.GetPlatformId(),
		ClientId:   session.GetClientId(),
		DeviceId:   session.GetDeviceId(),
		UserId:     session.GetUserId(),
		ConnId:     session.GetConnId(),
		LinkIp:     h.podIp,
		NanoTime:   time.Now().UnixNano(),
		Time:       time.Now().Unix(),
		Msg:        string(msg.Payload),
		Topic:      string(msg.Topic),
		MsgId:      msgId,
		Base:       session.GetBase(),
	}
	if env.IsTest() {
		logger.Debugf("onPublish msgEvent=%+v", msgEvent)
	}
	kafka.SendMsgEvent(msgEvent)
	return ret, nil
}
func (h *cmHandler) onPingreq(session *Session, msg *mqtt.Pingreq) (*message.Message, error) {
	if session.GetToken() == "" {
		return nil, util.NewError(util.RetUserTokenError)
	}
	if !session.IsChecked() {
		return nil, util.NewError(util.RetSessionUncheck)
	}
	if session.IsStop() {
		return nil, util.NewError(util.RetSessionClosed)
	}

	//上报ping到topic服务
	go grpccli.ConnStatusReport(basemsgtopic.ConnStatusType_PingType, session.GetToken(), session.GetConnId(), h.podIp)

	//上报ping到kafka
	msgEvent := &basemsgbroker.MsgEvent{
		Type:       basemsgbroker.MsgEventType_PingType,
		PlatformId: session.GetPlatformId(),
		ClientId:   session.GetClientId(),
		DeviceId:   session.GetDeviceId(),
		UserId:     session.GetUserId(),
		ConnId:     session.GetConnId(),
		LinkIp:     h.podIp,
		NanoTime:   time.Now().UnixNano(),
		Time:       time.Now().Unix(),
		Base:       session.GetBase(),
	}
	kafka.SendMsgEvent(msgEvent)
	ack := &mqtt.Pingresp{}
	ret := &message.Message{Message: ack}
	return ret, nil
}
func (h *cmHandler) onDisconnect(session *Session, msg *mqtt.Disconnect) (*message.Message, error) {
	//如果session的token为空，为错误长连接，不上报
	//如果session已经关闭过，不用重复关闭上报
	if session.GetToken() == "" || session.IsDisconned() {
		return nil, nil
	}
	//先设置为关闭状态，后续其他关闭请求就不处理
	session.SetDisconned()
	//上报disconn到topic服务
	go grpccli.ConnStatusReport(basemsgtopic.ConnStatusType_CloseType, session.GetToken(), session.GetConnId(), h.podIp)

	//上报disconn到kafka
	msgEvent := &basemsgbroker.MsgEvent{
		Type:       basemsgbroker.MsgEventType_CloseType,
		PlatformId: session.GetPlatformId(),
		ClientId:   session.GetClientId(),
		DeviceId:   session.GetDeviceId(),
		UserId:     session.GetUserId(),
		ConnId:     session.GetConnId(),
		LinkIp:     h.podIp,
		NanoTime:   time.Now().UnixNano(),
		Time:       time.Now().Unix(),
		Base:       session.GetBase(),
	}
	kafka.SendMsgEvent(msgEvent)

	return nil, nil
}

// 目前还不支持用户自己订阅topic，考虑到topic跟业务有关，需要业务检查用户是否有订阅相关权限
func (h *cmHandler) onSubscribe(session *Session, msg *mqtt.Subscribe) (*message.Message, error) {
	if session.GetToken() == "" {
		return nil, util.NewError(util.RetUserTokenError)
	}
	if !session.IsChecked() {
		return nil, util.NewError(util.RetSessionUncheck)
	}
	if session.IsStop() {
		return nil, util.NewError(util.RetSessionClosed)
	}
	//直接返回，不执行实际订阅操作
	ack := &mqtt.Suback{
		MessageID:   msg.MessageID,
		ReasonCodes: make([]uint8, 0, len(msg.Subscriptions)),
	}
	ret := &message.Message{Message: ack}
	return ret, nil
}

// 目前还不支持用户取消订阅topic，考虑到topic跟业务有关，需要业务检查用户是否有订阅相关权限
func (h *cmHandler) onUnsubscribe(session *Session, msg *mqtt.Unsubscribe) (*message.Message, error) {
	if session.GetToken() == "" {
		return nil, util.NewError(util.RetUserTokenError)
	}
	if !session.IsChecked() {
		return nil, util.NewError(util.RetSessionUncheck)
	}
	if session.IsStop() {
		return nil, util.NewError(util.RetSessionClosed)
	}
	//直接返回，不执行实际取消订阅操作
	ack := &mqtt.Unsuback{
		MessageID:   msg.MessageID,
		ReasonCodes: make([]uint8, 0, len(msg.Topics)),
	}
	ret := &message.Message{Message: ack}
	return ret, nil
}
func (h *cmHandler) onPuback(session *Session, msq *mqtt.Puback) {
	//token有问题直接返回，不处理此消息
	if session.GetToken() == "" {
		return
	}

	//上报到监控系统
	go reportPubackMsg(session.GetPlatformId(), h.podIp, session.GetConnId(), msq.MessageID)
}

// 发布下行消息
func (h *cmHandler) Publish(connIds []uint64, msg *message.Message) error {
	//发布下行消息
	writeConnIds := make([]uint64, 0)
	for _, connId := range connIds {
		session := GetSession(connId)
		if session == nil {
			continue
		}
		writeConnIds = append(writeConnIds, connId)
		err := session.Write(msg)
		if err == nil {
			logger.Infof("session %+v writeToChannel msg %+v success", session, *msg)
			if env.IsTest() {
				logger.Debugf("writeToChannel msg=%+v", baselibUtil.JsonStr(msg))
			}
		} else {
			logger.Errorf("session %+v writeToChannel msg %+v error %v", session, *msg, err)
		}
	}
	//上报到监控系统
	if pubMsg, ok := msg.Message.(*mqtt.Publish); ok && pubMsg.QoS > 0 {
		go reportPublishMsg(h.podIp, writeConnIds, pubMsg.MessageID)
	}
	return nil
}

func xcWebsocketInit(session *Session, msg *message.XcWsMsg) error {
	//解析初始化消息
	initdata := &message.XcWsInitMsgData{}
	err := json.Unmarshal(msg.Data, initdata)
	if err != nil {
		logger.Errorf("xcwebsocket wrong initdata %s", string(msg.Data))
		return err
	}

	//协商设置ContentEncoding
	if initdata.ContentEncoding != "" {
		ss := strings.Split(initdata.ContentEncoding, ",")
		for _, s := range ss {
			s = strings.TrimSpace(s)
			if s == "application/json" {
				initdata.ContentEncoding = "application/json"
				break
			}
		}
	}

	//设置长连接的sessionid为用户id+设备id
	initdata.SessionId = util.GetXCSessionId(session.GetDeviceId(), session.GetUserId())
	mid, _ := util.ParseInt64(session.GetUserId())

	//协商设置AcceptEncoding，并设置长连接消息的解压缩器
	if initdata.AcceptEncoding != "" {
		ss := strings.Split(initdata.AcceptEncoding, ",")
		isSet := false
		for _, s := range ss {
			s = strings.TrimSpace(s)
			if s == "deflate" {
				session.UpdateCodec(&ws.XcWsCodec{Compressor: compressor.Deflater})
				initdata.AcceptEncoding = "deflate"
				isSet = true
				break
			}
		}
		if !isSet {
			for _, s := range ss {
				s = strings.TrimSpace(s)
				if s == "gzip" {
					session.UpdateCodec(&ws.XcWsCodec{Compressor: compressor.Gziper})
					initdata.AcceptEncoding = "gzip"
					isSet = true
					break
				}
			}
		}
	}

	//回复初始化消息，下发协商好的数据
	if msg.Type == message.WsMsgTypeInit {
		initdata.Sessionid = initdata.SessionId
		initdata.PingInterval = pingPeriod
		codest := util.XcCodeSt{Mid: mid, Ts: time.Now().Unix()}
		initdata.Code, _ = util.XcEncodeCode(codest)
		bs, _ := json.Marshal(initdata)
		rspdata := &message.XcWsMsg{
			Id:   util.Genmsgid(),
			Type: message.WsMsgTypeInitResp,
			Data: json.RawMessage(bs),
		}
		rsppayload, _ := json.Marshal(rspdata)
		rspMsg := &mqtt.Publish{Payload: rsppayload, Topic: []byte(ws.MsgWrite_Direct)}
		session.Write(&message.Message{Message: rspMsg})
	}

	logger.Debugf("xcwebsocket session %+v inited", session)
	return nil
}

func reportPublishMsg(podIp string, connIds []uint64, msgId uint16) {
	if len(connIds) <= 0 {
		return
	}
	// 对应长连接都是同一个平台的
	var platformId uint64
	for _, connId := range connIds {
		session := GetSession(connId)
		if session != nil && session.GetToken() != "" {
			platformId = session.GetPlatformId()
			break
		}
	}

	// 保存记录到缓存
	nowtime := time.Now().UnixNano()
	redis.MSetMsgPublishLog(podIp, connIds, msgId, nowtime)
	// 上报消息发布总数到监控系统
	stat.PublishMsgTotalAdd(platformId, len(connIds))
}

func reportPubackMsg(platformId uint64, podIp string, connId uint64, msgId uint16) {
	endTime := time.Now().UnixNano()
	startTime, _ := redis.GetMsgPublishLog(podIp, connId, msgId)
	if startTime <= 0 {
		return
	}
	defer redis.DelMsgPublishLog(podIp, connId, msgId)

	//上报消息成功发布总数到监控系统
	stat.PublishMsgSuccAdd(platformId, 1)
	//上报下行消息（publish和puback来回）耗时数据到监控系统
	diffTime := float64(endTime-startTime) / (1000 * 1000 * 1000)
	stat.PublishMsgLatencySecond(platformId, diffTime)
}
