package broker

import (
	"net"

	log "xim/baselib/logger"
)

type TcpServer struct {
	listener net.Listener
	acceptor Acceptor
}

// 初始化tcp服务
func NewTcpServer(a Acceptor) *TcpServer {
	s := &TcpServer{
		acceptor: a,
	}
	return s
}

// 开启tcp监听服务
func (s *TcpServer) Serve(l net.Listener) error {
	s.listener = l
	for {
		conn, err := s.listener.Accept()
		if err != nil {
			log.Errorf("listener %+v accept conn error %v", s.listener, err)
			continue
		}
		log.Infof("new_conn remote addr %v", conn.RemoteAddr())
		session := NewSession(ConnType_TCP, conn)
		go s.acceptor.OnAccept(session)
	}
}

// 停止tcp监听服务
func (s *TcpServer) Stop() {
	if s.listener != nil {
		s.listener.Close()
	}
}
