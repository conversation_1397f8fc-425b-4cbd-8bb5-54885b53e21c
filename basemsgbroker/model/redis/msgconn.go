package redis

import (
	"fmt"
	"time"

	log "xim/baselib/logger"

	"github.com/go-redis/redis"
)

// 下行消息发送记录
// type: string（有过期时间）
// key：下行消息唯一标识，后缀为：podIp_connId_msgId
// value：时间戳，纳秒级别
func GetMsgPublishLogKey(podIp string, connId uint64, msgId uint16) string {
	return fmt.Sprintf("basemsgbroker:s:msgid:%s:%d:%d", podIp, connId, msgId)
}

func MSetMsgPublishLog(podIp string, connIds []uint64, msgId uint16, value int64) error {
	pipe := getCache(cacheMaster).Pipeline()
	for _, connId := range connIds {
		key := GetMsgPublishLogKey(podIp, connId, msgId)
		pipe.Set(key, value, 2*time.Minute) //默认设置记录的过期时间为分钟级别
	}
	_, err := pipe.Exec()
	if err != nil {
		log.Errorf("MSetMsgPublishLog podIp %s connIds %+v msgId %d error %v",
			podIp, connIds, msgId, err)
		return err
	}
	return nil
}

func GetMsgPublishLog(podIp string, connId uint64, msgId uint16) (int64, error) {
	key := GetMsgPublishLogKey(podIp, connId, msgId)
	value, err := getCache(cacheMaster).Get(key).Int64()
	if err == redis.Nil {
		log.Infof("GetMsgPublishLog key %s missing", key)
		return 0, nil
	}
	if err != nil {
		log.Errorf("GetMsgPublishLog key %s error %v", key, err)
		return 0, err
	}
	return value, nil
}

func DelMsgPublishLog(podIp string, connId uint64, msgId uint16) error {
	key := GetMsgPublishLogKey(podIp, connId, msgId)
	err := getCache(cacheMaster).Del(key).Err()
	if err != nil {
		log.Errorf("DelMsgPublishLog key %s error %v", key, err)
		return err
	}
	return nil
}
