package redis

import (
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis"
)

// lock错误
var (
	ErrLockFailed = errors.New("lock failed")
)

func GetPlatformUserDeviceidKey(platformId uint64, userId, deviceId string) string {
	return fmt.Sprintf("basemsgbroker:s:userunique:%d:%s:%s", platformId, userId, deviceId)
}

// GetLock 获取锁
func GetLock(key string, expiration time.Duration) error {
	v := time.Now().UnixNano()
	ok, err := getCache(cacheLock).SetNX(key, v, expiration).Result()
	if err != nil { //缓存操作失败
		return err
	}
	if !ok { //拿锁失败
		return ErrLockFailed
	}
	return nil
}

// DelLock 删除锁
func DelLock(key string) error {
	err := getCache(cacheLock).Del(key).Err()
	if err != nil && err != redis.Nil { //删除缓存失败
		return err
	}
	return nil
}
