package kafka

import (
	"context"
	"time"
	"xim/baselib/trace"

	"xim/baselib/logger"
	"xim/basemsgbroker/model/syncmsg"
	"xim/proto/api/basemsgbroker"
)

func SendMsgEvent(e *basemsgbroker.MsgEvent) (err error) {
	select {
	case sendChannel <- e:
	default:
		logger.Infof("push channel fail")
	}
	return nil
}

var sendChannel = make(chan *basemsgbroker.MsgEvent, 128*1024)

func init() {
	for i := 0; i < 100; i++ {
		go func() {
			for event := range sendChannel {
				ctx, _ := trace.NewSpan(context.Background(), "SendMsgEvent")
				syncmsg.SyncMsgToPlatform(ctx, event)
			}
		}()
	}
	go func() {
		ticker := time.NewTicker(time.Second)
		for range ticker.C {
			logger.Infof("channelLen %d", len(sendChannel))
		}
	}()
}
