package kafka

import (
	"fmt"
	"sync"
	"time"

	log "xim/baselib/logger"
	"xim/basemsgbroker/config"

	"github.com/IBM/sarama"
)

var producerHelper *Producer

type Producer struct {
	addrs     []string
	topic     string
	writerNum int
	msg<PERSON>han   chan []byte
	mutext    sync.RWMutex
	stoped    bool
}

func NewProducer(conf config.KafkaConfig) error {
	producerHelper = &Producer{
		addrs:     conf.Addrs,
		topic:     conf.Producetopic,
		writerNum: conf.Writers,
		msgChan:   make(chan []byte, 256),
		mutext:    sync.RWMutex{},
	}
	for i := 1; i <= producerHelper.writerNum; i++ {
		go producerHelper.initWriter(i)
	}
	return nil
}

func StopProducer() {
	log.Infof("Kafka Producer stop")
	producerHelper.mutext.Lock()
	producerHelper.stoped = true
	producerHelper.mutext.Unlock()
}

func SendMsg(msg []byte) error {
	select {
	case producerHelper.msgChan <- msg:
		return nil
	default:
		return fmt.Errorf("msg<PERSON>han busy, try again")
	}
	return nil
}

// 初始化kafka生产者，并开始监听channel，往kafka发送消息
func (p *Producer) initWriter(writerNo int) {
	config := sarama.NewConfig()
	config.Producer.Return.Errors = true
	ap, err := sarama.NewAsyncProducer(p.addrs, config)
	if err != nil {
		log.Errorf("WriterNo.%d NewAsyncProducer %+v error %v", writerNo, p.addrs, err)
		return
	}
	log.Infof("WriterNo.%d NewAsyncProducer success", writerNo)

	defer func() {
		log.Infof("WriterNo.%d stop", writerNo)
		ap.Close()
	}()

	// 定时检查错误，并输出
	go func() {
		tiker := time.NewTicker(time.Second)
		defer tiker.Stop()
		for {
			p.mutext.RLock()
			if p.stoped {
				p.mutext.RUnlock()
				break
			}
			p.mutext.RUnlock()

			select {
			case <-tiker.C:
				//不做什么
			case err := <-ap.Errors():
				if err != nil {
					log.Errorf("WriterNo.%d sendmsg error %v", writerNo, err)
				}
			}
		}
	}()

	//循环从channel获取消息，发到kafka
	for {
		var ok bool
		var msg []byte
		// 阻塞从channel获取消息
		select {
		case msg, ok = <-p.msgChan:
		}
		if !ok {
			break
		}
		// 封装消息并发到kafka
		pMsg := &sarama.ProducerMessage{
			Topic: p.topic,
			Value: sarama.ByteEncoder(msg),
		}
		ap.Input() <- pMsg
		log.Infof("WriterNo.%d sendmsg %s", writerNo, string(msg))
	}
}
