package grpccli

import (
	"context"
	"fmt"
	"time"
	"xim/baselib/grpccli"
	"xim/baselib/logger"
	"xim/proto/api/basemsgtopic"

	"google.golang.org/grpc"
)

var basemsgtopicClient basemsgtopic.SClient

func NewBasemsgtopicClinet() error {
	var err error
	var conn *grpc.ClientConn
	conn, err = grpccli.NewLocalConn()
	if err != nil {
		return err
	}
	basemsgtopicClient = basemsgtopic.NewSClient(conn)
	return nil
}

func ConnStatusReport(statusType basemsgtopic.ConnStatusType, token string, connId uint64, podIp string) error {
	req := &basemsgtopic.ConnStatusReportReq{
		Type:      statusType,
		UserToken: token,
		ConnId:    connId,
		BrokerIP:  podIp,
		OpTime:    time.Now().Unix(),
	}
	rsp, err := basemsgtopicClient.ConnStatusReport(context.Background(), req)
	defer func() {
		//if err == nil {
		//	log.Infof("ConnStatusReport req %+v rsp %v", req, rsp)
		//} else
		if err != nil {
			logger.Errorf("ConnStatusReport req %+v error %v", req, err)
		}
	}()
	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	}
	return err
}
