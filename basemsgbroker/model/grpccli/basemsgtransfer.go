package grpccli

import (
	"context"
	"fmt"
	"time"
	"xim/baselib/grpccli"
	log "xim/baselib/logger"
	"xim/proto/api/basemsgtransfer"

	"google.golang.org/grpc"
)

var baseMsgTransferClient basemsgtransfer.SClient

func NewBaseMsgTransferClient() error {
	var err error
	var conn *grpc.ClientConn
	conn, err = grpccli.NewLocalConn()
	if err != nil {
		return err
	}
	baseMsgTransferClient = basemsgtransfer.NewSClient(conn)
	return nil
}

func HandleMessage(ctx context.Context, req *basemsgtransfer.HandleMsgReq) error {
	ctx, cancel := context.WithTimeout(ctx, 1*time.Second)
	defer cancel()
	rsp, err := baseMsgTransferClient.HandleMsg(ctx, req)
	if err != nil {
		log.Errorf("HandleMessage error %v", err)
		return err
	} else if rsp == nil {
		log.Errorf("HandleMessage rsp nil")
		return fmt.Errorf("rsp nil")
	} else if rsp.GetBase().GetCode() != 0 {
		log.Errorf("HandleMessage failmsg %s", rsp.GetBase().GetMsg())
		return fmt.Errorf(rsp.GetBase().GetMsg())
	}
	return nil
}
