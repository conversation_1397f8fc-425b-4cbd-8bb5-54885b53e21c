package grpccli

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"xim/baselib/grpccli"
	log "xim/baselib/logger"
	"xim/basemsgbroker/util"
	"xim/proto/api/basemsgcallout"
)

var basemsgcalloutClient basemsgcallout.SClient

func NewBasemsgcalloutClient() error {
	var err error
	var conn *grpc.ClientConn
	conn, err = grpccli.NewLocalConn()
	if err != nil {
		return err
	}
	basemsgcalloutClient = basemsgcallout.NewSClient(conn)
	return nil
}

func CheckPlatformUserToken(platformId uint64, tokenInfo map[string]interface{}) (bool, error) {
	tokenData, err := util.Json.MarshalToString(tokenInfo)
	if err != nil {
		log.Errorf("CheckPlatformUserToken platformid %d token %+v marshal error %v",
			platformId, tokenInfo, err)
		return false, err
	}

	req := &basemsgcallout.CheckPlatformUserTokenReq{PlatformId: platformId, TokenData: tokenData}
	rsp, err := basemsgcalloutClient.CheckPlatformUserToken(context.Background(), req)
	isValid := false

	defer func() {
		if err == nil {
			log.Infof("CheckPlatformUserToken req %+v rsp %+v", req, rsp)
		} else {
			log.Errorf("CheckPlatformUserToken req %+v error %v", req, err)
		}
	}()

	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	} else if rsp.Data == nil {
		err = fmt.Errorf("rspdata nil")
	} else {
		isValid = rsp.Data.IsValid
	}
	return isValid, err
}
