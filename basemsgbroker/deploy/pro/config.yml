# 服务监听地址
addr: 0.0.0.0:8080

# 长连接服务监听地址
cmtcpaddr: 0.0.0.0:8080
cmwebsocketaddr: 0.0.0.0:8081
ppwebsocketaddr: 0.0.0.0:9722 #皮皮直播的ws监听

# 日志配置文件
logger: deploy/logger.xml

# 数据库配置
db:
  #实例列表
  instances:
    master:
      driver: mysql
      url: dev:tJipDi4shJaj@tcp(rm-uf6u858f8go98ji8y.mysql.rds.aliyuncs.com:3306)/xllivemp_base_conf?timeout=3s&readTimeout=3s&writeTimeout=3s&charset=utf8mb4

# 缓存配置
redis:
  #实例列表
  instances:
    master:
      url: r-uf6bkzfx9xrokicfv3.redis.rds.aliyuncs.com:6379
      password: 4YVQ6op1
    lock:
      url: r-uf6bkzfx9xrokicfv3.redis.rds.aliyuncs.com:6379
      password: 4YVQ6op1

kafka:
  producetopic: upstream_msg
  writers: 10
  addrs:
  - alikafka-pre-cn-2r42o82wv005-1-vpc.alikafka.aliyuncs.com:9092
  - alikafka-pre-cn-2r42o82wv005-2-vpc.alikafka.aliyuncs.com:9092
  - alikafka-pre-cn-2r42o82wv005-3-vpc.alikafka.aliyuncs.com:9092