package controller

import (
	"context"

	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/basemsgbroker/config"
	"xim/basemsgbroker/model"
	"xim/basemsgbroker/model/broker"
	"xim/basemsgbroker/util"
	"xim/basemsgbroker/util/message"
	"xim/basemsgbroker/util/mqtt"
	pb_basemsgbroker "xim/proto/api/basemsgbroker"
)

// BasemsgbrokerController 服务控制层
// 用于控制业务流程、处理事件并作出响应。“事件”包括 gRPC 接口请求和数据 Model 上的改变（例如nsq事件）
// 按业务将 Controller 分割成不同的文件以利维护
type BasemsgbrokerController struct {
	conf          *config.Config
	brokerService broker.GrpcService
}

// NewBasemsgbrokerController 创建basemsgbroker controller
func NewBasemsgbrokerController(conf *config.Config) (*BasemsgbrokerController, error) {
	c := &BasemsgbrokerController{
		conf:          conf,
		brokerService: broker.NewBroker(conf),
	}

	err := model.NewBasemsgbrokerModel(conf)
	if err != nil {
		return nil, err
	}

	return c, nil
}

// Test 实现Test接口
func (c *BasemsgbrokerController) Test(ctx context.Context, req *pb_basemsgbroker.TestReq) (*pb_basemsgbroker.TestRsp, error) {
	message := "basemsgbroker"
	return &pb_basemsgbroker.TestRsp{Result: util.Error_OK, Message: message}, nil
}

func (c *BasemsgbrokerController) Publish(ctx context.Context, req *pb_basemsgbroker.PublishReq) (*pb_basemsgbroker.PublishRsp, error) {
	res := &pb_basemsgbroker.PublishRsp{Result: util.RetOK, Message: util.NewError(util.RetOK).Error()}

	// 封装正常的publish消息
	msg := &message.Message{
		Message: &mqtt.Publish{
			Header:    mqtt.Header{QoS: 1},
			MessageID: util.GetMsgId(),
			Topic:     []byte(req.Topic),
			Payload:   []byte(req.Message),
		},
	}
	if env.IsTest() {
		logger.Debugf("Publish connIds=%+v topic=%s req.Message=%s", req.ConnIds, req.Topic, req.Message)
	}
	err := c.brokerService.OnPublish(req.ConnIds, msg)
	if err != nil {
		res.Result = util.RetPublishMessageErr
		res.Message = util.NewError(util.RetPublishMessageErr).Error()
	}
	return res, err
}

func (c *BasemsgbrokerController) Kick(ctx context.Context, req *pb_basemsgbroker.KickReq) (*pb_basemsgbroker.KickRsp, error) {
	res := &pb_basemsgbroker.KickRsp{Result: util.RetOK, Message: util.NewError(util.RetOK).Error()}

	//封装下线publish消息，通知用户关闭
	msg := &message.Message{
		Message: &mqtt.Publish{
			Topic:   []byte(req.Topic),
			Payload: []byte(req.Message),
		},
		Closing: true, //设置为关闭，服务端下发成功后主动退出
	}
	err := c.brokerService.OnPublish([]uint64{req.ConnId}, msg)
	if err != nil {
		res.Result = util.RetKickMessageErr
		res.Message = util.NewError(util.RetKickMessageErr).Error()
	}
	return res, err
}
