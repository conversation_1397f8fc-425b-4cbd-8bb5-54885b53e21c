package server

import (
	"time"

	log "xim/baselib/logger"
	"xim/basemsgbroker/config"
	"xim/basemsgbroker/controller"
	"xim/basemsgbroker/stat"
	pb_basemsgbroker "xim/proto/api/basemsgbroker"

	"golang.org/x/net/context"
)

// BasemsgbrokerServer 服务接入层
// 负责实现proto协议中定义的grpc接口，检查请求参数，拦截非法请求，记录访问日志
type BasemsgbrokerServer struct {
	controller *controller.BasemsgbrokerController
}

// NewBasemsgbrokerServer 创建basemsgbroker服务
// 初始化controller和业务自定义模块
func NewBasemsgbrokerServer(conf *config.Config) (*BasemsgbrokerServer, error) {
	s := &BasemsgbrokerServer{}

	var err error
	s.controller, err = controller.NewBasemsgbrokerController(conf)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Test 实现Test接口
func (s *BasemsgbrokerServer) Test(ctx context.Context, req *pb_basemsgbroker.TestReq) (*pb_basemsgbroker.TestRsp, error) {
	var (
		err       error
		res       *pb_basemsgbroker.TestRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("Test req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.Test(ctx, req)
	return res, nil
}

// Publish 发布下行消息
func (s *BasemsgbrokerServer) Publish(ctx context.Context, req *pb_basemsgbroker.PublishReq) (*pb_basemsgbroker.PublishRsp, error) {
	var (
		err       error
		res       *pb_basemsgbroker.PublishRsp
		beginTime = time.Now()
	)
	//defer func() {
	//	log.Infof("Publish req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	//}()

	res, err = s.controller.Publish(ctx, req)
	if err != nil {
		log.Infof("Publish req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}
	return res, nil
}

// Kick 关闭客户端长连接
func (s *BasemsgbrokerServer) Kick(ctx context.Context, req *pb_basemsgbroker.KickReq) (*pb_basemsgbroker.KickRsp, error) {
	var (
		err       error
		res       *pb_basemsgbroker.KickRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("Kick req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.Kick(ctx, req)
	return res, nil
}
