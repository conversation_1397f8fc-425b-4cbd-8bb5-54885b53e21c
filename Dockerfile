FROM registry.xunlei.cn/vcproject/proto_build:test AS proto_build

WORKDIR /proto

ARG GITLAB_USER
ARG NEW_GITLAB_USER
ARG GITLAB_TOKEN
ARG NEW_GITLAB_TOKEN
ARG SERVICE_NAME
ENV SERVICE_NAME=${SERVICE_NAME}
RUN git config --global url."https://${GITLAB_USER}:${GITLAB_TOKEN}@gitlab.xunlei.cn".insteadOf "https://gitlab.xunlei.cn"
RUN git config --global url."https://${NEW_GITLAB_USER}:${NEW_GITLAB_TOKEN}@new-gitlab.xunlei.cn".insteadOf "https://new-gitlab.xunlei.cn"

RUN git clone https://new-gitlab.xunlei.cn/vcproject/third-party.git

ADD ./proto/api /proto/api
RUN protoc -I /proto/api  -I /proto/third-party/protobuf/protobuf/src -I /proto/third-party/protobuf/googleapis \
    --go_out /proto/api --go_opt paths=source_relative  \
    --go-grpc_out /proto/api --go-grpc_opt=require_unimplemented_servers=false --go-grpc_opt paths=source_relative \
    --include_imports  --include_source_info --descriptor_set_out /proto/proto_descriptor.pb /proto/api/${SERVICE_NAME}/${SERVICE_NAME}.proto

# Stage 1: Build the Go application
FROM golang:1.22-bullseye AS builder

RUN apt-get install -y  git gcc g++ libc-dev curl
# Set the Current Working Directory inside the container
WORKDIR /app


# 设置 GitLab private token
ARG GITLAB_USER
ARG NEW_GITLAB_USER
ARG GITLAB_TOKEN
ARG NEW_GITLAB_TOKEN
RUN git config --global url."https://${NEW_GITLAB_USER}:${NEW_GITLAB_TOKEN}@new-gitlab.xunlei.cn".insteadOf "https://new-gitlab.xunlei.cn"
RUN git config --global url."https://${GITLAB_USER}:${GITLAB_TOKEN}@gitlab.xunlei.cn".insteadOf "https://gitlab.xunlei.cn"

# 设置 GOPROXY
ENV GOPROXY=https://goproxy.cn,direct


# Copy the source from the current directory to the Working Directory inside the container
COPY . .
# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download


# Build the Go app
ARG SERVICE_NAME
RUN GOOS=linux GOARCH=amd64 go build --ldflags '-extldflags -static' -o vc.${SERVICE_NAME}.s ./${SERVICE_NAME}



# get the pb descriptor
#RUN curl --header "${GITLAB_TOKEN}" \
#     "https://gitlab.changyinlive.com/api/v4/projects/603/repository/files/auth%2Fv1%2Fauth.pb/raw?ref=jacob_umine_20241105" \
#     --output auth.pb

# Stage 2: Create a small image
FROM debian:bullseye-slim AS production



# Set the timezone to Singapore time
RUN apt-get update && apt-get install -y \
    gettext-base \
    tzdata \
    procps \
    curl \
    net-tools \
    vim \
    && ln -fs /usr/share/zoneinfo/Asia/Singapore /etc/localtime \
    && dpkg-reconfigure -f noninteractive tzdata \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

ARG SERVICE_NAME
ENV SERVICE_NAME=${SERVICE_NAME}

# 创建目录并复制二进制文件
RUN mkdir -p /opt/vc/${SERVICE_NAME}
COPY --from=builder /app/vc.${SERVICE_NAME}.s /opt/vc/${SERVICE_NAME}/

# 创建启动脚本
# 创建启动脚本模板
RUN echo '#!/bin/sh' > /entrypoint.sh.template && \
    echo 'exec /opt/vc/$SERVICE_NAME/vc.$SERVICE_NAME.s "$@"' >> /entrypoint.sh.template && \
    # 使用 envsubst 替换变量
    envsubst < /entrypoint.sh.template > /entrypoint.sh && \
    chmod +x /entrypoint.sh && \
    rm /entrypoint.sh.template

WORKDIR /opt/vc/${SERVICE_NAME}

ENTRYPOINT ["/entrypoint.sh"]


FROM alpine:3.20 AS initdata
WORKDIR /initdata


COPY --from=proto_build /proto/proto_descriptor.pb /initdata/proto_descriptor.pb


FROM registry.xunlei.cn/vcproject/proto_build:test AS proto_build_only

VOLUME /proto/api
WORKDIR /proto

ARG NEW_GITLAB_USER
ARG NEW_GITLAB_TOKEN
ARG SERVICE_NAME
ENV SERVICE_NAME=${SERVICE_NAME}
RUN git config --global url."https://${NEW_GITLAB_USER}:${NEW_GITLAB_TOKEN}@new-gitlab.xunlei.cn".insteadOf "https://new-gitlab.xunlei.cn"

RUN git clone https://new-gitlab.xunlei.cn/vcproject/third-party.git


RUN protoc -I /proto/api  -I /proto/third-party/protobuf/protobuf/src -I /proto/third-party/protobuf/googleapis \
    --go_out /proto/api --go_opt paths=source_relative  \
    --go-grpc_out /proto/api --go-grpc_opt=require_unimplemented_servers=false --go-grpc_opt paths=source_relative \
    --include_imports  --include_source_info --descriptor_set_out /proto/proto_descriptor.pb /proto/api/${SERVICE_NAME}/${SERVICE_NAME}.proto
