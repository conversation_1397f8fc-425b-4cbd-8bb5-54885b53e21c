package controller

import (
	"context"
	"errors"
	"time"
	"xim/baselib/cache/lock"
	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/nsqutil"
	"xim/baselib/server/env"
	"xim/baselib/util"
	"xim/basemsgtransfer/storage/cache"
	"xim/basemsgtransfer/storage/mgo"
	"xim/basemsgtransfer/utils"
	"xim/proto/api/basemsgtransfer"
	mproto "xim/proto/api/basemsgtransfer"
	"xim/proto/consts/cachetime"
	"xim/proto/model"

	"xim/basemsgtransfer/storage/sql"

	"xim/proto/consts/mqtopic"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
)

func NewCommonDatabase(nsqConf *config.NsqConfig) (*CommonDatabase, error) {
	if !env.IsLocal() && nsqConf != nil {
		if err := nsqutil.NewProducer(*nsqConf); err != nil {
			logger.Panicf("error start nsq producer %v", err)
		}
	}

	return &CommonDatabase{
		msgDb:          mgo.NewMsgMongo(),
		msgCache:       cache.NewMsgCache(),
		seqCache:       cache.NewSeqCache(),
		historyMsgDb:   sql.NewHistoryMsgDb(),
		sessionCache:   cache.NewSessionInfoCache(),
		sessionDb:      mgo.NewSessionMongo(),
		userGroupDb:    mgo.NewUserGroupMongo(),
		userGroupCache: cache.NewUserGroupCache(),
	}, nil
}

type CommonDatabase struct {
	msgDb          *mgo.MsgMgo
	msgCache       *cache.MsgCache
	seqCache       *cache.SeqCache
	historyMsgDb   *sql.HistoryMsgDb
	sessionCache   *cache.SessionInfoCache
	sessionDb      *mgo.SessionMgo
	userGroupDb    *mgo.UserGroupMgo
	userGroupCache *cache.UserGroupInfoCache
}

func (db *CommonDatabase) MsgToStateMQ(ctx context.Context, msg any) error {
	err := nsqutil.PublishJSON(ctx, mqtopic.NsqTopicState, msg)
	return err
}

func (db *CommonDatabase) MsgToContentMQ(ctx context.Context, msg any) error {
	err := nsqutil.PublishJSON(ctx, mqtopic.NsqTopicMessage, msg)
	return err
}

func (db *CommonDatabase) Msg1v1Save(ctx context.Context, receiver int64, msgList []*mproto.MsgData) (insertSeqs []int64, err error) {
	maxSeq, err := db.Get1v1MaxSeq(ctx, receiver)
	if err != nil {
		logger.Errorf("Msg1v1Save,err %v", err)
		return nil, err
	}

	curSeq := maxSeq
	var msgs []*model.MsgDataModel
	for _, v := range msgList {
		curSeq++
		v.Seq = curSeq
		insertSeqs = append(insertSeqs, curSeq)

		msgs = append(msgs, &model.MsgDataModel{
			Receiver:    receiver,
			LocalId:     v.LocalId,
			MsgId:       v.MsgId,
			GroupID:     v.GroupId,
			SessionType: v.SessionType,
			SessionId:   v.SessionId,
			From:        v.From,
			To:          v.To,
			MsgFrom:     v.MsgFrom,
			ContentType: v.ContentType,
			Content:     v.Content,
			Seq:         v.Seq,
			SendTime:    v.SendTime,
			CreateTime:  v.CreateTime,
			Options:     v.Options,
			// AtUserIdList: v.AtUserIdList,
			Exts:    v.Exts,
			WaitAck: 1,
		})

	}
	err = db.Msg1v1InsertDb(ctx, msgs)
	if err != nil {
		logger.Errorf("err %v", err)
		return nil, err
	}

	err = db.msgCache.SetMessage1v1ToCache(ctx, msgList)
	if err != nil {
		logger.Errorf("err %v", err)
		return nil, err
	}

	err = db.seqCache.Set1v1MaxSeq(ctx, receiver, curSeq)
	if err != nil {
		logger.Errorf("err %v", err)
		return nil, err
	}

	err = db.Msg1v1InsertHistoryDb(ctx, msgs)
	if err != nil {
		logger.Errorf("err %v", err)
		return nil, err
	}

	err = db.seqCache.PushWaitAckSeqX(ctx, receiver, insertSeqs)
	if err != nil {
		logger.Errorf("err %v", err)
		return nil, err
	}

	for _, v := range msgList {
		err = db.msgCache.SetMsgIdempotent(ctx, v.From, v.LocalId)
		if err != nil {
			logger.Errorf("err %v", err)
			return nil, err
		}
	}

	return insertSeqs, err
}

func (db *CommonDatabase) GetCacheClient() *cache.MsgCache {
	return db.msgCache
}

func (db *CommonDatabase) MsgGroupSave(ctx context.Context, groupId int64, msgList []*mproto.MsgData) (insertSeqs []int64, err error) {
	maxSeq, err := db.GetGroupMaxSeq(ctx, groupId)
	if err != nil {
		logger.Errorf("MsgGroupSave,err %v", err)
		return nil, err
	}

	curSeq := maxSeq
	for _, v := range msgList {
		curSeq++
		v.Seq = curSeq
		insertSeqs = append(insertSeqs, curSeq)

		if v.GroupId != groupId {
			return nil, errors.New("groupId not match")
		}
	}

	var list []*model.MsgDataModel
	copier.Copy(&list, &msgList)
	err = db.MsgGroupInsertDb(ctx, groupId, list)
	if err != nil {
		logger.Errorf("MsgGroupSave,err %v", err)
		return nil, err
	}

	err = db.msgCache.SetMessageGroupToCache(ctx, groupId, msgList)
	if err != nil {
		logger.Errorf("MsgGroupSave,err %v", err)
		return nil, err
	}

	err = db.seqCache.SetGroupMaxSeq(ctx, groupId, curSeq)
	if err != nil {
		logger.Errorf("MsgGroupSave,err %v", err)
		return nil, err
	}

	sid := utils.SessionIdGen(int32(basemsgtransfer.SessionType_SessionTypeGroup), 0, 0, groupId)
	err = db.MsgGroupInsertHistoryDb(ctx, sid, list)
	if err != nil {
		logger.Errorf("MsgGroupSave,err %v", err)
	}
	return insertSeqs, err
}

func (db *CommonDatabase) Msg1v1InsertHistoryDb(ctx context.Context, msgList []*model.MsgDataModel) error {
	if env.IsProd() { // 线上环境先屏蔽掉
		return nil
	}
	if len(msgList) == 0 {
		return nil
	}

	sessionMsgList := make(map[string][]*model.HistoryMsgModel)
	for _, v := range msgList {
		sessionMsgList[v.SessionId] = append(sessionMsgList[v.SessionId], &model.HistoryMsgModel{
			SessionId: v.SessionId,
			MsgData:   util.JsonStr(v),
			Ct:        time.Now().Unix(),
		})
	}

	for sessionId, list := range sessionMsgList {
		err := db.historyMsgDb.Insert(ctx, sessionId, list)
		if err != nil {
			return err
		}
	}

	return nil
}

func (db *CommonDatabase) MsgGroupInsertHistoryDb(ctx context.Context, sessionId string, msgList []*model.MsgDataModel) error {
	if env.IsProd() {
		return nil
	}
	if len(msgList) == 0 {
		return nil
	}

	var msgToDb []*model.HistoryMsgModel
	for _, v := range msgList {
		msgToDb = append(msgToDb, &model.HistoryMsgModel{
			SessionId: sessionId,
			MsgData:   util.JsonStr(v),
			Ct:        time.Now().Unix(),
		})
	}

	err := db.historyMsgDb.Insert(ctx, sessionId, msgToDb)
	return err
}

func (db *CommonDatabase) Msg1v1InsertDb(ctx context.Context, msgList []*model.MsgDataModel) error {
	if len(msgList) == 0 {
		return errors.New("msgList empty")
	}
	err := db.msgDb.Insert1v1Msgs(ctx, msgList)
	return err
}

func (db *CommonDatabase) MsgGroupInsertDb(ctx context.Context, groupId int64, msgList []*model.MsgDataModel) error {
	if len(msgList) == 0 {
		return errors.New("msgList empty")
	}

	firstSeq := msgList[0].Seq
	for i := 0; i < len(msgList); i++ {
		seq := firstSeq + int64(i)
		docUpdate := model.GroupMsgModel{
			GroupId: groupId,
			DocID:   model.GetDocID(seq),
		}
		var insert int // Inserted data number
		for j := i; j < len(msgList); j++ {
			seq = firstSeq + int64(j)
			if model.GetDocID(seq) != docUpdate.DocID {
				break
			}
			insert++
			docUpdate.Msgs = append(docUpdate.Msgs, msgList[j])
		}
		if len(docUpdate.Msgs) > 0 {
			err := db.msgDb.UpdateGroupMsgsToDoc(ctx, &docUpdate)
			if err != nil {
				return err
			}
		}

		i += insert - 1

	}

	return nil
}

// 同步离线消息 5000条 近到远同步
func (db *CommonDatabase) GetGroupMsgBySeqsRange(ctx context.Context, groupId int64, begin, end, num int64) ([]*mproto.MsgData, bool, error) {
	if groupId == 0 {
		return nil, true, nil
	}
	maxSeq, err := db.GetGroupMaxSeq(ctx, groupId)
	if err != nil {
		return nil, false, err
	}
	if maxSeq == 0 {
		return nil, true, nil
	}

	if end > maxSeq {
		end = maxSeq
	}

	if end < begin || begin <= 0 {
		return nil, false, errors.New("seq end < begin")
	}

	// 最多同步最近5000条
	maxLimit := int64(5000)
	if maxSeq-end >= maxLimit {
		return nil, true, nil
	}
	if num == 0 || num > 200 {
		num = 200
	}

	if !env.IsProd() {
		maxLimit = 99
		// if num == 0 || num > 5 {
		// 	num = 5
		// }
	}

	var seqs []int64
	if end-begin+1 <= num {
		for i := begin; i <= end; i++ {
			seqs = append(seqs, i)
		}
	} else {
		for i := end - num + 1; i <= end; i++ {
			seqs = append(seqs, i)
			begin = i
		}
	}

	if len(seqs) == 0 {
		return nil, true, nil
	}

	msgs, err := db.getMsgGroupBySeqs(ctx, groupId, seqs)
	if err != nil {
		return nil, false, err
	}

	return msgs, maxSeq-begin+1 >= maxLimit, err

}

// func (db *CommonDatabase) GetMsgBySeqs(ctx context.Context, userID string, conversationID string, seqs []int64) (int64, int64, []*sdkws.MsgData, error) {
// 	// 1. 获取会话maxseq minseq  圈定会话的可见范围

// 	// 2.从缓存拉消息 mgo拉消息

// 	// 3.对于seq之中的空隙  下发空消息 (兜底)

// 	userMinSeq, err := db.seqCache.GetConversationUserMinSeq(ctx, conversationID, userID)
// 	if err != nil && errs.Unwrap(err) != redis.Nil {
// 		return 0, 0, nil, err
// 	}
// 	minSeq, err := db.seq.GetMinSeq(ctx, conversationID)
// 	if err != nil && errs.Unwrap(err) != redis.Nil {
// 		return 0, 0, nil, err
// 	}
// 	maxSeq, err := db.seq.GetMaxSeq(ctx, conversationID)
// 	if err != nil && errs.Unwrap(err) != redis.Nil {
// 		return 0, 0, nil, err
// 	}
// 	if userMinSeq < minSeq {
// 		minSeq = userMinSeq
// 	}
// 	var newSeqs []int64
// 	for _, seq := range seqs {
// 		if seq >= minSeq && seq <= maxSeq {
// 			newSeqs = append(newSeqs, seq)
// 		}
// 	}
// 	successMsgs, failedSeqs, err := db.msg.GetMessagesBySeq(ctx, conversationID, newSeqs)
// 	if err != nil {
// 		if err != redis.Nil {
// 			log.ZError(ctx, "get message from redis exception", err, "failedSeqs", failedSeqs, "conversationID", conversationID)
// 		}
// 	}
// 	log.ZDebug(ctx, "storage.seq.GetMessagesBySeq", "userID", userID, "conversationID", conversationID, "seqs",
// 		seqs, "len(successMsgs)", len(successMsgs), "failedSeqs", failedSeqs)

// 	if len(failedSeqs) > 0 {
// 		mongoMsgs, err := db.getMsgBySeqs(ctx, userID, conversationID, failedSeqs)
// 		if err != nil {

// 			return 0, 0, nil, err
// 		}

// 		successMsgs = append(successMsgs, mongoMsgs...)
// 	}
// 	return minSeq, maxSeq, successMsgs, nil
// }

func (db *CommonDatabase) getWaitAckedMsg1v1BySeqs(ctx context.Context, receiver int64, seqs []int64) (msgsData []*mproto.MsgData, err error) {
	if len(seqs) == 0 {
		return nil, nil
	}

	successMsgs, failedSeqs, err := db.msgCache.GetMessages1v1BySeq(ctx, receiver, seqs)
	if err != nil {
		return nil, err
	}
	if len(failedSeqs) > 0 {
		var mongoMsgs []*mproto.MsgData
		msgFromDb, err := db.msgDb.GetWaitAcked1v1MsgsBySeqs(ctx, receiver, failedSeqs)
		if err != nil {
			return nil, err
		}

		for _, v := range msgFromDb {
			pbMsg := mproto.MsgData{}
			copier.Copy(&pbMsg, &v)
			mongoMsgs = append(mongoMsgs, &pbMsg)

		}
		successMsgs = append(successMsgs, mongoMsgs...)
	}
	return successMsgs, nil
}

func (db *CommonDatabase) getMsgGroupBySeqs(ctx context.Context, groupId int64, seqs []int64) (totalMsgs []*mproto.MsgData, err error) {
	if len(seqs) == 0 {
		return nil, nil
	}

	successMsgs, failedSeqs, err := db.msgCache.GetMessagesGroupBySeq(ctx, groupId, seqs)
	if err != nil {
		return nil, err
	}

	if len(failedSeqs) > 0 {
		var mongoMsgs []*mproto.MsgData
		for docID, seqsInDoc := range getDocIDSeqsMap(failedSeqs) {
			msgDoc, err := db.msgDb.GetGroupMsgDoc(ctx, groupId, docID)
			if err != nil {
				return nil, err
			}

			for _, seqId := range seqsInDoc {
				index := model.GetMsgIndex(seqId)
				mTmp := msgDoc.Msgs[index]
				if mTmp != nil {
					pbMsg := mproto.MsgData{}
					copier.Copy(&pbMsg, mTmp)
					mongoMsgs = append(mongoMsgs, &pbMsg)
				}

			}
			db.msgCache.SetMessageGroupToCache(ctx, groupId, mongoMsgs)
		}
		successMsgs = append(successMsgs, mongoMsgs...)
	}
	return successMsgs, nil
}

// func (db *CommonDatabase) findMsgInfoBySeqInDoc(ctx context.Context, userID int64, docID string, sessionId string, seqs []int64) (totalMsgs []*model.MsgInfoModel, err error) {
// 	msgs, err := db.msgDb.GetMsgBySeqIndexIn1Doc(ctx, userID, docID, seqs)
// 	return msgs, err
// }

func getDocIDSeqsMap(seqs []int64) map[int64][]int64 {
	t := make(map[int64][]int64)
	for i := 0; i < len(seqs); i++ {
		docID := model.GetDocID(seqs[i])
		if value, ok := t[docID]; !ok {
			var temp []int64
			t[docID] = append(temp, seqs[i])
		} else {
			t[docID] = append(value, seqs[i])
		}
	}
	return t
}
func (db *CommonDatabase) OfflineMsgAcked(ctx context.Context, receiver int64, seqs []int64) error {
	err := db.seqCache.DelSeqInWaitAckQueue(ctx, receiver, seqs)
	if err != nil {
		return err
	}

	err = db.msgDb.UnsetAck1v1MsgsBySeqs(ctx, receiver, seqs)
	if err != nil {
		return err
	}

	err = db.msgCache.DeleteMessages1v1Cache(ctx, receiver, seqs)
	return err
}

func (db *CommonDatabase) UpdateSessionMsgUt(ctx context.Context, sessionIds []string) error {
	return db.sessionDb.UpdateMsgUtBySessionId(ctx, sessionIds)
}

func (db *CommonDatabase) Get1v1MaxSeq(ctx context.Context, receiver int64) (maxSeq int64, err error) {

	maxSeq, err = db.seqCache.Get1v1MaxSeq(ctx, receiver)
	if err == redis.Nil { // reload
		lockKey := cache.Get1v1SeqLockKey(receiver)
		slk, lockERR := lock.NewRetryLock(db.seqCache.GetClient(), lockKey, cachetime.SEC_5, time.Millisecond*20, 50)
		if lockERR != nil {
			return 0, lockERR
		} else {
			defer slk.Release()
			{ // double check lock
				maxSeq, err = db.seqCache.Get1v1MaxSeq(ctx, receiver)
				if err != nil && err != redis.Nil {
					logger.Errorf("Get1v1MaxSeq,err %v", err)
					return 0, err
				}

				if err == nil {
					return maxSeq, nil
				}

				maxMsg, err1 := db.msgDb.GetLatestReceiveMsg1v1(ctx, receiver)
				if err1 != nil {
					logger.Errorf("Get1v1MaxSeq,err %v", err)
					return 0, err1
				}

				if maxMsg != nil {
					maxSeq = maxMsg.Seq
				}

				err = db.seqCache.Set1v1MaxSeq(ctx, receiver, maxSeq)
				if err != nil {
					logger.Errorf("Get1v1MaxSeq,err %v", err)
					return 0, err
				}
			}
		}

	}

	return

}
func (db *CommonDatabase) GetGroupMaxSeq(ctx context.Context, groupId int64) (maxSeq int64, err error) {

	maxSeq, err = db.seqCache.GetGroupMaxSeq(ctx, groupId)
	if err == redis.Nil { // reload
		lockKey := cache.GetGroupSeqLockKey(groupId)
		slk, lockERR := lock.NewRetryLock(db.seqCache.GetClient(), lockKey, cachetime.SEC_5, time.Millisecond*20, 50)
		if lockERR != nil {
			return 0, lockERR
		} else {
			defer slk.Release()
			{ // double check lock
				maxSeq, err = db.seqCache.GetGroupMaxSeq(ctx, groupId)
				if err != nil && err != redis.Nil {
					return 0, err
				}

				if err == nil {
					return maxSeq, nil
				}

				msgDoc, err1 := db.msgDb.GetLatestMsgInGroup(ctx, groupId)
				if err1 != nil {
					return 0, err1
				}

				for i := len(msgDoc.Msgs) - 1; i >= 0; i-- {
					if msgDoc.Msgs[i] != nil && msgDoc.Msgs[i].MsgId != "" {
						maxSeq = msgDoc.Msgs[i].Seq
						break
					}
				}

				err = db.seqCache.SetGroupMaxSeq(ctx, groupId, maxSeq)
				if err != nil {
					return 0, err
				}
			}
		}

	}

	return

}

// func (db *CommonDatabase) insertWaitAckList(ctx context.Context, receiver int64, seqIds []int64) (err error) {
// 	err = db.seqCache.PushWaitAckSeqX(ctx, receiver, seqIds)
// 	return
// }

func (db *CommonDatabase) GetWaitAckMsgs(ctx context.Context, receiver int64, limit int) ([]*mproto.MsgData, error) {
	seqs, loadFromDb, err := db.seqCache.GetWaitAckSeqsFromQueue(ctx, receiver, limit)
	if err != nil {
		return nil, err
	}

	if !loadFromDb {
		return db.getWaitAckedMsg1v1BySeqs(ctx, receiver, seqs)
	}

	var msgData []*mproto.MsgData
	msgsDb, err := db.msgDb.GetWaitAcked1v1MsgsByRange(ctx, receiver, limit)
	if err != nil {
		return nil, err
	}
	err = copier.Copy(&msgData, &msgsDb)
	if len(msgsDb) == limit {
		return msgData, err
	}

	err = db.seqCache.InitWaitAckQueue(ctx, receiver, getSeqsFromData(msgData))
	return msgData, err
}

func getSeqsFromData(msgData []*mproto.MsgData) (seqs []int64) {
	for _, v := range msgData {
		seqs = append(seqs, v.Seq)
	}
	return
}
