package controller

import (
	"context"
	"errors"
	mproto "xim/proto/api/basemsgtransfer"
	"xim/proto/model"

	"github.com/jinzhu/copier"
)

func (c *CommonDatabase) GetSessionInfo(ctx context.Context, userid int64, sessionId string) (results *mproto.SessionInfo, err error) {
	res, err := c.GetSessionInfos(ctx, userid, []string{sessionId})
	if err != nil {
		return nil, err
	}

	if len(res) > 0 {
		return res[0], nil
	}

	return nil, nil
}

func (c *CommonDatabase) GetSessionInfos(ctx context.Context, userid int64, sessionIds []string) (results []*mproto.SessionInfo, err error) {
	sessionCache, missKeys, err := c.sessionCache.MGetSessionInfo(ctx, userid, sessionIds)
	if err != nil {
		return nil, err
	}

	results = append(results, sessionCache...)
	if len(missKeys) > 0 {
		sessionDb, err := c.sessionDb.FindUseidSession(ctx, userid, missKeys)
		if err != nil {
			return nil, err
		}
		var tmp []*mproto.SessionInfo
		copier.Copy(&tmp, &sessionDb)
		err = c.sessionCache.MsetSessionInfo(ctx, userid, tmp)
		if err != nil {
			return nil, err
		}
		results = append(results, tmp...)
	}

	return results, nil

}

func (c *CommonDatabase) CreateSessionPair(ctx context.Context, session *mproto.SessionInfo) (err error) {
	if (session.SessionType == int32(mproto.SessionType_SessionTypeChat) && (session.OwnUserid == 0 || session.PeerId == 0)) ||
		(session.SessionType == int32(mproto.SessionType_SessionTypeGroup) && (session.GroupId == 0)) {
		return errors.New("session param error")
	}

	var models []*model.SessionModel
	m1, m2 := model.SessionModel{}, model.SessionModel{}
	copier.Copy(&m1, session)
	models = append(models, &m1)
	m2 = m1
	m2.OwnUserid, m2.PeerId = m2.PeerId, m2.OwnUserid
	models = append(models, &m2)
	err = c.sessionDb.Create(ctx, models)
	return
}

func (c *CommonDatabase) UpdateSession(ctx context.Context, session *mproto.SessionInfo) (err error) {
	model := &model.SessionModel{}
	copier.Copy(model, session)
	err = c.sessionDb.Update(ctx, model)
	if err != nil {
		return
	}

	err = c.sessionCache.DeleteSessionInfos(ctx, session.OwnUserid, []string{session.SessionId})
	return
}
