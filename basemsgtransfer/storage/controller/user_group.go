package controller

import (
	"context"
	"xim/proto/model"
)

func (c *CommonDatabase) UserUpdateGroup(ctx context.Context, userGroup *model.UserGroupModel) (err error) {
	err = c.userGroupDb.Update(ctx, userGroup)
	if err != nil {
		return
	}

	err = c.userGroupCache.DeleteUserGroups(ctx, userGroup.UserId)
	return
}

func (c *CommonDatabase) GetUserGroupIdList(ctx context.Context, userid int64) (groupIds []int64, err error) {
	found, userGroups, err := c.userGroupCache.GetUserGroups(ctx, userid)
	if err != nil {
		return
	}
	if found {
		for _, ug := range userGroups {
			groupIds = append(groupIds, ug.GroupId)
		}
		return
	}
	userGroups, err = c.userGroupDb.Find(ctx, userid)
	if err != nil {
		return nil, err
	}
	var targets []*model.UserGroupModel
	for _, ug := range userGroups {
		if ug.Status == 1 {
			continue
		}
		targets = append(targets, ug)
	}
	err = c.userGroupCache.SetUserGroups(ctx, userid, targets)
	if err != nil {
		return nil, err
	}
	for _, ug := range targets {
		groupIds = append(groupIds, ug.GroupId)
	}
	return
}
