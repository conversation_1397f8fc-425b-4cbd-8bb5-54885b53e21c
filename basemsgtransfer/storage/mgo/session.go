package mgo

import (
	"context"
	"time"

	"xim/proto/api/basemsgtransfer"
	"xim/proto/model"

	"xim/baselib/database/mongo"
	"xim/baselib/logger"

	"go.mongodb.org/mongo-driver/bson"
	mgodriver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func NewSessionMongo() *SessionMgo {
	collSession := mongo.GetMongoDBClient().NewCollectionWrapper("chat", "session")

	err := collSession.CreateIndexes(context.Background(), []mgodriver.IndexModel{
		{Keys: bson.D{
			{Key: "ownUserid", Value: 1},
			{Key: "sessionId", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{Keys: bson.D{
			{Key: "sessionId", Value: 1}},
			Options: options.Index(),
		},
	})
	if err != nil {
		logger.Panicf("CreateIndexOne, err %v", err)
	}

	return &SessionMgo{coll: collSession}
}

type SessionMgo struct {
	coll mongo.CollectionWrapper
}

func (c *SessionMgo) UpdateMsgUtBySessionId(ctx context.Context, sessionIds []string) (err error) {
	filter := bson.M{"sessionId": bson.M{"$in": sessionIds}}
	update := bson.M{"msgUt": time.Now().Unix()}
	_, err = c.coll.UpdateOne(ctx, filter, bson.M{"$set": update}, false)
	return
}

// func (c *SessionMgo) Create(ctx context.Context, model *model.SessionModel) (err error) {
// 	_, err = c.coll.InsertOne(ctx, model)
// 	return
// }

func (c *SessionMgo) Create(ctx context.Context, models []*model.SessionModel) (err error) {
	var operations []mgodriver.WriteModel
	for _, session := range models {
		operation := mgodriver.NewUpdateOneModel().
			SetFilter(bson.M{"ownUserid": session.OwnUserid, "sessionId": session.SessionId}).
			SetUpdate(bson.M{
				"$setOnInsert": session,
			}).
			SetUpsert(true)
		operations = append(operations, operation)
	}

	// 执行批量写入操作
	opts := options.BulkWrite().SetOrdered(false)
	_, err = c.coll.BulkWrite(ctx, operations, opts)
	return
}

func (c *SessionMgo) Update(ctx context.Context, session *model.SessionModel) (err error) {
	filter := bson.M{"ownUserid": session.OwnUserid, "sessionId": session.SessionId}
	_, err = c.coll.UpdateOne(ctx, filter, bson.M{"$set": session}, false)
	return
}

func (c *SessionMgo) Find(ctx context.Context, userid string, session_ids []string) (results []*model.SessionModel, err error) {
	filter := bson.M{"ownUserid": userid, "sessionId": bson.M{"$in": session_ids}}
	err = c.coll.Find(ctx, filter, &results, nil, 0, 0)
	return
}

func (c *SessionMgo) FindUseridAllSessionId(ctx context.Context, userid int64) (session_ids []string, err error) {
	filter := bson.M{"ownUserid": userid}
	opts := options.Find().SetProjection(bson.M{"_id": 0, "sessionId": 1})
	err = c.coll.Find(ctx, filter, &session_ids, nil, 0, 0, opts)
	return
}

func (c *SessionMgo) Take(ctx context.Context, userid int64, sessionId string) (result *model.SessionModel, err error) {
	filter := bson.M{"ownUserid": userid, "sessionId": sessionId}
	_, err = c.coll.FindOne(ctx, filter, result, nil, 0)
	return
}

func (c *SessionMgo) FindUserSessionByUtSort(ctx context.Context, userid, limit int64) (results []*model.SessionModel, err error) {
	filter := bson.M{"ownUserid": userid}
	sort := []string{"-msgUt"}
	err = c.coll.Find(ctx, filter, &results, sort, 0, limit)
	return
}

func (c *SessionMgo) FindUseidSession(ctx context.Context, userid int64, sessionIDs []string) (results []*model.SessionModel, err error) {
	filter := bson.M{"ownUserid": userid, "sessionId": bson.M{"$in": sessionIDs}}
	err = c.coll.Find(ctx, filter, &results, nil, 0, 0)
	return
}

func (c *SessionMgo) FindUserGroupSessions(ctx context.Context, userid int64) (results []*model.SessionModel, err error) {
	filter := bson.M{"ownUserid": userid, "sessionType": basemsgtransfer.SessionType_SessionTypeGroup}
	err = c.coll.Find(ctx, filter, &results, nil, 0, 0)
	return
}
