package mgo

import (
	"context"
	"time"
	"xim/baselib/database/mongo"
	"xim/baselib/logger"
	"xim/proto/model"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	mgodriver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 如果使用分片集
// 采用 receiver + seq 组合方式分片键 同理群离线消息可以采用 groupid + docId
func NewMsgMongo() *MsgMgo {
	collMsg1v1 := mongo.GetMongoDBClient().NewCollectionWrapper("chat", "msg_1v1")
	collMsgGroup := mongo.GetMongoDBClient().NewCollectionWrapper("chat", "msg_group")

	err := collMsg1v1.CreateIndexes(context.Background(), []mgodriver.IndexModel{
		{Keys: bson.D{
			{Key: "receiver", Value: 1},
			{Key: "waitAck", Value: 1},
			{Key: "seq", Value: 1}},
			Options: options.Index().SetUnique(true).SetSparse(true),
		},
		{Keys: bson.D{
			{Key: "ackTime", Value: 1}},
			Options: options.Index().SetSparse(true),
		},
	})
	if err != nil {
		logger.Panicf("CreateIndexes, err %v", err)
	}

	err = collMsgGroup.CreateIndexOne(context.Background(), mgodriver.IndexModel{
		Keys: bson.D{
			{Key: "groupId", Value: 1},
			{Key: "docId", Value: 1},
		},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		logger.Panicf("CreateIndexOne, err %v", err)
	}

	return &MsgMgo{
		collMsg1v1:   collMsg1v1,
		collMsgGroup: collMsgGroup,
	}
}

type MsgMgo struct {
	collMsg1v1   mongo.CollectionWrapper
	collMsgGroup mongo.CollectionWrapper
}

// func (c *MsgMgo) PushMsgsToDoc(ctx context.Context, sessionId string, docID int64, msgsToMongo []*model.MsgInfoModel) (err error) {
// 	filter := bson.M{"sessionId": sessionId, "docId": docID}
// 	update := bson.M{"$push": bson.M{"msgs": bson.M{"$each": msgsToMongo}}}
// 	_, err = c.coll.UpdateOne(ctx, filter, update, true)
// 	return
// }

func (c *MsgMgo) Insert1v1Msgs(ctx context.Context, msgToMongo []*model.MsgDataModel) error {

	var models []mgodriver.WriteModel
	for _, doc := range msgToMongo {
		model := mgodriver.NewUpdateOneModel().
			SetFilter(bson.M{"receiver": doc.Receiver, "seq": doc.Seq}).
			SetUpdate(bson.M{"$set": doc}).
			SetUpsert(true)
		models = append(models, model)
	}

	_, err := c.collMsg1v1.BulkWrite(ctx, models)
	return err
}

func (c *MsgMgo) GetWaitAcked1v1MsgsBySeqs(ctx context.Context, receiver int64, seqs []int64) (result []*model.MsgDataModel, err error) {
	//filter := bson.M{"receiver": receiver, "seq": bson.M{"$in": seqs}, "waitAck": bson.M{"$exists": true}}
	filter := bson.M{"receiver": receiver, "seq": bson.M{"$in": seqs}, "waitAck": 1}
	err = c.collMsg1v1.Find(ctx, filter, &result, nil, 0, 0)
	return
}

func (c *MsgMgo) GetWaitAcked1v1MsgsByRange(ctx context.Context, receiver int64, limit int) (result []*model.MsgDataModel, err error) {
	//filter := bson.M{"receiver": receiver, "waitAck": bson.M{"$exists": true}}
	filter := bson.M{"receiver": receiver, "waitAck": 1}
	err = c.collMsg1v1.Find(ctx, filter, &result, nil, 0, int64(limit))
	return
}

func (c *MsgMgo) UnsetAck1v1MsgsBySeqs(ctx context.Context, receiver int64, seqs []int64) (err error) {
	//filter := bson.M{"receiver": receiver, "seq": bson.M{"$in": seqs}}
	//update := bson.M{"$unset": bson.M{"waitAck": ""}, "$set": bson.M{"ackTime": time.Now().Unix()}}
	filter := bson.M{"receiver": receiver, "seq": bson.M{"$in": seqs}, "waitAck": 1}
	update := bson.M{"$set": bson.M{"waitAck": 2, "ackTime": time.Now().Unix()}}
	_, err = c.collMsg1v1.UpdateMany(ctx, filter, update, false)
	return
}

func (c *MsgMgo) Delete1v1MsgsByTime(ctx context.Context, deleteTimeStamp int64) (err error) {
	filter := bson.M{"ackTime": bson.M{"$lt": deleteTimeStamp}}
	_, err = c.collMsg1v1.DeleteMany(ctx, filter)
	return
}

func (c *MsgMgo) UpdateGroupMsgsToDoc(ctx context.Context, docToMongo *model.GroupMsgModel) error {
	filter := bson.M{"groupId": docToMongo.GroupId, "docId": docToMongo.DocID}

	fields := bson.M{}
	for _, msg := range docToMongo.Msgs {
		indexStr := cast.ToString(model.GetMsgIndex(msg.Seq))
		fields["msgs."+indexStr] = msg
	}

	update := bson.M{"$set": fields}
	res, err := c.collMsgGroup.UpdateOne(ctx, filter, update, false)
	if err != nil {
		return err
	}

	if res.MatchedCount == 0 {
		docMsgsNew := make([]*model.MsgDataModel, model.SingleGocGroupMsgNum)
		for _, v := range docToMongo.Msgs {
			docMsgsNew[model.GetMsgIndex(v.Seq)] = v
		}

		docToMongo.Msgs = docMsgsNew
		_, err = c.collMsgGroup.InsertMany(ctx, []interface{}{docToMongo})
		if err != nil {
			return err
		}
	}

	return nil
}

// func (c *MsgMgo) Delete1v1Msgs(ctx context.Context, receiver int64, seqs []int64) error {
// 	if len(seqs) == 0 {
// 		return nil
// 	}
// 	filter := bson.M{"to": receiver, "seq": bson.M{"$in": seqs}}
// 	_, err := c.collMsg1v1.DeleteMany(ctx, filter)
// 	return err
// }

// func (c *MsgMgo) GetMsgBySeqIndexIn1Doc(ctx context.Context, sessionId string, docID int64, seqs []int64) (msgDoc *model.MsgDocModel, err error) {
// 	filter := bson.M{"sessionId": sessionId, "docId": docID}

// 	projection := bson.M{}
// 	for _, seq := range seqs {
// 		projection["msgs."+cast.ToString(model.GetMsgIndex(seq))+".msg"] = 1
// 	}
// 	opts := options.FindOne().SetProjection(projection)
// 	_, err = c.coll.FindOne(ctx, filter, &msgDoc, nil, 0, opts)
// 	return
// }

func (c *MsgMgo) GetGroupMsgDoc(ctx context.Context, groupId int64, docID int64) (msgDoc *model.GroupMsgModel, err error) {
	filter := bson.M{"groupId": groupId, "docId": docID}
	_, err = c.collMsgGroup.FindOne(ctx, filter, &msgDoc, nil, 0)
	return
}

func (c *MsgMgo) GetLatestReceiveMsg1v1(ctx context.Context, receiver int64) (msg *model.MsgDataModel, err error) {
	msg = &model.MsgDataModel{}
	filter := bson.M{"receiver": receiver}
	opts := options.FindOne().SetSort(bson.D{{"seq", -1}})
	_, err = c.collMsg1v1.FindOne(ctx, filter, msg, nil, 0, opts)
	return
}

func (c *MsgMgo) GetLatestMsgInGroup(ctx context.Context, groupId int64) (*model.GroupMsgModel, error) {
	msg := model.GroupMsgModel{}
	filter := bson.M{"groupId": groupId}
	// opts := options.FindOne().SetSort(bson.D{{"seq", -1}})
	opts := options.FindOne().SetSort(bson.D{{"docId", -1}})
	_, err := c.collMsgGroup.FindOne(ctx, filter, &msg, nil, 0, opts)
	return &msg, err
}
