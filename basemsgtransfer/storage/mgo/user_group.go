package mgo

import (
	"context"
	"xim/baselib/database/mongo"
	"xim/baselib/logger"
	"xim/proto/model"

	"go.mongodb.org/mongo-driver/bson"
	mgodriver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func NewUserGroupMongo() *UserGroupMgo {
	collUserGroup := mongo.GetMongoDBClient().NewCollectionWrapper("chat", "user_group")

	err := collUserGroup.CreateIndexes(context.Background(), []mgodriver.IndexModel{
		{Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "groupId", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		logger.Panicf("CreateIndexOne, err %v", err)
	}

	return &UserGroupMgo{coll: collUserGroup}
}

type UserGroupMgo struct {
	coll mongo.CollectionWrapper
}

func (c *UserGroupMgo) Update(ctx context.Context, group *model.UserGroupModel) (err error) {
	filter := bson.M{"userId": group.UserId, "groupId": group.GroupId}
	_, err = c.coll.UpdateOne(ctx, filter, bson.M{"$set": group}, true)
	return
}

func (c *UserGroupMgo) Find(ctx context.Context, userid int64) (results []*model.UserGroupModel, err error) {
	filter := bson.M{"userId": userid}
	err = c.coll.Find(ctx, filter, &results, nil, 0, 0)
	return
}
