package cache

import (
	"context"
	"fmt"
	"time"
	"xim/baselib/cache"
	"xim/baselib/util"

	mproto "xim/proto/api/basemsgtransfer"

	"github.com/go-redis/redis/v8"
	"golang.org/x/sync/errgroup"
)

const (
	msg1v1CacheKey     = "xim_msg_1v1_%v:%v"
	msg1v1CacheTimeout = time.Second * 60 * 5

	msgGroupCacheKey     = "xim_msg_group_%v:%v"
	msgGroupCacheTimeout = time.Second * 60 * 30
	concurrentLimit      = 3

	msgIdempotentCacheKey = "xim_msg_idempotent_%v:%v"
	msgIdempotentTimeout  = time.Second * 60 * 3
)

type MsgCache struct {
	rdb *redis.Client
}

func NewMsgCache() *MsgCache {
	return &MsgCache{
		rdb: cache.GetRedisClient("chat"),
	}
}

func getMsgGroupCacheKey(groupId, seqId int64) string {
	return fmt.Sprintf(msgGroupCacheKey, groupId, seqId)
}

func getMsg1v1CacheKey(receiver, seqId int64) string {
	return fmt.Sprintf(msg1v1CacheKey, receiver, seqId)
}

func (c *MsgCache) SetMessage1v1ToCache(ctx context.Context, msgs []*mproto.MsgData) error {
	wg := errgroup.Group{}
	wg.SetLimit(concurrentLimit)

	for _, v := range msgs {
		msg := v // closure safe var
		wg.Go(func() error {
			s := util.PbToString(msg)
			key := getMsg1v1CacheKey(msg.Receiver, msg.Seq)
			if err := c.rdb.Set(ctx, key, s, msg1v1CacheTimeout).Err(); err != nil {
				return err
			}
			return nil
		})
	}
	return wg.Wait()
}

func (c *MsgCache) SetMessageGroupToCache(ctx context.Context, groupId int64, msgs []*mproto.MsgData) error {
	wg := errgroup.Group{}
	wg.SetLimit(concurrentLimit)

	for _, v := range msgs {
		msg := v // closure safe var
		wg.Go(func() error {
			s := util.PbToString(msg)
			key := getMsgGroupCacheKey(groupId, msg.Seq)
			if err := c.rdb.Set(ctx, key, s, msgGroupCacheTimeout).Err(); err != nil {
				return err
			}
			return nil
		})
	}
	return wg.Wait()
}

func (c *MsgCache) DeleteMessages1v1Cache(ctx context.Context, receiver int64, seqs []int64) error {
	wg := errgroup.Group{}
	wg.SetLimit(concurrentLimit)

	for _, seq := range seqs {
		seq := seq
		wg.Go(func() error {
			err := c.rdb.Del(ctx, getMsg1v1CacheKey(receiver, seq)).Err()
			if err != nil {
				return err
			}
			return nil
		})
	}

	return wg.Wait()
}

func (c *MsgCache) GetMessagesGroupBySeq(ctx context.Context, groupId int64, seqs []int64) (seqMsgs []*mproto.MsgData, failedSeqs []int64, err error) {
	type entry struct {
		err error
		msg *mproto.MsgData
	}

	wg := errgroup.Group{}
	wg.SetLimit(concurrentLimit)

	results := make([]entry, len(seqs)) // set slice len/cap to length of seqs.
	for idx, seq := range seqs {
		// closure safe var
		idx := idx
		seq := seq

		wg.Go(func() error {
			res, err := c.rdb.Get(ctx, getMsgGroupCacheKey(groupId, seq)).Result()
			if err != nil {
				results[idx] = entry{err: err}
				return nil
			}

			msg := mproto.MsgData{}
			if err = util.String2Pb(res, &msg); err != nil {
				results[idx] = entry{err: err}
				return nil
			}
			results[idx] = entry{msg: &msg}
			return nil
		})
	}

	_ = wg.Wait()

	for idx, res := range results {
		if res.err == redis.Nil {
			failedSeqs = append(failedSeqs, seqs[idx])
			continue
		} else if res.err != nil {
			err = res.err
			return
		}

		seqMsgs = append(seqMsgs, res.msg)
	}

	return
}

func (c *MsgCache) GetMessages1v1BySeq(ctx context.Context, recevier int64, seqs []int64) (seqMsgs []*mproto.MsgData, failedSeqs []int64, err error) {
	type entry struct {
		err error
		msg *mproto.MsgData
	}

	wg := errgroup.Group{}
	wg.SetLimit(concurrentLimit)

	results := make([]entry, len(seqs)) // set slice len/cap to length of seqs.
	for idx, seq := range seqs {
		// closure safe var
		idx := idx
		seq := seq

		wg.Go(func() error {
			res, err := c.rdb.Get(ctx, getMsg1v1CacheKey(recevier, seq)).Result()
			if err != nil {
				results[idx] = entry{err: err}
				return nil
			}

			msg := mproto.MsgData{}
			if err = util.String2Pb(res, &msg); err != nil {
				results[idx] = entry{err: err}
				return nil
			}
			results[idx] = entry{msg: &msg}
			return nil
		})
	}

	_ = wg.Wait()

	for idx, res := range results {
		if res.err == redis.Nil {
			failedSeqs = append(failedSeqs, seqs[idx])
			continue
		} else if res.err != nil {
			err = res.err
			return
		}

		seqMsgs = append(seqMsgs, res.msg)
	}

	return
}

func getMsgIdempotentKey(from int64, localId string) string {
	return fmt.Sprintf(msgIdempotentCacheKey, from, localId)
}

func (c *MsgCache) SetMsgIdempotent(ctx context.Context, from int64, localId string) (err error) {
	_, err = c.rdb.SetNX(ctx, getMsgIdempotentKey(from, localId), int(1), msgIdempotentTimeout).Result()
	return
}

func (c *MsgCache) VerifyMsgIdempotent(ctx context.Context, from int64, localId string) (hasSend bool, err error) {
	ret := int64(0)
	ret, err = c.rdb.Exists(ctx, getMsgIdempotentKey(from, localId)).Result()
	return ret > 0, err
}
