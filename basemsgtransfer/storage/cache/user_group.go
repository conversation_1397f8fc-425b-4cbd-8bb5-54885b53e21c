package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"xim/baselib/cache"
	"xim/proto/model"

	"github.com/go-redis/redis/v8"
)

const (
	userGroupCacheTimeout = time.Second * 3600
	userGroupInfoKey      = "xim_user_group:%v"
)

type UserGroupInfoCache struct {
	rdb                   *redis.Client
	userGroupCacheTimeout time.Duration
}

func NewUserGroupCache() *UserGroupInfoCache {
	return &UserGroupInfoCache{
		rdb:                   cache.GetRedisClient("chat"),
		userGroupCacheTimeout: userGroupCacheTimeout,
	}
}

func getUserGroupInfoKey(userid int64) string {
	return fmt.Sprintf(userGroupInfoKey, userid)
}

func (c *UserGroupInfoCache) GetUserGroups(ctx context.Context, userid int64) (found bool, result []*model.UserGroupModel, err error) {
	res, err := c.rdb.Get(ctx, getUserGroupInfoKey(userid)).Bytes()
	if err != nil {
		if err == redis.Nil {
			err = nil
			return
		}
		return
	}

	err = json.Unmarshal(res, &result)
	if err != nil {
		return
	}

	found = true
	return
}

func (c *UserGroupInfoCache) SetUserGroups(ctx context.Context, userid int64, data []*model.UserGroupModel) error {
	v, err := json.Marshal(data)
	if err != nil {
		return err
	}
	_, err = c.rdb.Set(ctx, getUserGroupInfoKey(userid), string(v), c.userGroupCacheTimeout).Result()
	return err
}

func (c *UserGroupInfoCache) DeleteUserGroups(ctx context.Context, userid int64) (err error) {
	_, err = c.rdb.Del(ctx, getUserGroupInfoKey(userid)).Result()
	return
}
