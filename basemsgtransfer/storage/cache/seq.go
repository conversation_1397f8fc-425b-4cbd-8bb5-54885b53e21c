package cache

import (
	"context"
	"fmt"
	"time"
	"xim/baselib/cache"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
)

const (
	seq1v1CacheTimeout = time.Second * 3600 * 1
	seq1v1CacheKey     = "xim_seq1v1:%v"

	seqGroupCacheTimeout = time.Second * 3600 * 3
	seqGroupCacheKey     = "xim_seqgroup:%v"

	seqLockKey1v1   = "xim_seqlk_1v1:%v"
	seqLockKeyGroup = "xim_seqlk_group:%v"

	waitAckSeqsKey     = "xim_wait_seqs:%v"
	waitAckSeqsTimeout = time.Second * 60 * 10
	WaitAckSeqsLimit   = 200
)

type SeqCache struct {
	rdb *redis.Client
}

func NewSeqCache() *SeqCache {
	return &SeqCache{
		rdb: cache.GetRedisClient("chat"),
	}
}

func (c *SeqCache) GetClient() *redis.Client {
	return c.rdb
}

func Get1v1SeqLockKey(receiver int64) string {
	return fmt.Sprintf(seqLockKey1v1, receiver)
}

func GetGroupSeqLockKey(groupId int64) string {
	return fmt.Sprintf(seqLockKeyGroup, groupId)
}

func get1v1SeqKey(receiver int64) string {
	return fmt.Sprintf(seq1v1CacheKey, receiver)
}

func (c *SeqCache) Set1v1MaxSeq(ctx context.Context, receiver int64, seq int64) error {
	err := c.rdb.Set(ctx, get1v1SeqKey(receiver), seq, seq1v1CacheTimeout).Err()
	return err
}

func (c *SeqCache) Get1v1MaxSeq(ctx context.Context, receiver int64) (int64, error) {
	res, err := c.rdb.Get(ctx, get1v1SeqKey(receiver)).Int64()
	return res, err
}

func getGroupSeqKey(groupId int64) string {
	return fmt.Sprintf(seqGroupCacheKey, groupId)
}

func (c *SeqCache) SetGroupMaxSeq(ctx context.Context, groupId int64, seq int64) error {
	err := c.rdb.Set(ctx, getGroupSeqKey(groupId), seq, seqGroupCacheTimeout).Err()
	return err
}

func (c *SeqCache) GetGroupMaxSeq(ctx context.Context, groupId int64) (int64, error) {
	res, err := c.rdb.Get(ctx, getGroupSeqKey(groupId)).Int64()
	return res, err
}

func getWaitAckSeqsKey(userid int64) string {
	return fmt.Sprintf(waitAckSeqsKey, userid)
}

func (c *SeqCache) InitWaitAckQueue(ctx context.Context, receiver int64, seqs []int64) error {
	key := getWaitAckSeqsKey(receiver)
	exist, err := c.rdb.Exists(ctx, key).Result()
	if err != nil {
		return err
	}
	if exist == 0 {
		vals := []*redis.Z{&redis.Z{0, 0}}
		for _, v := range seqs {
			vals = append(vals, &redis.Z{
				Score:  float64(v),
				Member: v,
			})
		}
		err := c.rdb.ZAdd(ctx, key, vals...).Err()
		if err != nil {
			return err
		}

		// err := c.rdb.RPush(ctx, key, append([]any{0}, seqs...)).Err()
		// if err != nil {
		// 	return err
		// }
		err = c.rdb.Expire(ctx, key, waitAckSeqsTimeout).Err()
		return err
	}

	return nil
}

func (c *SeqCache) PushWaitAckSeqX(ctx context.Context, receiver int64, seqs []int64) error {
	// err := c.rdb.RPushX(ctx, getWaitAckSeqsKey(receiver), seqs...).Err()
	// return err
	key := getWaitAckSeqsKey(receiver)
	exist, err := c.rdb.Exists(ctx, key).Result()
	if err != nil {
		return err
	}

	if exist > 0 {
		var vals []*redis.Z
		for _, v := range seqs {
			vals = append(vals, &redis.Z{
				Score:  float64(v),
				Member: v,
			})
		}

		err := c.rdb.ZAdd(ctx, key, vals...).Err()
		if err != nil {
			return err
		}
	}
	return nil
}

// 缓存队列 = 初始化标志项 + 消息项
// 消息RPush RPop
func (c *SeqCache) GetWaitAckSeqsFromQueue(ctx context.Context, receiver int64, limit int) (waitAckSeqs []int64, toLoadFromDb bool, err error) {

	key := getWaitAckSeqsKey(receiver)
	llen, err := c.rdb.ZCard(ctx, key).Result()
	if err != nil {
		return nil, false, err
	}

	if llen <= 1 {
		return nil, llen == 0, nil
	}

	dataCache, err := c.rdb.ZRevRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    "1",
		Max:    "+inf",
		Offset: 0,
		Count:  int64(limit),
	}).Result()
	for _, v := range dataCache {
		waitAckSeqs = append(waitAckSeqs, cast.ToInt64(v))
	}

	// dataCache, err := c.rdb.LRange(ctx, key, 1, 1+int64(rangeCount)).Result()
	// for _, v := range dataCache {
	// 	waitAckSeqs = append(waitAckSeqs, cast.ToInt64(v))
	// }
	return waitAckSeqs, false, err
}

func (c *SeqCache) DelSeqInWaitAckQueue(ctx context.Context, receiver int64, seqs []int64) error {
	var seqIds []any
	for _, v := range seqs {
		seqIds = append(seqIds, v)
	}

	key := getWaitAckSeqsKey(receiver)
	err := c.rdb.ZRem(ctx, key, seqIds...).Err()
	return err
}
