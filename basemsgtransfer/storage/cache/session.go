package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"xim/baselib/cache"
	"xim/baselib/util"

	mproto "xim/proto/api/basemsgtransfer"

	"github.com/go-redis/redis/v8"
)

const (
	sessionCacheTimeout = time.Second * 3600
	sessionInfoKey      = "xim_session:%v:%v"
)

type SessionInfoCache struct {
	rdb                 *redis.Client
	sessionCacheTimeout time.Duration
}

func NewSessionInfoCache() *SessionInfoCache {
	return &SessionInfoCache{
		rdb:                 cache.GetRedisClient("chat"),
		sessionCacheTimeout: sessionCacheTimeout,
	}
}

func getSessionInfoKey(userid int64, sessionId string) string {
	return fmt.Sprintf(sessionInfoKey, userid, sessionId)
}

func (c *SessionInfoCache) MGetSessionInfo(ctx context.Context, userid int64, sessionIds []string) (result []*mproto.SessionInfo, missKeys []string, err error) {
	var keys []string
	for _, v := range sessionIds {
		keys = append(keys, getSessionInfoKey(userid, v))
	}

	res, err := c.rdb.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, nil, err
	}

	for i, v := range res {
		if v != nil {
			tmp := mproto.SessionInfo{}
			err = json.Unmarshal([]byte(v.(string)), &tmp)
			if err != nil {
				return nil, nil, err
			}
			result = append(result, &tmp)
		} else {
			missKeys = append(missKeys, sessionIds[i])
		}
	}

	return result, missKeys, nil

}

func (c *SessionInfoCache) MsetSessionInfo(ctx context.Context, userid int64, data []*mproto.SessionInfo) error {

	pipe := c.rdb.Pipeline()

	for _, v := range data {
		key := getSessionInfoKey(userid, v.SessionId)
		pipe.Set(ctx, key, util.JsonStr(v), c.sessionCacheTimeout)
	}

	_, err := pipe.Exec(ctx)
	return err

}

func (c *SessionInfoCache) DeleteSessionInfos(ctx context.Context, userid int64, sessionIds []string) error {
	pipe := c.rdb.Pipeline()

	for _, v := range sessionIds {
		key := getSessionInfoKey(userid, v)
		pipe.Del(ctx, key)
	}

	_, err := pipe.Exec(ctx)
	return err

}
