package sql

import (
	"context"
	"xim/baselib/database"
	"xim/proto/model"

	"gorm.io/gorm"
)

func NewHistoryMsgDb() *HistoryMsgDb {
	return &HistoryMsgDb{
		db: database.GetMysqlDB("vc_chat"),
	}
}

type HistoryMsgDb struct {
	db *gorm.DB
}

func (d *HistoryMsgDb) Insert(ctx context.Context, sessionId string, insertData []*model.HistoryMsgModel) error {
	err := d.db.Table(model.HistoryMsgModel{}.TableName(sessionId)).Create(&insertData).Error
	return err
}
