package controller

import (
	"errors"
	"io"
	"net/http"
	"strconv"
	"xim/baselib/ginserver"
	"xim/baselib/logger"
	"xim/baselib/msgchan"
	"xim/baselib/util"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"

	"github.com/gin-gonic/gin"
)

type XimController struct {
	ginserver.Controller
}

func newXimController() *XimController {
	return &XimController{}
}

func (c XimController) Name() string {
	return "v1"
}

func (c XimController) Routes() []ginserver.ControllerRoute {
	return []ginserver.ControllerRoute{
		{
			Method: ginserver.POST,
			Name:   "get_chan_token",
			Func:   c.<PERSON>han<PERSON>oken,
		},
	}
}

func getHeaderOrDefault(ctx *gin.Context, key, defaultValue string) string {
	if value := ctx.GetHeader(key); value != "" {
		return value
	}
	return defaultValue
}

func getBaseParam(ctx *gin.Context) (baseParam *common.BaseParam) {
	dt, _ := strconv.ParseInt(getHeaderOrDefault(ctx, "dt", "0"), 10, 32)
	nt, _ := strconv.ParseInt(getHeaderOrDefault(ctx, "nt", "0"), 10, 32)
	ts, _ := strconv.ParseInt(getHeaderOrDefault(ctx, "ts", "0"), 10, 64)
	baseParam = &common.BaseParam{
		// 从 header 中获取各种参数
		App:     getHeaderOrDefault(ctx, "app", ""),
		Av:      getHeaderOrDefault(ctx, "av", ""),
		Dt:      int32(dt),
		Did:     getHeaderOrDefault(ctx, "did", ""),
		Nt:      int32(nt),
		Ch:      getHeaderOrDefault(ctx, "ch", ""),
		Md:      getHeaderOrDefault(ctx, "md", ""),
		Os:      getHeaderOrDefault(ctx, "os", ""),
		Ts:      ts,
		Ip:      getHeaderOrDefault(ctx, "x-forwarded-for", ""),
		Imei:    getHeaderOrDefault(ctx, "imei", ""),
		Oaid:    getHeaderOrDefault(ctx, "oaid", ""),
		Bd:      getHeaderOrDefault(ctx, "bd", ""),
		Idfa:    getHeaderOrDefault(ctx, "idfa", ""),
		Vpn:     getHeaderOrDefault(ctx, "vpn", ""),
		Traceid: getHeaderOrDefault(ctx, "traceid", ""),
	}
	return
}

func (c XimController) GetChanToken(ctx *gin.Context) {
	content, err := c.getBody(ctx)
	if err != nil {
		return
	}
	var req common.BizBaseReq
	err = util.Unmarshal(content, &req)
	if err != nil {
		logger.Errorf("GetChanToken  Unmarshal fail,err:%v", err)
		c.fail(ctx)
		return
	}
	baseParam := getBaseParam(ctx)
	did := baseParam.GetDid()
	userId := req.GetUserId()
	logger.Infof("GetChanToken userId:%v,did:%v", userId, did)
	req.Base = baseParam
	data, err := msgchan.GetTokenData(ctx, userId, did)
	if err != nil {
		c.fail(ctx)
		logger.Errorf("msgchan.GetTokenData fail,err:%v", err)
		return
	}
	respData := &basemsgtransfer.ChanTokenRespData{
		Token: data.Token,
	}
	if data.MasterAddr != nil {
		respData.MasterAddr = data.MasterAddr
	}
	if data.SlaveAddr != nil {
		respData.SlaveAddr = data.SlaveAddr
	}
	resp := &basemsgtransfer.ChanTokenResp{
		Base: &common.SvcBaseResp{
			Code: 0,
			Msg:  "success",
		},
		Data: respData,
	}

	ctx.JSON(http.StatusOK, resp)
}

func (c XimController) getBody(ctx *gin.Context) (content []byte, err error) {
	defer ctx.Request.Body.Close()
	content, err = io.ReadAll(ctx.Request.Body)
	if err != nil {
		return nil, err
	}

	// todo remove log
	logger.Infof("agora callback body:%v", string(content))
	if len(content) == 0 {
		logger.Errorf("agora callback body is empty")
		err = errors.New("body is empty")
		c.fail(ctx)
		return
	}
	return
}

func (c XimController) fail(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{
		"code": errcode.ErrorNOK.Code,
		"msg":  "fail",
	})
}

func (c XimController) success(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
	})
}
