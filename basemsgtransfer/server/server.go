package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"xim/baselib/kafka"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/msgchan"
	"xim/baselib/util"
	"xim/basemsgtransfer/controller"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts"

	storage "xim/basemsgtransfer/storage/controller"

	"xim/baselib/config"

	"github.com/spf13/cast"
)

// BaseMsgTransferServer 服务接入层
// 负责实现proto协议中定义的grpc接口，检查请求参数，拦截非法请求，记录访问日志
type BaseMsgTransferServer struct {
	controller *controller.BaseMsgTransferServerController

	storage      *storage.CommonDatabase
	directHandle *directHandler // 避免消息中间件出问题 不走中间件调用
}

type directHandler struct {
	msg   *ConsumerHandlerV2
	state *StateConsumerHandler
}

func (c *BaseMsgTransferServer) Test(ctx context.Context, req *common.TestReq) (*common.TestResp, error) {
	//TODO implement me
	return &common.TestResp{
		I:    1111,
		Data: "1111",
	}, nil
}

// NewBaseMsgTransferServer 创建basemsgtransfer服务
// 初始化controller和业务自定义模块
func NewBaseMsgTransferServer(conf *config.ServerConfig) (*BaseMsgTransferServer, error) {
	s := &BaseMsgTransferServer{}
	if conf.Kafka.Close {
		storage, _ := storage.NewCommonDatabase(nil)
		s.directHandle = &directHandler{
			msg: &ConsumerHandlerV2{
				storage: storage,
			},
			state: &StateConsumerHandler{},
		}
	}

	var err error
	s.controller, err = controller.NewBaseMsgTransferServerController()
	if err != nil {
		return nil, err
	}

	s.storage, err = storage.NewCommonDatabase(&conf.Nsq)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (c *BaseMsgTransferServer) WebSocket(ctx context.Context, req *basemsgtransfer.Req) (*basemsgtransfer.Resp, error) {
	return nil, nil
}

func (c *BaseMsgTransferServer) GetChanToken(ctx context.Context, req *common.BizBaseReq) (resp *basemsgtransfer.ChanTokenResp, err error) {
	did := req.GetBase().GetDid()
	userId := req.GetUserId()
	data, err := msgchan.GetTokenData(ctx, userId, did)
	if err != nil {
		logger.Errorf("msgchan.GetTokenData fail,err:%v", err)
		return nil, err
	}
	respData := &basemsgtransfer.ChanTokenRespData{
		Token: data.Token,
	}
	if data.MasterAddr != nil {
		respData.MasterAddr = data.MasterAddr
	}
	if data.SlaveAddr != nil {
		respData.SlaveAddr = data.SlaveAddr
	}
	resp = &basemsgtransfer.ChanTokenResp{
		Base: &common.SvcBaseResp{
			Code: 0,
			Msg:  "success",
		},
		Data: respData,
	}
	return

}

func (c *BaseMsgTransferServer) HandleMsg(ctx context.Context, req *basemsgtransfer.HandleMsgReq) (*basemsgtransfer.HandleMsgRsp, error) {
	logger.Infof("get RawData：%v", req.MsgJson)
	resp := &basemsgtransfer.HandleMsgRsp{
		Base: &common.SvcBaseResp{
			Code: 0,
			Msg:  "success",
		},
	}

	msgData := basemsgtransfer.UpstreamMsgData{}
	err := json.Unmarshal([]byte(req.MsgJson), &msgData)

	if err != nil {
		logger.Errorf("error unmarshal msgData %v", err)
	}

	userid, err := strconv.ParseInt(msgData.UserId, 10, 64)
	if err != nil {
		logger.Errorf("error %v", err)
		return nil, err
	}
	_ = msgData.MsgId
	interMsg := basemsgtransfer.InterMsgData{UserId: userid, Timestamp: msgData.Time, Base: msgData.GetBase(), NanoTime: msgData.NanoTime}
	switch msgData.Type {
	case basemsgtransfer.MsgEventType_connected, basemsgtransfer.MsgEventType_ping, basemsgtransfer.MsgEventType_closed:
		interMsg.Type = int32(msgData.Type)
		//err = c.storage.MsgToStateMQ(ctx, &interMsg)
		// dKey := cast.ToString(userid)
		// if _, _, errTEST := kafka.Client(consts.XIM_STATE_TOPIC()).SendMessage(ctx, dKey, &interMsg); errTEST != nil {
		// 	logger.Errorf("err %v", errTEST)
		// }
		err = c.handleStateMsg(ctx, userid, &interMsg)
		if err != nil {
			logger.Errorf("handleStateMsg err %v", err)
		}

	case basemsgtransfer.MsgEventType_message:
		msgType, err := decodeBizMsgData(msgData.Msg, &interMsg)
		if err != nil {
			logger.Errorf("err %v", err)
			return resp, err
		}
		logger.Infof("msgData.Msg, msg [%v], interMsg %+v", msgData.Msg, util.JsonStr(&interMsg))
		//err = c.storage.MsgToContentMQ(ctx, &interMsg)

		// dKey := cast.ToString(GetSendMsgDistibutionCh(interMsg.Type2Data))
		// if _, _, errTEST := kafka.Client(consts.XIM_MSG_TOPIC()).SendMessage(ctx, dKey, &interMsg); errTEST != nil {
		// 	logger.Errorf("err %v", errTEST)
		// }

		switch msgType {
		case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq):
			err = c.handleMsg(ctx, interMsg.Type2Data, &interMsg)
		case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePing):
			err = c.handlePing(ctx, interMsg.Type1Data, &interMsg)
		case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotifyAck):
			err = c.handleNotifyAck(ctx, interMsg.Type5Data, &interMsg)
		case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePullMsgBySeqList):
			err = c.handlePullMsgBySeqList(ctx, interMsg.Type8Data, &interMsg)
		}
		if err != nil {
			logger.Errorf("HandleMsg err %v", err)
		}
	}
	return resp, err
}

func (c *BaseMsgTransferServer) handleMsg(ctx context.Context, msgData *basemsgtransfer.MsgData, interMsg *basemsgtransfer.InterMsgData) (err error) {
	if c.directHandle != nil {
		return c.directHandle.msg.handleMessag(interMsg)
	}

	dKey := cast.ToString(GetSendMsgDistibutionCh(msgData))
	if _, _, err = kafka.Client(consts.XIM_MSG_TOPIC()).SendMessage(ctx, dKey, interMsg); err != nil {
		return
	}
	return
}

func (c *BaseMsgTransferServer) handlePing(ctx context.Context, msgData *basemsgtransfer.PingData, interMsg *basemsgtransfer.InterMsgData) (err error) {
	if c.directHandle != nil {
		return c.directHandle.msg.handleMessag(interMsg)
	}

	dKey := cast.ToString(GetPingMsgDistibutionCh(msgData))
	if _, _, err = kafka.Client(consts.XIM_MSG_TOPIC()).SendMessage(ctx, dKey, interMsg); err != nil {
		return
	}
	return
}

func (c *BaseMsgTransferServer) handleNotifyAck(ctx context.Context, msgData []*basemsgtransfer.NotifySyncData, interMsg *basemsgtransfer.InterMsgData) (err error) {
	if c.directHandle != nil {
		return c.directHandle.msg.handleMessag(interMsg)
	}

	dKey := cast.ToString(GetNotifyAckMsgDistibutionCh(msgData))
	if _, _, err = kafka.Client(consts.XIM_MSG_TOPIC()).SendMessage(ctx, dKey, interMsg); err != nil {
		return
	}
	return
}

func (c *BaseMsgTransferServer) handlePullMsgBySeqList(ctx context.Context, msgData *basemsgtransfer.PullMessageBySeqsReq, interMsg *basemsgtransfer.InterMsgData) (err error) {
	if c.directHandle != nil {
		return c.directHandle.msg.handleMessag(interMsg)
	}

	dKey := cast.ToString(GetPullMsgByListDistibutionCh(msgData))
	if _, _, err = kafka.Client(consts.XIM_MSG_TOPIC()).SendMessage(ctx, dKey, interMsg); err != nil {
		return
	}
	return
}

func (c *BaseMsgTransferServer) handleStateMsg(ctx context.Context, userid int64, interMsg *basemsgtransfer.InterMsgData) (err error) {
	if c.directHandle != nil {
		return c.directHandle.state.handleMessage(interMsg)
	}
	dKey := cast.ToString(userid)
	if _, _, err = kafka.Client(consts.XIM_STATE_TOPIC()).SendMessage(ctx, dKey, interMsg); err != nil {
		return
	}
	return
}

func decodeBizMsgData(msgJson string, msg *basemsgtransfer.InterMsgData) (msgType int32, err error) {
	var rawMsg map[string]interface{}
	err = json.Unmarshal([]byte(msgJson), &rawMsg)
	if err != nil {
		return 0, err
	}

	if dataObj, exists := rawMsg["data"]; exists {
		if dataMap, ok := dataObj.(map[string]interface{}); ok {
			dataBytes, err := json.Marshal(dataMap)
			if err != nil {
				return 0, fmt.Errorf("failed to marshal data object: %v", err)
			}
			rawMsg["data"] = string(dataBytes)

			processedBytes, err := json.Marshal(rawMsg)
			if err != nil {
				return 0, fmt.Errorf("failed to marshal processed message: %v", err)
			}
			msgJson = string(processedBytes)
		}
	}

	req := basemsgtransfer.ReqMsgData{}
	err = json.Unmarshal([]byte(msgJson), &req)
	if err != nil {
		return 0, err
	}
	if req.Data == "" {
		return 0, errors.New("msgJson = " + msgJson)
	}

	msgType = req.Type
	msg.Type = req.Type
	switch req.Type {
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePing):
		pingData := basemsgtransfer.PingData{}
		err = json.Unmarshal([]byte(req.Data), &pingData)
		msg.Type1Data = &pingData
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq):
		sendData := basemsgtransfer.MsgData{}
		err = json.Unmarshal([]byte(req.Data), &sendData)
		msg.Type2Data = &sendData
		if msg.Type2Data.Receiver == 0 {
			msg.Type2Data.Receiver = msg.Type2Data.To
		}
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotifyAck):
		var notifyAck []*basemsgtransfer.NotifySyncData
		err = json.Unmarshal([]byte(req.Data), &notifyAck)
		msg.Type5Data = notifyAck
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePullMsgBySeqList):
		pullMsgData := basemsgtransfer.PullMessageBySeqsReq{}
		err = json.Unmarshal([]byte(req.Data), &pullMsgData)
		msg.Type8Data = &pullMsgData

	default:
		err = errors.New("err msg type")
	}

	label := map[string]string{
		"result":   "0",
		"error":    "",
		"msg_type": basemsgtransfer.ChatMsgType(req.Type).String(),
		//"userid":   strconv.FormatInt(msg.UserId, 10),
	}
	if err != nil {
		logger.Errorf("error unmarshal msgJson %v , %v", msgJson, err)
		label["result"] = err.Error()
	}
	metric.CounterWithLabels("msg_upstream", label).Inc()
	return msgType, err
}

// 处理客户端上行消息
// func (c *BaseMsgTransferServer) handleUpstream(ctx context.Context, senderId int64, req *basemsgtransfer.ReqMsgData) (err error) {
// 	switch req.Type {
// 	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePing):
// 		err = c.handlePingMsg(ctx, req.Data)
// 		if err != nil {
// 			logger.Errorf("handleReqData,err %v", err)
// 		}
// 	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq):
// 		err = c.handleSendMsg(ctx, senderId, req.Data)
// 		if err != nil {
// 			logger.Errorf("handleReqData,err %v", err)
// 		}
// 	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotifyAck):
// 		err = c.handleNotifyAckMsg(ctx, senderId, req.Data)
// 		if err != nil {
// 			logger.Errorf("handleReqData,err %v", err)
// 		}
// 	default:
// 		err = errors.New("err msg type")
// 	}

// 	return err
// }

// func (c *BaseMsgTransferServer) handlePingMsg(ctx context.Context, msgData string) error {
// 	logger.Infof("handlePingMsg %v", msgData)
// 	pingData := basemsgtransfer.PingData{}
// 	err := json.Unmarshal([]byte(msgData), &pingData)
// 	if err != nil {
// 		return err
// 	}

// 	userid := cast.ToInt64(pingData.Userid)
// 	waitAckMsgs, err := c.storage.GetWaitAckMsgs(ctx, userid, 200)
// 	if err != nil {
// 		return err
// 	}
// 	if len(waitAckMsgs) > 0 {
// 		logger.Infof("waitAckMsgs, %+v", waitAckMsgs)
// 		err = msgchan.PublishRawMsg(userid, "", util.JsonStr(basemsgtransfer.RespMsgData{
// 			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
// 			Data: util.JsonStr(waitAckMsgs),
// 		}))
// 	}

// 	return err
// }

// func (c *BaseMsgTransferServer) handleSendMsg(ctx context.Context, senderId int64, msgData string) error {
// 	logger.Infof("handleSendMsg userid %v,msgdata %v", senderId, msgData)

// 	// 1. decode && check
// 	m, err := sendDataDecode(senderId, msgData)
// 	if err != nil {
// 		logger.Errorf("handleSendMsg,err %v", err)
// 		if err1 := msgchan.PublishRawMsg(senderId, "", util.JsonStr(basemsgtransfer.RespMsgData{
// 			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
// 			Data: util.JsonStr(basemsgtransfer.SendRespData{
// 				ErrCode: errcode.ErrorParam.Code,
// 				ErrMsg:  err.Error(),
// 			}),
// 		})); err1 != nil {
// 			return err1
// 		}
// 		return err
// 	}

// 	hadSent, err := c.storage.GetCacheClient().VerifyMsgIdempotent(ctx, m.From, m.LocalId)
// 	if err != nil {
// 		return err
// 	}
// 	if hadSent {
// 		err = msgchan.PublishRawMsg(senderId, "", util.JsonStr(basemsgtransfer.RespMsgData{
// 			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
// 			Data: util.JsonStr(basemsgtransfer.SendRespData{
// 				ErrCode:    errcode.ErrorOK.Code,
// 				ErrMsg:     errcode.ErrorOK.Msg,
// 				LocalId:    m.LocalId,
// 				MsgId:      m.MsgId,
// 				SessionId:  m.SessionId,
// 				SendTime:   util.NowTime(),
// 				CreateTime: util.NowTime(),
// 			}),
// 		}))
// 		return err
// 	}

// 	// 2. server hook
// 	if err := WebhookImSendMsg(ctx, m); err != nil {
// 		return err
// 	}

// 	// 3. mq to save
// 	if err := c.storage.MsgToMQ(ctx, m); err != nil {
// 		logger.Errorf("MsgToMQ,err %v", err)
// 		return err
// 	}

// 	// 4. ack
// 	err = msgchan.PublishRawMsg(senderId, "", util.JsonStr(basemsgtransfer.RespMsgData{
// 		Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
// 		Data: util.JsonStr(basemsgtransfer.SendRespData{
// 			ErrCode:    errcode.ErrorOK.Code,
// 			ErrMsg:     errcode.ErrorOK.Msg,
// 			LocalId:    m.LocalId,
// 			MsgId:      m.MsgId,
// 			SessionId:  m.SessionId,
// 			SendTime:   util.NowTime(),
// 			CreateTime: util.NowTime(),
// 		}),
// 	}))
// 	if err != nil {
// 		return err
// 	}

// 	err = c.storage.GetCacheClient().SetMsgIdempotent(ctx, m.From, m.LocalId)
// 	if err != nil {
// 		return err
// 	}

// 	{ //todo remove
// 		// msgchan.PublishRawMsg(m.To, "", util.JsonStr(basemsgtransfer.RespMsgData{
// 		// 	Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
// 		// 	Data: util.JsonStr([]basemsgtransfer.MsgData{*m}),
// 		// }))
// 		// todo test    move to svcchat
// 		go c.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
// 			Upsert: &basemsgtransfer.SessionInfo{
// 				SessionId:   m.SessionId,
// 				SessionType: 1,
// 				OwnUserid:   m.From,
// 				PeerId:      m.To,
// 				Ct:          time.Now().Unix(),
// 				Sweet:       1.0,
// 			},
// 		})

// 		go c.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
// 			Upsert: &basemsgtransfer.SessionInfo{
// 				SessionId:   m.SessionId,
// 				SessionType: 1,
// 				OwnUserid:   m.To,
// 				PeerId:      m.From,
// 				Ct:          time.Now().Unix(),
// 				Sweet:       1.0,
// 			},
// 		})
// 	}

// 	return nil

// }

// func (c *BaseMsgTransferServer) handleNotifyAckMsg(ctx context.Context, recevier int64, msgData string) error {
// 	logger.Infof("handleNotifyAckMsg userid %v,msgdata %v", recevier, msgData)

// 	var notifyAckData []*basemsgtransfer.NotifySyncData
// 	err := json.Unmarshal([]byte(msgData), &notifyAckData)
// 	if err != nil {
// 		return err
// 	}
// 	var seqs []int64
// 	for _, v := range notifyAckData {
// 		seqs = append(seqs, v.Seq)
// 	}

// 	return c.storage.OfflineMsgAcked(ctx, recevier, seqs)

// }

// func sendDataDecode(senderId int64, sendData string) (*basemsgtransfer.MsgData, error) {
// 	m := basemsgtransfer.MsgData{}
// 	err := json.Unmarshal([]byte(sendData), &m)
// 	if err != nil {
// 		return nil, err
// 	}

// 	m.SessionId = utils.SessionIdGen(m.SessionType, m.From, m.To, m.GroupId)
// 	if _, ok := basemsgtransfer.SessionType_name[m.SessionType]; !ok || m.SessionType == 0 || m.SessionId == "" || m.LocalId == "" {
// 		return nil, errors.New("param error")
// 	}

// 	if senderId != m.From {
// 		return nil, fmt.Errorf("sendId %v != from %v", senderId, m.From)
// 	}

// 	m.MsgId = utils.SnowNext()
// 	if m.SendTime == 0 {
// 		m.SendTime = time.Now().UnixMilli()
// 	}
// 	m.CreateTime = time.Now().UnixMilli()
// 	// decode url
// 	// switch m.ContentType {
// 	// case int32(basemsgtransfer.ContentType_Image):

// 	// case int32(basemsgtransfer.ContentType_Voice):
// 	// 	var v basemsgtransfer.VoiceElem
// 	// 	err := json.Unmarshal([]byte(m.Content), &v)
// 	// 	if err != nil {
// 	// 		return nil, err
// 	// 	}
// 	// 	v.VoiceUrl = util.FulllChatAudioUrl(v.ObjectId)
// 	// 	m.ContentTypeVoiceData = &v
// 	// }

// 	return &m, nil
// }
