package server

import (
	"context"
	"time"
	"xim/basemsgtransfer/storage/mgo"
	"xim/basemsgtransfer/utils"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"
	"xim/proto/model"

	"github.com/jinzhu/copier"
)

func (c *BaseMsgTransferServer) GetSession(ctx context.Context, req *basemsgtransfer.GetSessionReq) (resp *basemsgtransfer.GetSessionResp, err error) {

	if r := paramDecode(req); r != nil {
		return &basemsgtransfer.GetSessionResp{
			Base: r.ToSvcBaseResp(),
		}, nil
	}

	resp = &basemsgtransfer.GetSessionResp{}
	if req.SessionId != "" {
		resp.Session, err = c.storage.GetSessionInfo(ctx, req.Userid, req.SessionId)
		if err != nil {
			return &basemsgtransfer.GetSessionResp{
				Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
			}, err
		}
	}

	if resp.Session == nil && req.Upsert != nil {
		req.Upsert.Ct = time.Now().Unix()
		err := c.storage.CreateSessionPair(ctx, req.Upsert)
		if err != nil {
			return &basemsgtransfer.GetSessionResp{
				Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
			}, err
		}
		resp.Session = req.Upsert
		resp.IsNew = true
	}

	resp.Base = errcode.ErrorOK.ToSvcBaseResp()
	return resp, nil
}

func paramDecode(r *basemsgtransfer.GetSessionReq) *errcode.ErrorCode {
	if r.Upsert != nil {
		if r.GroupId == 0 {
			r.GroupId = r.Upsert.GroupId
		}
		if r.Userid == 0 {
			r.Userid = r.Upsert.OwnUserid
		}
		if r.To == 0 {
			r.To = r.Upsert.PeerId
		}
		if r.Upsert.SessionType == 0 {
			r.Upsert.SessionType = utils.GetSessionType(r.Upsert.OwnUserid, r.Upsert.PeerId, r.Upsert.GroupId)
		}
		r.Upsert.SessionId = utils.SessionIdGen(r.Upsert.SessionType, r.Upsert.OwnUserid, r.Upsert.PeerId, r.Upsert.GroupId)

		if r.SessionId == "" {
			r.SessionId = r.Upsert.SessionId
		}
	}

	if r.SessionId == "" && (r.GroupId == 0 && r.To == 0) {
		return &errcode.ErrorParam
	}
	return nil
}

func (c *BaseMsgTransferServer) SetSession(ctx context.Context, req *basemsgtransfer.SetSessionReq) (*basemsgtransfer.SetSessionResp, error) {
	if req.SessionId == "" || req.OwnUserid <= 0 {
		return &basemsgtransfer.SetSessionResp{
			Base: errcode.ErrorParam.ToSvcBaseResp(),
		}, nil
	}

	sessions, err := c.storage.GetSessionInfos(ctx, req.OwnUserid, []string{req.SessionId})
	if err != nil || len(sessions) == 0 {
		return &basemsgtransfer.SetSessionResp{
			Base: errcode.ErrorParam.WithErrorFormatMsg("session not found: %v", err).ToSvcBaseResp(),
		}, nil
	}

	update := sessions[0]
	if req.IsPinned != nil {
		update.IsPinned = req.IsPinned.Value
	}

	if req.RecvMsgOpt != nil {
		update.RecvMsgOpt = req.RecvMsgOpt.Value
	}

	if req.Remark != nil {
		update.Remark = req.Remark.Value
	}

	err = c.storage.UpdateSession(ctx, update)
	if err != nil {
		return &basemsgtransfer.SetSessionResp{
			Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		}, err
	}

	return &basemsgtransfer.SetSessionResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
	}, nil
}

func (c *BaseMsgTransferServer) GetSortedSessionList(ctx context.Context, req *basemsgtransfer.GetSortedSessionListReq) (*basemsgtransfer.GetSortedSessionListResp, error) {
	if req.Userid <= 0 {
		return &basemsgtransfer.GetSortedSessionListResp{
			Base: errcode.ErrorParam.ToSvcBaseResp(),
		}, nil
	}

	sessions, err := mgo.NewSessionMongo().FindUserSessionByUtSort(ctx, req.Userid, 50)
	if err != nil {
		return &basemsgtransfer.GetSortedSessionListResp{
			Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		}, err
	}

	resp := &basemsgtransfer.GetSortedSessionListResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
		Data: &basemsgtransfer.SessionListData{},
	}

	copier.Copy(&resp.Data.Sessions, &sessions)
	return resp, nil
}

func (c *BaseMsgTransferServer) GetSessionInfo(ctx context.Context, req *basemsgtransfer.GetSessionInfoReq) (*basemsgtransfer.GetSessionInfoResp, error) {
	if req.Userid <= 0 {
		return &basemsgtransfer.GetSessionInfoResp{
			Base: errcode.ErrorParam.ToSvcBaseResp(),
		}, nil
	}

	if len(req.SessionIds) == 0 {
		return &basemsgtransfer.GetSessionInfoResp{
			Base: &common.SvcBaseResp{},
			Data: &basemsgtransfer.SessionListData{
				Sessions: make([]*basemsgtransfer.SessionInfo, 0),
			},
		}, nil
	}

	sessions, err := c.storage.GetSessionInfos(ctx, req.Userid, req.SessionIds)
	if err != nil {
		return &basemsgtransfer.GetSessionInfoResp{
			Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		}, err
	}
	resp := &basemsgtransfer.GetSessionInfoResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
		Data: &basemsgtransfer.SessionListData{},
	}

	copier.Copy(&resp.Data.Sessions, &sessions)
	return resp, nil
}

func (c *BaseMsgTransferServer) UserGroupIdList(ctx context.Context, req *basemsgtransfer.UserGroupIdListReq) (*basemsgtransfer.UserGroupIdListResp, error) {
	groupIds, err := c.storage.GetUserGroupIdList(ctx, req.Userid)
	if err != nil {
		return &basemsgtransfer.UserGroupIdListResp{
			Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		}, err
	}

	return &basemsgtransfer.UserGroupIdListResp{
		Base:       &common.SvcBaseResp{},
		GrouidList: groupIds,
	}, nil
}

func (c *BaseMsgTransferServer) UserJoinGroup(ctx context.Context, req *basemsgtransfer.UserJoinGroupReq) (*common.SvcCommonResp, error) {
	err := c.storage.UserUpdateGroup(ctx, &model.UserGroupModel{
		UserId:  req.Userid,
		GroupId: req.Groupid,
		Status:  0,
		Ct:      time.Now().Unix(),
	})
	if err != nil {
		return &common.SvcCommonResp{
			Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		}, err
	}

	// 加入群聊立马 type7 消息
	go publishUserGroupMaxSeq(context.Background(), req.Userid, c.storage)
	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{},
	}, nil
}

func (c *BaseMsgTransferServer) UserLeaveGroup(ctx context.Context, req *basemsgtransfer.UserLeaveGroupReq) (*common.SvcCommonResp, error) {
	err := c.storage.UserUpdateGroup(ctx, &model.UserGroupModel{
		UserId:  req.Userid,
		GroupId: req.Groupid,
		Status:  1,
		Ut:      time.Now().Unix(),
	})
	if err != nil {
		return &common.SvcCommonResp{
			Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		}, err
	}
	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{},
	}, nil
}
