package server

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"
	"xim/baselib/config"
	"xim/baselib/kafka"
	"xim/baselib/logger"
	"xim/baselib/msgchan"
	"xim/baselib/util"
	"xim/basemsgtransfer/stat"
	storage "xim/basemsgtransfer/storage/controller"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts"
	"xim/proto/consts/errcode"

	"xim/basemsgtransfer/utils"

	"github.com/IBM/sarama"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
)

// const (
// 	ChannelNum               = 100
// 	UpstreamMsgTimeOutSecond = 60 * 3 // 超过3分钟不处理
// )

// func getSendMsgDistibutionCh(msg *basemsgtransfer.MsgData) (ch int64) {
// 	if msg.Receiver > 0 {
// 		return msg.Receiver % ChannelNum
// 	}
// 	return msg.GroupId % ChannelNum
// }

func NewConsumerHandlerV2(conf *config.Config, groupID string, topics []string, autoCommitEnable bool) (*ConsumerHandlerV2, error) {
	if conf == nil {
		err := fmt.Errorf("conf == nil ")
		return nil, err
	}

	var consumerHandler ConsumerHandlerV2
	var err error
	consumerHandler.storage, err = storage.NewCommonDatabase(nil)
	if err != nil {
		return nil, err
	}

	for i := int64(0); i < ChannelNum; i++ {
		consumerHandler.sendMsgChArrays[i] = make(chan *basemsgtransfer.InterMsgData, 50)
		go consumerHandler.distributeSendHandle(i)
	}

	consumerHandler.msgConsumerGroup, err = kafka.NewMConsumerGroup(conf, groupID, topics, autoCommitEnable)
	if err != nil {
		return nil, err
	}
	return &consumerHandler, nil
}

func InitMsgKafkaConsumer(conf *config.Config) {
	if conf == nil {
		logger.Errorf("conf == nil ")
		return
	}
	consumer, err := NewConsumerHandlerV2(conf, consts.XIM_MSG_GROUP(), []string{consts.XIM_MSG_TOPIC()}, true)
	if err != nil {
		panic(fmt.Errorf("NewConsumerHandlerV2  error %v", err))
	}
	fmt.Printf("err %v", err)
	go consumer.msgConsumerGroup.RegisterHandleAndConsumer(context.Background(), consumer)
}

func (c *ConsumerHandlerV2) handlePingMsg(ctx context.Context, base *common.BaseParam, pingData *basemsgtransfer.PingData) error {
	//logger.Infof("handlePingMsg %v", pingData)
	if pingData == nil {
		return errors.New("pingdata nil")
	}

	// 1v1  离线消息
	userid := cast.ToInt64(pingData.Userid)
	waitAckMsgs, err := c.storage.GetWaitAckMsgs(ctx, userid, 100)
	if err != nil {
		return err
	}
	if len(waitAckMsgs) > 0 {
		logger.Infof("waitAckMsgs, %+v", waitAckMsgs)
		err = msgchan.PublishRawMsg(userid, "", util.JsonStr(basemsgtransfer.RespMsgData{
			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
			Data: util.JsonStr(waitAckMsgs),
		}), basemsgtransfer.ChatMsgType_ChatMsgTypeNotify.String())
	}

	go WebhookImPing(ctx, userid, base, pingData.IsFront)

	// group msg maxseqid
	go publishUserGroupMaxSeq(ctx, userid, c.storage)

	return err
}

func (c *ConsumerHandlerV2) distributeSendMsg(ctx context.Context, r *basemsgtransfer.InterMsgData) {

	if r.Type2Data.Receiver == 0 {
		r.Type2Data.Receiver = r.Type2Data.To
	}
	ch := GetSendMsgDistibutionCh(r.Type2Data)
	c.sendMsgChArrays[ch] <- r
	return
}

func (c *ConsumerHandlerV2) handleSendMsg(ctx context.Context, msgData *basemsgtransfer.InterMsgData) error {
	senderId, m := msgData.UserId, msgData.Type2Data
	logger.Infof("handleSendMsg userid %v,msgdata %+v", senderId, util.JsonStr(m))
	if m == nil {
		return errors.New("handleSendMsg nil")
	}

	// 1. decode && check
	err := msgDataDecode(senderId, m)
	if err != nil {
		logger.Errorf("handleSendMsg,err %v", err)
		if err1 := msgchan.PublishRawMsg(senderId, "", util.JsonStr(basemsgtransfer.RespMsgData{
			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
			Data: util.JsonStr(basemsgtransfer.SendRespData{
				ErrCode: errcode.ErrorParam.Code,
				ErrMsg:  err.Error(),
			}),
		}), basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp.String()); err1 != nil {
			return err1
		}
		return err
	}

	if !m.SysMsg {
		hadSent, err := c.storage.GetCacheClient().VerifyMsgIdempotent(ctx, senderId, m.LocalId)
		if err != nil {
			return err
		}
		if hadSent {
			err = sendMsgAck(m, errcode.ErrorOK)
			return err
		}

	}

	switch basemsgtransfer.SessionType(msgData.Type2Data.SessionType) {
	case basemsgtransfer.SessionType_SessionTypeChat, // 1v1
		basemsgtransfer.SessionType_SessionTypeSystem:
		return c.handleSendMsg1v1(ctx, msgData)
	case basemsgtransfer.SessionType_SessionTypeMoment:
		return c.handleSendMoment(ctx, msgData)
	case basemsgtransfer.SessionType_SessionTypeGroup: //家族
		return c.handleSendMsgGroup(ctx, msgData)
	default: //语音房目前割裂 自己下发
		if err1 := msgchan.PublishRawMsg(senderId, "", util.JsonStr(basemsgtransfer.RespMsgData{
			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
			Data: util.JsonStr(basemsgtransfer.SendRespData{
				ErrCode: errcode.ErrorParam.Code,
				ErrMsg:  errcode.ErrorParam.Msg,
			}),
		}), basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp.String()); err1 != nil {
			return err1
		}
	}

	return nil

}

func (c *ConsumerHandlerV2) handleSendMsg1v1(ctx context.Context, msgData *basemsgtransfer.InterMsgData) (err error) {
	m := msgData.Type2Data

	// 2. server hook
	if code := WebhookImSendMsg(ctx, msgData); code.Code != errcode.ErrorOK.Code {
		stat.UpstreamMsgToAckLatencyMilliSecond(m.ContentType, msgData.NanoTime)
		err := sendMsgAck(m, code)
		return err
	}

	// 3. save  后续失败 告警介入 处理webhook逻辑相关回滚，保证一致性
	_, err = c.storage.Msg1v1Save(ctx, m.Receiver, []*basemsgtransfer.MsgData{m})
	if err != nil {
		return err
	}

	// 4. msgchan
	err = sendToUserByChan(m)
	if err != nil {
		return err
	}
	stat.UpstreamMsgToAckLatencyMilliSecond(m.ContentType, msgData.NanoTime)
	return
}

func (c *ConsumerHandlerV2) handleSendMsgGroup(ctx context.Context, msgData *basemsgtransfer.InterMsgData) (err error) {
	m := msgData.Type2Data

	// 2. server hook
	topic, code := WebhookFamilySendMsg(ctx, msgData)
	if code.Code != errcode.ErrorOK.Code {
		err := sendMsgAck(m, code)
		return err
	}

	if topic != "" {
		// 3. save
		insertSeqs, err := c.storage.MsgGroupSave(ctx, msgData.Type2Data.GroupId, []*basemsgtransfer.MsgData{msgData.Type2Data})
		if err != nil {
			logger.Errorf("MsgGroupSave err %v", err)
			return err
		}

		if len(insertSeqs) > 0 {
			m.Seq = insertSeqs[0]
		}

		// 4. broadcast
		err = broadcastByChan(topic, m)
		if err != nil {
			return err
		}
	}

	return nil
}

func broadcastByChan(topic string, m *basemsgtransfer.MsgData) error {

	err1 := sendMsgAck(m, errcode.ErrorOK) // sender
	if err1 != nil {
		logger.Errorf("broadcastByChan err %v", err1)
	}

	err2 := msgchan.Broadcast(topic, util.JsonStr(basemsgtransfer.RespMsgData{
		Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
		Data: util.JsonStr([]*basemsgtransfer.MsgData{m}),
	}))
	if err2 != nil {
		logger.Errorf("err %v", err2)
	}

	if err1 != nil || err2 != nil {
		return fmt.Errorf("err1:%v,err2:%v", err1, err2)
	}

	return nil
}

func (c *ConsumerHandlerV2) handleNotifyAckMsg(ctx context.Context, recevier int64, notifyAckData []*basemsgtransfer.NotifySyncData) error {
	//logger.Infof("handleNotifyAckMsg userid %v,msgdata %+v", recevier, notifyAckData)
	if len(notifyAckData) == 0 {
		return errors.New("notifyAckData nil")
	}

	var seqs []int64
	for _, v := range notifyAckData {
		seqs = append(seqs, v.Seq)
	}

	return c.storage.OfflineMsgAcked(ctx, recevier, seqs)

}

// func (c *ConsumerHandlerV2) publishUserGroupMaxSeq(ctx context.Context, userid int64) (err error) {
// 	if resp, err := container.basemsgTransClient.UserGroupIdList(ctx, &basemsgtransfer.UserGroupIdListReq{
// 		Userid: userid,
// 	}); err == nil && errcode.IsOk(resp) {
// 		var maxSeqRespData *basemsgtransfer.MaxSeqRespData
// 		sessionType := int32(basemsgtransfer.SessionType_SessionTypeGroup)
// 		for _, groupId := range resp.GrouidList {
// 			maxSeq, _ := c.storage.GetGroupMaxSeq(ctx, groupId)
// 			if maxSeqRespData == nil {
// 				maxSeqRespData = &basemsgtransfer.MaxSeqRespData{MaxSeqs: make(map[string]int64)}
// 			}
// 			maxSeqRespData.MaxSeqs[utils.SessionIdGen(sessionType, 0, 0, groupId)] = maxSeq
// 		}
// 		err = msgchan.PublishRawMsg(userid, "", util.JsonStr(basemsgtransfer.RespMsgData{
// 			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNewestSeq),
// 			Data: util.JsonStr(maxSeqRespData),
// 		}))
// 		if err != nil {
// 			logger.Errorf("publishUserGroupMaxSeq userid %v err %v ", userid, err)
// 		}
// 	} else {
// 		logger.Errorf("publishUserGroupMaxSeq userid %v err %v resp %v", userid, err, util.JsonStr(resp))
// 	}
// 	return
// }

func (c *ConsumerHandlerV2) handlePullMessageBySeqs(ctx context.Context, msgReq *basemsgtransfer.PullMessageBySeqsReq) (err error) {
	pullMsgs := make(map[string]*basemsgtransfer.PullMsgs)

	// 同步离线消息
	nowTs := time.Now().Unix()
	latest7Day := int64(7 * 86400)
	for _, req := range msgReq.SeqRanges {
		sessionInfos := strings.Split(req.SessionId, "_")
		if len(sessionInfos) < 2 {
			continue
		}
		if msgDataList, isEnd, err := c.storage.GetGroupMsgBySeqsRange(ctx, cast.ToInt64(sessionInfos[1]), req.Begin, req.End, req.Num); err == nil {
			pullMsg := &basemsgtransfer.PullMsgs{Msgs: msgDataList}
			if !isEnd {
				for _, msg := range msgDataList {
					if nowTs-msg.CreateTime >= latest7Day {
						isEnd = true
						break
					}
				}
			}
			pullMsg.IsEnd = isEnd
			pullMsgs[req.SessionId] = pullMsg
		} else {
			logger.Errorf("handlePullMessageBySeqs GetGroupMsgBySeqsRange userid %v err %v resp %v", msgReq.UserID, err)
		}
	}

	err = msgchan.PublishRawMsg(msgReq.UserID, "", util.JsonStr(basemsgtransfer.RespMsgData{
		Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypePullMsgBySeqListRsp),
		Data: util.JsonStr(&basemsgtransfer.PullMessageBySeqsRsp{Msgs: pullMsgs}),
	}), basemsgtransfer.ChatMsgType_ChatMsgTypePullMsgBySeqListRsp.String())

	return err
}

type ConsumerHandlerV2 struct {
	msgConsumerGroup *kafka.MConsumerGroup
	storage          *storage.CommonDatabase
	sendMsgChArrays  [ChannelNum]chan *basemsgtransfer.InterMsgData
}

func (c ConsumerHandlerV2) distributeSendHandle(ch int64) {
	for msg := range c.sendMsgChArrays[ch] {
		err := c.handleSendMsg(context.Background(), msg)
		if err != nil {
			logger.Errorf("err %v", err) // todo   监控消息处理失败
		}
	}
}

func (c *ConsumerHandlerV2) handleMessag(msgData *basemsgtransfer.InterMsgData) error {
	// 队列积压， 非系统消息 超时不下发
	if !msgData.GetType2Data().GetSysMsg() &&
		msgData.Timestamp > 0 && (time.Now().Unix()-msgData.Timestamp) > UpstreamMsgTimeOutSecond {
		err := fmt.Errorf("msg timeout msguserid %v, msgType %v", msgData.UserId, msgData.Type)
		return err
	}

	ctx := context.Background()
	var err error
	switch msgData.Type {
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePing):
		err = c.handlePingMsg(ctx, msgData.Base, msgData.Type1Data)
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq):
		c.distributeSendMsg(ctx, msgData)
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotifyAck):
		err = c.handleNotifyAckMsg(ctx, msgData.UserId, msgData.Type5Data)
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePullMsgBySeqList):
		err = c.handlePullMessageBySeqs(ctx, msgData.Type8Data)

	default:
		err = errors.New("err msg type")
	}
	if err != nil {
		logger.Errorf("HandleMessage,type:%v message.Body %+v err %v", msgData.Type, msgData, err)
	}

	return nil

}

func (*ConsumerHandlerV2) Setup(sarama.ConsumerGroupSession) error {
	logger.Debugf("ConsumerHandlerV2 Setup")
	return nil
}

func (*ConsumerHandlerV2) Cleanup(sarama.ConsumerGroupSession) error {
	logger.Debugf("ConsumerHandlerV2 Cleanup")
	return nil
}

func (c *ConsumerHandlerV2) ConsumeClaim(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		if sess == nil {
			logger.Warnf("sess == nil, waiting", nil)
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	for {
		select {
		case msg, ok := <-claim.Messages():
			if !ok {
				logger.Error("chan is closing")
				return nil
			} else {
				m := &basemsgtransfer.InterMsgData{}
				err := proto.Unmarshal(msg.Value, m)
				if err != nil {
					logger.Errorf("err %v", err)
				}

				logger.Infof("topic %v, kafka msg %+v", consts.XIM_MSG_TOPIC(), m)
				err = c.handleMessag(m)
				if err != nil {
					logger.Errorf("err %v", err)
				}

				sess.MarkMessage(msg, "") // 入库完成之后才ack  不需要在mq层面保证至少一次
			}
		case <-sess.Context().Done():
			return nil
		}
	}

}

func publishUserGroupMaxSeq(ctx context.Context, userid int64, storage *storage.CommonDatabase) (err error) {
	if resp, err := container.basemsgTransClient.UserGroupIdList(ctx, &basemsgtransfer.UserGroupIdListReq{
		Userid: userid,
	}); err == nil && errcode.IsOk(resp) {
		var maxSeqRespData *basemsgtransfer.MaxSeqRespData
		sessionType := int32(basemsgtransfer.SessionType_SessionTypeGroup)
		for _, groupId := range resp.GrouidList {
			maxSeq, _ := storage.GetGroupMaxSeq(ctx, groupId)
			if maxSeqRespData == nil {
				maxSeqRespData = &basemsgtransfer.MaxSeqRespData{MaxSeqs: make(map[string]int64)}
			}
			maxSeqRespData.MaxSeqs[utils.SessionIdGen(sessionType, 0, 0, groupId)] = maxSeq
		}
		if maxSeqRespData != nil && len(maxSeqRespData.MaxSeqs) > 0 {
			err = msgchan.PublishRawMsg(userid, "", util.JsonStr(basemsgtransfer.RespMsgData{
				Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNewestSeq),
				Data: util.JsonStr(maxSeqRespData),
			}), basemsgtransfer.ChatMsgType_ChatMsgTypeNewestSeq.String())
			if err != nil {
				logger.Errorf("publishUserGroupMaxSeq userid %v err %v ", userid, err)
			}
		}
	} else {
		logger.Errorf("publishUserGroupMaxSeq userid %v err %v resp %v", userid, err, util.JsonStr(resp))
	}
	return
}

func (c *ConsumerHandlerV2) handleSendMoment(ctx context.Context, msgData *basemsgtransfer.InterMsgData) (err error) {
	m := msgData.Type2Data
	// 2. server hook
	if code := WebhookMomentSendMsg(ctx, msgData); code.Code != errcode.ErrorOK.Code {
		err := sendMsgAck(m, code)
		return err
	}
	// 4. msgchan
	err = sendToUserByChan(m)
	if err != nil {
		return err
	}
	return
}
