package server

import (
	"context"
	"fmt"
	"time"
	"xim/baselib/config"
	"xim/baselib/kafka"
	"xim/baselib/logger"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/consts"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/proto"
)

type StateConsumerHandler struct {
	stateConsumerGroup *kafka.MConsumerGroup
}

func NewStateConsumerHandler(conf *config.Config, groupID string, topics []string, autoCommitEnable bool) (v *StateConsumerHandler, err error) {
	if conf == nil {
		logger.Errorf("conf == nil ")
		return
	}

	var handle StateConsumerHandler
	handle.stateConsumerGroup, err = kafka.NewMConsumerGroup(conf, groupID, topics, autoCommitEnable)
	if err != nil {
		return nil, err
	}
	return &handle, nil
}
func InitStateKafkaConsumer(conf *config.Config) {
	if conf == nil {
		logger.Errorf("conf == nil ")
		return
	}
	consumer, err := NewStateConsumerHandler(conf, consts.XIM_STATE_GROUP(), []string{consts.XIM_STATE_TOPIC()}, true)
	if err != nil {
		panic(fmt.Errorf("NewStateConsumerHandler  error %v", err))
	}
	fmt.Printf("err %v", err)
	go consumer.stateConsumerGroup.RegisterHandleAndConsumer(context.Background(), consumer)
}

// 状态同步的消息 ping  connect  close
func (c StateConsumerHandler) handleMessage(msgData *basemsgtransfer.InterMsgData) error {

	ctx := context.Background()

	if msgData.Timestamp > 0 && (time.Now().Unix()-msgData.Timestamp) > SyncMsgTimeOutSecond {
		logger.Errorf("msg timeout msguserid %v, msgType %v", msgData.UserId, msgData.Type)
		return nil
	}

	var err error
	switch msgData.Type {
	case int32(basemsgtransfer.MsgEventType_connected):
		err = WebhookImConnect(ctx, msgData.UserId, msgData.Base)
	case int32(basemsgtransfer.MsgEventType_closed):
		err = WebhookImDisconnect(ctx, msgData.UserId, msgData.Base)
	case int32(basemsgtransfer.MsgEventType_ping):
		//err = WebhookImPing(ctx, msgData.UserId, msgData.Base)
		return nil // ws层的ping暂时没用
	}

	if err != nil {
		logger.Errorf("msgData %+v err %v", msgData, err)
	}
	return nil
}

func (*StateConsumerHandler) Setup(sarama.ConsumerGroupSession) error {
	logger.Debug("StateConsumerHandler Setup")
	return nil
}

func (*StateConsumerHandler) Cleanup(sarama.ConsumerGroupSession) error {
	logger.Debug("StateConsumerHandler Cleanup")
	return nil
}

func (c *StateConsumerHandler) ConsumeClaim(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		if sess == nil {
			logger.Warnf("sess == nil, waiting", nil)
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	for {
		select {
		case msg, ok := <-claim.Messages():
			if !ok {
				logger.Error("chan is closing")
				return nil
			} else {
				m := &basemsgtransfer.InterMsgData{}
				err := proto.Unmarshal(msg.Value, m)
				if err != nil {
					logger.Errorf("err %v", err)
				}

				err = c.handleMessage(m)
				if err != nil {
					logger.Errorf("err %v", err)
				}

				sess.MarkMessage(msg, "") // 入库完成之后才ack  不需要在mq层面保证至少一次
			}
		case <-sess.Context().Done():
			return nil
		}
	}

}
