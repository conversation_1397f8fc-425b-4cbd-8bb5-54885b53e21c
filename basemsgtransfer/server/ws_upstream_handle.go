package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
	"xim/baselib/config"
	"xim/baselib/logger"
	"xim/baselib/msgchan"
	"xim/baselib/util"
	storage "xim/basemsgtransfer/storage/controller"
	"xim/basemsgtransfer/utils"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"

	"github.com/nsqio/go-nsq"
	"github.com/spf13/cast"
)

const (
	ChannelNum               = 200
	UpstreamMsgTimeOutSecond = 60 * 3 // 超过3分钟不处理
)

type sendMsgNsqRaw struct {
	nsqRaw   *nsq.Message
	sendData *basemsgtransfer.InterMsgData
}

type sendMsgKafkaRaw struct {
	sendData *basemsgtransfer.InterMsgData
}

type WsUpstreamMessageHandler struct {
	storage         *storage.CommonDatabase
	sendMsgChArrays [ChannelNum]chan *sendMsgNsqRaw
}

func NewWsUpstreamMessageHandler(conf config.NsqConfig) (v WsUpstreamMessageHandler, err error) {
	v.storage, err = storage.NewCommonDatabase(&conf)

	for i := int64(0); i < ChannelNum; i++ {
		v.sendMsgChArrays[i] = make(chan *sendMsgNsqRaw, 50)
		go v.distributeSendHandle(i)
	}

	return
}

func (c WsUpstreamMessageHandler) distributeSendHandle(ch int64) {
	for {
		select {
		case msg := <-c.sendMsgChArrays[ch]:
			err := c.handleSendMsg(context.Background(), msg.sendData)
			if err != nil {
				logger.Errorf("err %v", err) // todo   监控消息处理失败
			}

			msg.nsqRaw.Finish() // 手动提交
		}
	}
}

func GetSendMsgDistibutionCh(msg *basemsgtransfer.MsgData) (ch int64) {
	if msg == nil {
		return 0
	}

	if (msg.SessionType != int32(basemsgtransfer.SessionType_SessionTypeGroup) &&
		msg.SessionType != int32(basemsgtransfer.SessionType_SessionTypeVoiceRoom)) &&
		msg.Receiver > 0 {
		return msg.Receiver % ChannelNum
	}
	return msg.GroupId % ChannelNum
}

func GetPingMsgDistibutionCh(msg *basemsgtransfer.PingData) (ch int64) {
	if msg == nil {
		return 0
	}

	id, _ := strconv.ParseInt(msg.Userid, 10, 64)
	return id % ChannelNum

}

func GetNotifyAckMsgDistibutionCh(msgData []*basemsgtransfer.NotifySyncData) (ch int64) {
	if msgData == nil {
		return 0
	}
	if len(msgData) > 0 {
		return msgData[0].Seq % ChannelNum
	}
	return 0
}

func GetPullMsgByListDistibutionCh(msgData *basemsgtransfer.PullMessageBySeqsReq) (ch int64) {
	if msgData == nil {
		return 0
	}
	return msgData.UserID % ChannelNum
}

// 处理业务上行的消息
func (c WsUpstreamMessageHandler) HandleMessage(message *nsq.Message) error {

	logger.Infof("HandleMessage, msg raw: %v", string(message.Body))

	ctx := context.Background()
	msgData := basemsgtransfer.InterMsgData{}
	err := json.Unmarshal(message.Body, &msgData)
	if err != nil {
		logger.Errorf("error unmarshal msgData.Msg %v", err)
		return nil
	}

	// 队列积压， 非系统消息 超时不下发
	if !msgData.GetType2Data().GetSysMsg() &&
		msgData.Timestamp > 0 && (time.Now().Unix()-msgData.Timestamp) > UpstreamMsgTimeOutSecond {
		logger.Errorf("msg timeout msguserid %v, msgType %v", msgData.UserId, msgData.Type)
		return nil
	}

	switch msgData.Type {
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypePing):
		err = c.handlePingMsg(ctx, msgData.Base, msgData.Type1Data)
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq):
		c.distributeSendMsg(ctx, &sendMsgNsqRaw{message, &msgData})
	case int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotifyAck):
		err = c.handleNotifyAckMsg(ctx, msgData.UserId, msgData.Type5Data)
	default:
		err = errors.New("err msg type")
	}
	if err != nil {
		logger.Errorf("HandleMessage, message.Body %v err %v", string(message.Body), err)
	}

	return nil
}

func (c *WsUpstreamMessageHandler) handlePingMsg(ctx context.Context, base *common.BaseParam, pingData *basemsgtransfer.PingData) error {
	//logger.Infof("handlePingMsg %v", pingData)
	if pingData == nil {
		return errors.New("pingdata nil")
	}

	userid := cast.ToInt64(pingData.Userid)
	waitAckMsgs, err := c.storage.GetWaitAckMsgs(ctx, userid, 200)
	if err != nil {
		return err
	}
	if len(waitAckMsgs) > 0 {
		logger.Infof("waitAckMsgs, %+v", waitAckMsgs)
		err = msgchan.PublishRawMsg(userid, "", util.JsonStr(basemsgtransfer.RespMsgData{
			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
			Data: util.JsonStr(waitAckMsgs),
		}), basemsgtransfer.ChatMsgType_ChatMsgTypeNotify.String())
	}

	go WebhookImPing(ctx, userid, base, pingData.IsFront)
	return err
}

func (c *WsUpstreamMessageHandler) distributeSendMsg(ctx context.Context, r *sendMsgNsqRaw) {
	r.nsqRaw.DisableAutoResponse()

	if r.sendData.Type2Data.Receiver == 0 {
		r.sendData.Type2Data.Receiver = r.sendData.Type2Data.To
	}

	ch := GetSendMsgDistibutionCh(r.sendData.Type2Data)
	c.sendMsgChArrays[ch] <- r
	return
}

func (c *WsUpstreamMessageHandler) handleSendMsg(ctx context.Context, msgData *basemsgtransfer.InterMsgData) error {
	senderId, m := msgData.UserId, msgData.Type2Data
	logger.Infof("handleSendMsg userid %v,msgdata %+v", senderId, util.JsonStr(m))
	if m == nil {
		return errors.New("handleSendMsg nil")
	}

	// 1. decode && check
	err := msgDataDecode(senderId, m)
	if err != nil {
		logger.Errorf("handleSendMsg,err %v", err)
		if err1 := msgchan.PublishRawMsg(senderId, "", util.JsonStr(basemsgtransfer.RespMsgData{
			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
			Data: util.JsonStr(basemsgtransfer.SendRespData{
				ErrCode: errcode.ErrorParam.Code,
				ErrMsg:  err.Error(),
			}),
		}), basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp.String()); err1 != nil {
			return err1
		}
		return err
	}

	if !m.SysMsg {
		hadSent, err := c.storage.GetCacheClient().VerifyMsgIdempotent(ctx, senderId, m.LocalId)
		if err != nil {
			return err
		}
		if hadSent {
			err = sendMsgAck(m, errcode.ErrorOK)
			return err
		}

	}

	// 2. server hook
	if code := WebhookImSendMsg(ctx, msgData); code.Code != errcode.ErrorOK.Code {
		err := sendMsgAck(m, code)
		return err
	}

	// 3. save  后续失败 告警介入 处理webhook逻辑相关回滚，保证一致性
	_, err = c.storage.Msg1v1Save(ctx, m.Receiver, []*basemsgtransfer.MsgData{m})
	if err != nil {
		return err
	}

	// 4. msgchan
	err = sendToUserByChan(m)
	if err != nil {
		return err
	}

	return nil

}

func sendToUserByChan(m *basemsgtransfer.MsgData) error {

	err1 := sendMsgAck(m, errcode.ErrorOK) // sender
	if err1 != nil {
		logger.Errorf("err %v", err1)
	}

	// recevier
	err2 := msgchan.PublishRawMsg(m.Receiver, "", util.JsonStr(basemsgtransfer.RespMsgData{
		Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
		Data: util.JsonStr([]*basemsgtransfer.MsgData{m}),
	}), basemsgtransfer.ChatMsgType_ChatMsgTypeNotify.String())
	if err2 != nil {
		logger.Errorf("err %v", err2)
	}

	if m.Options != nil {
		if flag, ok := m.Options["senderSync"]; ok && flag {
			err3 := msgchan.PublishRawMsg(m.From, "", util.JsonStr(basemsgtransfer.RespMsgData{
				Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
				Data: util.JsonStr([]*basemsgtransfer.MsgData{m}),
			}), basemsgtransfer.ChatMsgType_ChatMsgTypeNotify.String())
			if err3 != nil {
				logger.Errorf("err %v", err3)
			}
		}
	}

	if err1 != nil || err2 != nil {
		return fmt.Errorf("err1:%v,err2:%v", err1, err2)
	}

	return nil
}

func sendMsgAck(m *basemsgtransfer.MsgData, code errcode.ErrorCode) error {
	if m.SysMsg { // 系统消息  不需要回包
		return nil
	}
	orderTime := ""
	if m.SessionType == int32(basemsgtransfer.SessionType_SessionTypeChat) && len(m.Exts) > 0 {
		if ts, ok := m.Exts["order_ts"]; ok {
			orderTime = ts
		}
	}
	err := msgchan.PublishRawMsg(m.From, "", util.JsonStr(basemsgtransfer.RespMsgData{
		Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp),
		Data: util.JsonStr(basemsgtransfer.SendRespData{
			ErrCode:     code.Code,
			ErrMsg:      code.Msg,
			LocalId:     m.LocalId,
			MsgId:       m.MsgId,
			SessionId:   m.SessionId,
			SendTime:    time.Now().UnixMilli(),
			CreateTime:  time.Now().UnixMilli(),
			SessionType: m.SessionType,
			Seq:         m.Seq,
			OrderTime:   orderTime,
		}),
	}), basemsgtransfer.ChatMsgType_ChatMsgTypeSendRsp.String())
	return err
}

func (c *WsUpstreamMessageHandler) handleNotifyAckMsg(ctx context.Context, recevier int64, notifyAckData []*basemsgtransfer.NotifySyncData) error {
	//logger.Infof("handleNotifyAckMsg userid %v,msgdata %+v", recevier, notifyAckData)
	if len(notifyAckData) == 0 {
		return errors.New("notifyAckData nil")
	}

	var seqs []int64
	for _, v := range notifyAckData {
		seqs = append(seqs, v.Seq)
	}

	return c.storage.OfflineMsgAcked(ctx, recevier, seqs)

}

func msgDataDecode(senderId int64, m *basemsgtransfer.MsgData) error {

	m.SessionId = utils.SessionIdGen(m.SessionType, m.From, m.To, m.GroupId)
	if _, ok := basemsgtransfer.SessionType_name[m.SessionType]; !ok || m.SessionType == 0 || m.SessionId == "" {
		return errors.New("param error")
	}

	if m.MsgId == "" {
		m.MsgId = utils.SnowNext()
	}

	if m.SendTime == 0 {
		m.SendTime = time.Now().UnixMilli()
	}

	if m.CreateTime == 0 {
		m.CreateTime = time.Now().UnixMilli()
	}

	return nil
}
