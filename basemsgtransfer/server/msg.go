package server

import (
	"context"
	"errors"
	"strings"
	"time"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/msgchan"
	"xim/baselib/util"
	"xim/basemsgtransfer/utils"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"
)

func (c *BaseMsgTransferServer) SendMsg(ctx context.Context, req *basemsgtransfer.SendMsgReq) (*basemsgtransfer.SendMsgResp, error) {
	resp := &basemsgtransfer.SendMsgResp{
		Base: &common.SvcBaseResp{},
	}
	for _, v := range req.Msgs {
		if isFilter, _ := c.checkIsBlackUser(ctx, v); isFilter {
			continue
		}
		v.SysMsg = true
		if v.MsgFrom == uint32(basemsgtransfer.MsgFromEnum_MsgFromRealTime) {
			return &basemsgtransfer.SendMsgResp{
				Base: errcode.ErrorParam.WithErrorMsg("not support real time msg").ToSvcBaseResp(),
			}, nil
		}

		if v.SessionType == int32(basemsgtransfer.SessionType_SessionTypeUnknown) {
			err := errors.New("message type is empty")
			return &basemsgtransfer.SendMsgResp{
				Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
			}, err
		}

		if v.SessionId == "" {
			v.SessionId = utils.SessionIdGen(v.SessionType, v.From, v.To, v.GroupId)
		}

		if v.Receiver == 0 {
			v.Receiver = v.To
		}

		v.MsgId = utils.SnowNext()
		resp.Msgids = append(resp.Msgids, v.MsgId)

		if v.CreateTime < 1000000000000 {
			v.CreateTime = time.Now().UnixMilli()
		}

		if v.SendTime < 1000000000000 {
			v.SendTime = time.Now().UnixMilli()
		}

		interMsg := &basemsgtransfer.InterMsgData{
			Type:      int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq),
			UserId:    v.From,
			Type2Data: v,
		}

		// dKey := cast.ToString(GetSendMsgDistibutionCh(v))
		// if _, _, errTEST := kafka.Client(consts.XIM_MSG_TOPIC()).SendMessage(ctx, dKey, interMsg); errTEST != nil {
		// 	logger.Errorf("err %v", errTEST)
		// 	return &basemsgtransfer.SendMsgResp{
		// 		Base: errcode.ErrorInternal.WithErrorMsg(errTEST.Error()).ToSvcBaseResp(),
		// 	}, errTEST
		// }

		if err := c.handleMsg(ctx, v, interMsg); err != nil {
			logger.Errorf("err %v", err)
			return &basemsgtransfer.SendMsgResp{
				Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
			}, err
		}

		// err := c.storage.MsgToContentMQ(ctx, &basemsgtransfer.InterMsgData{
		// 	Type:      int32(basemsgtransfer.ChatMsgType_ChatMsgTypeSendReq),
		// 	UserId:    v.From,
		// 	Type2Data: v,
		// })
		// if err != nil {
		// 	return &basemsgtransfer.SendMsgResp{
		// 		Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp(),
		// 	}, err
		// }
	}

	return resp, nil

}

func (c *BaseMsgTransferServer) checkIsBlackUser(ctx context.Context, msgData *basemsgtransfer.MsgData) (isBlackUser bool, err error) {
	// if msgData.From > 0 && msgData.From <= 10 {
	// 	userid := msgData.To
	// 	userOnlineResp, err := container.memberClient.UserOnlineInfos(ctx, &svcmember.UserOnlineInfosReq{
	// 		Userids: []int64{userid},
	// 	})
	// 	if err != nil {
	// 		logger.Errorf("checkIsBlackUser call UserOnlineInfos error=%+v userID=%d", err, userid)
	// 		return false, err
	// 	}
	// 	logger.Debugf("checkIsBlackUser  userOnlineResp=%+v userID=%d", userOnlineResp, userid)
	// 	if userOnlineResp.Base.Code != errcode.ErrorOK.Code || len(userOnlineResp.Infos) == 0 {
	// 		logger.Warnf("checkIsBlackUser call UserOnlineInfos retCode=%d onlineInfos=%+v userID=%d", userOnlineResp.Base.Code, len(userOnlineResp.Infos), userid)
	// 		return false, nil
	// 	}
	// 	if onlineInfo, ok := userOnlineResp.Infos[userid]; ok {
	// 		isBlackReq := &svcconfig.IsBlackUserReq{
	// 			PackageName: onlineInfo.App,
	// 			ChannelName: onlineInfo.Ch,
	// 			Version:     onlineInfo.Av,
	// 			Did:         onlineInfo.Did,
	// 			Dt:          onlineInfo.Dt,
	// 			UserId:      userid,
	// 			Ip:          onlineInfo.Ip,
	// 		}
	// 		isBlackResp, tmpErr := container.configClinet.IsBlackListUser(context.Background(), isBlackReq)
	// 		logger.Debugf("checkIsBlackUser IsBlackListUser userID=%d tmpErr=%+v isBlackReq=%+v isBlackResp=%+v", userid, tmpErr, isBlackReq, isBlackResp)
	// 		if tmpErr != nil {
	// 			logger.Errorf("checkIsBlackUser IsBlackListUser userID=%d tmpErr=%+v isBlackReq=%+v", userid, tmpErr, isBlackReq)
	// 			return false, tmpErr
	// 		}
	// 		if isBlackResp.Data.IsBlack {
	// 			logger.Warnf("checkIsBlackUser IsBlackListUser userID=%d isBlack isBlackReq=%+v", userid, isBlackReq)
	// 			return true, nil
	// 		}
	// 	}
	// 	return false, nil
	// }
	return false, nil

}

func (c *BaseMsgTransferServer) SendRealTimeMsg(ctx context.Context, req *basemsgtransfer.SendMsgReq) (*basemsgtransfer.SendMsgResp, error) {
	for _, v := range req.Msgs {
		// if v.LocalId == "" {
		// 	v.LocalId = "0"
		// }

		if v.SessionId == "" {
			v.SessionId = utils.SessionIdGen(v.SessionType, v.From, v.To, v.GroupId)
		}

		if v.Receiver == 0 {
			v.Receiver = v.To
		}

		v.MsgId = utils.SnowNext()
		v.SendTime = time.Now().UnixMilli()
		v.CreateTime = time.Now().UnixMilli()
		v.MsgFrom = uint32(basemsgtransfer.MsgFromEnum_MsgFromRealTime)
		err := msgchan.PublishRawMsg(v.Receiver, "", util.JsonStr(basemsgtransfer.RespMsgData{
			Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
			Data: util.JsonStr([]*basemsgtransfer.MsgData{v}),
		}), basemsgtransfer.ChatMsgType_ChatMsgTypeNotify.String())
		if err != nil {
			logger.Errorf("SendRealTimeMsg,err %v", err)
			return &basemsgtransfer.SendMsgResp{
				Base: errcode.ErrorNOK.WithErrorMsg(err.Error()).ToSvcBaseResp(),
			}, err
		}
	}
	return &basemsgtransfer.SendMsgResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
	}, nil
}

func (c *BaseMsgTransferServer) MulticastRealTimeMsg(ctx context.Context, req *basemsgtransfer.MulticastRealTimeMsgReq) (*basemsgtransfer.SendMsgResp, error) {
	var msgs []*basemsgtransfer.MsgData

	for _, v := range req.Msgs {
		v.MsgId = utils.SnowNext()
		v.SendTime = time.Now().UnixMilli()
		v.CreateTime = time.Now().UnixMilli()
		v.MsgFrom = uint32(basemsgtransfer.MsgFromEnum_MsgFromRealTime)
		msgs = append(msgs, v)

		label := map[string]string{
			"result":       "0",
			"error":        "",
			"msg_type":     basemsgtransfer.ChatMsgType_ChatMsgTypeNotify.String(),
			"session_type": basemsgtransfer.SessionType_name[v.SessionType],
		}
		metric.CounterWithLabels("msg_downstream", label).Inc()
	}

	var groupMsgStores []*basemsgtransfer.MsgData
	for _, v := range req.Msgs { // 先这么处理后期让上游不调用这个接口
		switch v.SessionType {
		case int32(basemsgtransfer.SessionType_SessionTypeGroup):
			if v.GroupId == 0 {
				continue
			}
			switch v.ContentType {
			case 120001:
				v.SessionId = utils.SessionIdGen(v.SessionType, v.From, v.To, v.GroupId)
				v.MsgFrom = 0
				groupMsgStores = append(groupMsgStores, v)
			}
		}
	}

	if len(groupMsgStores) != 0 {
		insertSeqs, _ := c.storage.MsgGroupSave(ctx, groupMsgStores[0].GroupId, groupMsgStores)
		for index, seq := range insertSeqs {
			groupMsgStores[index].Seq = seq
		}
	}

	err := msgchan.Broadcast(req.Topic, util.JsonStr(basemsgtransfer.RespMsgData{
		Type: int32(basemsgtransfer.ChatMsgType_ChatMsgTypeNotify),
		Data: util.JsonStr(msgs),
	}))
	if err != nil {
		return &basemsgtransfer.SendMsgResp{Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp()}, err
	}

	return &basemsgtransfer.SendMsgResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
	}, nil
}

func (c *BaseMsgTransferServer) SubscribeTopic(ctx context.Context, req *basemsgtransfer.SubscribeTopicReq) (*basemsgtransfer.SendMsgResp, error) {
	topics := strings.Split(req.GetTopic(), ",")
	err := msgchan.Subscribe(req.Userid, topics)
	if err != nil {
		return &basemsgtransfer.SendMsgResp{Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp()}, err
	}

	return &basemsgtransfer.SendMsgResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
	}, nil
}

func (c *BaseMsgTransferServer) UnSubscribeTopic(ctx context.Context, req *basemsgtransfer.UnSubscribeTopicReq) (*basemsgtransfer.SendMsgResp, error) {
	topics := strings.Split(req.GetTopic(), ",")
	err := msgchan.UnSubscribe(req.Userid, topics)
	if err != nil {
		return &basemsgtransfer.SendMsgResp{Base: errcode.ErrorInternal.WithErrorMsg(err.Error()).ToSvcBaseResp()}, err
	}

	return &basemsgtransfer.SendMsgResp{
		Base: errcode.ErrorOK.ToSvcBaseResp(),
	}, nil
}
