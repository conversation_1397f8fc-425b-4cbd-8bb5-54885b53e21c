package server

import (
	"context"
	"time"
	"xim/baselib/grpccli"
	"xim/baselib/logger"
	"xim/baselib/util"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/api/svcaccount"
	"xim/proto/api/svcchat"
	"xim/proto/api/svcfamily"
	"xim/proto/api/svcvc"
	"xim/proto/api/transfer"
	"xim/proto/consts/errcode"

	"google.golang.org/grpc"
)

// 触发回调到业务层
var (
	container *serviceContainer
)

type serviceContainer struct {
	accountClient      svcaccount.SClient
	chatClient         svcchat.SClient
	basemsgTransClient basemsgtransfer.SClient
	familyClient       svcfamily.SClient
	transferClient     transfer.SClient
	vcClient           svcvc.SClient
}

func InitWebhookServices() (err error) {
	if container == nil {
		var localConn *grpc.ClientConn
		localConn, err = grpccli.NewLocalConn()
		if err != nil {
			logger.Panicf("NewLocalConn error %v", err)
			return
		}
		container = &serviceContainer{
			accountClient:      svcaccount.NewSClient(localConn),
			chatClient:         svcchat.NewSClient(localConn),
			basemsgTransClient: basemsgtransfer.NewSClient(localConn),
			familyClient:       svcfamily.NewSClient(localConn),
			transferClient:     transfer.NewSClient(localConn),
			vcClient:           svcvc.NewSClient(localConn),
		}
	}
	return
}

func WebhookImConnect(ctx context.Context, userid int64, base *common.BaseParam) error {
	logger.Infof("WebhookImConnect userid %v,base %v", userid, util.JsonStr(base))
	rsp, err := container.accountClient.OnlineLimiter(ctx, &svcaccount.AccountOnlineReq{
		Userid:        userid,
		State:         true,
		Data:          base,
		Heartbeat:     true,
		OnConnectType: svcaccount.OnConnectType_CONNECTED,
	})
	err = errcode.GetSvcRspErr(err, rsp)
	if err != nil {
		logger.Errorf("WebhookImConnect,err %v", err)
	}
	return err
}

func WebhookImPing(ctx context.Context, userid int64, base *common.BaseParam, isFront bool) error {
	logger.Infof("WebhookImPing userid %v,base %v", userid, util.JsonStr(base))
	rsp, err := container.accountClient.OnlineLimiter(ctx, &svcaccount.AccountOnlineReq{
		Userid:        userid,
		State:         true,
		Data:          base,
		Front:         isFront,
		Heartbeat:     true,
		OnConnectType: svcaccount.OnConnectType_PING,
	})
	err = errcode.GetSvcRspErr(err, rsp)
	if err != nil {
		//logger.Errorf("WebhookImPing,err %v", err)
	}
	return err
}

func WebhookImDisconnect(ctx context.Context, userid int64, base *common.BaseParam) error {
	logger.Infof("WebhookImDisconnect userid %v,base %v,isReview %v", userid, util.JsonStr(base))
	rsp, err := container.accountClient.OnlineLimiter(ctx, &svcaccount.AccountOnlineReq{
		Userid:        userid,
		State:         false,
		Data:          base,
		OnConnectType: svcaccount.OnConnectType_DISCONNECTED,
	})
	err = errcode.GetSvcRspErr(err, rsp)
	if err != nil {
		logger.Errorf("WebhookImDisconnect,err %v", err)
		return err
	}

	rsp2, err := container.chatClient.SetUserOffline(ctx, &common.SvcUseridReq{
		Userid: userid,
	})
	err = errcode.GetSvcRspErr(err, rsp2)
	if err != nil {
		logger.Errorf("WebhookImDisconnect,err %v", err)
	}
	return err
}

func WebhookImSendMsg(ctx context.Context, msg *basemsgtransfer.InterMsgData) errcode.ErrorCode {
	logger.Infof("WebhookImSendMsg msg %v", util.JsonStr(msg))

	go func() { // todo  remove to svcchat
		container.basemsgTransClient.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
			Upsert: &basemsgtransfer.SessionInfo{
				SessionId:   msg.Type2Data.SessionId,
				SessionType: 1,
				OwnUserid:   msg.Type2Data.From,
				PeerId:      msg.Type2Data.To,
				Ct:          time.Now().Unix(),
			},
		})
		container.basemsgTransClient.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
			Upsert: &basemsgtransfer.SessionInfo{
				SessionId:   msg.Type2Data.SessionId,
				SessionType: 1,
				OwnUserid:   msg.Type2Data.To,
				PeerId:      msg.Type2Data.From,
				Ct:          time.Now().Unix(),
			},
		})
	}()

	if msg.Type2Data.SysMsg {
		return errcode.ErrorOK // 系统消息不处理
	}

	var (
		rsp *svcchat.MsgHandleResp
		err error
	)
	rsp, err = container.chatClient.MsgHandle(ctx, &svcchat.MsgHandleReq{
		Msg:  msg.Type2Data,
		Base: msg.GetBase()})
	msg.Type2Data.Exts = util.MergeMapString(msg.Type2Data.Exts, rsp.GetExts())
	util.MergeMapString(msg.GetType2Data().GetExts(), rsp.GetExts())
	return errcode.CheckSvcResp(rsp, err)
}

// func WebhooImSendMsgDone(ctx context.Context, msg *basemsgtransfer.InterMsgData) {
// 	logger.Infof("WebhooImSendMsgDone msg %v", util.JsonStr(msg))

// 	rsp, err := container.chatClient.MsgHandle(ctx, &svcchat.MsgHandleReq{
// 		Msg:  msg.Type2Data,
// 		Base: msg.GetBase()})
// 	return errcode.CheckSvcResp(rsp, err)

// }

func WebhookFamilySendMsg(ctx context.Context, msg *basemsgtransfer.InterMsgData) (topic string, code errcode.ErrorCode) {
	logger.Infof("WebhookFamilySendMsg msg %v", util.JsonStr(msg))

	var (
		rsp *svcfamily.MsgHandleResp
		err error
	)
	rsp, err = container.familyClient.MsgHandle(ctx, &svcfamily.MsgHandleReq{
		Msg:  msg.Type2Data,
		Base: msg.GetBase()})

	msg.Type2Data.Exts = util.MergeMapString(msg.Type2Data.Exts, rsp.GetExts())
	util.MergeMapString(msg.GetType2Data().GetExts(), rsp.GetExts())
	if rsp != nil {
		topic = rsp.Topic
	}

	// topic 不为空，一定是群组成员
	if len(topic) != 0 && !msg.Type2Data.SysMsg {
		go func() {
			container.basemsgTransClient.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
				Upsert: &basemsgtransfer.SessionInfo{
					SessionId:   msg.Type2Data.SessionId,
					SessionType: msg.Type2Data.SessionType,
					OwnUserid:   msg.Type2Data.From,
					Ct:          time.Now().Unix(),
				},
			})
		}()
	}

	return topic, errcode.CheckSvcResp(rsp, err)
}

func WebhookMomentSendMsg(ctx context.Context, msg *basemsgtransfer.InterMsgData) errcode.ErrorCode {
	logger.Infof("WebhookMomentSendMsg msg %v", util.JsonStr(msg))
	// go func() { // todo  remove to svcchat
	// 	container.basemsgTransClient.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
	// 		Upsert: &basemsgtransfer.SessionInfo{
	// 			SessionId:   msg.Type2Data.SessionId,
	// 			SessionType: 1,
	// 			OwnUserid:   msg.Type2Data.From,
	// 			PeerId:      msg.Type2Data.To,
	// 			Ct:          time.Now().Unix(),
	// 		},
	// 	})
	// 	container.basemsgTransClient.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
	// 		Upsert: &basemsgtransfer.SessionInfo{
	// 			SessionId:   msg.Type2Data.SessionId,
	// 			SessionType: 1,
	// 			OwnUserid:   msg.Type2Data.To,
	// 			PeerId:      msg.Type2Data.From,
	// 			Ct:          time.Now().Unix(),
	// 		},
	// 	})
	// }()
	rsp, err := container.vcClient.HandleAudioMsg(ctx, &svcvc.AudioMsgReq{
		Msg:  msg.Type2Data,
		Base: msg.GetBase()})
	msg.Type2Data.Exts = util.MergeMapString(msg.Type2Data.Exts, rsp.GetExts())
	util.MergeMapString(msg.GetType2Data().GetExts(), rsp.GetExts())
	return errcode.CheckSvcResp(rsp, err)
}
