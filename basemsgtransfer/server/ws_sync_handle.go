package server

import (
	"context"
	"encoding/json"
	"time"
	"xim/baselib/config"
	"xim/baselib/kafka"
	"xim/baselib/logger"
	"xim/proto/api/basemsgtransfer"

	"github.com/IBM/sarama"
	"github.com/nsqio/go-nsq"
	"google.golang.org/protobuf/proto"
)

const SyncMsgTimeOutSecond = 60 * 3 // 超过3分钟不处理

type WsSyncMessageHandler struct {
}

func NewWsSyncMessageHandler(conf config.NsqConfig) (v WsSyncMessageHandler, err error) {
	return
}

// todo kafka消费
// 状态同步的消息 ping  connect  close
func (c WsSyncMessageHandler) HandleMessage(message *nsq.Message) error {
	logger.Infof("WsSyncMessageHandler, msg raw: %v", string(message.Body))

	ctx := context.Background()
	msgData := &basemsgtransfer.InterMsgData{}
	err := json.Unmarshal(message.Body, &msgData)
	if err != nil {
		logger.Errorf("error unmarshal msgData.Msg %v", err)
		return nil
	}

	if msgData.Timestamp > 0 && (time.Now().Unix()-msgData.Timestamp) > SyncMsgTimeOutSecond {
		logger.Errorf("msg timeout msguserid %v, msgType %v", msgData.UserId, msgData.Type)
		return nil
	}

	switch msgData.Type {
	case int32(basemsgtransfer.MsgEventType_connected):
		err = WebhookImConnect(ctx, msgData.UserId, msgData.Base)
	case int32(basemsgtransfer.MsgEventType_closed):
		err = WebhookImDisconnect(ctx, msgData.UserId, msgData.Base)
	case int32(basemsgtransfer.MsgEventType_ping):
		//err = WebhookImPing(ctx, msgData.UserId, msgData.Base)
		return nil // ws层的ping暂时没用
	}

	if err != nil {
		logger.Errorf("msgData %+v err %v", msgData, err)
	}
	return nil
}

// ---- kafka
type ConsumerHandler struct {
	msgConsumerGroup *kafka.MConsumerGroup
}

func NewConsumerHandler(conf *config.Config, groupID string, topics []string, autoCommitEnable bool) (*ConsumerHandler, error) {
	var consumerHandler ConsumerHandler
	var err error
	consumerHandler.msgConsumerGroup, err = kafka.NewMConsumerGroup(conf, groupID, topics, autoCommitEnable)
	if err != nil {
		return nil, err
	}
	return &consumerHandler, nil
}

func (*ConsumerHandler) Setup(sarama.ConsumerGroupSession) error { return nil }

func (*ConsumerHandler) Cleanup(sarama.ConsumerGroupSession) error { return nil }

func (c *ConsumerHandler) ConsumeClaim(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		if sess == nil {
			logger.Warnf("sess == nil, waiting", nil)
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	for {
		select {
		case msg, ok := <-claim.Messages():
			if !ok {
				logger.Error("!ok")
			} else {
				m := &basemsgtransfer.InterMsgData{}
				err := proto.Unmarshal(msg.Value, m)
				logger.Infof("ConsumeClaim msg %+v,err %v", m, err)
				sess.MarkMessage(msg, "")
			}
		}
	}

	return nil
}
