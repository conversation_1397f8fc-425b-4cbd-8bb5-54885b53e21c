package server

import (
	"context"
	"fmt"
	"testing"
	"time"
	"xim/baselib/cache"
	"xim/baselib/config"
	"xim/baselib/database"
	"xim/baselib/database/mongo"
	"xim/baselib/kafka"
	"xim/baselib/logger"
	"xim/baselib/server/env"
	"xim/proto/api/basemsgtransfer"
	mproto "xim/proto/api/basemsgtransfer"
	"xim/proto/model"

	"xim/basemsgtransfer/storage/mgo"

	"github.com/jinzhu/copier"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

var conf *config.ServerConfig

func init() {
	env.SetEnv("local")
	testing.Init()
	config.Conf = "../deploy/dev/config.yml"
	config.Init()
	conf = config.GetServerConfig()
	logger.InitLogger(conf.Logger)

	cache.InitRedis(config.GetServerConfig().Redis)
	mongo.InitMongoDB(conf.MongodbIm)
	database.InitMysql(conf.Mysql)
	kafka.KafkaInit(conf.Kafka, []string{"xim_message"})

}

func TestKafkaIn(t *testing.T) {

	msg := mproto.MsgData{
		To: 2,
	}

	fmt.Print(kafka.Client("xim_message").SendMessage(context.Background(), "0", &msg))
}

func TestKafkaOut(t *testing.T) {

	consumer, err := NewConsumerHandler(conf.Kafka.Build(), "111", []string{"xim_message"}, true)
	fmt.Printf("err %v", err)
	consumer.msgConsumerGroup.RegisterHandleAndConsumer(context.Background(), consumer)

	for {
		time.Sleep(time.Second)
	}

}

func TestCreateSession(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	// s.CreateSession(context.Background(), &basemsgtransfer.CreateSessionReq{
	// 	Session: &basemsgtransfer.SessionInfo{
	// 		SessionId:   "s_6_20",
	// 		SessionType: 1,
	// 		OwnUserid:   6,
	// 		PeerId:      20,
	// 		GroupId:     0,
	// 		RecvMsgOpt:  0,
	// 		IsPinned:    true,
	// 		Ct:          time.Now().Unix(),
	// 	},
	// })
}

func TestSortedSessionList(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	s.GetSortedSessionList(context.Background(), &basemsgtransfer.GetSortedSessionListReq{
		Userid: 123,
	})
}

func TestGetSessionInfo(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	rsp, err := s.GetSessionInfo(context.Background(), &basemsgtransfer.GetSessionInfoReq{
		Userid:     6,
		SessionIds: []string{"s_1_8", "s_6:20"},
	})
	fmt.Print(rsp, err)
}

func TestMsg1v1Handle(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	ctx := context.Background()
	msg := mproto.MsgData{
		To:          123,
		MsgId:       "xxx",
		SessionId:   "s_123_1",
		From:        1,
		SessionType: 1,
		ContentType: 1,
		Content:     "xxx",
	}

	insertSeqs, err := s.storage.Msg1v1Save(ctx, msg.To, []*mproto.MsgData{&msg})
	if err != nil {
		fmt.Print(insertSeqs, err)
	}

	// err = s.storage.InsertWaitAckList(ctx, msg.To, insertSeqs)
	// if err != nil {
	// 	fmt.Print(err, err)
	// }
}

func TestMsgGroupSaveHandle(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	ctx := context.Background()
	msg := mproto.MsgData{
		GroupId:     666,
		MsgId:       "xxx",
		SessionId:   "g_666",
		From:        1,
		SessionType: 2,
		ContentType: 1,
		Content:     "xxx",
	}

	insertSeqs, err := s.storage.MsgGroupSave(ctx, msg.GroupId, []*mproto.MsgData{&msg})
	if err != nil {
		fmt.Print(insertSeqs, err)
	}

}

func TestHistorySaveHandle(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	ctx := context.Background()
	msg := mproto.MsgData{
		GroupId:     666,
		MsgId:       "xxx",
		SessionId:   "s_666_999",
		From:        1,
		SessionType: 2,
		ContentType: 1,
		Content:     "xxx",
	}

	aa := &model.MsgDataModel{}
	copier.Copy(aa, &msg)
	var bb []*model.MsgDataModel
	bb = append(bb, aa)
	err = s.storage.Msg1v1InsertHistoryDb(ctx, bb)
	if err != nil {
		fmt.Print(err)
	}
}

func TestMsg1v1PingHandle(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)
	waitAckMsgs, err := s.storage.GetWaitAckMsgs(context.Background(), 123, 200)
	fmt.Print(waitAckMsgs, err)
}

func TestMsg1v1NotifyAckHandle(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)
	// err = s.storage.OfflineMsgAck(context.Background(), 123, []int64{99, 1})
	// fmt.Print(err)
}

func TestGetMsg(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	// get 1v1 msg
	// get group msg
	//rsp, err := s.storage.GetMsgBySeqsRange(context.Background(), 66, "s_66_88", 1, 1000, 100)
	//fmt.Print(rsp, err)
}

func TestSetSessionOpt(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	rsp, err := s.SetSession(context.Background(), &basemsgtransfer.SetSessionReq{
		SessionId: "s_8_186",
		OwnUserid: 186,
		Remark: &wrapperspb.StringValue{
			Value: "xxx",
		},
	})
	fmt.Print(rsp, err)
}

func TestGetSessionOpt(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	rsp, err := s.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
		Upsert: &basemsgtransfer.SessionInfo{
			SessionId:   "s_8_186",
			SessionType: 1,
			OwnUserid:   186,
			PeerId:      8,
			GroupId:     0,
			RecvMsgOpt:  0,
			IsPinned:    true,
			Ct:          time.Now().Unix(),
		},
	})

	// rsp, err := s.GetSession(context.Background(), &basemsgtransfer.GetSessionReq{
	// 	SessionId: "s_6:20",
	// 	Userid:    6,
	// })
	fmt.Print(rsp, err)
}

func TestCreateHistoryMsgTable(t *testing.T) {
	sql := "CREATE TABLE `history_msg_%03d` (`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',`session_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',`msg_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,`ct` bigint unsigned NOT NULL,PRIMARY KEY (`id`),KEY `idx` (`session_id`,`ct` DESC) USING BTREE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;\n"
	for i := 0; i < 256; i++ {
		fmt.Printf(sql, i)
	}
}

func TestGetLatestMsgInGroup(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	db := mgo.NewMsgMongo()
	msg, err := db.GetLatestMsgInGroup(context.Background(), 1)
	fmt.Print(msg, err)
}

func TestGetGroupMsgBySeqsRange(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	s.storage.GetGroupMaxSeq(context.Background(), 1)

	s.storage.GetGroupMsgBySeqsRange(context.Background(), 1, 1, 200, 200)
}

func TestInsertUserGroup(t *testing.T) {
	s, err := NewBaseMsgTransferServer(conf)
	fmt.Print(s, err)

	var userids = []int64{642628, 642877, 643162, 643164}
	for _, userid := range userids {
		s.storage.UserUpdateGroup(context.Background(), &model.UserGroupModel{
			UserId:  userid,
			GroupId: 1,
			Ct:      time.Now().Unix(),
		})
	}
}

func TestDecodeBizMsgData(t *testing.T) {
	data := `{"data":"{\"atList\":[{\"text\":\"@Doris\",\"atUserId\":643407}],\"seq\":0,\"createTime\":1730796970015,\"errCode\":0,\"retry\":0,\"groupId\":80,\"status\":2,\"to\":80,\"isRead\":true,\"sessionType\":2,\"msgId\":\"\",\"isAutoIncrement\":true,\"identifier\":0,\"from\":643781,\"content\":\"{\\\"content\\\":\\\"@Doris\\\"}\",\"slivers\":0,\"isAudioRead\":false,\"errMsg\":\"\",\"msgFrom\":0,\"sendTime\":1730796970015,\"localId\":\"1730796970562\",\"sessionId\":\"g_80\",\"isReadPacketRead\":false,\"isHeartBeatRead\":false,\"upgrade\":false,\"contentType\":101}","type":2}`
	msg := &basemsgtransfer.InterMsgData{}
	decodeBizMsgData(data, msg)
}

// 测试修复后的JSON反序列化问题
func TestDecodeBizMsgData_WithObjectData(t *testing.T) {
	// 测试data字段为对象的情况（这是导致错误的情况）
	msgJson := `{
		"type": 1,
		"data": {
			"isFront": true,
			"userid": ""
		}
	}`

	msg := &basemsgtransfer.InterMsgData{}
	msgType, err := decodeBizMsgData(msgJson, msg)

	// 应该成功处理，不报错
	if err != nil {
		t.Errorf("Expected no error, but got: %v", err)
	}
	if msgType != 1 {
		t.Errorf("Expected msgType 1, but got: %d", msgType)
	}
	if msg.Type != 1 {
		t.Errorf("Expected msg.Type 1, but got: %d", msg.Type)
	}

	// 验证Type1Data被正确解析
	if msg.Type1Data == nil {
		t.Error("Expected Type1Data to be not nil")
	} else {
		if msg.Type1Data.IsFront != true {
			t.Errorf("Expected IsFront to be true, but got: %v", msg.Type1Data.IsFront)
		}
		if msg.Type1Data.Userid != "" {
			t.Errorf("Expected Userid to be empty, but got: %s", msg.Type1Data.Userid)
		}
	}
}

func TestDecodeBizMsgData_WithType2ObjectData(t *testing.T) {
	// 测试type=2的消息，data字段为对象
	msgJson := `{
		"type": 2,
		"data": {
			"localId": "test123",
			"sessionId": "s_123",
			"from": 1001,
			"to": 1002,
			"sessionType": 1,
			"contentType": 101,
			"content": "hello world"
		}
	}`

	msg := &basemsgtransfer.InterMsgData{}
	msgType, err := decodeBizMsgData(msgJson, msg)

	// 应该成功处理
	if err != nil {
		t.Errorf("Expected no error, but got: %v", err)
	}
	if msgType != 2 {
		t.Errorf("Expected msgType 2, but got: %d", msgType)
	}
	if msg.Type != 2 {
		t.Errorf("Expected msg.Type 2, but got: %d", msg.Type)
	}

	// 验证Type2Data被正确解析
	if msg.Type2Data == nil {
		t.Error("Expected Type2Data to be not nil")
	} else {
		if msg.Type2Data.LocalId != "test123" {
			t.Errorf("Expected LocalId to be 'test123', but got: %s", msg.Type2Data.LocalId)
		}
		if msg.Type2Data.SessionId != "s_123" {
			t.Errorf("Expected SessionId to be 's_123', but got: %s", msg.Type2Data.SessionId)
		}
		if msg.Type2Data.From != 1001 {
			t.Errorf("Expected From to be 1001, but got: %d", msg.Type2Data.From)
		}
		if msg.Type2Data.To != 1002 {
			t.Errorf("Expected To to be 1002, but got: %d", msg.Type2Data.To)
		}
	}
}
