package main

import (
	"context"
	"net"
	"time"
	"xim/baselib/cache"
	"xim/baselib/config"
	"xim/baselib/database"
	"xim/baselib/database/mongo"
	"xim/baselib/ginserver"
	"xim/baselib/kafka"
	"xim/baselib/logger"
	"xim/baselib/metric"
	"xim/baselib/msgchan"
	"xim/baselib/nsqutil"
	"xim/baselib/server/env"
	"xim/baselib/server/interceptor"
	"xim/baselib/trace"
	"xim/baselib/util"
	"xim/basemsgtransfer/controller"
	"xim/basemsgtransfer/cron"
	"xim/basemsgtransfer/server"
	"xim/basemsgtransfer/stat"
	"xim/proto/api/basemsgtransfer"
	"xim/proto/api/common"
	"xim/proto/consts"
	"xim/proto/consts/errcode"
	"xim/proto/consts/mqtopic"

	"google.golang.org/grpc"
)

func main() {
	// 初始化监控
	ginserver.Init()
	controller.Init()
	msgchan.Init()
	//go ginserver.Start()

	config.Init()
	conf := config.GetServerConfig()

	// logger.InitLogger(conf.Logger)
	logger.InitLoggerWitchLevel(conf.Logger, conf.LogLevel)

	ctx := context.Background()
	logger.Infof("config:%v", util.JsonStr(conf))
	if conf.Jaeger != nil && conf.Jaeger.Enable {
		prv, err := trace.NewProvider(ctx, trace.ProviderConfig{
			Endpoint:       conf.Jaeger.Endpoint, // Jaeger
			ServiceName:    conf.Jaeger.ServiceName,
			ServiceVersion: "v0.0.1",
			Environment:    conf.Jaeger.Environment,
		})
		if err != nil {
			logger.Fatalf("Failed to create trace provider: %v", err)
		}
		defer prv.Close(ctx)
	}

	svr := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			interceptor.RecoverWithRespFunc(func(stack string) interface{} {
				msg := "internal server error"
				if !env.IsProd() {
					msg = stack
				}
				return &common.SvcCommonResp{
					Base: &common.SvcBaseResp{
						Code: errcode.ErrorInternal.Code,
						Msg:  msg,
					},
				}
			}),
			interceptor.ServerLog,
			metric.UnaryServerInterceptor(),
		),
		trace.GRPCServerStatsHandler(),
	)

	mongo.InitMongoDB(conf.MongodbIm)
	cache.InitRedis(conf.Redis)
	database.InitMysql(conf.Mysql)
	//nsqInit(conf)
	kafkaInit(conf)
	cron.StartCronTask()

	baseMsgTransferServer, err := server.NewBaseMsgTransferServer(conf)
	if err != nil {
		logger.Errorf("New server err:%v", err)
		return
	}

	if err := server.InitWebhookServices(); err != nil {
		logger.Errorf("InitWebhookServices err:%v", err)
		return
	}

	// 注册gRPC服务实现
	basemsgtransfer.RegisterSServer(svr, baseMsgTransferServer)
	// 导出监控数据
	metric.InitPrometheus(svr, "")
	metric.MustRegister(stat.StatMetric()...)

	var addr = "0.0.0.0:9000"
	// 启动服务
	ls, err := net.Listen("tcp", addr)
	if err != nil {
		logger.Errorf("Listen error: %v\n", err)
		return
	}
	logger.Infof("Started Application at %v, listen on %v", time.Now().Format("January 2, 2006 at 3:04pm (MST)"), addr)
	svr.Serve(ls)

}

func kafkaInit(conf *config.ServerConfig) {
	if !conf.Kafka.Close {
		consts.SetKafkaTopic(conf.Kafka)
		kafka.KafkaInit(conf.Kafka, []string{consts.XIM_MSG_TOPIC(), consts.XIM_STATE_TOPIC()})
		server.InitMsgKafkaConsumer(conf.Kafka.Build())
		server.InitStateKafkaConsumer(conf.Kafka.Build())
	}
}

func nsqInit(conf *config.ServerConfig) {
	upstreamHandler, err := server.NewWsUpstreamMessageHandler(conf.Nsq)
	if err != nil {
		logger.Panicf("nsqInit error %v", err)
	}

	syncHandler, err := server.NewWsSyncMessageHandler(conf.Nsq)
	if err != nil {
		logger.Panicf("nsqInit error %v", err)
	}

	if err := nsqutil.NewConsumer(conf.Nsq); err != nil {
		logger.Panicf("nsqInit error %v", err)
	}
	if err := nsqutil.NewProducer(conf.Nsq); err != nil {
		logger.Panicf("nsqInit error start nsq producer %v", err)
	}
	_, err = nsqutil.Subscribe(mqtopic.NsqTopicState, mqtopic.NsqChannelStateHandle, syncHandler)
	if err != nil {
		logger.Panicf("nsqInit error sub scribe %v", err)
	}

	// todo 改成kafka 按照key 水平扩展实例
	// _, err = lock.NewSimpleLock(cache.GetRedisClient("chat"), "nsq_init", time.Second*10)
	// if err == lock.ErrNotObtained {
	// 	return
	// }
	_, err = nsqutil.SubscribeSingle(mqtopic.NsqTopicMessage, mqtopic.NsqChannelMessageHandle, upstreamHandler)
	if err != nil {
		logger.Panicf("nsqInit error sub scribe %v", err)
	}

}
