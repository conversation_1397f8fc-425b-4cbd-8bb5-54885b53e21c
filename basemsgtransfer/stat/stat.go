package stat

import (
	"fmt"
	"time"
	"xim/baselib/server/env"
	"xim/proto/api/basemsgtransfer"

	"github.com/prometheus/client_golang/prometheus"
)

var (
	// 上行消息到响应ack耗时
	upstreamMsgToAckCostTime = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "xllivemp",
			Subsystem: "basemsgtransfer",
			Name:      "request_duration",
			Help:      "The request latencies in seconds",
			ConstLabels: prometheus.Labels{
				"pod":     env.GetHostName(),
				"service": env.GetServiceName(),
			},
			Buckets: []float64{200, 300, 350, 400, 450, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 2500, 3000, 3500, 4000},
		},
		[]string{"content_type"},
	)
)

func StatMetric() []prometheus.Collector {
	return []prometheus.Collector{
		upstreamMsgToAckCostTime,
	}
}

func UpstreamMsgToAckLatencyMilliSecond(contentType int32, startNanoTime int64) {
	if startNanoTime <= 0 {
		return
	}
	switch basemsgtransfer.ContentType(contentType) {
	case basemsgtransfer.ContentType_Text,
		basemsgtransfer.ContentType_Image,
		basemsgtransfer.ContentType_Voice:
	default:
		return
	}
	lvs := []string{fmt.Sprintf("%v", contentType)}
	costTime := float64(time.Now().UnixNano()-startNanoTime) / 1e6
	upstreamMsgToAckCostTime.WithLabelValues(lvs...).Observe(costTime)
}
