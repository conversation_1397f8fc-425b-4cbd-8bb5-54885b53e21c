package utils

import (
	"fmt"
	"sort"
	"strings"
	"xim/proto/api/basemsgtransfer"
)

func SessionIdGen(sessionType int32, from, to, groupId int64) string {
	switch sessionType {
	case int32(basemsgtransfer.SessionType_SessionTypeChat):
		if from > to {
			return fmt.Sprintf("s_%d_%d", to, from)
		}
		return fmt.Sprintf("s_%d_%d", from, to)
	case int32(basemsgtransfer.SessionType_SessionTypeGroup):
		return fmt.Sprintf("g_%v", groupId)
	case int32(basemsgtransfer.SessionType_SessionTypeSystem):
		return fmt.Sprintf("n_%d", from)
	}

	return ""
}

func GetSessionType(from, to, groupId int64) int32 {
	if groupId != 0 {
		return int32(basemsgtransfer.SessionType_SessionTypeGroup)
	}

	return int32(basemsgtransfer.SessionType_SessionTypeChat)
}

// func OperationIDGenerator() string {
// 	return strconv.FormatInt(time.Now().UnixNano()+int64(rand.Uint32()), 10)
// }

func GenConversationIDForSingle(sendID, recvID string) string {
	l := []string{sendID, recvID}
	sort.Strings(l)
	return "s_" + strings.Join(l, "_")
}

// func GenConversationUniqueKeyForGroup(groupID string) string {
// 	return groupID
// }

// func GenGroupConversationID(groupID string) string {
// 	return "sg_" + groupID
// }

// func GenConversationUniqueKeyForSingle(sendID, recvID string) string {
// 	l := []string{sendID, recvID}
// 	sort.Strings(l)
// 	return strings.Join(l, "_")
// }

// func GetNotificationConversationIDByConversationID(conversationID string) string {
// 	l := strings.Split(conversationID, "_")
// 	if len(l) > 1 {
// 		l[0] = "n"
// 		return strings.Join(l, "_")
// 	}
// 	return ""
// }

// func GetSelfNotificationConversationID(userID string) string {
// 	return "n_" + userID + "_" + userID
// }
