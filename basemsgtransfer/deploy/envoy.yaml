static_resources:
  listeners:
    - name: listener_0
      address:
        socket_address: { address: 0.0.0.0, port_value: 80 }
      filter_chains:
        - filters:
            - name: envoy.http_connection_manager
              config:
                codec_type: auto
                stat_prefix: gprc_json
                route_config:
                  name: local_route
                  virtual_hosts:
                    - name: local_service
                      domains: ["*"]
                      routes:
                        - match: { prefix: "/" }
                          route: { cluster: transfer_service, timeout: { seconds: 60 } }
                http_filters:
                  - name: envoy.grpc_json_transcoder
                    config:
                      proto_descriptor: "/etc/envoy/proto.pb"
                      services:
                        - "vc.basemsgtransfer.s"
                      print_options:
                        add_whitespace: true
                        always_print_primitive_fields: true
                        always_print_enums_as_ints: false
                        preserve_proto_field_names: false
                  - name: envoy.router
  clusters:
    - name: transfer_service
      connect_timeout: 15s
      type: strict_dns
      lb_policy: round_robin
      http2_protocol_options: {}
      hosts:
        - socket_address:
            address: basemsgtransfer
            port_value: 9000


admin:
  access_log_path: "/dev/stdout"
  address:
    socket_address:
      address: 0.0.0.0
      port_value: 8001
