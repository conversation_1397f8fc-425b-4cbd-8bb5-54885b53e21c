# 服务监听地址
addr: 0.0.0.0:9000

# 日志配置文件
logger: /dev/stdout


redis:
  - name: chat
    db:
    addr: r-os-test.redis.singapore.rds.aliyuncs.com:6379
    password: yXmIui77r9bC

mongodb_im:
  db:
  user: root
  password: lYu5V38yPdrk
  host: mongodb://root:<EMAIL>:3717,dds-t4n4a59b40254de41281-pub.mongodb.singapore.rds.aliyuncs.com:3717/admin?replicaSet=mgset-309007509
  port: 3717
  pool_size: 3

nsq:
  lookupds:
    - ***********:4161
  topic_prefix: melon


mysql:
  - host: rm-os-test.mysql.singapore.rds.aliyuncs.com
    port: 3306
    db-name: melon_chat
    extras:
    user: root1
    password: hetdvK8FIlci
    max-idle-conns: 4
    max-open-conns: 4
    logger-mode:
    logger-zap:


kafka:
  producerAck: wait_for_local
  stateTopic:
    group: "xim_state_group_2"
    topic: "xim_state_2"
  messageTopic:
    group: "xim_message_group_2"
    topic: "xim_message_2"
  address: ["***********:9095","***********:9096","***********:9097"]

jaeger:
  enable: true
  endpoint: jaeger:4317
  environment: dev
  service_name: vc.basemsgtransfer.s