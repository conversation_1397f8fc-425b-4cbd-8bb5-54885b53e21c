package cron

import (
	"context"
	"sync"
	"time"
	"xim/baselib/cache"
	"xim/baselib/cache/lock"
	"xim/baselib/logger"
	"xim/basemsgtransfer/storage/mgo"
)

// 删除已经确认的离线消息
const MsgExpireTimeSecond = 3600 * 24 * 15

var mongoClient *mgo.MsgMgo
var once sync.Once

func deleteMsgExpiredJob() {
	lock.NewLockDoJob(cache.GetRedisClient("chat"), "msg_expire_job", deleteMsgExpired, &lock.LockDoOption{
		RedisErrorRetryInterval: 10 * time.Second,
		ReLockInterval:          5 * time.Minute,
		LockHoldInterval:        10 * time.Minute,
		LockRefreshInterval:     5 * time.Minute,
		TaskRunInterval:         10 * time.Minute,
	}).Start()

}

func deleteMsgExpired() {
	logger.Infof("deleteMsgExpired")

	once.Do(func() {
		logger.Infof("mgo init")
		mongoClient = mgo.NewMsgMongo()
	})

	err := mongoClient.Delete1v1MsgsByTime(context.Background(), time.Now().Unix()-MsgExpireTimeSecond)
	if err != nil {
		logger.Errorf("err %v ", err)
	}
}
