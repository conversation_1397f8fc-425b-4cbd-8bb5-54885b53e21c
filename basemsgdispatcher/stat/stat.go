package stat

import (
	"fmt"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var (
	basemsgdispatcherLatency = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "xllivemp",
			Subsystem: "basemsgdispatcher",
			Name:      "basemsgdispatcher_request_duration",
			Help:      "The request latencies in seconds",
			Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10},
		},
		[]string{"cmd", "result"},
	)
)

func init() {
	prometheus.MustRegister(basemsgdispatcherLatency)
}

// BasemsgdispatcherLatency 自定义统计
func BasemsgdispatcherLatency(cmd string, result int32, duration float64) {
	lvs := []string{cmd, fmt.Sprint(result)}
	basemsgdispatcherLatency.WithLabelValues(lvs...).Observe(duration)
}

// Latency 计算耗时，单位秒
func Latency(t time.Time) float64 {
	return float64(time.Now().Sub(t).Nanoseconds()) / (1000 * 1000 * 1000)
}
