package util

/**
通用方法定义
*/

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"xim/common/header"
	"xim/proto/api/basemsgdispatcher"

	"golang.org/x/net/context"
)

var (
	Rand = rand.New(rand.NewSource(time.Now().UnixNano()))
)

// 字符串时间（格式 YYYY-MM-DD HH:mm:ss）转 Unix 时间戳
func ParseUnixTimestamp(srcTime string) int64 {
	if srcTime == "0000-00-00 00:00:00" || srcTime == "0" {
		return int64(0)
	}
	timeLayout := "2006-01-02 15:04:05"                          //转化所需模板
	loc, _ := time.LoadLocation("Local")                         //重要：获取时区
	theTime, _ := time.ParseInLocation(timeLayout, srcTime, loc) //使用模板在对应时区转化为time.time类型
	return int64(theTime.Unix())
}

// Unix 时间戳转为字符串（格式 YYYY-MM-DD HH:mm:ss）
func FormatUnixTimestamp(ts int64) string {
	timeLayout := "2006-01-02 15:04:05"
	t := time.Unix(ts, 0)
	return t.Format(timeLayout)
}

// 字符串和整形相互转换
func ParseInt64(s string) (int64, error) {
	value, err := strconv.ParseInt(s, 10, 64)
	return value, err
}
func ParseUint64(s string) (uint64, error) {
	value, err := strconv.ParseUint(s, 10, 64)
	return uint64(value), err
}
func ParseInt32(s string) (int32, error) {
	value, err := strconv.ParseInt(s, 10, 32)
	return int32(value), err
}
func ParseUint32(s string) (uint32, error) {
	value, err := strconv.ParseUint(s, 10, 32)
	return uint32(value), err
}
func ParseInt(s string) (int, error) {
	value, err := strconv.ParseUint(s, 10, 32)
	return int(value), err
}

func FormatInt64(i int64) string {
	value := strconv.FormatInt(i, 10)
	return value
}
func FormatUint64(i uint64) string {
	value := strconv.FormatUint(i, 10)
	return value
}
func FormatInt32(i int32) string {
	value := strconv.FormatInt(int64(i), 10)
	return value
}
func FormatUint32(i uint32) string {
	value := strconv.FormatUint(uint64(i), 10)
	return value
}

// 获取key的cookie值
func GetCookieValue(ctx context.Context, cookleKey string) string {
	headerData := header.NewHeader(ctx)
	return headerData.GetCookie(cookleKey)
}

// 解析平台用户长连接唯一标识
func ParsePlatformUserConnUniqueId(userConnUniqueId string) (*PlatformUserConnInfo, error) {
	values := strings.Split(userConnUniqueId, "_")
	if len(values) != 4 {
		return nil, fmt.Errorf("UniqueId %s format error", userConnUniqueId)
	}
	var err error
	connInfo := &PlatformUserConnInfo{}
	connInfo.PlatformId, err = ParseUint64(values[0])
	if err != nil {
		return nil, fmt.Errorf("UniqueId %s parse error %v", userConnUniqueId, err)
	}
	connInfo.UserId = values[1]
	connInfo.BrokerIp = values[2]
	connInfo.ConnId, err = ParseUint64(values[3])
	if err != nil {
		return nil, fmt.Errorf("UniqueId %s parse error %v", userConnUniqueId, err)
	}
	return connInfo, nil
}

// 检查客户端限制配置，判断是否放行
func CheckClientLimit(clientId uint32, clientVersion string, clientLimitList []*basemsgdispatcher.ClientLimit) bool {
	isPass := true //默认放行
	for _, clientLimit := range clientLimitList {
		//客户端id命中配置才比较，没命中的默认不比较放行
		if clientId == clientLimit.ClientId {
			isMatchVersion := false
			compareResult, err := CompareVersion(clientVersion, clientLimit.ClientVersion)
			switch clientLimit.VersionCompare {
			case ">=":
				if err == nil && compareResult >= 0 {
					isMatchVersion = true
				}
			case ">":
				if err == nil && compareResult > 0 {
					isMatchVersion = true
				}
			case "=":
				if err == nil && compareResult == 0 {
					isMatchVersion = true
				}
			case "<=":
				if err == nil && compareResult <= 0 {
					isMatchVersion = true
				}
			case "<":
				if err == nil && compareResult < 0 {
					isMatchVersion = true
				}
			default:
				//不是上述的版本比较类型，默认不比较，直接设为版本匹配
				isMatchVersion = true
			}
			if clientLimit.LimitType == basemsgdispatcher.PublishTargetClientLimitType_PassType {
				isPass = isMatchVersion
				break
			} else if clientLimit.LimitType == basemsgdispatcher.PublishTargetClientLimitType_BlockType {
				isPass = !isMatchVersion
				break
			}
		}
	}
	return isPass
}

// 版本比对：v1小于v2返回-1，v1等于v2返回0，v1大于v2返回1。v1和v2格式都为0.0.1这样格式，没有英文字符v
func CompareVersion(v1, v2 string) (result int, err error) {
	a1 := strings.Split(v1, ".")
	a2 := strings.Split(v2, ".")
	if v1 == "" || v2 == "" {
		err = fmt.Errorf("version is empty")
		return
	}
	i := 0
	k := 0
	if len(a1) < len(a2) {
		i = len(a1)
	} else {
		i = len(a2)
	}

	for k < i {
		a, _ := strconv.Atoi(a1[k])
		b, _ := strconv.Atoi(a2[k])
		if a < b {
			result = -1
			return
		} else if a > b {
			result = 1
			return
		}
		k++
	}

	if len(a1) < len(a2) {
		result = -1
		return
	} else if len(a1) > len(a2) {
		result = 1
		return
	}
	result = 0
	return
}
