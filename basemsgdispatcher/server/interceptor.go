package server

import (
	"context"

	"google.golang.org/grpc"
)

// 免检测函数白名单，直接略过
func isPassAuth(method string) bool {
	if method != "" {
		return true
	}
	return false
}

// AuthUnaryIntercepter 用户session拦截器
func AuthUnaryIntercepter() func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		if !isPassAuth(info.FullMethod) {
			return handler(ctx, req)
		}
		return handler(ctx, req)
	}
}
