package server

import (
	"time"

	log "xim/baselib/logger"
	"xim/basemsgdispatcher/config"
	"xim/basemsgdispatcher/controller"
	"xim/basemsgdispatcher/stat"
	pb_basemsgdispatcher "xim/proto/api/basemsgdispatcher"

	"golang.org/x/net/context"
)

// BasemsgdispatcherServer 服务接入层
// 负责实现proto协议中定义的grpc接口，检查请求参数，拦截非法请求，记录访问日志
type BasemsgdispatcherServer struct {
	controller *controller.BasemsgdispatcherController
}

// NewBasemsgdispatcherServer 创建basemsgdispatcher服务
// 初始化controller和业务自定义模块
func NewBasemsgdispatcherServer(conf *config.Config) (*BasemsgdispatcherServer, error) {
	s := &BasemsgdispatcherServer{}

	var err error
	s.controller, err = controller.NewBasemsgdispatcherController(conf)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Test 实现Test接口
func (s *BasemsgdispatcherServer) Test(ctx context.Context, req *pb_basemsgdispatcher.TestReq) (*pb_basemsgdispatcher.TestRsp, error) {
	var (
		err       error
		res       *pb_basemsgdispatcher.TestRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("Test req:%+v res:%v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.Test(ctx, req)
	return res, nil
}

func (s *BasemsgdispatcherServer) PublishMsg(ctx context.Context, req *pb_basemsgdispatcher.PublishMsgReq) (*pb_basemsgdispatcher.PublishMsgRsp, error) {
	var (
		err       error
		res       *pb_basemsgdispatcher.PublishMsgRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("PublishMsg req:%+v res:%v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.PublishMsg(ctx, req)
	return res, nil
}
