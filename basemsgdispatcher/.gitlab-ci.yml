variables:
  NAME: basemsgdispatcher
  GROUP_NAME: gitlab.xunlei.cn/xllivemp
  REPO_NAME: ${GROUP_NAME}/${NAME}
  CI_PRODUCTION_REGISTRY: registry.xunlei.cn
  CI_PRODUCTION_REGISTRY_IMAGE: ${CI_PRODUCTION_REGISTRY}/xllivemp/${NAME}
  CI_PRODUCTION_REGISTRY_USER: xllive
  CI_PRODUCTION_REGISTRY_PASSWORD: Xllive20170914
  DEV_DOMAIN_NAME: tdc.xunlei.cn

before_script:
- go version
- echo $CI_BUILD_REF
- mkdir -p $GOPATH/src/$GROUP_NAME
- cp -rf $CI_PROJECT_DIR $GOPATH/src/$REPO_NAME
- cd $GOPATH/src/$REPO_NAME

build-dev:
  stage: build
  variables:
    GIT_STRATEGY: fetch
  only:
  - dev
  script:
  - VERSION=${CI_COMMIT_REF_NAME} make image
  - cp -rf ./deploy/dev/* ./deploy/
  - docker build ./ -f Dockerfile -t ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}
  - docker build ./ -f Dockerfile.initdata -t ${CI_REGISTRY_IMAGE}/${NAME}-initdata:${CI_COMMIT_REF_NAME}
  - docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
  - docker push ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}
  - docker push ${CI_REGISTRY_IMAGE}/${NAME}-initdata:${CI_COMMIT_REF_NAME}
  - docker rmi ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}
  - docker rmi ${CI_REGISTRY_IMAGE}/${NAME}-initdata:${CI_COMMIT_REF_NAME}

build-pro:
  stage: build
  variables:
    GIT_STRATEGY: fetch
  only:
  - tags@xllivemp/basemsgdispatcher
  script:
  - VERSION=${CI_COMMIT_REF_NAME} make image
  - cp -rf ./deploy/pro/* ./deploy/
  - docker build ./ -f Dockerfile -t ${CI_PRODUCTION_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}
  - docker build ./ -f Dockerfile.initdata -t ${CI_PRODUCTION_REGISTRY_IMAGE}/${NAME}-initdata:${CI_COMMIT_REF_NAME}
  - docker login ${CI_PRODUCTION_REGISTRY} -u ${CI_PRODUCTION_REGISTRY_USER} -p ${CI_PRODUCTION_REGISTRY_PASSWORD}
  - docker push ${CI_PRODUCTION_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}
  - docker push ${CI_PRODUCTION_REGISTRY_IMAGE}/${NAME}-initdata:${CI_COMMIT_REF_NAME}
  - docker rmi ${CI_PRODUCTION_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}
  - docker rmi ${CI_PRODUCTION_REGISTRY_IMAGE}/${NAME}-initdata:${CI_COMMIT_REF_NAME}

deploy-pro:
  stage: deploy
  image: registry.xunlei.cn/xllive/cideploy:v1.0.0
  before_script:
  - echo Project=${NAME}
  - echo Tag=${CI_COMMIT_REF_NAME}
  only:
  - tags
  script:
  - updateTime=`git show  --date=iso8601|grep Date|head -n1|awk '{print $2" "$3}'`
  - echo $updateTime
  - git tag -ln ${CI_COMMIT_REF_NAME}
  - descript=`git tag -ln ${CI_COMMIT_REF_NAME}`
  - jsonstr='{"name":"'${CI_COMMIT_REF_NAME}'","projectName":"'xllivemp-$NAME-mesh'","projectGroup":"xllivemp","projectUpdateTime":"'$updateTime'","description":"'$descript'","gitUrl":"https://'${REPO_NAME}'"}'
  - echo UpdateTime=$updateTime Descript=$descript JSON=$jsonstr
  - curl -XPOST https://${DEV_DOMAIN_NAME}/api/v1/tags -d "$jsonstr"