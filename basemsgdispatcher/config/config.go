package config

import (
	"flag"
	"os"

	log "xim/baselib/logger"

	"github.com/jinzhu/configor"
)

type BrokerConf struct {
	InK8s      bool              `yaml:"in_k8s"`
	KubeConfig string            `yaml:"kube_config"`
	NameSpace  string            `yaml:"namespace"`
	PodLabel   map[string]string `yaml:"pod_label"`
	SrvName    string            `yaml:"srv_name"`
}

type JaegerConfig struct {
	ServiceName string `yaml:"service_name"`
	Endpoint    string `yaml:"endpoint"`
	Enable      bool   `default:"false" yaml:"enable"`
	Environment string `yaml:"environment"`
}

// Config 服务配置
type Config struct {
	Addr   string     `default:"0.0.0.0:8080"`
	Logger string     `default:"deploy/logger.xml"`
	Broker BrokerConf `yaml:"broker"`
	Jaeger JaegerConfig
}

// NewConfig 读取配置
func NewConfig() Config {
	configPath := flag.String("basemsg_config", "deploy/config.yml", "configuration file")
	flag.Parse()

	var conf Config
	if err := configor.Load(&conf, *configPath); err != nil {
		log.Panicf("load config error: %v", err)
		os.Exit(1)
	}

	return conf
}
