package controller

import (
	"context"
	"math/rand"
	"time"

	"fmt"
	"io"
	"strings"

	log "xim/baselib/logger"
	"xim/baselib/server/env"
	baselibUtil "xim/baselib/util"
	"xim/basemsgdispatcher/config"
	"xim/basemsgdispatcher/model"
	"xim/basemsgdispatcher/model/brokerwatcher"
	"xim/basemsgdispatcher/model/grpccli"
	"xim/basemsgdispatcher/util"
	"xim/proto/api/basemsgbroker"
	pb_basemsgdispatcher "xim/proto/api/basemsgdispatcher"
)

// BasemsgdispatcherController 服务控制层
// 用于控制业务流程、处理事件并作出响应。“事件”包括 gRPC 接口请求和数据 Model 上的改变（例如nsq事件）
// 按业务将 Controller 分割成不同的文件以利维护
type BasemsgdispatcherController struct {
	conf *config.Config
}

// NewBasemsgdispatcherController 创建basemsgdispatcher controller
func NewBasemsgdispatcherController(conf *config.Config) (*BasemsgdispatcherController, error) {
	c := &BasemsgdispatcherController{conf: conf}

	err := model.NewBasemsgdispatcherModel(conf)
	if err != nil {
		return nil, err
	}

	return c, nil
}

// Test 实现Test接口
func (c *BasemsgdispatcherController) Test(ctx context.Context, req *pb_basemsgdispatcher.TestReq) (*pb_basemsgdispatcher.TestRsp, error) {
	message := "basemsgdispatcher"
	return &pb_basemsgdispatcher.TestRsp{Result: util.RetOK, Message: message}, nil
}

func (c *BasemsgdispatcherController) PublishMsg(ctx context.Context, req *pb_basemsgdispatcher.PublishMsgReq) (*pb_basemsgdispatcher.PublishMsgRsp, error) {
	res := &pb_basemsgdispatcher.PublishMsgRsp{Result: util.RetOK, Message: util.ErrorMsg[util.RetOK]}
	var err error
	switch req.Type {
	case pb_basemsgdispatcher.PublishTargetType_UserType:
		//发布消息到平台下的用户
		//其实发送到用户个人的，也可以通过topic来解决，单个用户的长连接都订阅到各自的一个topic
		err = c.publishUserMsg(ctx, req)
	case pb_basemsgdispatcher.PublishTargetType_TopicType:
		//发布消息到平台topic下的所有用户
		err = c.publishTopicMsg(ctx, req)
	case pb_basemsgdispatcher.PublishTargetType_AllType:
		//todo 目前先不写发布到平台所有用户的逻辑，可以通过topic来解决，全平台用户都订阅一个alltopic
		err = fmt.Errorf("not implemented PublishTarget All")
	case pb_basemsgdispatcher.PublishTargetType_TopicLimitType:
		//发布消息到平台topic下，符合限制条件的用户
		err = c.publishTopicLimitMsg(ctx, req)
	case pb_basemsgdispatcher.PublishTargetType_AllLimitType:
		//todo 目前先不写发布平台所有用户的逻辑，可以通过topic来解决，全平台都订阅一个alltopic
		err = fmt.Errorf("not implemented PublishTarget AllLimit")
	default:
		err = fmt.Errorf("unknown PublishTargetType %d", req.Type)
	}
	if env.IsTest() {
		log.Debugf("PublishMsg req=%+v err=%+v", baselibUtil.JsonStr(req), err)
	}
	if err != nil {
		res.Result = util.RetPublishError
		res.Message = fmt.Sprintf("%s error %v", util.ErrorMsg[util.RetPublishError], err)
		return res, err
	}
	return res, nil
}

func (c *BasemsgdispatcherController) publishUserMsg(ctx context.Context, req *pb_basemsgdispatcher.PublishMsgReq) error {
	if req.PlatformId <= 0 || req.UserId == "" {
		return fmt.Errorf("invalid param")
	}
	userConns, err := grpccli.GetPlatformUserConn(ctx, req.PlatformId, req.UserId, req.DeviceId)
	if err != nil {
		log.Errorf("publishUserMsg GetPlatformUserConn platformid %d userid %s deviceid %s error %v",
			req.PlatformId, req.UserId, req.DeviceId, err)
		return err
	}

	excludeDeviceIdMap := make(map[string]bool, len(req.ExcludeDeviceIds))
	for _, deviceId := range req.ExcludeDeviceIds {
		excludeDeviceIdMap[deviceId] = true
	}
	for _, conn := range userConns {
		if excludeDeviceIdMap[conn.DeviceId] {
			//如果长连接的设备id在用户设备黑名单里面，就不发布消息
			log.Warnf("publishUserMsg in excludeDeviceIds connInfo %+v", conn)
			continue
		}
		if !util.CheckClientLimit(conn.ClientId, conn.ClientVersion, req.LimitList) {
			//如果长连接的客户端标识id和客户端版本被屏蔽了，就不发布消息
			log.Warnf("publishUserMsg in clientLimit connInfo %+v clientLimit %+v", conn, req.LimitList)
			continue
		}
		log.Debugf("publishUserMsg ready connInfo %+v", conn)
		// 发给用户个人的下行消息的topic定为用户id
		go c.publishMsgToBrokerGivenConn(context.Background(), req.Payload, req.UserId, conn.BrokerIp, []uint64{conn.ConnId})
	}

	return nil
}

func (c *BasemsgdispatcherController) publishTopicMsg(ctx context.Context, req *pb_basemsgdispatcher.PublishMsgReq) error {
	if req.PlatformId <= 0 || strings.TrimSpace(req.Topic) == "" {
		return fmt.Errorf("invalid param")
	}
	stream, err := grpccli.StreamTopicUsers(ctx, req.PlatformId, req.Topic)
	if err != nil {
		log.Errorf("publishTopicMsg StreamTopicUsers platformid %d topic %s error %v", req.PlatformId, req.Topic, err)
		return err
	}

	for {
		rsp, err := stream.Recv()
		if err == io.EOF {
			log.Infof("platformid %d topic %s stream recv EOF", req.PlatformId, req.Topic)
			break
		}
		if err != nil {
			log.Errorf("publishTopicMsg platformid %d topic %s recv error %v", req.PlatformId, req.Topic, err)
			return err
		}
		if rsp == nil || rsp.Result != 0 || rsp.Data == nil {
			log.Errorf("publishTopicMsg platformid %d topic %s recv rsp %+v", req.PlatformId, req.Topic, rsp)
			continue
		}
		userConnIds := rsp.Data.UserIds
		brokerConnIdMap := make(map[string][]uint64, 0)
		for _, userConnId := range userConnIds {
			userConn, err := util.ParsePlatformUserConnUniqueId(userConnId)
			if err != nil {
				log.Errorf("publishTopicMsg ParsePlatformUserConnUniqueId %s error %v", userConnId, err)
				continue
			}
			if brokerConnIdMap[userConn.BrokerIp] == nil {
				brokerConnIdMap[userConn.BrokerIp] = make([]uint64, 0)
			}
			brokerConnIdMap[userConn.BrokerIp] = append(brokerConnIdMap[userConn.BrokerIp], userConn.ConnId)
		}
		for brokerIp, connIds := range brokerConnIdMap {
			log.Infof("publishTopicMsg ready brokerip %s connids %+v", brokerIp, connIds)
			go c.publishMsgToBrokerGivenConn(context.Background(), req.Payload, req.Topic, brokerIp, connIds)
		}
	}

	return nil
}

func (c *BasemsgdispatcherController) publishTopicLimitMsg(ctx context.Context, req *pb_basemsgdispatcher.PublishMsgReq) error {
	if req.PlatformId <= 0 || strings.TrimSpace(req.Topic) == "" {
		return fmt.Errorf("invalid param")
	}
	//如果req.IncludeUserIds没有数据，并且req.BroadcastRate概率为0，就相当于不给任何长连接下发消息，这个请求参数就有问题
	if len(req.IncludeUserIds) == 0 && req.BroadcastRate == 0 {
		return fmt.Errorf("wrong limit condition")
	}

	includeUserIdMap := make(map[string]bool, len(req.IncludeUserIds))
	for _, userId := range req.IncludeUserIds {
		includeUserIdMap[userId] = true
	}
	excludeUserIdMap := make(map[string]bool, len(req.ExcludeUserIds))
	for _, userId := range req.ExcludeUserIds {
		excludeUserIdMap[userId] = true
	}
	broadcastRate := int32(req.BroadcastRate)

	if len(req.LimitList) <= 0 {
		stream, err := grpccli.StreamTopicUsers(ctx, req.PlatformId, req.Topic)
		if err != nil {
			log.Errorf("publishTopicLimitMsg StreamTopicUsers platformid %d topic %s error %v", req.PlatformId, req.Topic, err)
			return err
		}

		for {
			rsp, err := stream.Recv()
			if err == io.EOF {
				log.Infof("platformid %d topic %s stream recv EOF", req.PlatformId, req.Topic)
				break
			}
			if err != nil {
				log.Errorf("publishTopicLimitMsg platformid %d topic %s recv error %v", req.PlatformId, req.Topic, err)
				return err
			}
			if rsp == nil || rsp.Result != 0 || rsp.Data == nil {
				log.Errorf("publishTopicLimitMsg platformid %d topic %s recv rsp %+v", req.PlatformId, req.Topic, rsp)
				continue
			}
			userConnIds := rsp.Data.UserIds
			brokerConnIdMap := make(map[string][]uint64, 0)
			for _, userConnId := range userConnIds {
				userConn, err := util.ParsePlatformUserConnUniqueId(userConnId)
				if err != nil {
					log.Errorf("publishTopicLimitMsg ParsePlatformUserConnUniqueId %s error %v", userConnId, err)
					continue
				}
				//如果长连接在ExcludeUserIds名单，或者不在IncludeUserIds名单但概率大于等于BroadcastRate，则不能发布消息到该长连接
				pr := rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(100)
				log.Debugf("publishTopicLimitMsg checkuser userid %s pr %d, broadcastRate %d excludeUserIdMap %+v includeUserIdMap %+v", userConn.UserId, pr, broadcastRate, excludeUserIdMap, includeUserIdMap)
				if excludeUserIdMap[userConn.UserId] ||
					(!includeUserIdMap[userConn.UserId] && pr >= broadcastRate) {
					continue
				}
				if brokerConnIdMap[userConn.BrokerIp] == nil {
					brokerConnIdMap[userConn.BrokerIp] = make([]uint64, 0)
				}
				brokerConnIdMap[userConn.BrokerIp] = append(brokerConnIdMap[userConn.BrokerIp], userConn.ConnId)
			}
			for brokerIp, connIds := range brokerConnIdMap {
				log.Infof("publishTopicLimitMsg ready brokerip %s connids %+v", brokerIp, connIds)
				go c.publishMsgToBrokerGivenConn(context.Background(), req.Payload, req.Topic, brokerIp, connIds)
			}
		}
	} else {
		stream, err := grpccli.StreamTopicUsersWithConnInfo(ctx, req.PlatformId, req.Topic)
		if err != nil {
			log.Errorf("publishTopicLimitMsg StreamTopicUsersWithConnInfo platformid %d topic %s error %v", req.PlatformId, req.Topic, err)
			return err
		}

		for {
			rsp, err := stream.Recv()
			if err == io.EOF {
				log.Infof("platformid %d topic %s stream recv EOF", req.PlatformId, req.Topic)
				break
			}
			if err != nil {
				log.Errorf("publishTopicLimitMsg platformid %d topic %s recv error %v", req.PlatformId, req.Topic, err)
				return err
			}
			if rsp == nil || rsp.Result != 0 || rsp.Data == nil {
				log.Errorf("publishTopicLimitMsg platformid %d topic %s recv rsp %+v", req.PlatformId, req.Topic, rsp)
				continue
			}
			userConns := rsp.Data.Conns
			brokerConnIdMap := make(map[string][]uint64, 0)
			for _, userConn := range userConns {
				//如果长连接在ExcludeUserIds名单，或者不在IncludeUserIds名单但概率大于等于BroadcastRate，则不能发布消息到该长连接
				pr := rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(100)
				log.Debugf("publishTopicLimitMsg checkuser userid %s pr %d, broadcastRate %d excludeUserIdMap %+v includeUserIdMap %+v", userConn.UserId, pr, broadcastRate, excludeUserIdMap, includeUserIdMap)
				if excludeUserIdMap[userConn.UserId] ||
					(!includeUserIdMap[userConn.UserId] && pr >= broadcastRate) {
					continue
				}
				//如果长连接的客户端标识id和版本命中了客户端限制配置，则不能发送消息到该长连接
				if !util.CheckClientLimit(userConn.ClientId, userConn.ClientVersion, req.LimitList) {
					continue
				}
				if brokerConnIdMap[userConn.BrokerIp] == nil {
					brokerConnIdMap[userConn.BrokerIp] = make([]uint64, 0)
				}
				brokerConnIdMap[userConn.BrokerIp] = append(brokerConnIdMap[userConn.BrokerIp], userConn.ConnId)
			}
			for brokerIp, connIds := range brokerConnIdMap {
				log.Infof("publishTopicLimitMsg ready brokerip %s connids %+v", brokerIp, connIds)
				go c.publishMsgToBrokerGivenConn(context.Background(), req.Payload, req.Topic, brokerIp, connIds)
			}
		}
	}

	return nil
}

func (c *BasemsgdispatcherController) publishMsgToBrokerGivenConn(ctx context.Context, payload, topic, brokerIp string, connIds []uint64) error {
	broker := brokerwatcher.GetBroker(brokerIp)
	if broker == nil {
		log.Warnf("publishMsgToBrokerGivenConn brokerIp %s client is nil", brokerIp)
		return nil
	}
	res, err := broker.Publish(ctx, &basemsgbroker.PublishReq{
		Message: payload,
		Topic:   topic,
		ConnIds: connIds,
	})
	if err != nil {
		log.Errorf("publishMsgToBrokerGivenConn msg %s topic %s brokerip %s connids %+v error %v",
			payload, topic, brokerIp, connIds, err)
		return err
	}
	log.Infof("publishMsgToBrokerGivenConn msg %s topic %s brokerip %s connids %+v rsp %+v",
		payload, topic, brokerIp, connIds, res)
	return nil
}
