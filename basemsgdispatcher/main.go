package main

import (
	"context"
	"net"
	"runtime"
	"time"
	"xim/baselib/server/env"
	"xim/baselib/server/interceptor"
	"xim/baselib/trace"

	"xim/baselib/logger"
	"xim/baselib/metric"

	log "xim/baselib/logger"
	"xim/basemsgdispatcher/config"
	"xim/basemsgdispatcher/server"
	pb_basemsgdispatcher "xim/proto/api/basemsgdispatcher"
	"xim/proto/api/common"
	"xim/proto/consts/errcode"

	"google.golang.org/grpc"
)

func main() {
	defer log.Sync()
	runtime.GOMAXPROCS(runtime.NumCPU())
	logger.InitLogger("/dev/stdout")

	conf := config.NewConfig()

	ctx := context.Background()
	if conf.Jaeger.Enable {
		prv, err := trace.NewProvider(ctx, trace.ProviderConfig{
			Endpoint:       conf.Jaeger.Endpoint, // Jaeger
			ServiceName:    conf.Jaeger.ServiceName,
			ServiceVersion: "v0.0.1",
			Environment:    conf.Jaeger.Environment,
		})
		if err != nil {
			logger.Fatalf("Failed to create trace provider: %v", err)
		}
		defer prv.Close(ctx)
	}

	svr := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			interceptor.RecoverWithRespFunc(func(stack string) interface{} {
				msg := "internal server error"
				if !env.IsProd() {
					msg = stack
				}
				return &common.BizBaseResp{
					Code: errcode.ErrorInternal.Code,
					Msg:  msg,
				}
			}),
			interceptor.ServerLog,
			metric.UnaryServerInterceptor(),
			server.AuthUnaryIntercepter(),
		),
		trace.GRPCServerStatsHandler(),
	)

	metric.InitPrometheus(svr, "")

	basemsgdispatcherServer, err := server.NewBasemsgdispatcherServer(&conf)
	if err != nil {
		log.Errorf("New server err:%v", err)
		return
	}

	// 注册gRPC服务实现
	pb_basemsgdispatcher.RegisterSServer(svr, basemsgdispatcherServer)
	// 导出监控数据
	//env.ServePromHttp(svr)

	var addr = "0.0.0.0:9000"
	// 启动服务
	ls, err := net.Listen("tcp", addr)
	if err != nil {
		log.Errorf("Listen error: %v\n", err)
		return
	}
	log.Infof("Started Application at %v, listen on %v", time.Now().Format("January 2, 2006 at 3:04pm (MST)"), addr)
	svr.Serve(ls)
}
