package grpccli

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"xim/baselib/server/env"

	"xim/baselib/grpccli"
	log "xim/baselib/logger"
	"xim/proto/api/basemsgtopic"
)

var basemsgtopicClient basemsgtopic.SClient

func NewBasemsgtopicClinet() error {
	var err error
	var conn *grpc.ClientConn
	if env.IsDev() {
		conn, err = grpccli.NewRemoteConn("basemsgtopic:9000")
	} else {
		conn, err = grpccli.NewLocalConn()
	}
	if err != nil {
		return err
	}
	basemsgtopicClient = basemsgtopic.NewSClient(conn)
	return nil
}

func StreamTopicUsers(ctx context.Context, platformId uint64, topic string) (basemsgtopic.S_StreamTopicUsersClient, error) {
	return basemsgtopicClient.StreamTopicUsers(ctx, &basemsgtopic.TopicUsersReq{PlatformId: platformId, Topic: topic})
}

func StreamTopicUsersWithConnInfo(ctx context.Context, platformId uint64, topic string) (basemsgtopic.S_StreamTopicUsersWithConnInfoClient, error) {
	return basemsgtopicClient.StreamTopicUsersWithConnInfo(ctx, &basemsgtopic.TopicUsersReq{PlatformId: platformId, Topic: topic})
}

func GetPlatformUserConn(ctx context.Context, platformId uint64, userId, deviceId string) ([]*basemsgtopic.PlatformUserConn, error) {
	req := &basemsgtopic.GetPlatformUserConnReq{
		PlatformId: platformId,
		UserId:     userId,
		DeviceId:   deviceId,
	}
	rsp, err := basemsgtopicClient.GetPlatformUserConn(ctx, req)
	defer func() {
		if err == nil {
			log.Infof("GetPlatformUserConn req %+v rsp %+v", req, rsp)
		} else {
			log.Errorf("GetPlatformUserConn req %+v error %v", req, err)
		}
	}()
	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	}
	if err != nil {
		return nil, err
	}
	if rsp.Data == nil {
		return nil, nil
	}
	return rsp.Data.Conns, nil
}
