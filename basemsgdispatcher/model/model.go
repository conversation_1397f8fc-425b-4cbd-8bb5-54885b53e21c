package model

import (
	"xim/basemsgdispatcher/config"
	"xim/basemsgdispatcher/model/brokerwatcher"
	"xim/basemsgdispatcher/model/grpccli"
)

// BasemsgdispatcherModel 数据模型层
// 封装与业务逻辑相关的数据以及对数据的处理方法，包括内部数据模型、数据库、缓存、消息通道、grpc服务、PHP服务等等
// 按来源、按业务将 Model 分割成不同的文件以利维护
type BasemsgdispatcherModel struct {
}

// NewBasemsgdispatcherModel 创建basemsgdispatcher model
func NewBasemsgdispatcherModel(conf *config.Config) error {
	if err := grpccli.NewClients(); err != nil {
		return err
	}
	if err := brokerwatcher.NewBrokerWatcher(conf.Broker); err != nil {
		return err
	}
	return nil
}
