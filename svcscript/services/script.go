package services

import (
	"context"
	"fmt"
	"math/rand/v2"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/config"

	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/mq/event/pusher"

	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/cache"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"

	"golang.org/x/sync/errgroup"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/model"
)

// ScriptServiceInterface 剧本服务接口
type ScriptServiceInterface interface {
	CreateScript(ctx context.Context, req *svcscript.CreateScriptReq) (int64, error)
	GetScriptList(ctx context.Context, req *svcscript.GetScriptListReq) (*svcscript.GetScriptListResp, error)
	GetScriptDetail(ctx context.Context, scriptID int64, userID int64) (*svcscript.Script, error)
	Search(ctx context.Context, req *svcscript.SearchReq) (*svcscript.SearchRespData, error)
	ShareScript(ctx context.Context, scriptID int64, userID int64, platform string) error
	ConvertToProtoScript(ctx context.Context, script *model.Script, userID int64) (*svcscript.Script, error)
	ReviewScriptCallback(ctx context.Context, req *svcscript.ReviewCallbackReq) (*common.SvcCommonResp, error)
	NotifyScript(ctx context.Context, script *model.Script, scriptId ...int64)

	// 管理后台方法
	DeleteScript(ctx context.Context, scriptID int64) error
	AuditScript(ctx context.Context, req *svcscript.AuditScriptReq) error
	GenerateScriptLines(ctx context.Context, characterIds []int64) ([]*svcscript.Line, map[int64]*svcscript.Character, error)
}

// ScriptService 剧本服务
type ScriptService struct {
	baseModel                model.BaseModelInterface
	scriptModel              model.ScriptModelInterface
	scriptCharacterModel     model.ScriptCharacterRelationModelInterface
	lineModel                model.LineModelInterface
	topicModel               model.TopicModelInterface
	topicService             TopicServiceInterface
	scriptTopicRelationModel model.ScriptTopicRelationModelInterface
	likeModel                model.LikeModelInterface
	dubbingModel             model.DubbingModelInterface
	commentModel             model.CommentModelInterface
	scriptShareModel         model.ScriptShareModelInterface
	characterModel           model.CharacterModelInterface
	characterAssetModel      model.CharacterAssetModelInterface
	userStatsModel           model.UserStatsModelInterface
	coverModel               model.CoverModelInterface

	characterService CharacterServiceInterface
	commentService   CommentServiceInterface
	dubbingService   DubbingServiceInterface
	eventPusher      pusher.EventScriptPushInterface
	limiter          cache.RateLimiterInterface
}

// NewScriptService 创建剧本服务实例
func NewScriptService(
	baseModel model.BaseModelInterface,
	scriptModel model.ScriptModelInterface,
	scriptCharacterModel model.ScriptCharacterRelationModelInterface,
	lineModel model.LineModelInterface,
	topicModel model.TopicModelInterface,
	topicService TopicServiceInterface,
	scriptTopicRelationModel model.ScriptTopicRelationModelInterface,
	likeModel model.LikeModelInterface,
	dubbingModel model.DubbingModelInterface,
	commentModel model.CommentModelInterface,
	scriptShareModel model.ScriptShareModelInterface,
	characterModel model.CharacterModelInterface,
	characterAssetModel model.CharacterAssetModelInterface,
	userStatsModel model.UserStatsModelInterface,
	coverModel model.CoverModelInterface,
	characterService CharacterServiceInterface,
	commentService CommentServiceInterface,
	dubbingService DubbingServiceInterface,
	eventPusher pusher.EventScriptPushInterface,
	limiter cache.RateLimiterInterface,
) ScriptServiceInterface {
	return &ScriptService{
		baseModel:                baseModel,
		scriptModel:              scriptModel,
		scriptCharacterModel:     scriptCharacterModel,
		lineModel:                lineModel,
		topicModel:               topicModel,
		topicService:             topicService,
		scriptTopicRelationModel: scriptTopicRelationModel,
		likeModel:                likeModel,
		dubbingModel:             dubbingModel,
		commentModel:             commentModel,
		scriptShareModel:         scriptShareModel,
		characterModel:           characterModel,
		characterAssetModel:      characterAssetModel,
		userStatsModel:           userStatsModel,
		coverModel:               coverModel,
		characterService:         characterService,
		commentService:           commentService,
		dubbingService:           dubbingService,
		eventPusher:              eventPusher,
		limiter:                  limiter,
	}
}

// CreateScript 创建剧本
func (s *ScriptService) CreateScript(ctx context.Context, req *svcscript.CreateScriptReq) (int64, error) {
	// 频控：每用户每分钟最多5次
	if err := s.limiter.CreateScriptLimiter(ctx, req.AuthorId); err != nil {
		return 0, err
	}

	if len(req.CharacterAssetIds) == 0 {
		return 0, errcode.ErrorParam
	}

	// 获取并检查角色/角色素材、台词数
	var characterIds []int64
	var characterLinesMap = make(map[int64]int32, len(req.CharacterAssetIds))
	var finalCharAssetMap = make(map[int64]*model.CharacterAsset, len(req.CharacterAssetIds)) //map[characterId]*model.CharacterAsset

	for _, ca := range req.CharacterAssetIds {
		if ca.CharacterId == 0 {
			logger.Errorf("CreateScript character id is zero")
			return 0, errcode.ErrorParam
		}
		characterIds = append(characterIds, ca.CharacterId)
		characterLinesMap[ca.CharacterId] = 0
	}
	if len(characterIds) == 0 {
		return 0, errcode.ErrorParam
	}

	// map[角色id]map[角色资源id]角色资源
	var characterAssetMap = make(map[int64]map[int64]*model.CharacterAsset, len(req.CharacterAssetIds))
	characterAssetsData, err := s.characterAssetModel.GetAssetsByCharacterIDs(ctx, characterIds, true)
	if err != nil {
		logger.Errorf("GetAssetsByCharacterIDs error: %v", err)
		return 0, err
	}

	// 检查所有请求的角色是否都有配置资源
	for _, characterId := range characterIds {
		assets, exists := characterAssetsData[characterId]
		if !exists || len(assets) == 0 {
			logger.Errorf("character asset is empty, characterId:%v", characterId)
			return 0, errcode.ErrorParam.WithMessage(fmt.Sprintf("角色ID %d 没有配置资源包", characterId))
		}
		assetMap := make(map[int64]*model.CharacterAsset, len(assets))
		for _, asset := range assets {
			assetMap[asset.ID] = asset
		}
		characterAssetMap[characterId] = assetMap
	}

	for _, ca := range req.CharacterAssetIds {
		var asset *model.CharacterAsset
		if ca.CharacterAssetId == 0 {
			//如果没传，则从角色资源包中随机一个
			asset = characterAssetsData[ca.CharacterId][rand.Int64N(int64(len(characterAssetsData[ca.CharacterId])))]
			ca.CharacterAssetId = asset.ID
		} else {
			if v, ok := characterAssetMap[ca.CharacterId][ca.CharacterAssetId]; !ok {
				logger.Errorf("character asset id params is invalid, characterId:%v, assetId:%v", ca.CharacterId, ca.CharacterAssetId)
				return 0, errcode.ErrorParam.WithMessage("角色与资源包不匹配，可能角色未预设导致，请检查")
			} else {
				asset = v
			}
		}
		finalCharAssetMap[ca.CharacterId] = asset
	}
	characterAssetsData = nil

	// 准备台词数据
	lines := make([]*model.Line, len(req.Lines))
	// 用于记录台词中实际使用的角色ID，用于过滤未使用的角色
	usedCharacterIds := make(map[int64]bool)

	for i, line := range req.Lines {
		// 初始化台词
		lineObj := &model.Line{
			Content:     line.Content,
			Sort:        int32(i + 1),
			CharacterID: line.CharacterId,
			CreatedAt:   util.NowTimeMillis(),
			Status:      int32(svcscript.Status_STATUS_ACTIVE),
		}

		if lineObj.CharacterID <= 0 {
			logger.Errorf("CreateScript line obj err: line character id is empty")
			return 0, errcode.ErrorParam
		}
		if _, ok := characterAssetMap[lineObj.CharacterID]; !ok {
			logger.Errorf("CreateScript line obj err: line character id not in character map")
			return 0, errcode.ErrorParam
		}

		// 记录台词中实际使用的角色ID
		usedCharacterIds[lineObj.CharacterID] = true

		// 如果没有指定资源包ID，则随机资源包
		var asset *model.CharacterAsset
		if line.CharacterAssetId == 0 {
			asset = finalCharAssetMap[lineObj.CharacterID]
		} else {
			if v, ok := characterAssetMap[lineObj.CharacterID][line.CharacterAssetId]; !ok {
				logger.Errorf("character asset id params is invalid, characterId:%v, assetId:%v", lineObj.CharacterID, line.CharacterAssetId)
				return 0, errcode.ErrorParam
			} else {
				asset = v
			}
		}
		if asset != nil {
			lineObj.CharacterAssetID = asset.ID
			lineObj.BackgroundUrl = asset.BackgroundURL
		} else {
			logger.Errorf("CreateScript get asset failed: %v", lineObj.CharacterID)
			return 0, errcode.ErrCharacterAssetNotFound
		}
		lines[i] = lineObj

		// 角色台词数
		if _, ok := characterLinesMap[line.CharacterId]; ok {
			characterLinesMap[line.CharacterId] += 1
		}
	}

	// 将话题名称转换为话题ID，不存在则创建
	topicIDs := make([]int64, 0, len(req.TopicNames))
	for _, topicName := range req.TopicNames {
		if topicName == "" {
			continue
		}

		// 根据话题名称查找或创建话题
		topicID, err := s.topicService.GetOrCreateTopicByName(ctx, topicName)
		if err != nil {
			logger.Errorf("Get or create topic error: %v, name: %s", err, topicName)
			continue
		}

		topicIDs = append(topicIDs, topicID)
	}

	// 使用事务创建剧本及其关联数据
	scriptID, err := s.baseModel.CreateWithTransaction(ctx, func(tx *gorm.DB) (int64, error) {
		// 1. 创建剧本记录
		script := &model.Script{
			Title:          req.Title,
			Cover:          util.ExtractPath(req.Cover),
			AuthorID:       req.AuthorId,
			CreationUserId: uint(req.CreationUserId),
			BGMUrl:         util.ExtractPath(req.BgmUrl),
			BGMDuration:    req.BgmDuration,
			ThemeColor:     req.ThemeColor,
			Score:          0,
			Sort:           0,
			PublishStatus:  svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED,
			Status:         svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL,
			ReviewStatus:   svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED,
			CreatedAt:      util.NowTimeMillis(),
			UpdatedAt:      util.NowTimeMillis(),
		}
		// 获取主题色
		imgUrl := script.GetFullCover()
		if imgUrl != "" {
			options := util.DefaultThemeColorOptions()
			themeColor, colorInfo, err := util.ExtractThemeColor(imgUrl, options)
			if err != nil {
				logger.Warnf("提取主题色失败: %v", err)
			} else {
				script.ThemeColor = themeColor
				script.ColorInfo = util.JsonStr(colorInfo)
				logger.Infof("成功提取主题色: %s", themeColor)
			}
		}
		if err := s.scriptModel.CreateScriptTx(ctx, tx, script); err != nil {
			logger.Errorf("CreateScript error: %v", err)
			return 0, err
		}

		// 2. 创建台词
		for i := range lines {
			lines[i].ScriptID = script.ID
		}
		if err := s.lineModel.BatchCreateLinesTx(ctx, tx, lines); err != nil {
			logger.Errorf("Create lines error: %v", err)
			return 0, err
		}

		// 3. 创建话题关联
		if len(topicIDs) > 0 {
			relations := make([]*model.ScriptTopicRelation, len(topicIDs))
			for i, topicID := range topicIDs {
				relations[i] = &model.ScriptTopicRelation{
					ScriptID:  script.ID,
					TopicID:   topicID,
					CreatedAt: util.NowTimeMillis(),
				}
			}
			if err := s.scriptTopicRelationModel.BatchCreateScriptTopicRelationsTx(ctx, tx, relations); err != nil {
				logger.Errorf("Create script topic relations error: %v", err)
				return 0, err
			}
		}

		// 4. 创建角色关联 - 只为台词中实际使用的角色创建关联
		characters := make([]*model.ScriptCharacterRelation, 0, len(usedCharacterIds))
		for characterId := range usedCharacterIds {
			// 确保角色在finalCharAssetMap中存在（理论上应该存在，因为前面已经验证过）
			asset, exists := finalCharAssetMap[characterId]
			if !exists {
				logger.Infof("Character %d used in lines but not found in finalCharAssetMap", characterId)
				continue
			}

			characters = append(characters, &model.ScriptCharacterRelation{
				ScriptID:         script.ID,
				CharacterID:      characterId,
				CharacterAssetID: asset.ID,
				LineCount:        characterLinesMap[characterId],
				CreatedAt:        util.NowTimeMillis(),
			})
		}
		if err := s.scriptCharacterModel.BatchCreateScriptCharactersTx(ctx, tx, characters); err != nil {
			logger.Errorf("Create script characters error: %v", err)
			return 0, err
		}

		return script.ID, nil
	})
	if err != nil {
		logger.Errorf("Create script with transaction error: %v", err)
		return 0, err
	}

	// 剧本统计 event
	if err = s.eventPusher.PushScriptCreateEvent(ctx, scriptID, req.AuthorId); err != nil {
		logger.Errorf("Create script event error: %v", err)
	}

	// 剧本审核事件
	cover := util.ExtractPath(req.Cover)
	err = s.eventPusher.PushReviewScriptEvent(ctx, scriptID, req.AuthorId, req.TopicNames, cover, req.Title)
	if err != nil {
		logger.Errorf("Create script review event error: %v", err)
	}

	// 剧本创建指标上报
	if err := metric.ReportPv("script.create.count", 1, nil); err != nil {
		logger.Errorf("Report script create metric error: %v", err)
	}

	// tts event
	derivedCtx := context.WithoutCancel(ctx)
	util.SafeGo(func() {
		logger.Infof("tts event start, scriptID:%v", scriptID)
		lines, err = s.lineModel.GetLinesByScriptID(derivedCtx, scriptID)
		if err != nil {
			logger.Errorf("Get lines by script id error: %v, scriptID:%v", err, scriptID)
			return
		}

		if len(lines) == 0 {
			logger.Errorf("Get lines by script id is empty error: %v, scriptID:%v", err, scriptID)
			return
		}
		reqItems := []*vcxxjob.AsyncTTSReq{}
		for _, line := range lines {
			asset := characterAssetMap[line.CharacterID][line.CharacterAssetID]
			reqItems = append(reqItems, &vcxxjob.AsyncTTSReq{
				ReferAudioUrl:  asset.SampleAudio,
				ReferAudioText: asset.SampleAudioText,
				Text:           line.Content,
				TtsEngine:      asset.TtsEngine,
				Source:         0,
				Ext: util.JsonStr(map[string]interface{}{
					"line_id":            line.ID,
					"character_id":       line.CharacterID,
					"character_asset_id": line.CharacterAssetID,
					"script_id":          line.ScriptID,
				}),
				OutId:  fmt.Sprintf("%d", line.ID),
				UseVc:  true,
				UseRvc: asset.ReferenceAudioUseRvc == 1,
			})
		}
		if len(reqItems) > 0 {
			resp, tmpErr := svcmgr.VcxxjobClient().AsyncBatchTTS(derivedCtx, &vcxxjob.AsyncBatchTTSReq{
				List: reqItems,
			})
			if tmpErr != nil {
				logger.Errorf("AsyncBatchTTS error: %v", tmpErr)
			}
			logger.Infof("AsyncBatchTTS resp: %+v", resp)
		}
	})

	return scriptID, nil
}

// GetScriptList 获取剧本列表
func (s *ScriptService) GetScriptList(ctx context.Context, req *svcscript.GetScriptListReq) (*svcscript.GetScriptListResp, error) {
	resp := &svcscript.GetScriptListResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
		Data: &svcscript.GetScriptListRespData{
			Scripts:      []*svcscript.Script{},
			ScriptTotal:  0,
			DubbingTotal: 0,
		},
	}

	if req.Page <= 0 {
		req.Page = consts.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = consts.DefaultMinPageSize
	}

	var scripts []*model.Script
	var dubbingTotal int64
	var total int64
	var err error

	// 根据不同的列表类型调用不同的方法
	switch req.TopicId {
	case consts.TopicRecommend:
		// 推荐剧本列表：按照分数（editor_score）和创建时间排序
		scripts, total, dubbingTotal, err = s.scriptModel.ListScripts(ctx, req.UserId, req.TopicId, req.IsAggregation, req.Page, req.PageSize)
	case consts.TopicFollow:
		if req.UserId == 0 {
			return resp, nil
		}

		followedUserIDs, err := getFollowedUserIDs(ctx, req.UserId)
		if err != nil {
			logger.Errorf("getFollowedUserIDs error: %v, userID: %d", err, req.UserId)
			return resp, nil
		}
		if len(followedUserIDs) == 0 {
			return resp, nil
		}

		// 已关注用户发布的剧本列表
		scripts, total, dubbingTotal, err = s.scriptModel.GetScriptsByAuthorIDs(ctx, followedUserIDs, req.Page, req.PageSize)
		if err != nil {
			return nil, err
		}
	default:
		// 话题剧本列表
		scripts, total, dubbingTotal, err = s.scriptModel.ListScripts(ctx, req.UserId, req.TopicId, req.IsAggregation, req.Page, req.PageSize)
	}

	if err != nil {
		return nil, err
	}

	if len(scripts) == 0 {
		return resp, nil
	}

	// 使用批量转换方法处理所有剧本，加载完整信息
	pbScripts, err := s.batchConvertToProtoScripts(ctx, scripts, req.UserId, true)
	if err != nil {
		return nil, err
	}

	// 增加话题浏览数
	if err = s.eventPusher.PushScriptTopicViewEvent(ctx, req.TopicId, req.TopicId, req.UserId); err != nil {
		logger.Errorf("dispatchScriptTopicViewEvent error: %v", err)
	}

	return &svcscript.GetScriptListResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
		Data: &svcscript.GetScriptListRespData{
			Scripts:      pbScripts,
			ScriptTotal:  total,
			DubbingTotal: dubbingTotal,
		},
	}, nil
}

// batchConvertToProtoScripts 批量转换剧本列表为proto格式，并发获取相关数据
func (s *ScriptService) batchConvertToProtoScripts(ctx context.Context, scripts []*model.Script, userID int64, loadOptions ...bool) ([]*svcscript.Script, error) {
	if len(scripts) == 0 {
		return []*svcscript.Script{}, nil
	}

	// 默认加载完整信息
	loadFullInfo := true
	if len(loadOptions) > 0 {
		loadFullInfo = loadOptions[0]
	}

	// 收集所有剧本ID，用于批量查询
	scriptIDs := make([]int64, len(scripts))

	// 创建结果数组
	pbScripts := make([]*svcscript.Script, len(scripts))

	// 基础信息转换
	for i, script := range scripts {
		scriptIDs[i] = script.ID
		pbScripts[i] = &svcscript.Script{
			Id:            script.ID,
			Title:         script.Title,
			Cover:         script.GetFullCover(),
			BgmUrl:        script.GetFullBGM(),
			BgmDuration:   script.BGMDuration,
			ThemeColor:    script.ThemeColor,
			AuthorId:      script.AuthorID,
			Score:         script.Score,
			Sort:          script.Sort,
			PublishStatus: script.PublishStatus,
			Status:        script.Status,
			CreatedAt:     script.CreatedAt,
			UpdatedAt:     script.UpdatedAt,
			ShareCount:    script.ShareCount,
			DubbingCount:  script.DubbingCount,
			CommentCount:  script.CommentCount,
			LikeCount:     script.LikeCount,
			ReviewStatus:  script.ReviewStatus,
		}
	}

	// 部分信息加载
	// 如果不需要加载完整信息，只赋值作者信息，减少查询消耗
	if !loadFullInfo {
		var authorInfos map[int64]*svcscript.UserInfo
		var scriptTopics map[int64][]*model.Topic
		var userLikedStatus map[int64]bool

		// 收集所有作者ID
		authorIDs := make([]int64, 0, len(scripts))
		authorIDMap := make(map[int64]bool)
		for _, script := range scripts {
			if !authorIDMap[script.AuthorID] {
				authorIDs = append(authorIDs, script.AuthorID)
				authorIDMap[script.AuthorID] = true
			}
		}

		// 批量获取作者信息
		var err error
		authorInfos, err = batchGetUserInfo(ctx, userID, authorIDs)
		if err != nil {
			return pbScripts, nil
		}

		// 批量获取剧本话题
		scriptTopics, err = s.scriptModel.GetScriptTopics(ctx, scriptIDs)
		userLikedStatus, err = s.likeModel.BatchIsLiked(ctx, userID, scriptIDs, svcscript.LikeType_LIKE_TYPE_SCRIPT)

		for i, script := range scripts {
			// 设置作者信息
			if authorInfos != nil {
				if author, ok := authorInfos[script.AuthorID]; ok {
					pbScripts[i].Author = author
				}
			}

			// 用户点赞状态
			if userLikedStatus != nil {
				if status, ok := userLikedStatus[script.ID]; ok {
					pbScripts[i].IsLiked = status
				}
			}

			// 设置话题信息
			if scriptTopics != nil {
				if topics, ok := scriptTopics[script.ID]; ok {
					protoTopics := make([]*svcscript.Topic, 0, len(topics))
					for _, topic := range topics {
						protoTopic := &svcscript.Topic{
							Id:   topic.ID,
							Name: topic.Name,
							Sort: topic.Sort,
						}
						protoTopics = append(protoTopics, protoTopic)
					}
					pbScripts[i].Topics = protoTopics
				}
			}
		}

		return pbScripts, nil
	}

	// 以下为完整信息加载
	// 创建错误组和新的上下文
	eg, egCtx := errgroup.WithContext(ctx)

	// 存储批量查询结果的变量
	var (
		scriptTopics     map[int64][]*model.Topic
		scriptLines      map[int64][]*model.Line
		scriptCharacters map[int64][]*svcscript.Character
		userLikedStatus  map[int64]bool
		userSharedStatus map[int64]bool
		authorInfos      map[int64]*svcscript.UserInfo
	)

	// 并发获取话题
	eg.Go(func() error {
		var err error
		scriptTopics, err = s.scriptModel.GetScriptTopics(egCtx, scriptIDs)
		return err
	})

	// 并发获取台词
	eg.Go(func() error {
		var err error
		scriptLines, err = s.lineModel.BatchGetLinesByScriptIDs(egCtx, scriptIDs)
		return err
	})

	// 并发获取角色和角色关联
	eg.Go(func() error {
		var charResultMap = make(map[int64][]*svcscript.Character)

		// 批量获取所有剧本的角色信息和资源（保持台词数量排序）
		charactersMap, assetsMap, err := s.scriptCharacterModel.BatchGetCharactersByScriptIDsOrdered(egCtx, scriptIDs)
		if err != nil {
			return err
		}

		// 处理每个剧本的角色
		for _, script := range scripts {
			scriptID := script.ID
			characters := charactersMap[scriptID]
			assets := assetsMap[scriptID]

			// 转换为proto格式，保持台词数量倒序
			var chars []*svcscript.Character
			for _, character := range characters {
				asset, ok := assets[character.ID]
				if !ok {
					continue
				}
				protoChar := s.characterService.ConvertToProtoCharacter(character, asset)
				chars = append(chars, protoChar)
			}
			charResultMap[scriptID] = chars
		}

		scriptCharacters = charResultMap
		return nil
	})

	// 如果有登录用户，获取用户关联状态
	if userID > 0 {
		// 并发获取用户点赞状态
		eg.Go(func() error {
			var err error
			userLikedStatus, err = s.likeModel.BatchIsLiked(egCtx, userID, scriptIDs, svcscript.LikeType_LIKE_TYPE_SCRIPT)
			if err != nil {
				return err
			}
			return nil
		})

		// 并发获取用户分享状态
		eg.Go(func() error {
			var err error
			userSharedStatus, err = s.scriptModel.BatchCheckUserShared(egCtx, userID, scriptIDs)
			return err
		})
	}

	// 并发获取作者信息
	eg.Go(func() error {
		// 收集所有作者ID
		authorIDs := make([]int64, 0, len(scripts))
		authorIDMap := make(map[int64]bool)
		for _, script := range scripts {
			if !authorIDMap[script.AuthorID] {
				authorIDs = append(authorIDs, script.AuthorID)
				authorIDMap[script.AuthorID] = true
			}
		}

		// 批量获取作者信息
		var err error
		authorInfos, err = batchGetUserInfo(ctx, userID, authorIDs)
		if err != nil {
			return err
		}
		return nil
	})

	// 等待所有并发请求完成
	if err := eg.Wait(); err != nil {
		logger.Errorf("Failed to fetch script details: %v", err)
		return nil, err
	}

	// 组装剧本信息
	for i, script := range scripts {
		// 设置作者信息
		if authorInfos != nil {
			if author, ok := authorInfos[script.AuthorID]; ok {
				pbScripts[i].Author = author
			}
		}

		// 设置话题信息
		if scriptTopics != nil {
			if topics, ok := scriptTopics[script.ID]; ok {
				protoTopics := make([]*svcscript.Topic, 0, len(topics))
				for _, topic := range topics {
					protoTopic := &svcscript.Topic{
						Id:   topic.ID,
						Name: topic.Name,
						Sort: topic.Sort,
					}
					protoTopics = append(protoTopics, protoTopic)
				}
				pbScripts[i].Topics = protoTopics
			}
		}

		// 设置台词信息
		if scriptLines != nil {
			if lines, ok := scriptLines[script.ID]; ok {
				protoLines := make([]*svcscript.Line, 0, len(lines))
				for _, line := range lines {
					protoLine := &svcscript.Line{
						Id:               line.ID,
						ScriptId:         line.ScriptID,
						Content:          line.Content,
						Sort:             line.Sort,
						CreatedAt:        line.CreatedAt,
						CharacterId:      line.CharacterID,
						CharacterAssetId: line.CharacterAssetID,
						BackgroundUrl:    line.BackgroundUrl,
						DubbingCount:     line.DubbingCount,
						DubbingDuration:  line.DubbingDuration,
					}
					protoLines = append(protoLines, protoLine)
				}
				pbScripts[i].Lines = protoLines
			}
		}

		// 设置角色信息
		if scriptCharacters != nil {
			if chars, ok := scriptCharacters[script.ID]; ok {
				pbScripts[i].Characters = chars
			}
		}

		// 用户点赞状态
		if userLikedStatus != nil {
			if status, ok := userLikedStatus[script.ID]; ok {
				pbScripts[i].IsLiked = status
			}
		}

		// 用户分享状态
		if userSharedStatus != nil {
			if status, ok := userSharedStatus[script.ID]; ok {
				pbScripts[i].IsShared = status
			}
		}
	}

	return pbScripts, nil
}

// GetScriptDetail 获取剧本详情
func (s *ScriptService) GetScriptDetail(ctx context.Context, scriptID int64, userID int64) (*svcscript.Script, error) {
	script, err := s.scriptModel.GetScriptByID(ctx, scriptID)
	if err != nil {
		logger.Errorf("GetScriptByID error: %v", err)
		return nil, err
	}

	if script == nil {
		return nil, nil
	}

	// 增加剧本浏览量
	if err := s.eventPusher.PushScriptViewEvent(ctx, scriptID, userID); err != nil {
		logger.Errorf("IncrementViewCount error: %v", err)
	}

	return s.ConvertToProtoScript(ctx, script, userID)
}

// Search 搜索
func (s *ScriptService) Search(ctx context.Context, req *svcscript.SearchReq) (*svcscript.SearchRespData, error) {
	respData := &svcscript.SearchRespData{}

	if req.Page <= 0 {
		req.Page = consts.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = consts.DefaultPageSize
	}

	switch req.SearchType {
	case svcscript.SearchType_SEARCH_TYPE_SCRIPT:
		// 搜索剧本
		scripts, total, err := s.scriptModel.SearchScripts(ctx, req.UserId, req.Keyword, req.Page, req.PageSize)
		if err != nil {
			logger.Errorf("SearchScripts error: %v", err)
			return nil, err
		}

		// 转换为proto格式并填充附加信息
		protoScripts := make([]*svcscript.Script, 0, len(scripts))
		protoScripts, err = s.batchConvertToProtoScripts(ctx, scripts, req.UserId, false)
		if err != nil {
			logger.Errorf("BatchConvertToProtoScripts error: %v", err)
			return nil, err
		}

		respData.Scripts = protoScripts
		respData.Total = total

	case svcscript.SearchType_SEARCH_TYPE_USER:
		// 搜索用户
		list, total, err := s.userStatsModel.SearchUsers(ctx, req.Keyword, req.Page, req.PageSize)
		if err != nil {
			logger.Errorf("SearchTopics error: %v", err)
			return nil, err
		}
		if len(list) == 0 {
			return respData, nil
		}
		userIds := make([]int64, 0, len(list))
		for _, user := range list {
			userIds = append(userIds, user.UserID)
		}

		userInfos, err := batchGetUserInfo(ctx, req.UserId, userIds)
		if err != nil {
			logger.Errorf("BatchGetUserInfo error: %v", err)
			return nil, err
		}

		// 转换为proto格式
		protoUsers := make([]*svcscript.UserInfo, 0, len(list))
		for _, user := range list {
			userInfo, ok := userInfos[user.UserID]
			if !ok {
				continue
			}
			if userInfo.IsDeleted {
				logger.Infof("用户已注销, 不返回。 userID:%v", user.UserID)
				continue
			}
			protoUser := &svcscript.UserInfo{
				UserId:           userInfo.UserId,
				Nickname:         userInfo.Nickname,
				Avatar:           userInfo.Avatar,
				IsPremiumCreator: userInfo.IsPremiumCreator,
				FansCount:        user.FansCount,
				ScriptCount:      user.ScriptCount,
				IsFollowed:       userInfo.IsFollowed,
				IsFollowing:      userInfo.IsFollowing,
				IsDeleted:        userInfo.IsDeleted,
			}
			protoUsers = append(protoUsers, protoUser)
		}

		respData.Users = protoUsers
		respData.Total = total

	case svcscript.SearchType_SEARCH_TYPE_TOPIC:
		// 搜索话题
		topics, total, err := s.topicModel.SearchTopics(ctx, req.Keyword, req.Page, req.PageSize)
		if err != nil {
			logger.Errorf("SearchTopics error: %v", err)
			return nil, err
		}

		// 转换为proto格式
		protoTopics := make([]*svcscript.Topic, 0, len(topics))
		for _, topic := range topics {
			protoTopic := &svcscript.Topic{
				Id:           topic.ID,
				Name:         topic.Name,
				Sort:         topic.Sort,
				ScriptCount:  topic.ScriptCount,
				DubbingCount: topic.DubbingCount,
				CreatedAt:    topic.CreatedAt,
			}
			protoTopics = append(protoTopics, protoTopic)
		}

		respData.Topics = protoTopics
		respData.Total = total

	}

	return respData, nil
}

// ShareScript 分享剧本
func (s *ScriptService) ShareScript(ctx context.Context, scriptID int64, userID int64, platform string) error {
	share := &model.ScriptShare{
		ScriptID:  scriptID,
		UserID:    userID,
		Platform:  platform,
		CreatedAt: util.NowTimeMillis(),
	}

	err := s.scriptShareModel.CreateScriptShare(ctx, share)
	if err != nil {
		logger.Errorf("CreateScriptShare error: %v", err)
		return err
	}

	return nil
}

// convertToProtoScript 转换为proto格式的剧本
func (s *ScriptService) ConvertToProtoScript(ctx context.Context, script *model.Script, userID int64) (*svcscript.Script, error) {
	// 对单个剧本的处理可以通过批量处理函数实现
	scripts, err := s.batchConvertToProtoScripts(ctx, []*model.Script{script}, userID)
	if err != nil {
		return nil, err
	}

	if len(scripts) == 0 {
		return nil, errcode.ErrScriptNotFound
	}

	return scripts[0], nil
}

// GenerateScriptLines 生成剧本台词
func (s *ScriptService) GenerateScriptLines(ctx context.Context, characterIds []int64) ([]*svcscript.Line, map[int64]*svcscript.Character, error) {
	// 检查角色ID是否为空
	if len(characterIds) == 0 {
		return nil, nil, errcode.ErrorParam
	}

	// 这里是Demo版本，实际应该调用AI服务生成
	characters, assetMap, err := s.characterModel.GetCharactersWithAssetsByCharIds(ctx, characterIds)
	if err != nil {
		logger.Errorf("GenerateScriptLines GetCharactersWithAssets error: %v", err)
	}
	if len(characters) == 0 {
		return nil, nil, errcode.ErrorParam
	}

	// 从配置中随机选择一组台词
	allLineGroups := config.GetDefaultLines()
	if len(allLineGroups) == 0 {
		return nil, nil, errcode.ErrConfigNotFound
	}

	// 随机选择一组台词
	randomGroupIndex := rand.Int64N(int64(len(allLineGroups)))
	lineContents := allLineGroups[randomGroupIndex]

	if len(lineContents) == 0 {
		return nil, nil, errcode.ErrConfigNotFound
	}

	// 双向循环绑定台词与角色，智能判断循环策略
	// 场景1：角色数 >= 台词数，循环使用台词，确保所有角色都有台词
	// 场景2：台词数 > 角色数，循环使用角色，确保所有台词都有角色

	characterCount := len(characterIds)
	lineCount := len(lineContents)

	// 确定最终生成的台词数量：取角色数和台词数的最大值
	maxCount := characterCount
	if lineCount > characterCount {
		maxCount = lineCount
	}

	lines := make([]*svcscript.Line, maxCount)

	for i := 0; i < maxCount; i++ {
		// 循环获取角色ID和台词内容
		characterIndex := i % characterCount
		contentIndex := i % lineCount

		characterId := characterIds[characterIndex]
		content := lineContents[contentIndex]

		// 确保角色存在于返回的map中
		character, exists := characters[characterId]
		if !exists {
			logger.Errorf("Character not found in result map: %d", characterId)
			return nil, nil, errcode.ErrCharacterNotFound
		}

		lines[i] = &svcscript.Line{
			Id:          int64(i + 1),
			Content:     content,
			Sort:        int32(i + 1),
			CreatedAt:   util.NowTimeMillis(),
			CharacterId: character.ID,
		}
	}

	var charactersList = make(map[int64]*svcscript.Character, len(characters))
	for _, character := range characters {
		assets := assetMap[character.ID]
		asset := assets[rand.Int64N(int64(len(assets)))]
		charactersList[character.ID] = s.characterService.ConvertToProtoCharacter(character, asset)
	}

	return lines, charactersList, nil
}

// 剧本审核
func (s *ScriptService) ReviewScriptCallback(ctx context.Context, req *svcscript.ReviewCallbackReq) (*common.SvcCommonResp, error) {
	resp := &common.SvcCommonResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
	}

	switch req.Type {
	case svcreview.ReviewBizType_ScriptDubbingVoice:
		dubbing, err := s.dubbingModel.GetDubbingByID(ctx, req.Id)
		if err != nil {
			logger.Errorf("ReviewDubbing error: %v", err)
			resp.Base = errcode.ErrDBQueryFailed.ToSvcBaseResp()
			return resp, err
		}

		if req.Result == svcreview.AuditResult_reject {
			dubbing.ReviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED
		} else {
			dubbing.ReviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED
			if !config.IsDubbingManualReviewEnabled() {
				dubbing.ReviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED
			}
		}
		dubbing.UpdatedAt = util.NowTimeMillis()
		if err := s.dubbingModel.UpdateDubbing(ctx, dubbing); err != nil {
			logger.Errorf("ReviewDubbing error: %v", err)
			resp.Base = errcode.ErrDBUpdateFailed.ToSvcBaseResp()
			return resp, err
		}

		// 通知
		// 如果机审通过 且 人审关闭，则发送通知，否则在人审通过时发送通知
		if req.Result != svcreview.AuditResult_reject && !config.IsDubbingManualReviewEnabled() {
			s.dubbingService.NotifyDubbing(ctx, dubbing)
		}

	case svcreview.ReviewBizType_ScriptCommentVoice:
		comment, err := s.commentModel.GetCommentByID(ctx, req.Id)
		if err != nil {
			logger.Errorf("ReviewDubbing error: %v", err)
			resp.Base = errcode.ErrDBQueryFailed.ToSvcBaseResp()
		}
		if req.Result == svcreview.AuditResult_reject {
			comment.ReviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED
		} else {
			comment.ReviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED
			if !config.IsDubbingManualReviewEnabled() {
				comment.ReviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED
			}
		}
		comment.UpdatedAt = util.NowTimeMillis()
		if err := s.commentModel.UpdateComment(ctx, comment); err != nil {
			logger.Errorf("ReviewDubbing error: %v", err)
			resp.Base = errcode.ErrDBUpdateFailed.ToSvcBaseResp()
			return resp, err
		}

		// 通知
		// 如果机审通过 且 人审关闭，则发送通知，否则在人审通过时发送通知
		if req.Result != svcreview.AuditResult_reject && !config.IsCommentManualReviewEnabled() {
			s.commentService.NotifyComment(ctx, comment)
		}
	}
	return resp, nil
}

// DeleteScript 管理员删除剧本
func (s *ScriptService) DeleteScript(ctx context.Context, scriptID int64) error {
	// 检查剧本是否存在
	_, err := s.scriptModel.GetScriptByID(ctx, scriptID)
	if err != nil {
		logger.Errorf("AdminDeleteScript GetScriptByID error: %v, scriptID: %d", err, scriptID)
		return err
	}

	// 执行删除
	err = s.scriptModel.DeleteScript(ctx, scriptID)
	if err != nil {
		logger.Errorf("AdminDeleteScript DeleteScript error: %v, scriptID: %d", err, scriptID)
		return err
	}

	return nil
}

// AuditScript 管理员审核剧本
func (s *ScriptService) AuditScript(ctx context.Context, req *svcscript.AuditScriptReq) error {
	// 检查剧本是否存在
	script, err := s.scriptModel.GetScriptByID(ctx, req.ScriptId)
	if err != nil {
		logger.Errorf("AdminAuditScript GetScriptByID error: %v, scriptID: %d", err, req.ScriptId)
		return err
	}

	// 更新审核状态
	script.ReviewStatus = req.ReviewStatus
	script.UpdatedAt = util.NowTimeMillis()

	// 执行更新
	err = s.scriptModel.UpdateScript(ctx, script)
	if err != nil {
		logger.Errorf("AdminAuditScript UpdateScript error: %v, scriptID: %d", err, req.ScriptId)
		return err
	}

	// 人审拒绝时通知
	if req.ReviewStatus == svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED ||
		req.ReviewStatus == svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED {
		// 发送通知
		s.NotifyScript(ctx, script, req.ScriptId)
	}

	return nil
}

func (s *ScriptService) NotifyScript(ctx context.Context, script *model.Script, scriptId ...int64) {
	var err error
	if len(scriptId) > 0 && scriptId[0] > 0 && script == nil {
		script, err = s.scriptModel.GetScriptByID(ctx, scriptId[0])
		if err != nil {
			logger.Errorf("GetScriptByID error: %v", err)
			return
		}
	}

	if script == nil {
		logger.Errorf("script is nil, scriptId:%d", scriptId)
		return
	}

	protoScript, err := s.ConvertToProtoScript(ctx, script, script.AuthorID)
	if err != nil {
		logger.Errorf("ConvertToProtoScript error: %v", err)
	}

	msgContent := map[string]any{
		"title": &svcchat.TemplateMsg{
			Tpl: "审核通知",
		},
		"content": &svcchat.TemplateMsg{
			Tpl:  fmt.Sprintf("您的剧本:【%s】未通过审核，可能包含违规内容或不适宜公开展示", script.Title),
			Url:  "",
			Type: svcchat.TemplateJumpType_jump_script,
		},
		"bgm_url":  alioss.FillImageUrl(script.Cover),
		"jump_url": "",
		"script":   protoScript,
	}

	msg := &basemsgtransfer.SendMsgReq{
		Msgs: []*basemsgtransfer.MsgData{
			{
				From:        consts.OfficialSecretaryUserid,
				To:          script.AuthorID,
				SessionType: int32(basemsgtransfer.SessionType_SessionTypeSystem),
				ContentType: int32(basemsgtransfer.ContentType_ReviewScript),
				Content:     util.JsonStr(msgContent),
				MsgFrom:     uint32(basemsgtransfer.MsgFromEnum_MsgFromIm),
				CreateTime:  util.NowTimeMillis(),
			},
		},
	}
	logger.Infof("script review notify msg=%+v", util.JsonStr(msg))
	vResp, tmpErr := svcmgr.BaseMsgTransferClient().SendMsg(ctx, msg)
	if tmpErr != nil || errcode.NotOk(vResp) {
		logger.Errorf("发送剧本审核通知失败:err=%+v vResp=%+v", tmpErr, util.JsonStr(vResp))
	}
}
