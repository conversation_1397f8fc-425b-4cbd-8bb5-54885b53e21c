package services

import (
	"context"
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"

	"golang.org/x/sync/errgroup"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/model"
)

// UserServiceInterface 用户服务接口
type UserServiceInterface interface {
	GetUserStats(ctx context.Context, userID int64) (*svcscript.GetUserStatsRespData, error)
	BatchGetUserStats(ctx context.Context, userIDs []int64) (map[int64]*svcscript.GetUserStatsRespData, error)
	GetUserScriptLists(ctx context.Context, req *svcscript.GetUserScriptListsReq) (*svcscript.GetUserScriptListsRespData, error)
}

// UserService 用户服务
type UserService struct {
	scriptModel          model.ScriptModelInterface
	dubbingModel         model.DubbingModelInterface
	dubbingRecordModel   model.DubbingRecordModelInterface
	likeModel            model.LikeModelInterface
	commentModel         model.CommentModelInterface
	lineModel            model.LineModelInterface
	characterModel       model.CharacterModelInterface
	characterAssetModel  model.CharacterAssetModelInterface
	topicModel           model.TopicModelInterface
	userStatsModel       model.UserStatsModelInterface
	scriptCharacterModel model.ScriptCharacterRelationModelInterface

	characterService CharacterServiceInterface
}

// NewUserService 创建用户服务实例
func NewUserService(
	scriptModel model.ScriptModelInterface,
	dubbingModel model.DubbingModelInterface,
	dubbingRecordModel model.DubbingRecordModelInterface,
	likeModel model.LikeModelInterface,
	commentModel model.CommentModelInterface,
	lineModel model.LineModelInterface,
	characterModel model.CharacterModelInterface,
	characterAssetModel model.CharacterAssetModelInterface,
	topicModel model.TopicModelInterface,
	userStatsModel model.UserStatsModelInterface,
	scriptCharacterModel model.ScriptCharacterRelationModelInterface,
	characterService CharacterServiceInterface,
) UserServiceInterface {
	return &UserService{
		scriptModel:          scriptModel,
		dubbingModel:         dubbingModel,
		dubbingRecordModel:   dubbingRecordModel,
		likeModel:            likeModel,
		commentModel:         commentModel,
		lineModel:            lineModel,
		characterModel:       characterModel,
		characterAssetModel:  characterAssetModel,
		topicModel:           topicModel,
		userStatsModel:       userStatsModel,
		scriptCharacterModel: scriptCharacterModel,
		characterService:     characterService,
	}
}

// GetUserStats 获取用户统计数据（点赞数和回声数）
func (s *UserService) GetUserStats(ctx context.Context, userID int64) (*svcscript.GetUserStatsRespData, error) {
	if userID <= 0 {
		return nil, errcode.ErrorParam
	}

	userInfo, err := getUserInfo(ctx, userID)
	if err != nil {
		return nil, errcode.ErrUserNotFound
	}

	// 注销用户不返回统计数据
	if userInfo.IsDeleted {
		return &svcscript.GetUserStatsRespData{
			ScriptLikes:  0,
			DubbingLikes: 0,
			CommentLikes: 0,
			EchoCount:    0,
			TotalLikes:   0,
			ScriptCount:  0,
		}, nil
	}

	stats, err := s.userStatsModel.GetUserStats(ctx, userID)
	if err != nil || stats == nil {
		logger.Errorf("获取用户统计数据失败: %v", err)
		return nil, errcode.ErrScriptUserStat
	}

	if stats.Nickname == "" {
		util.SafeGo(func() {
			childCtx := context.WithoutCancel(ctx)
			userInfo, err := getUserInfo(childCtx, userID)
			if err != nil {
				logger.Errorf("获取用户信息失败: %v", err)
			} else {
				stats.Nickname = userInfo.Nickname
				stats.NamePinyin = util.ConvertToPinyin(userInfo.Nickname)
				err = s.userStatsModel.UpdateUserStats(childCtx, stats)
				if err != nil {
					logger.Errorf("更新用户统计数据失败: %v", err)
				}
			}
		})
	}

	// 计算总点赞数
	totalLikes := stats.ScriptLikes + stats.DubbingLikes + stats.CommentLikes

	return &svcscript.GetUserStatsRespData{
		ScriptLikes:  stats.ScriptLikes,
		DubbingLikes: stats.DubbingLikes,
		CommentLikes: stats.CommentLikes,
		EchoCount:    stats.EchoCount,
		TotalLikes:   totalLikes,
		ScriptCount:  stats.ScriptCount,
	}, nil
}

// BatchGetUserStats 批量获取用户统计数据
func (s *UserService) BatchGetUserStats(ctx context.Context, userIDs []int64) (map[int64]*svcscript.GetUserStatsRespData, error) {
	if len(userIDs) == 0 {
		return make(map[int64]*svcscript.GetUserStatsRespData), nil
	}

	// 批量获取用户统计数据
	statsMap, err := s.userStatsModel.BatchGetUserStats(ctx, userIDs)
	if err != nil {
		logger.Errorf("批量获取用户统计数据失败: %v", err)
		return nil, errcode.ErrScriptUserStat
	}

	userInfoList, err := batchGetUserInfo(ctx, 0, userIDs)
	if err != nil {
		logger.Errorf("批量获取用户信息失败: %v", err)
	}

	// 转换为响应格式
	result := make(map[int64]*svcscript.GetUserStatsRespData)
	for userID, stats := range statsMap {
		if stats == nil {
			continue
		}

		if u, ok := userInfoList[userID]; ok && u.IsDeleted {
			// 注销用户不返回统计数据
			result[userID] = &svcscript.GetUserStatsRespData{
				ScriptLikes:  0,
				DubbingLikes: 0,
				CommentLikes: 0,
				EchoCount:    0,
				TotalLikes:   0,
				ScriptCount:  0,
			}
			continue
		}

		// 计算总点赞数
		totalLikes := stats.ScriptLikes + stats.DubbingLikes + stats.CommentLikes

		result[userID] = &svcscript.GetUserStatsRespData{
			ScriptLikes:  stats.ScriptLikes,
			DubbingLikes: stats.DubbingLikes,
			CommentLikes: stats.CommentLikes,
			EchoCount:    stats.EchoCount,
			TotalLikes:   totalLikes,
			ScriptCount:  stats.ScriptCount,
		}
	}

	return result, nil
}

// GetUserScriptLists 获取用户的剧本列表
func (s *UserService) GetUserScriptLists(ctx context.Context, req *svcscript.GetUserScriptListsReq) (*svcscript.GetUserScriptListsRespData, error) {
	userID := req.UserId
	listType := req.ListType
	page := req.Page
	pageSize := req.PageSize

	if page <= 0 {
		page = consts.DefaultPage
	}
	if pageSize <= 0 {
		pageSize = consts.DefaultPageSize
	}

	if userID <= 0 {
		return nil, errcode.ErrorParam
	}

	result := &svcscript.GetUserScriptListsRespData{}

	switch listType {
	case svcscript.GetUserScriptListsReq_LIST_TYPE_CREATED:
		// 获取用户创建的剧本
		scripts, total, err := s.scriptModel.GetScriptsByAuthorIDPaged(ctx, userID, page, pageSize)
		if err != nil {
			logger.Errorf("GetScriptsByAuthorIDPaged error: %v", err)
			return nil, errcode.ErrScriptQueryFailed
		}

		// 转换为proto格式，并补充相关信息
		protoScripts, err := s.convertScriptsToProto(ctx, scripts, userID)
		if err != nil {
			logger.Errorf("convertScriptsToProto error: %v", err)
			return nil, errcode.ErrScriptUserData
		}

		// 只设置创建的剧本列表，其他字段保持空
		result.CreatedScripts = protoScripts
		result.Total = total

	case svcscript.GetUserScriptListsReq_LIST_TYPE_VOICED:
		// 获取用户配音过的剧本
		voicedScriptInfo, total, err := s.getUserVoicedScripts(ctx, userID, page, pageSize)
		if err != nil {
			logger.Errorf("getUserVoicedScripts error: %v", err)
			return nil, errcode.ErrScriptUserData
		}

		// 只设置配音过的剧本列表，其他字段保持空
		result.VoicedScripts = voicedScriptInfo
		result.Total = total

	case svcscript.GetUserScriptListsReq_LIST_TYPE_LIKED:
		// 获取用户点赞过的剧本
		scriptIDs, total, err := s.getLikedScriptIDs(ctx, userID, page, pageSize)
		if err != nil {
			logger.Errorf("getLikedScriptIDs error: %v", err)
			return nil, errcode.ErrScriptUserData
		}

		// 获取剧本详情（对于用户自己的剧本不过滤状态）
		scripts, err := s.scriptModel.GetScriptsByIDsWithOwner(ctx, scriptIDs, userID)
		if err != nil {
			logger.Errorf("GetScriptsByIDsWithOwner error: %v", err)
			return nil, errcode.ErrScriptQueryFailed
		}

		// 转换为proto格式，并补充相关信息
		protoScripts, err := s.convertScriptsToProto(ctx, scripts, userID)
		if err != nil {
			logger.Errorf("convertScriptsToProto error: %v", err)
			return nil, errcode.ErrScriptUserData
		}

		// 对于点赞过的剧本，标记为已点赞
		for i := range protoScripts {
			protoScripts[i].IsLiked = true
		}

		// 只设置点赞过的剧本列表，其他字段保持空
		result.LikedScripts = protoScripts
		result.Total = total

	default:
		return nil, errcode.ErrorParam
	}

	return result, nil
}

// convertScriptsToProto 将剧本列表转换为proto格式
func (s *UserService) convertScriptsToProto(ctx context.Context, scripts []*model.Script, userID int64) ([]*svcscript.Script, error) {
	if len(scripts) == 0 {
		return []*svcscript.Script{}, nil
	}

	var currentUserID int64
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo != nil {
		currentUserID = authInfo.UserId
	}

	// 收集所有需要的ID
	scriptIDs := make([]int64, 0, len(scripts))
	authorIDs := make([]int64, 0, len(scripts))
	authorIDMap := make(map[int64]bool)

	for _, script := range scripts {
		scriptIDs = append(scriptIDs, script.ID)
		if !authorIDMap[script.AuthorID] {
			authorIDs = append(authorIDs, script.AuthorID)
			authorIDMap[script.AuthorID] = true
		}
	}

	// 并发获取所有需要的数据
	var (
		authorInfos      map[int64]*svcscript.UserInfo
		scriptTopics     map[int64][]*model.Topic
		userLiked        map[int64]bool
		scriptCharacters map[int64][]*svcscript.Character
	)

	eg, egCtx := errgroup.WithContext(context.WithoutCancel(ctx))

	// 1. 获取作者信息
	eg.Go(func() error {
		var err error
		authorInfos, err = batchGetUserInfo(egCtx, userID, authorIDs)
		return err
	})

	// 2. 获取剧本话题
	eg.Go(func() error {
		topics, err := s.scriptModel.GetScriptTopics(egCtx, scriptIDs)
		if err != nil {
			return err
		}
		scriptTopics = topics
		return nil
	})

	// 3. 并发获取角色和角色关联
	eg.Go(func() error {
		var charResultMap = make(map[int64][]*svcscript.Character)

		// 批量获取所有剧本的角色信息和资源（保持台词数量排序）
		charactersMap, assetsMap, err := s.scriptCharacterModel.BatchGetCharactersByScriptIDsOrdered(egCtx, scriptIDs)
		if err != nil {
			return err
		}

		// 处理每个剧本的角色
		for _, script := range scripts {
			scriptID := script.ID
			characters := charactersMap[scriptID]
			assets := assetsMap[scriptID]

			// 转换为proto格式，保持台词数量倒序
			var chars []*svcscript.Character
			for _, character := range characters {
				asset, ok := assets[character.ID]
				if !ok {
					continue
				}
				protoChar := s.characterService.ConvertToProtoCharacter(character, asset)
				chars = append(chars, protoChar)
			}
			charResultMap[scriptID] = chars
		}

		scriptCharacters = charResultMap
		return nil
	})

	// 4. 获取用户是否点赞
	if userID > 0 && currentUserID > 0 {
		// 11. 获取用户是否点赞
		eg.Go(func() error {
			liked, err := s.likeModel.BatchIsLiked(egCtx, currentUserID, scriptIDs, svcscript.LikeType_LIKE_TYPE_SCRIPT)
			if err != nil {
				return err
			}
			userLiked = liked
			return nil
		})
	}

	// 等待所有并发任务完成
	if err := eg.Wait(); err != nil {
		logger.Errorf("Failed to fetch script related data: %v", err)
		return nil, err
	}

	// 转换为proto格式
	var protoScripts []*svcscript.Script
	for _, script := range scripts {
		protoScript := &svcscript.Script{
			Id:            script.ID,
			Title:         script.Title,
			Cover:         script.GetFullCover(),
			AuthorId:      script.AuthorID,
			ReviewStatus:  script.ReviewStatus,
			Score:         script.Score,
			Sort:          script.Sort,
			PublishStatus: script.PublishStatus,
			Status:        script.Status,
			CreatedAt:     script.CreatedAt,
			UpdatedAt:     script.UpdatedAt,
			Author:        authorInfos[script.AuthorID],
			DubbingCount:  script.DubbingCount,
			LikeCount:     script.LikeCount,
		}

		// 设置话题
		if topics, ok := scriptTopics[script.ID]; ok {
			protoTopics := make([]*svcscript.Topic, 0, len(topics))
			for _, topic := range topics {
				protoTopics = append(protoTopics, &svcscript.Topic{
					Id:   topic.ID,
					Name: topic.Name,
				})
			}
			protoScript.Topics = protoTopics
		}

		// 设置角色信息
		if scriptCharacters != nil {
			if chars, ok := scriptCharacters[script.ID]; ok {
				protoScript.Characters = chars
			}
		}

		// 设置用户相关状态
		if userID > 0 {
			protoScript.IsLiked = userLiked[script.ID]
		}

		protoScripts = append(protoScripts, protoScript)
	}

	return protoScripts, nil
}

// getUserVoicedScripts 获取用户配音过的剧本（基于配音记录表）
func (s *UserService) getUserVoicedScripts(ctx context.Context, userID int64, page, pageSize int32) ([]*svcscript.UserDubbingScript, int64, error) {
	// 1. 获取用户的配音记录（按最后配音时间排序）
	dubbingRecords, total, err := s.dubbingRecordModel.GetUserDubbingRecords(ctx, userID, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户配音记录失败: %w", err)
	}

	if len(dubbingRecords) == 0 {
		return []*svcscript.UserDubbingScript{}, 0, nil
	}

	// 2. 收集剧本ID
	scriptIDs := make([]int64, 0, len(dubbingRecords))
	for _, record := range dubbingRecords {
		scriptIDs = append(scriptIDs, record.ScriptID)
	}

	// 3. 获取剧本信息（配音过的剧本不过滤删除状态）
	scripts, err := s.scriptModel.GetScriptsByIDsForVoiced(ctx, scriptIDs)
	if err != nil {
		return nil, 0, fmt.Errorf("获取剧本信息失败: %w", err)
	}
	// 创建剧本ID到剧本的映射
	scriptMap := make(map[int64]*model.Script)
	for _, script := range scripts {
		scriptMap[script.ID] = script
	}

	// 4. 获取剧本话题
	scriptTopics, err := s.scriptModel.GetScriptTopics(ctx, scriptIDs)
	if err != nil {
		return nil, 0, fmt.Errorf("获取剧本话题失败: %w", err)
	}

	// 5. 组装结果
	var result []*svcscript.UserDubbingScript

	// 按照配音记录的顺序处理各个剧本，确保保持排序
	for _, record := range dubbingRecords {
		script, ok := scriptMap[record.ScriptID]
		if !ok {
			logger.Warnf("配音记录中的剧本不存在: recordID=%d, scriptID=%d", record.ID, record.ScriptID)
			continue
		}

		// 解析台词配音映射，获取配音过的角色信息
		lineDubbingMappings, err := record.GetLineDubbingMappings()
		if err != nil {
			logger.Errorf("解析台词配音映射失败: recordID=%d, error=%v", record.ID, err)
			continue
		}

		// 收集所有配音ID，用于获取角色信息
		var allDubbingIDs []int64
		for _, mapping := range lineDubbingMappings {
			allDubbingIDs = append(allDubbingIDs, mapping.DubbingIDs...)
		}

		// 获取配音详情，用于获取角色信息和计算点赞数
		var charIds []int64
		var assetIds []int64
		var totalLikes int32
		if len(allDubbingIDs) > 0 {
			// 这里我们需要查询配音表获取角色信息和点赞数
			dubbings, err := s.dubbingModel.GetDubbingsByIDs(ctx, allDubbingIDs)
			if err != nil {
				logger.Errorf("获取配音详情失败: recordID=%d, error=%v", record.ID, err)
				continue
			}

			// 收集角色ID和资源包ID（去重），并计算总点赞数
			charIdMap := make(map[int64]bool)
			assetIdMap := make(map[int64]bool)
			for _, dubbing := range dubbings {
				// 累计点赞数
				totalLikes += dubbing.Likes

				// 收集角色ID（去重）
				if !charIdMap[dubbing.CharacterID] {
					charIds = append(charIds, dubbing.CharacterID)
					charIdMap[dubbing.CharacterID] = true
				}
				// 收集资源包ID（去重）
				if !assetIdMap[dubbing.CharacterAssetID] {
					assetIds = append(assetIds, dubbing.CharacterAssetID)
					assetIdMap[dubbing.CharacterAssetID] = true
				}
			}
		}

		// 获取配音过的角色列表
		var chars []*svcscript.Character
		if len(charIds) > 0 {
			characters, assetMap, err := s.characterModel.GetCharactersWithAsset(ctx, charIds, assetIds)
			if err != nil {
				logger.Errorf("GetCharactersWithAssets error: %v", err)
				continue
			}

			// 创建角色映射，方便前端直接通过ID查找角色
			for _, character := range characters {
				asset, ok := assetMap[character.ID]
				if !ok {
					asset = &model.CharacterAsset{}
				}
				protoCharacter := s.characterService.ConvertToProtoCharacter(character, asset)
				chars = append(chars, protoCharacter)
			}
		}

		// 获取该剧本的话题
		var protoTopics []*svcscript.Topic
		if topics, ok := scriptTopics[record.ScriptID]; ok {
			protoTopics = make([]*svcscript.Topic, 0, len(topics))
			for _, topic := range topics {
				protoTopics = append(protoTopics, &svcscript.Topic{
					Id:   topic.ID,
					Name: topic.Name,
				})
			}
		}

		// 创建UserDubbingScript对象（基于配音记录）
		voicedScript := &svcscript.UserDubbingScript{
			ScriptId:        record.ScriptID,
			Title:           script.Title,
			Cover:           script.GetFullCover(),
			Topics:          protoTopics,
			Characters:      chars,
			DubbingCount:    record.DubbingCount,
			LastDubbingTime: record.CreatedAt,
			TotalLikes:      totalLikes,
			DubbingRecordId: record.ID,
			Status:          script.Status,
		}

		result = append(result, voicedScript)
	}

	return result, total, nil
}

// getLikedScriptIDs 获取用户点赞过的剧本ID
func (s *UserService) getLikedScriptIDs(ctx context.Context, userID int64, page, pageSize int32) ([]int64, int64, error) {
	// 获取用户点赞过的剧本记录
	likes, total, err := s.likeModel.GetLikesByUserIDAndTypePaged(ctx, userID, svcscript.LikeType_LIKE_TYPE_SCRIPT, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户点赞记录失败: %w", err)
	}

	// 提取剧本ID
	scriptIDs := make([]int64, 0, len(likes))
	for _, like := range likes {
		scriptIDs = append(scriptIDs, like.TargetID)
	}

	return scriptIDs, total, nil
}
