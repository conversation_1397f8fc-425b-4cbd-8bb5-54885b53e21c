package model

import (
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

// UserStatsModelInterface 用户统计数据模型接口
type UserStatsModelInterface interface {
	// 基本操作
	GetUserStats(ctx context.Context, userID int64) (*UserStats, error)
	BatchGetUserStats(ctx context.Context, userIDs []int64) (map[int64]*UserStats, error)
	UpdateUserStats(ctx context.Context, stats *UserStats) error
	SearchUsers(ctx context.Context, keyword string, page, pageSize int32) ([]*UserStats, int64, error)

	// 计数操作 - 通用方法
	IncrementStat(ctx context.Context, userID int64, field string, value int32) error

	// 剧本相关
	IncrementScriptCount(ctx context.Context, userID int64, value int32) error
	IncrementScriptLikes(ctx context.Context, userID int64, value int32) error

	// 配音相关
	IncrementDubbings(ctx context.Context, userID int64, value int32) error
	IncrementDubbingLikes(ctx context.Context, userID int64, value int32) error

	// 评论点赞相关
	IncrementCommentCount(ctx context.Context, userID int64, value int32) error
	IncrementCommentLikes(ctx context.Context, userID int64, value int32) error

	// 回声相关
	IncrementEchoCount(ctx context.Context, userID int64, value int32) error

	// 用户关注相关
	IncrementFansCount(ctx context.Context, userID int64, value int32) error
}

// UserStats 用户统计表结构
type UserStats struct {
	ID           int64  `gorm:"primaryKey;column:id" json:"id"`                      // 主键ID
	UserID       int64  `gorm:"column:user_id;uniqueIndex" json:"user_id"`           // 用户ID
	Nickname     string `gorm:"column:nickname" json:"nickname"`                     // 用户昵称
	NamePinyin   string `gorm:"column:name_pinyin" json:"name_pinyin"`               // 昵称拼音
	ScriptCount  int32  `gorm:"column:script_count" json:"script_count"`             // 用户剧本数
	ScriptLikes  int32  `gorm:"column:script_likes;default:0" json:"script_likes"`   // 剧本获得的点赞数
	DubbingCount int32  `gorm:"column:dubbing_count;default:0" json:"dubbing_count"` // 用户配音数
	DubbingLikes int32  `gorm:"column:dubbing_likes;default:0" json:"dubbing_likes"` // 配音获得的点赞数
	CommentCount int32  `gorm:"column:comment_count" json:"comment_count"`           // 用户评论数
	CommentLikes int32  `gorm:"column:comment_likes;default:0" json:"comment_likes"` // 评论获得的点赞数
	EchoCount    int32  `gorm:"column:echo_count;default:0" json:"echo_count"`       // 回声数（被配音次数）
	FansCount    int32  `gorm:"column:fans_count;default:0" json:"fans_count"`       // 用户粉丝数
	CreatedAt    int64  `gorm:"column:created_at" json:"created_at"`                 // 创建时间
	UpdatedAt    int64  `gorm:"column:updated_at" json:"updated_at"`                 // 更新时间
}

// TableName 表名
func (UserStats) TableName() string {
	return "user_stats"
}

// UserStatsModel 用户统计数据模型
type UserStatsModel struct {
	BaseModelInterface
}

// NewUserStatsModel 创建用户统计数据模型实例
func NewUserStatsModel(baseModel BaseModelInterface) UserStatsModelInterface {
	return &UserStatsModel{
		baseModel,
	}
}

// GetUserStats 获取用户统计数据
func (m *UserStatsModel) GetUserStats(ctx context.Context, userID int64) (*UserStats, error) {
	now := util.NowTimeMillis()

	stats := UserStats{
		UserID:       userID,
		ScriptCount:  0,
		ScriptLikes:  0,
		DubbingCount: 0,
		DubbingLikes: 0,
		CommentCount: 0,
		CommentLikes: 0,
		EchoCount:    0,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// 先尝试创建记录(如果记录不存在)
	err := m.GetDB().WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}},
		DoNothing: true,
	}).Create(&stats).Error

	if err != nil {
		return nil, errcode.ErrScriptUserStat
	}

	// 再查询最新记录
	err = m.GetDB().WithContext(ctx).Where("user_id = ?", userID).First(&stats).Error
	if err != nil {
		return nil, errcode.ErrScriptUserStat
	}

	return &stats, nil
}

// BatchGetUserStats 批量获取用户统计数据
func (m *UserStatsModel) BatchGetUserStats(ctx context.Context, userIDs []int64) (map[int64]*UserStats, error) {
	if len(userIDs) == 0 {
		return make(map[int64]*UserStats), nil
	}

	now := util.NowTimeMillis()
	result := make(map[int64]*UserStats)

	// 先查询已存在的记录
	var existingStats []*UserStats
	err := m.GetDB().WithContext(ctx).Where("user_id IN ?", userIDs).Find(&existingStats).Error
	if err != nil {
		return nil, errcode.ErrScriptUserStat
	}

	// 将已存在的记录放入结果map
	existingUserIDs := make(map[int64]bool)
	for _, stats := range existingStats {
		result[stats.UserID] = stats
		existingUserIDs[stats.UserID] = true
	}

	// 找出不存在的用户ID，为它们创建默认记录
	var missingUserIDs []int64
	for _, userID := range userIDs {
		if !existingUserIDs[userID] {
			missingUserIDs = append(missingUserIDs, userID)
		}
	}

	// 为不存在的用户创建默认统计记录
	if len(missingUserIDs) > 0 {
		var newStats []*UserStats
		for _, userID := range missingUserIDs {
			stats := &UserStats{
				UserID:       userID,
				ScriptCount:  0,
				ScriptLikes:  0,
				DubbingCount: 0,
				DubbingLikes: 0,
				CommentCount: 0,
				CommentLikes: 0,
				EchoCount:    0,
				CreatedAt:    now,
				UpdatedAt:    now,
			}
			newStats = append(newStats, stats)
			result[userID] = stats
		}

		// 批量创建记录（忽略冲突）
		err = m.GetDB().WithContext(ctx).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "user_id"}},
			DoNothing: true,
		}).Create(&newStats).Error
		if err != nil {
			logger.Errorf("BatchGetUserStats create new stats error: %v", err)
			// 即使创建失败，也返回默认值，不影响业务逻辑
		}
	}

	return result, nil
}

// UpdateUserStats 更新用户统计数据
func (m *UserStatsModel) UpdateUserStats(ctx context.Context, stats *UserStats) error {
	stats.UpdatedAt = util.NowTimeMillis()

	err := m.GetDB().WithContext(ctx).Model(&UserStats{}).Where("user_id = ?", stats.UserID).Updates(stats).Error
	if err != nil {
		return errcode.ErrScriptUserStat
	}

	return nil
}

// SearchUsers 搜索用户
func (m *UserStatsModel) SearchUsers(ctx context.Context, keyword string, page, pageSize int32) ([]*UserStats, int64, error) {
	var users []*UserStats
	var total int64

	// 构建搜索条件，支持昵称和拼音搜索，使用BINARY确保精确匹配emoji
	searchCondition := "nickname LIKE BINARY ? OR name_pinyin LIKE ?"
	likeKeyword := "%" + keyword + "%"

	err := m.GetDB().WithContext(ctx).
		Model(&UserStats{}).
		Where(searchCondition, likeKeyword, likeKeyword).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrUserQueryFailed
	}

	err = m.GetDB().WithContext(ctx).
		Where(searchCondition, likeKeyword, likeKeyword).
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Order("fans_count DESC, name_pinyin ASC").
		Find(&users).Error
	if err != nil {
		return nil, 0, errcode.ErrUserQueryFailed
	}

	return users, total, nil
}

// IncrementStat 通用的统计数据递增方法
func (m *UserStatsModel) IncrementStat(ctx context.Context, userID int64, field string, value int32) error {
	now := util.NowTimeMillis()

	// 创建一个空的统计记录，如果记录不存在则创建，存在则更新指定字段
	stats := UserStats{
		UserID:    userID,
		CreatedAt: now,
		UpdatedAt: now,
	}

	err := m.GetDB().WithContext(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "user_id"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			field:        gorm.Expr(field+" + ?", value),
			"updated_at": now,
		}),
	}).Create(&stats).Error

	if err != nil {
		return errcode.ErrScriptUserStat
	}

	return nil
}

// IncrementScriptCount 增加剧本数
func (m *UserStatsModel) IncrementScriptCount(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "script_count", value)
}

// IncrementScriptLikes 增加用户获得的点赞数
func (m *UserStatsModel) IncrementScriptLikes(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "script_likes", value)
}

// IncrementDubbings 增加配音数
func (m *UserStatsModel) IncrementDubbings(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "dubbing_count", value)
}

// IncrementDubbingLikes 增加用户配音获得的点赞数
func (m *UserStatsModel) IncrementDubbingLikes(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "dubbing_likes", value)
}

// IncrementCommentCount 增加评论数
func (m *UserStatsModel) IncrementCommentCount(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "comment_count", value)
}

// IncrementCommentLikes 增加用户评论获得的点赞数
func (m *UserStatsModel) IncrementCommentLikes(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "comment_likes", value)
}

// IncrementEchoCount 增加用户的回声数
func (m *UserStatsModel) IncrementEchoCount(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "echo_count", value)
}

// IncrementFansCount 用户粉丝数
func (m *UserStatsModel) IncrementFansCount(ctx context.Context, userID int64, value int32) error {
	return m.IncrementStat(ctx, userID, "fans_count", value)
}
