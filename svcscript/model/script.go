package model

import (
	"context"
	"errors"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/config"
)

// ScriptModelInterface 剧本模型接口
type ScriptModelInterface interface {
	// 基本操作
	CreateScriptTx(ctx context.Context, tx interface{}, script *Script) error
	GetScriptByID(ctx context.Context, id int64) (*Script, error)
	GetScriptByIDWithoutStatus(ctx context.Context, id int64) (*Script, error)
	GetScriptsByIDs(ctx context.Context, ids []int64) ([]*Script, error)
	GetScriptsByIDsWithOwner(ctx context.Context, ids []int64, ownerUserID int64) ([]*Script, error)
	GetScriptsByIDsForVoiced(ctx context.Context, ids []int64) ([]*Script, error)
	UpdateScript(ctx context.Context, script *Script) error
	DeleteScript(ctx context.Context, id int64) error
	GetScriptsByAuthorID(ctx context.Context, authorID int64, page, pageSize int32) ([]*Script, int64, error)
	GetScriptsByAuthorIDs(ctx context.Context, authorIDs []int64, page, pageSize int32) ([]*Script, int64, int64, error)
	SearchScripts(ctx context.Context, authorID int64, keyword string, page, pageSize int32) ([]*Script, int64, error)
	ListScripts(ctx context.Context, authorID, topicID int64, isAggregation bool, page, pageSize int32) ([]*Script, int64, int64, error)
	GetScriptsByAuthorIDPaged(ctx context.Context, authorID int64, page, pageSize int32) ([]*Script, int64, error)
	GetScriptTopics(ctx context.Context, scriptIDs []int64) (map[int64][]*Topic, error)
	BatchCheckUserShared(ctx context.Context, userID int64, scriptIDs []int64) (map[int64]bool, error)
	// 统计相关
	IncreaseDubbings(ctx context.Context, id int64, count int64) error
	IncreaseComments(ctx context.Context, id int64, count int64) error
	IncreaseLikes(ctx context.Context, id int64, count int64) error
	IncreaseFollows(ctx context.Context, id int64, count int64) error
	IncreaseViews(ctx context.Context, id int64, count int64) error
	IncreaseShares(ctx context.Context, id int64, count int64) error
}

// Script 剧本表结构
type Script struct {
	ID             int64                   `gorm:"primaryKey;column:id" json:"id"`                              // 剧本ID
	Title          string                  `gorm:"column:title;not null" json:"title"`                          // 标题
	Cover          string                  `gorm:"column:cover" json:"cover"`                                   // 封面地址
	AuthorID       int64                   `gorm:"column:author_id;not null;index" json:"author_id"`            // 作者用户ID
	CreationUserId uint                    `gorm:"column:creation_user_id;default:0;index" json:"creation_user_id"` // 创建该剧本的admin用户ID，用于数据权限控制
	BGMUrl         string                  `gorm:"column:bgm_url" json:"bgm_url"`                               // 背景音乐地址
	BGMDuration    int32                   `gorm:"column:bgm_duration" json:"bgm_duration"`                     // 背景音乐时长
	ThemeColor     string                  `gorm:"column:theme_color" json:"theme_color"`                       // 主题色
	ColorInfo      string                  `gorm:"column:color_info" json:"color_info"`                         // 颜色信息
	ReviewStatus   svcscript.ReviewStatus  `gorm:"column:review_status;default:0" json:"review_status"`         // 审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝
	Score          int32                   `gorm:"column:score;default:0" json:"score"`                         // 剧本质量分数
	Sort           int32                   `gorm:"column:sort;default:0" json:"sort"`                           // 排序值
	DubbingCount   int32                   `gorm:"column:dubbing_count;default:0" json:"dubbing_count"`         // 配音数
	CommentCount   int32                   `gorm:"column:comment_count;default:0" json:"comment_count"`         // 评论数
	LikeCount      int32                   `gorm:"column:like_count;default:0" json:"like_count"`               // 点赞数
	FollowCount    int32                   `gorm:"column:follow_count;default:0" json:"follow_count"`           // 关注数
	ViewCount      int32                   `gorm:"column:view_count;default:0" json:"view_count"`               // 浏览数
	ShareCount     int32                   `gorm:"column:share_count;default:0" json:"share_count"`             // 分享数
	PublishStatus  svcscript.PublishStatus `gorm:"column:publish_status;default:0;index" json:"publish_status"` // 发布状态
	Status         svcscript.ScriptStatus  `gorm:"column:status;default:1" json:"status"`                       // 剧本状态
	CreatedAt      int64                   `gorm:"column:created_at" json:"created_at"`                         // 创建时间
	UpdatedAt      int64                   `gorm:"column:updated_at" json:"updated_at"`                         // 更新时间
}

// TableName 表名
func (Script) TableName() string {
	return "scripts"
}

// ScriptModel 剧本模型
type ScriptModel struct {
	BaseModelInterface
}

// NewScriptModel 创建剧本模型实例
func NewScriptModel(baseModel BaseModelInterface) ScriptModelInterface {
	return &ScriptModel{
		baseModel,
	}
}

// buildReviewStatusCondition 构建审核状态查询条件
// 根据配置决定是否允许用户查看自己未审核通过的内容（包括审核中、审核被拒绝等所有非已审核通过状态的内容）
func (m *ScriptModel) buildReviewStatusCondition(authorID int64) (string, []interface{}) {
	if config.IsShowOwnUnApprovedContentEnabled() {
		// 允许用户查看自己未审核通过的内容
		return "(review_status = ? OR author_id = ?)", []interface{}{svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED, authorID}
	} else {
		// 不允许用户查看自己未审核通过的内容，只能查看已审核通过的内容
		return "review_status = ?", []interface{}{svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED}
	}
}

func (s Script) GetFullCover() string {
	return alioss.FillImageUrl(s.Cover)
}

func (s Script) GetFullBGM() string {
	return alioss.FillImageUrl(s.BGMUrl)
}

// CreateScriptTx 创建剧本
func (m *ScriptModel) CreateScriptTx(ctx context.Context, tx interface{}, script *Script) error {
	if script.CreatedAt == 0 {
		script.CreatedAt = util.NowTimeMillis()
	}
	if script.UpdatedAt == 0 {
		script.UpdatedAt = util.NowTimeMillis()
	}

	err := tx.(*gorm.DB).Create(script).Error
	if err != nil {
		return errcode.ErrScriptCreateFailed
	}

	return nil
}

// GetScriptByID 根据ID获取剧本
func (m *ScriptModel) GetScriptByID(ctx context.Context, id int64) (*Script, error) {
	var script Script

	err := m.GetDB().WithContext(ctx).Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).First(&script).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrScriptNotFound
		}
		return nil, errcode.ErrScriptQueryFailed
	}

	return &script, nil
}

func (m *ScriptModel) GetScriptByIDWithoutStatus(ctx context.Context, id int64) (*Script, error) {
	var script Script
	err := m.GetDB().WithContext(ctx).Where("id =?", id).First(&script).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrScriptNotFound
		}
		return nil, errcode.ErrScriptQueryFailed
	}
	return &script, nil
}

// GetScriptsByIDs 根据ID列表获取剧本列表
func (m *ScriptModel) GetScriptsByIDs(ctx context.Context, ids []int64) ([]*Script, error) {
	var scripts []*Script

	if len(ids) == 0 {
		return scripts, nil
	}

	// 从数据库批量获取
	err := m.GetDB().WithContext(ctx).Where("id IN (?) AND status = ?", ids, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).Find(&scripts).Error
	if err != nil {
		return nil, errcode.ErrScriptQueryFailed
	}

	// 保持原始ID的顺序
	if len(ids) > 0 && len(scripts) > 0 {
		scriptMap := make(map[int64]*Script)
		for _, script := range scripts {
			scriptMap[script.ID] = script
		}

		orderedScripts := make([]*Script, 0, len(scripts))
		for _, id := range ids {
			if script, exists := scriptMap[id]; exists {
				orderedScripts = append(orderedScripts, script)
			}
		}
		return orderedScripts, nil
	}

	return scripts, nil
}

// GetScriptsByIDsWithOwner 根据ID列表获取剧本列表，对于用户自己的剧本不过滤状态
func (m *ScriptModel) GetScriptsByIDsWithOwner(ctx context.Context, ids []int64, ownerUserID int64) ([]*Script, error) {
	var scripts []*Script

	if len(ids) == 0 {
		return scripts, nil
	}

	// 从数据库批量获取，对于用户自己的剧本根据配置决定是否过滤状态，其他人的剧本过滤状态
	var err error
	if config.IsShowOwnUnApprovedContentEnabled() {
		// 允许用户查看自己未审核通过的内容
		err = m.GetDB().WithContext(ctx).Where("id IN (?) AND (author_id = ? OR status = ?)", ids, ownerUserID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).Find(&scripts).Error
	} else {
		// 不允许用户查看自己未审核通过的内容，只能查看正常状态的剧本
		err = m.GetDB().WithContext(ctx).Where("id IN (?) AND status = ?", ids, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).Find(&scripts).Error
	}
	if err != nil {
		return nil, errcode.ErrScriptQueryFailed
	}

	// 保持原始ID的顺序
	if len(ids) > 0 && len(scripts) > 0 {
		scriptMap := make(map[int64]*Script)
		for _, script := range scripts {
			scriptMap[script.ID] = script
		}

		orderedScripts := make([]*Script, 0, len(scripts))
		for _, id := range ids {
			if script, exists := scriptMap[id]; exists {
				orderedScripts = append(orderedScripts, script)
			}
		}
		return orderedScripts, nil
	}

	return scripts, nil
}

// GetScriptsByIDsForVoiced 根据ID列表获取剧本列表，用于配音过的剧本查询（不过滤删除状态）
func (m *ScriptModel) GetScriptsByIDsForVoiced(ctx context.Context, ids []int64) ([]*Script, error) {
	var scripts []*Script

	if len(ids) == 0 {
		return scripts, nil
	}

	// 从数据库批量获取，不过滤删除状态，因为用户配音过的剧本都应该返回
	err := m.GetDB().WithContext(ctx).Where("id IN (?)", ids).Find(&scripts).Error
	if err != nil {
		return nil, errcode.ErrScriptQueryFailed
	}

	// 保持原始ID的顺序
	if len(ids) > 0 && len(scripts) > 0 {
		scriptMap := make(map[int64]*Script)
		for _, script := range scripts {
			scriptMap[script.ID] = script
		}

		orderedScripts := make([]*Script, 0, len(scripts))
		for _, id := range ids {
			if script, exists := scriptMap[id]; exists {
				orderedScripts = append(orderedScripts, script)
			}
		}
		return orderedScripts, nil
	}

	return scripts, nil
}

// UpdateScript 更新剧本
func (m *ScriptModel) UpdateScript(ctx context.Context, script *Script) error {
	script.UpdatedAt = util.NowTimeMillis()

	err := m.GetDB().WithContext(ctx).Model(&Script{}).Where("id = ?", script.ID).Updates(script).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

// DeleteScript 删除剧本
func (m *ScriptModel) DeleteScript(ctx context.Context, id int64) error {
	// 软删除，更新状态
	updates := map[string]interface{}{
		"status":     svcscript.ScriptStatus_SCRIPT_STATUS_DELETED,
		"updated_at": util.NowTimeMillis(),
	}

	err := m.GetDB().WithContext(ctx).Model(&Script{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		return errcode.ErrScriptDeleteFailed
	}

	return nil
}

// GetScriptsByAuthorID 获取作者的剧本列表
func (m *ScriptModel) GetScriptsByAuthorID(ctx context.Context, authorID int64, page, pageSize int32) ([]*Script, int64, error) {
	var scripts []*Script
	var total int64

	// 计算总数
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("author_id = ? AND status = ?", authorID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrScriptQueryFailed
	}

	// 获取分页数据
	err = m.GetDB().WithContext(ctx).
		Where("author_id = ? AND status = ?", authorID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Order("created_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&scripts).Error
	if err != nil {
		return nil, 0, errcode.ErrScriptQueryFailed
	}

	return scripts, total, nil
}

// GetScriptsByAuthorIDs 根据作者ID列表获取剧本列表
func (m *ScriptModel) GetScriptsByAuthorIDs(ctx context.Context, authorIDs []int64, page, pageSize int32) ([]*Script, int64, int64, error) {
	var scripts []*Script
	var total int64
	var totalDubbingCount int64

	// 查询符合条件的剧本
	query := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("author_id IN (?) AND status = ? AND publish_status = ? AND review_status = ?",
			authorIDs, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL,
			svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED,
			svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, 0, errcode.ErrScriptQueryFailed
	}

	// 统计所有剧本的配音数量总和
	err = m.GetDB().WithContext(ctx).Model(&Script{}).
		Where(query.Statement.Clauses["WHERE"].Expression).
		Select("COALESCE(SUM(dubbing_count), 0)").
		Scan(&totalDubbingCount).Error
	if err != nil {
		return nil, 0, 0, errcode.ErrScriptQueryFailed
	}

	// 获取分页数据，按照创建时间排序（最新的在前）
	err = query.
		Order("created_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&scripts).Error
	if err != nil {
		return nil, 0, 0, errcode.ErrScriptQueryFailed
	}

	return scripts, total, totalDubbingCount, nil
}

// SearchScripts 搜索剧本
func (m *ScriptModel) SearchScripts(ctx context.Context, authorID int64, keyword string, page, pageSize int32) ([]*Script, int64, error) {
	var scripts []*Script
	var total int64

	likeTerm := "%" + keyword + "%"

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(authorID)

	// 搜索已发布且已审核通过的剧本，使用BINARY确保精确匹配emoji字符
	query := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("(title LIKE BINARY ? OR id = ?) AND status = ? AND publish_status = ? AND "+reviewCondition,
			append([]interface{}{likeTerm, keyword, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED}, reviewArgs...)...)

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrScriptQueryFailed
	}

	// 获取分页数据
	err = query.
		Order("like_count DESC, created_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&scripts).Error
	if err != nil {
		return nil, 0, errcode.ErrScriptQueryFailed
	}

	return scripts, total, nil
}

// ListScripts 根据不同类型获取剧本列表
func (m *ScriptModel) ListScripts(ctx context.Context, authorID, topicID int64, isAggregation bool, page, pageSize int32) ([]*Script, int64, int64, error) {
	var scripts []*Script
	var total int64
	var totalDubbingCount int64

	// 基础查询
	query := m.GetDB().WithContext(ctx).Table("scripts")

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(authorID)

	// 根据列表类型构建不同的查询条件
	switch topicID {
	case consts.TopicRecommend:
		// 推荐列表：按照分数（editor_score）和创建时间排序
		query = query.Where("status = ? AND publish_status = ? AND "+reviewCondition,
			append([]interface{}{svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED}, reviewArgs...)...).
			Order("scripts.score DESC, scripts.created_at DESC") // 首先按分数排序，分数相同时按创建时间排序
	default:
		query = query.Joins("JOIN script_topic_relation ON scripts.id = script_topic_relation.script_id").
			Where("script_topic_relation.topic_id = ? AND scripts.status = ? AND scripts.publish_status = ? AND "+reviewCondition,
				append([]interface{}{topicID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED}, reviewArgs...)...)

		// 话题聚合页（排序：点赞数+发布时间倒序）
		if isAggregation {
			query = query.Order("scripts.like_count DESC, scripts.created_at DESC")
		} else {
			query = query.Order("scripts.score DESC, scripts.created_at DESC")
		}
	}

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, 0, errcode.ErrScriptQueryFailed
	}

	// 统计所有剧本的配音数量总和
	dubbingCountQuery := m.GetDB().WithContext(ctx).Model(&Script{})

	// 根据不同的列表类型构建不同的查询条件
	switch topicID {
	case consts.TopicRecommend:
		dubbingCountQuery = dubbingCountQuery.Where("status = ? AND publish_status = ? AND "+reviewCondition,
			append([]interface{}{svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED}, reviewArgs...)...)
	default:
		dubbingCountQuery = dubbingCountQuery.Joins("JOIN script_topic_relation ON scripts.id = script_topic_relation.script_id").
			Where("script_topic_relation.topic_id = ? AND scripts.status = ? AND scripts.publish_status = ? AND "+reviewCondition,
				append([]interface{}{topicID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.PublishStatus_PUBLISH_STATUS_PUBLISHED}, reviewArgs...)...)
	}

	err = dubbingCountQuery.Select("COALESCE(SUM(dubbing_count), 0)").
		Scan(&totalDubbingCount).Error
	if err != nil {
		return nil, 0, 0, errcode.ErrScriptQueryFailed
	}

	// 获取分页数据
	err = query.Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&scripts).Error
	if err != nil {
		return nil, 0, 0, errcode.ErrScriptQueryFailed
	}

	return scripts, total, totalDubbingCount, nil
}

// GetScriptsByAuthorIDPaged 获取作者的剧本列表（分页）
func (m *ScriptModel) GetScriptsByAuthorIDPaged(ctx context.Context, authorID int64, page, pageSize int32) ([]*Script, int64, error) {
	var scripts []*Script
	var total int64

	// 计算总数
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("author_id = ? AND status = ? AND review_status != ? AND review_status != ?",
			authorID,
			svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL,
			svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED,
			svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrScriptQueryFailed
	}

	// 获取分页数据
	err = m.GetDB().WithContext(ctx).
		Where("author_id = ? AND status = ? AND review_status != ? AND review_status != ?",
			authorID,
			svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL,
			svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED,
			svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED).
		Order("created_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&scripts).Error
	if err != nil {
		return nil, 0, errcode.ErrScriptQueryFailed
	}

	return scripts, total, nil
}

// GetScriptTopics 获取剧本的话题
func (m *ScriptModel) GetScriptTopics(ctx context.Context, scriptIDs []int64) (map[int64][]*Topic, error) {
	if len(scriptIDs) == 0 {
		return map[int64][]*Topic{}, nil
	}

	// 查询剧本-话题关联
	var relations []*ScriptTopicRelation
	err := m.GetDB().WithContext(ctx).
		Where("script_id IN (?)", scriptIDs).
		Find(&relations).Error
	if err != nil {
		return nil, errcode.ErrScriptQueryFailed
	}

	if len(relations) == 0 {
		return map[int64][]*Topic{}, nil
	}

	// 获取话题ID列表
	var topicIDs []int64
	topicIDMap := make(map[int64]bool)
	for _, relation := range relations {
		if !topicIDMap[relation.TopicID] {
			topicIDs = append(topicIDs, relation.TopicID)
			topicIDMap[relation.TopicID] = true
		}
	}

	// 查询话题信息
	var topics []*Topic
	err = m.GetDB().WithContext(ctx).
		Where("id IN (?)", topicIDs).
		Find(&topics).Error
	if err != nil {
		return nil, errcode.ErrTopicQueryFailed
	}

	// 构建话题map
	topicMap := make(map[int64]*Topic)
	for _, topic := range topics {
		topicMap[topic.ID] = topic
	}

	// 按剧本ID分组
	result := make(map[int64][]*Topic)
	for _, relation := range relations {
		if topic, ok := topicMap[relation.TopicID]; ok {
			result[relation.ScriptID] = append(result[relation.ScriptID], topic)
		}
	}

	return result, nil
}

// BatchCheckUserShared 批量检查用户是否分享剧本
func (m *ScriptModel) BatchCheckUserShared(ctx context.Context, userID int64, scriptIDs []int64) (map[int64]bool, error) {
	if len(scriptIDs) == 0 || userID <= 0 {
		return map[int64]bool{}, nil
	}

	var sharedScripts []struct {
		ScriptID int64
	}

	err := m.GetDB().WithContext(ctx).Table("script_shares").
		Select("script_id").
		Where("user_id = ? AND script_id IN (?)", userID, scriptIDs).
		Find(&sharedScripts).Error
	if err != nil {
		return nil, errcode.ErrDBQueryFailed
	}

	// 构建结果
	result := make(map[int64]bool)
	for _, id := range scriptIDs {
		result[id] = false
	}

	for _, shared := range sharedScripts {
		result[shared.ScriptID] = true
	}

	return result, nil
}

func (m *ScriptModel) IncreaseDubbings(ctx context.Context, id int64, count int64) error {
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("dubbing_count", gorm.Expr("dubbing_count + ?", count)).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

func (m *ScriptModel) IncreaseComments(ctx context.Context, id int64, count int64) error {
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("comment_count", gorm.Expr("comment_count + ?", count)).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

// IncreaseLikes 增加剧本点赞数
func (m *ScriptModel) IncreaseLikes(ctx context.Context, id int64, count int64) error {
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("like_count", gorm.Expr("like_count + ?", count)).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

func (m *ScriptModel) IncreaseFollows(ctx context.Context, id int64, count int64) error {
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("follow_count", gorm.Expr("follow_count + ?", count)).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

func (m *ScriptModel) IncreaseViews(ctx context.Context, id int64, count int64) error {
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("view_count", gorm.Expr("view_count + ?", count)).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

func (m *ScriptModel) IncreaseShares(ctx context.Context, id int64, count int64) error {
	err := m.GetDB().WithContext(ctx).Model(&Script{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("share_count", gorm.Expr("share_count + ?", count)).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

// AdminGetScriptListParams 管理员获取剧本列表参数
type AdminGetScriptListParams struct {
	ScriptID      int64
	Title         string
	Status        int32
	PublishStatus int32
	ReviewStatus  int32
	AuthorID      int64
	TopicID       int64
	StartTime     int64
	EndTime       int64
	SortType      int32
	IsDesc        bool
	Page          int32
	PageSize      int32
}
