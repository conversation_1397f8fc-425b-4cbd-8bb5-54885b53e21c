package pusher

import (
	"context"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/nsqutil"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	protoEvent "new-gitlab.xunlei.cn/vcproject/backends/proto/event"
)

// EventScriptPushInterface 事件处理器接口
type EventScriptPushInterface interface {
	PushLikeCreatedEvent(ctx context.Context, likeType svcscript.LikeType, targetID, userID int64) error
	PushLikeCanceledEvent(ctx context.Context, likeType svcscript.LikeType, targetID, userID int64) error
	PushDubbingCreatedEvent(ctx context.Context, scriptID, characterID, userId int64) error
	PushDubbingDeletedEvent(ctx context.Context, scriptID, characterID, userId int64) error
	PushScriptCreateEvent(ctx context.Context, scriptID, userId int64) error
	PushScriptDeleteEvent(ctx context.Context, scriptID, userId int64) error
	PushScriptFollowEvent(ctx context.Context, scriptID, userId int64) error
	PushScriptUnfollowEvent(ctx context.Context, scriptID, userId int64) error
	PushScriptShareEvent(ctx context.Context, scriptID, userId int64) error
	PushScriptViewEvent(ctx context.Context, scriptID, userId int64) error
	PushCommentCreateEvent(ctx context.Context, scriptID, userId int64) error
	PushCommentDeleteEvent(ctx context.Context, scriptID, userId int64) error
	PushScriptTopicViewEvent(ctx context.Context, scriptID, topicID, userId int64) error
	PushReviewScriptEvent(ctx context.Context, scriptID, userId int64, topics []string, cover, title string) error
	PushReviewDubbingEvent(ctx context.Context, scriptID, userId, dubbingId int64, dubbingUrl string) error
	PushReviewCommentEvent(ctx context.Context, scriptID, userId, commentId int64, contentType svcscript.ContentType, content, voiceUrl string) error
}

// EventScriptPush 事件处理器
type EventScriptPush struct {
}

// NewScriptEventHandler 创建事件处理器实例
func NewScriptEventPush() EventScriptPushInterface {
	return &EventScriptPush{}
}

// DispatchLikeCreatedEvent 分发点赞创建事件
func (p *EventScriptPush) PushLikeCreatedEvent(ctx context.Context, likeType svcscript.LikeType, targetID, userID int64) error {
	// 创建事件载荷
	event := protoEvent.GenLikeCreatedEvent(likeType, targetID, userID)

	// 发布到消息队列
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布点赞创建事件到NSQ失败: %v", err)
	}
	return nil
}

// DispatchLikeCanceledEvent 分发点赞取消事件
func (p *EventScriptPush) PushLikeCanceledEvent(ctx context.Context, likeType svcscript.LikeType, targetID, userID int64) error {
	// 创建事件载荷
	event := protoEvent.GenLikeCanceledEvent(likeType, targetID, userID)

	// 发布到消息队列
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布点赞取消事件到NSQ失败: %v", err)
	}
	return nil
}

// DispatchDubbingCreatedEvent 分发配音创建事件
func (p *EventScriptPush) PushDubbingCreatedEvent(ctx context.Context, scriptID, characterID, userId int64) error {
	// 创建事件载荷
	event := protoEvent.GenDubbingCreatedEvent(scriptID, characterID, userId)

	// 发布到消息队列
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布配音创建事件到NSQ失败: %v", err)
	}
	return nil
}

// DispatchDubbingDeletedEvent 分发配音删除事件
func (p *EventScriptPush) PushDubbingDeletedEvent(ctx context.Context, scriptID, characterID, userId int64) error {
	// 创建事件载荷
	event := protoEvent.GenDubbingDeletedEvent(scriptID, characterID, userId)

	// 发布到消息队列
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布配音删除事件到NSQ失败: %v", err)
	}
	return nil
}

// DispatchScriptCreateEvent 分发剧本创建事件
func (p *EventScriptPush) PushScriptCreateEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenScriptCreatedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本创建事件到NSQ失败：%v", err)
	}
	return nil
}

// DispatchScriptCreateEvent 分发剧本删除事件
func (p *EventScriptPush) PushScriptDeleteEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenScriptDeletedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本删除事件到NSQ失败：%v", err)
	}
	return nil
}

// DispatchScriptCreateEvent 分发评论创建事件
func (p *EventScriptPush) PushCommentCreateEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenCommentCreatedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布评论创建事件到NSQ失败：%v", err)
	}
	return nil
}

// DispatchScriptCreateEvent 分发评论删除事件
func (p *EventScriptPush) PushCommentDeleteEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenCommentDeletedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布评论删除事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushScriptFollowEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenScriptFollowedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本关注事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushScriptUnfollowEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenScriptUnFollowedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本取消关注事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushScriptShareEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenScriptShareEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本分享事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushScriptViewEvent(ctx context.Context, scriptID, userId int64) error {
	event := protoEvent.GenScriptViewedEvent(scriptID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本浏览事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushScriptTopicViewEvent(ctx context.Context, scriptID, topicID, userId int64) error {
	event := protoEvent.GenScriptTopicViewedEvent(scriptID, topicID, userId)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布话题浏览事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushReviewScriptEvent(ctx context.Context, scriptID, userId int64, topics []string, cover, title string) error {
	event := protoEvent.GenScriptReviewEvent(scriptID, userId, topics, cover, title)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本审核事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushReviewDubbingEvent(ctx context.Context, scriptID, userId, dubbingId int64, dubbingUrl string) error {
	event := protoEvent.GenScriptReviewDubbingEvent(scriptID, userId, dubbingId, dubbingUrl)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本配音审核事件到NSQ失败：%v", err)
	}
	return nil
}

func (p *EventScriptPush) PushReviewCommentEvent(ctx context.Context, scriptID, userId, commentId int64, contentType svcscript.ContentType, content, voiceUrl string) error {
	event := protoEvent.GenScriptReviewCommentEvent(scriptID, userId, commentId, contentType, content, voiceUrl)
	err := nsqutil.PublishJSON(ctx, protoEvent.TopicScript, event)
	if err != nil {
		logger.Errorf("发布剧本评论审核事件到NSQ失败：%v", err)
	}
	return nil
}
