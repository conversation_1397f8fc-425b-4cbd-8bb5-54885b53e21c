HARBOR_REGISTRY ?= registry.xunlei.cn
HARBOR_USER ?= vcproject
HARBOR_PASSWORD ?= 7ytCXndsLy4w
ROOT_DIR := $(shell pwd)
export ROOT_DIR
# 定义通用目标
define BUILD_SERVICE
	@echo "Building project: $(SERVICE_NAME) Version: $(VERSION) rootdir:${ROOT_DIR}"

	@IMAGE_NAME="$(HARBOR_REGISTRY)/vcproject/xim/$(SERVICE_NAME)" && \
	docker build ${ROOT_DIR} -f  ${ROOT_DIR}/Dockerfile --target production   \
	--build-arg GITLAB_USER=oauth2 \
	--build-arg GITLAB_TOKEN=************************** \
	--build-arg NEW_GITLAB_USER=maoyifeng \
	--build-arg NEW_GITLAB_TOKEN=************************** \
	--build-arg SERVICE_NAME=${SERVICE_NAME} \
	-t $${IMAGE_NAME}:$(VERSION) --platform=linux/amd64 && \
	docker build ${ROOT_DIR} -f  ${ROOT_DIR}/Dockerfile --target initdata   \
	--build-arg GITLAB_USER=oauth2 \
	--build-arg GITLAB_TOKEN=************************** \
	--build-arg NEW_GITLAB_USER=maoyifeng \
	--build-arg NEW_GITLAB_TOKEN=************************** \
	--build-arg SERVICE_NAME=${SERVICE_NAME} \
	-t $${IMAGE_NAME}/$(SERVICE_NAME)-initdata:$(VERSION) --platform=linux/amd64 && \
	docker login $(HARBOR_REGISTRY) -u $(HARBOR_USER) -p $(HARBOR_PASSWORD) && \
	docker push $${IMAGE_NAME}:$(VERSION) && \
	docker push $${IMAGE_NAME}/$(SERVICE_NAME)-initdata:$(VERSION) && \
	docker rmi $${IMAGE_NAME}:$(VERSION) && \
	docker rmi $${IMAGE_NAME}/$(SERVICE_NAME)-initdata:$(VERSION);

	$(eval PRODUCT := $(if $(shell echo "$(VERSION)" | grep -E "^v[0-9]+\.[0-9]+\.[0-9]+$$"),beta,alpha))
	@echo "Configuration:"
	@echo "  VERSION: $(VERSION)"
	@echo "  PRODUCT: $(PRODUCT)"
	@echo "  SERVICE_NAME: $(SERVICE_NAME)"
	@echo "Publishing version info to xops..."
	@UPDATE_TIME=$$(git show --date=iso8601 | grep Date | head -n1 | awk '{print $$2" "$$3}') && \
	DESCRIPT=$$(git tag -ln ${VERSION}) && \
	REPO_NAME="https://new-gitlab.xunlei.cn/vcproject/xim/$(SERVICE_NAME)" && \
	JSON_DATA='{"tag_time": "'$$UPDATE_TIME'","product": "'$(PRODUCT)'","git_url": "'$$REPO_NAME'","service_name": "'${SERVICE_NAME}'","service_description": "'${SERVICE_NAME}'","version_description": "'$$DESCRIPT'","value": "'${VERSION}'", "group_name": "vcproject"}' && \
	echo "Request payload:" && \
	echo "$$JSON_DATA" && \
	echo "$$JSON_DATA" | curl --location 'https://xops.office.k8s.xunlei.cn/api/v1/public/service/version' \
		--header 'Content-Type: application/json' \
		--data @-  || { echo "Error: Failed to publish version info"; exit 1; }
	@echo "Version info published successfully"
endef

define USAGE
Usage:
    make build VERSION=<version>   # 部署项目
    make tag                        # 在 git 创建新版本号
    make help                       # 显示帮助信息

Options:
    VERSION: 版本类型
             - test: 测试版本
             - vx.y.z: 正式版本

Example:
	make build VERSION=test
	make build VERSION=v0.0.1
	make tag                         # 创建版本号，基于当前版本递增
	make tag VERSION=v0.0.1          # 创建版本号，指定版本
endef
export USAGE

.PHONY: all
all: help

.PHONY: help
help:
	@echo "$$USAGE"
.PHONY: tag
tag:
	@if [ -z "$$VERSION" ]; then \
		CURRENT_VERSION=$(shell git tag | grep '^v' | sort -V | tail -n 1 | sed 's/^v//'); \
		if [ -z "$$CURRENT_VERSION" ]; then \
			echo "没有找到版本号. 退出."; \
			exit 1; \
		fi; \
		echo "当前版本: v$$CURRENT_VERSION"; \
		NEW_MAJOR=$$(echo $$CURRENT_VERSION | awk -F. '{print $$1 + 1 ".0.0"}'); \
		NEW_MINOR=$$(echo $$CURRENT_VERSION | awk -F. '{print $$1 "." $$2 + 1 ".0"}'); \
		NEW_PATCH=$$(echo $$CURRENT_VERSION | awk -F. '{print $$1 "." $$2 "." $$3 + 1}'); \
		echo "选择版本增量类型:"; \
		echo "1) 修订版本 (v$$NEW_PATCH)"; \
		echo "2) 次版本 (v$$NEW_MINOR)"; \
		echo "3) 主版本 (v$$NEW_MAJOR)"; \
		read -p "输入你的选择 (1-3, 默认是 1): " choice; \
		if [ -z "$$choice" ]; then \
			choice=1; \
		fi; \
		if [ "$$choice" -eq 1 ]; then \
			new_version="v$$NEW_PATCH"; \
		elif [ "$$choice" -eq 2 ]; then \
			new_version="v$$NEW_MINOR"; \
		elif [ "$$choice" -eq 3 ]; then \
			new_version="v$$NEW_MAJOR"; \
		else \
			echo "无效选择. 退出."; \
			exit 1; \
		fi; \
	else \
		new_version="$$VERSION"; \
		echo "使用指定版本: $$new_version"; \
	fi; \
	echo "创建新标签: $$new_version"; \
	git tag $$new_version; \
	echo "已创建新标签: $$new_version"; \
	echo "正在推送新标签到远程..."; \
	git push origin $$new_version; \
	echo "已推送新标签: $$new_version"

.PHONY: proto
proto:
	docker build . -f  Dockerfile --target proto_build_only   \
	    --build-arg NEW_GITLAB_USER=maoyifeng \
		--build-arg NEW_GITLAB_TOKEN=************************** \
		--build-arg SERVICE_NAME=${SERVICE_NAME} \
		 --platform=linux/amd64 && \
	echo "build proto ok"