package main

import (
	"context"
	"google.golang.org/grpc/metadata"
	"log"
	"time"

	"google.golang.org/grpc"
	pb "xim/proto/api/basemsgcallin"
)

func main() {
	// 连接到 gRPC 服务器
	conn, err := grpc.Dial("192.168.11.129:9002", grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}
	defer conn.Close()

	// 创建 gRPC 客户端
	client := pb.NewSClient(conn)

	// 设置请求参数
	req := &pb.TestReq{
		// 填写请求参数
	}

	// 调用 Test 方法

	md := metadata.Pairs("x-client-trace-id", "abcdefg")
	ctx, cancel := context.WithTimeout(metadata.NewOutgoingContext(context.Background(), md), time.Second)
	defer cancel()

	res, err := client.Test(ctx, req)
	if err != nil {
		log.Fatalf("could not call Test: %v", err)
	}

	// 处理响应
	log.Printf("Response: %v", res)
}
