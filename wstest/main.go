package main

import (
	"log"
	"time"

	"github.com/gorilla/websocket"
)

func main() {
	// Connect to the WebSocket server
	url := "wss://conn-test.boychat.net?ticker=5RylnX0npEzAKb_lB7j-LX3F4X231CNAAAHiikqs5qXdHJBcOSy-pYJiqpRo7hKR21hCXmy2FC904GWV1lX8htw6_194Tg_kCIA6OX_iTN5Gx0paGP--aaCQTUk6UlvtLBeunWv89lbpLx-PS30AtXixo6-IvxPKGuocWXeYY-dVcGwlTxcsvumssGDwS262LqWr6kvExgrkCg1zEL7Y0AZdOwnR9myab8q15txZZu6suBX2wcT1G7nujAOUqG1RQvjcw-eZ83XdDwgQKyzN1TVw1xGa5-Cs_jSaVs0RwZyuhk2ytOwpfSPYCLGt4NUaApzF02_EweefrTmbI8qFP1NPvccZIp3RUr-GbNOXO5qpAiPaDnOWmAKNLqqXKdQLBkuU5TM008KcpGaYf-_buHib3reevhRMcNxPnAba7oYl5ThRp3liRrULvStKJcq88ggZtEkF9tx3W2o0jYjkmh_TjryZL3-TXuvZ3D2JUt8" // Change to your WebSocket server URL
	c, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer c.Close()

	// Set the binary message type
	c.SetWriteDeadline(time.Now().Add(10 * time.Second))
	//pingmsg := []byte("{\"topicName\":\"upstream_msg\",\"payload\":\"{\\\"data\\\":\\\"{\\\\\\\"isFront\\\\\\\":false,\\\\\\\"userid\\\\\\\":\\\\\\\"8\\\\\\\"}\\\",\\\"type\\\":1}\",\"ack\":1}")

	textmsg := []byte("{\"topicName\":\"upstream_msg\",\"payload\":\"{\"data\":\"{\\\"content\\\":\\\"{\\\\\\\"content\\\\\\\":\\\\\\\"Text\\\\\\\"}\\\",\\\"contentType\\\":101,\\\"createTime\\\":1716348724541,\\\"msgId\\\":\\\"05b3c8e0-b072-4ec1-8420-5a8fde317908\\\",\\\"localId\\\":\\\"05b3c8e0-b072-4ec1-8420-5a8fde317908\\\",\\\"status\\\":0,\\\"to\\\":\\\"8\\\",\\\"msgFrom\\\":0,\\\"upgrade\\\":false}\",\"type\":2}\",\"ack\":1}")

	//msg := []byte("{\"data\":\"{\\\"content\\\":\\\"{\\\\\\\"content\\\\\\\":\\\\\\\"Text\\\\\\\"}\\\",\\\"contentType\\\":101,\\\"createTime\\\":1716348724541,\\\"msgId\\\":\\\"05b3c8e0-b072-4ec1-8420-5a8fde317908\\\",\\\"localId\\\":\\\"05b3c8e0-b072-4ec1-8420-5a8fde317908\\\",\\\"status\\\":0,\\\"to\\\":\\\"8\\\",\\\"msgFrom\\\":0,\\\"upgrade\\\":false}\",\"type\":2}")
	err = c.WriteMessage(websocket.BinaryMessage, textmsg)
	if err != nil {
		log.Println("write:", err)
		return
	}

	// Read a binary message from the WebSocket server
	_, message, err := c.ReadMessage()
	if err != nil {
		log.Println("read:", err)
		return
	}
	log.Printf("Received: %s", message)
}
