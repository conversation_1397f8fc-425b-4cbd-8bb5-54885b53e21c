set -e

if [ $# -lt 1 ]; then
    echo "参数个数必须大于1个"
    exit
fi

ALL_PROJECT=(
    "admin"
    "bizaccount"
    "bizchat"
    "bizhome"
    "bizmember"
    "bizmoment"
    "bizmsgchan"
    "bizpay"
    "biznotify"

    "svcaccount"
    "svcchat"
    "svcchatfees"
    "svcconfig"
    "svcmember"
    "svcmoment"
    "svcpush"
    "svcrecommend"
)

function build() {
    PROJECT=$1
    CI_COMMIT_REF_NAME=v1.1.8
    CI_REGISTRY=harbor.changyinlive.com
    CI_REGISTRY_USER=deploy-ci
    CI_REGISTRY_PASSWORD=FLk5O6FfADbH

    if [[ $PROJECT == "biznotify" || $PROJECT == "bizadvert" || $PROJECT == "jobxxl" ]]; then
        mkdir -p ${PROJECT}/deploy
        cd ${PROJECT}
        NAME=${PROJECT}
        GOOS=linux GOARCH=amd64 go build --ldflags '-extldflags -static' -o bin/vc.${PROJECT}.s
        IMAGE_NAME="${CI_REGISTRY}/osproject/xim/${PROJECT}"
        echo $name
        echo $image_name
        docker build ./ -f Dockerfile -t ${IMAGE_NAME}:${CI_COMMIT_REF_NAME} --platform=linux/amd64
        docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
        docker push ${IMAGE_NAME}:${CI_COMMIT_REF_NAME}
        docker rmi ${IMAGE_NAME}:${CI_COMMIT_REF_NAME}
    else
        mkdir -p ${PROJECT}/deploy
        cp proto/api/${PROJECT}/${PROJECT}.pb ${PROJECT}/deploy/proto.pb
        cd ${PROJECT}
        NAME=${PROJECT}

        GOOS=linux GOARCH=amd64 go build --ldflags '-extldflags -static' -o bin/vc.${PROJECT}.s
        IMAGE_NAME="${CI_REGISTRY}/osproject/xim/${PROJECT}"
        echo $name
        echo $image_name
        docker build ./ -f Dockerfile -t ${IMAGE_NAME}:${CI_COMMIT_REF_NAME} --platform=linux/amd64
        docker build ./ -f Dockerfile.initdata -t ${IMAGE_NAME}/${NAME}-initdata:${CI_COMMIT_REF_NAME} --platform=linux/amd64
        docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
        docker push ${IMAGE_NAME}:${CI_COMMIT_REF_NAME}
        docker push ${IMAGE_NAME}/${NAME}-initdata:${CI_COMMIT_REF_NAME}
        docker rmi ${IMAGE_NAME}:${CI_COMMIT_REF_NAME}
        docker rmi ${IMAGE_NAME}/${NAME}-initdata:${CI_COMMIT_REF_NAME}
        rm -rf  ${PROJECT}/deploy/proto.pb
    fi
    # updateTime=$(git show --date=iso8601 | grep Date | head -n1 | awk '{print $2" "$3}')
    updateTime=$(date "+%Y-%m-%d %H:%M:%S")
    echo $updateTime
    descript=$(git tag -ln ${CI_COMMIT_REF_NAME})
    REPO_NAME=https://gitlab.changyinlive.com/osproject/xim/${NAME}
    echo '
{
    "tag_time": "'${updateTime}'",
    "product": "beta",
    "git_url": "'${REPO_NAME}'",
    "service_name": "'${NAME}'",
    "service_description": "'${NAME}'",
    "version_description": "'${descript}'",
    "value": "'${CI_COMMIT_REF_NAME}'",
    "group_name": "osproject"
}' | curl --location 'https://xops-office.dloverai.com/api/v1/public/service/version' \
        --header 'Content-Type: application/json' \
        --data @-
}

if [[ $1 == "all" ]]; then
    root_dir=$(pwd)
    for ((i = 0; i < ${#ALL_PROJECT[@]}; i++)); do
        #${#array[@]}获取数组长度用于循环
        build ${ALL_PROJECT[i]}
        cd $root_dir
    done
else
    build $1
fi

#build $1
