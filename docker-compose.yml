version: '3.3'
services:
    jaeger:
      image: jaegertracing/all-in-one:latest
      container_name: jaeger
      expose:
        - "16686"
      ports:
        - "4317:4317/tcp"  # UDP port for tchannel
        - "16686:16686/tcp"  # HTTP port for web UI
      networks:
        - xim
    redis:
        image: redis
        environment:
          REDIS_PASSWORD: 123456
        ports:
            - 6379:6379
        networks:
            - xim
    basemsgbroker:
      build:
        context: ./basemsgbroker/
        dockerfile: Dockerfile
      command:
        - ./basemsgbroker/vc.basemsgbroker.s
        - --basemsg_config=deploy/dev/config.yml
      ports:
        - 8081:8081
      environment:
        - SELF_POD_IP=basemsgbroker
        - env=dev
      depends_on:
        - redis
      networks:
        - xim
    basemsgcallin:
      build:
        context: ./basemsgcallin/
        dockerfile: Dockerfile
      command:
        - ./basemsgcallin/vc.basemsgcallin.s
        - --basemsg_config=deploy/dev/config.yml
      environment:
        - env=dev
      depends_on:
        - redis
      networks:
        - xim
    basemsgdispatcher:
      build:
        context: ./basemsgdispatcher/
        dockerfile: Dockerfile
      command:
        - ./basemsgdispatcher/vc.basemsgdispatcher.s
        - --basemsg_config=deploy/dev/config.yml
      environment:
        - env=dev
      depends_on:
        - redis
      networks:
        - xim
    basemsgtopic:
      build:
        context: ./basemsgtopic/
        dockerfile: Dockerfile
      command:
        - ./basemsgtopic/vc.basemsgtopic.s
        - --basemsg_config=deploy/dev/config.yml
      environment:
        - env=dev
      depends_on:
        - redis
      networks:
        - xim
    basemsgtransfer:
      build:
        context: ./basemsgtransfer/
        dockerfile: Dockerfile
      command:
        - -config=deploy/dev/config.yml
      environment:
        - env=dev
      depends_on:
        - redis
      networks:
        - xim
    envoy:
      image: envoyproxy/envoy:v1.9.0
      environment:
        - loglevel=debug
      volumes:
        - ./envoy/deploy:/etc/envoy/
      expose:
        - "9002"
        - "8001"
      ports:
        - "9002:9002"
        - "8001:8001"
      networks:
        - xim
networks:
  xim:
    driver: bridge