package model

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/database"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

// User 用户基础信息
type User struct {
	UserId            int64             `gorm:"primaryKey;column:user_id" json:"user_id"`                        //用户id
	Nickname          string            `gorm:"column:nickname" json:"nickname"`                                 //用户昵称
	NicknamePinyin    string            `gorm:"column:nickname_pinyin" json:"nickname_pinyin"`                   //昵称拼音
	Phone             string            `gorm:"column:phone" json:"phone"`                                       //手机号
	Avatar            string            `gorm:"column:avatar" json:"avatar"`                                     //头像
	Gender            int32             `gorm:"column:gender" json:"gender"`                                     //性别
	Status            consts.UserStatus `gorm:"column:status" json:"status"`                                     //状态
	BackgroundUrl     string            `gorm:"column:background_url" json:"background_url"`                     //背景图
	VoiceSignatureUrl string            `gorm:"column:voice_signature_url" json:"voice_signature_url"`           //语音签名
	VoiceDuration     int32             `gorm:"column:voice_duration" json:"voice_duration"`                     //语音时长
	IsPremiumCreator  bool              `gorm:"column:is_premium_creator" json:"is_premium_creator"`             //是否优质创作者
	CreationUserId    uint              `gorm:"column:creation_user_id;default:0;index" json:"creation_user_id"` //创建该用户的admin用户ID，用于数据权限控制
	DeletedAt         int64             `gorm:"column:deleted_at;default:0;index" json:"deleted_at"`             //软删除时间戳，0表示未删除
	CreatedAt         int64             `gorm:"column:created_at" json:"created_at"`                             //创建时间
	UpdatedAt         int64             `gorm:"column:updated_at" json:"updated_at"`                             //更新时间
}

func (User) TableName() string { return "users" }

// 数据库索引说明：
// 1. 复合唯一索引：UNIQUE KEY `uk_phone_deleted_at` (`phone`, `deleted_at`)
//    - 保证同一手机号在未删除状态下唯一
//    - 允许已删除用户的手机号重新注册
// 2. 普通索引：KEY `idx_deleted_at` (`deleted_at`)
//    - 便于查询已删除/未删除用户

type UserModelInterface interface {
	CreateUser(ctx context.Context, user *User) error
	CreateUserWithTx(ctx context.Context, tx *gorm.DB, user *User) error
	GetUserByUserId(ctx context.Context, userId int64) (*User, error)
	GetUserByPhone(ctx context.Context, phone string) (*User, error)
	GetActiveUserByPhone(ctx context.Context, phone string) (*User, error)
	GetUserByCreationUserId(ctx context.Context, creationUserId uint) (*User, error)
	ExistsNickname(ctx context.Context, nickname string) (bool, error)
	DeleteUser(ctx context.Context, userId int64) error
	GetUsersByIDs(ctx context.Context, userIDs []int64) ([]*User, error)
	UpdateUser(ctx context.Context, userId int64, updates map[string]any) error
	UpdateUserWithTx(ctx context.Context, tx *gorm.DB, userId int64, updates map[string]any) error
	SearchUsers(ctx context.Context, keyword string, page, pageSize int32) ([]*User, int64, error)
}

type UserModel struct {
	DB            *gorm.DB
	userAuthModel UserAuthModelInterface
	regInfoModel  UserRegisterInfoModelInterface
}

func NewUserModel() *UserModel {
	return &UserModel{
		DB:            database.GetMysqlDB("vc_user"),
		regInfoModel:  NewUserRegisterInfoModel(),
		userAuthModel: NewUserAuthModel(),
	}
}

func (u User) GetFullCharacterAvatarURL() string {
	return alioss.FillCharacterAvatarUrl(u.Avatar)
}

func (u User) GetFullBackgroundUrl() string {
	return alioss.FillImageUrl(u.BackgroundUrl)
}

func (u User) GetFullVoiceSignatureUrl() string {
	return alioss.FillAudioUrl(u.VoiceSignatureUrl)
}

func (m *UserModel) CreateUser(ctx context.Context, user *User) error {
	user.CreatedAt = util.NowTimeMillis()
	user.UpdatedAt = util.NowTimeMillis()
	err := m.DB.WithContext(ctx).Create(user).Error
	if err != nil {
		return errcode.ErrUserCreateFailed
	}
	return nil
}

func (m *UserModel) GetUserByUserId(ctx context.Context, userId int64) (*User, error) {
	var user User
	err := m.DB.WithContext(ctx).Where("user_id = ?", userId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrUserNotFound
		}
		return nil, errcode.ErrUserQueryFailed
	}
	return &user, nil
}

func (m *UserModel) GetUserByCreationUserId(ctx context.Context, creationUserId uint) (*User, error) {
	var user User
	err := m.DB.WithContext(ctx).Where("creation_user_id = ? AND deleted_at = 0", creationUserId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrUserNotFound
		}
		return nil, errcode.ErrUserQueryFailed
	}
	return &user, nil
}

func (m *UserModel) GetUserByPhone(ctx context.Context, phone string) (*User, error) {
	var user User
	err := m.DB.WithContext(ctx).Where("phone = ?", phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrUserNotFound
		}
		return nil, errcode.ErrUserQueryFailed
	}
	return &user, nil
}

// GetActiveUserByPhone 获取未删除的用户（专门用于登录验证）
func (m *UserModel) GetActiveUserByPhone(ctx context.Context, phone string) (*User, error) {
	var user User
	err := m.DB.WithContext(ctx).Where("phone = ? AND deleted_at = 0", phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrUserNotFound
		}
		return nil, errcode.ErrUserQueryFailed
	}
	return &user, nil
}

func (m *UserModel) ExistsNickname(ctx context.Context, nickname string) (bool, error) {
	var count int64
	err := m.DB.WithContext(ctx).Model(&User{}).
		Where("nickname = ? AND status = ? AND deleted_at = 0", nickname, 1).
		Count(&count).Error
	if err != nil {
		return false, errcode.ErrUserQueryFailed
	}
	return count > 0, nil
}

func (m *UserModel) DeleteUser(ctx context.Context, userId int64) error {
	return m.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查用户是否存在且未被删除
		var user User
		if err := tx.Where("user_id = ? AND deleted_at = 0", userId).First(&user).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errcode.ErrUserNotFound
			}
			return errcode.ErrUserQueryFailed
		}

		// 软删除用户：更新deleted_at字段
		now := util.NowTimeMillis()
		updates := map[string]any{
			"status":     consts.UserStatusCancel,
			"deleted_at": now,
			"updated_at": now,
		}
		if err := tx.Model(&User{}).Where("user_id = ? AND deleted_at = 0", userId).Updates(updates).Error; err != nil {
			return errcode.ErrUserUpdateFailed
		}

		// 删除用户认证信息（保持硬删除，因为认证信息不需要保留）
		if err := tx.Where("user_id = ?", userId).Delete(&UserAuth{}).Error; err != nil {
			return errcode.ErrUserAuthUpdateFailed
		}

		return nil
	})
}

func (m *UserModel) GetUsersByIDs(ctx context.Context, userIDs []int64) ([]*User, error) {
	var users []*User
	if len(userIDs) == 0 {
		return users, nil
	}

	err := m.DB.WithContext(ctx).
		Where("user_id IN ?", userIDs).
		Find(&users).Error
	if err != nil {
		return nil, errcode.ErrUserQueryFailed
	}

	return users, nil
}

func (m *UserModel) UpdateUser(ctx context.Context, userId int64, updates map[string]any) error {
	err := m.DB.WithContext(ctx).Model(&User{}).
		Where("user_id = ?", userId).
		UpdateColumns(updates).Error
	if err != nil {
		return errcode.ErrUserUpdateFailed
	}
	return nil
}

func (m *UserModel) UpdateUserWithTx(ctx context.Context, tx *gorm.DB, userId int64, updates map[string]any) error {
	err := tx.WithContext(ctx).Model(&User{}).
		Where("user_id = ?", userId).
		UpdateColumns(updates).Error
	if err != nil {
		return errcode.ErrUserUpdateFailed
	}
	return nil
}

func (m *UserModel) CreateUserWithTx(ctx context.Context, tx *gorm.DB, user *User) error {
	user.CreatedAt = util.NowTimeMillis()
	user.UpdatedAt = util.NowTimeMillis()
	err := tx.WithContext(ctx).Create(user).Error
	if err != nil {
		return errcode.ErrUserCreateFailed
	}
	return nil
}

func (m *UserModel) SearchUsers(ctx context.Context, keyword string, page, pageSize int32) ([]*User, int64, error) {
	var users []*User
	var total int64

	searchCondition := "nickname LIKE BINARY ? OR nickname_pinyin LIKE ?"
	likeKeyword := "%" + keyword + "%"

	err := m.DB.WithContext(ctx).
		Model(&User{}).
		Where(searchCondition, likeKeyword, likeKeyword).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrUserQueryFailed
	}

	err = m.DB.WithContext(ctx).
		Where(searchCondition, likeKeyword, likeKeyword).
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Order("nickname").
		Find(&users).Error
	if err != nil {
		return nil, 0, errcode.ErrUserQueryFailed
	}

	return users, total, nil
}
