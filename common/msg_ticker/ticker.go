package msg_ticker

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"xim/proto/api/common"
)

const (
	chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	encryptionKey = "nIASD91jsnfvas9y"
	ticketFmtLen  = 5
	tickerFmt     = "%s@%s@%s@%s@%d@%s@%s" //后4个分别是用户userid，和平台platformid，设备id，扩展信息
	tickerVer     = "msgserver.ticker.version"
)

// Ticket 连接凭证
type Ticket struct {
	Token          string           //token备份记录
	Time           int64            //token下发时间
	UserId         string           //用户userid
	PlatformId     uint64           //用户所在的平台
	DeviceId       string           //用户设备id
	ExtendDataJson string           //用户补充扩展信息，json包的字符串
	ExtendData     TicketExtendData //用户扩展数据，只会从ExtendDataJson解析获取，默认为空
}

type TicketExtendData struct {
	ClientId      uint32            `json:"clientId"`
	ClientVersion string            `json:"clientVersion"`
	Scheme        string            `json:"scheme"` //长连接协议，目前有mqtt，ws协议
	BaseParam     *common.BaseParam `json:"base_param"`
}

// Encode 获取token，并设置到ticker
func (t *Ticket) EncodeToToken() (err error) {
	randomStr := randString(3)
	hexStr := strconv.FormatInt(unixMilli(), 16)
	src := fmt.Sprintf(tickerFmt, tickerVer, randomStr, hexStr, t.UserId, t.PlatformId, t.DeviceId, t.ExtendDataJson)
	t.Token, err = AesEncrypt(src, encryptionKey)
	return
}

// Decode 解码字节形式的token
func (t *Ticket) Decode(token []byte) (err error) {
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Sprintf("panic info: %v", r)
			err = errors.New(errMsg)
		}
	}()
	if token == nil || len(token) < 1 {
		return errors.New("invalid token")
	}
	str := AesDecrypt(string(token), encryptionKey)
	ss := strings.Split(str, "@")
	if len(ss) < ticketFmtLen {
		return errors.New("token length error")
	}
	t.Token = string(token)
	if t.Time, err = strconv.ParseInt(ss[2], 16, 64); err != nil {
		return
	}
	t.UserId = ss[3]
	if t.PlatformId, err = strconv.ParseUint(ss[4], 10, 64); err != nil {
		return
	}
	if len(ss) > ticketFmtLen {
		t.DeviceId = ss[5]
	}
	if len(ss) > 6 {
		t.ExtendDataJson = ss[6]
		if t.ExtendDataJson != "" {
			json.Unmarshal([]byte(t.ExtendDataJson), &t.ExtendData)
		}
	}

	return
}

// Allow 是否有效
func (t *Ticket) Allow() bool {
	if t == nil {
		return false
	}
	if t.Token == "" || t.UserId == "" {
		return false
	}
	return true
}

// 生成随机字符串
func randString(size uint) string {
	rand.Seed(time.Now().UnixNano())
	s := make([]byte, size)
	for i := 0; i < int(size); i++ {
		s[i] = chars[rand.Intn(len(chars))]
	}
	return string(s)
}

// 返回当前时刻的milliseconds
func unixMilli() int64 {
	return time.Now().Round(time.Millisecond).UnixNano() / (int64(time.Millisecond) / int64(time.Nanosecond))
}
