package msg_ticker

import "testing"

func TestDecode(t *testing.T) {
	token := "pXUPYN5MYUuf8NSh_DVmbi895Uu0moX5s9wEcdNmFZltiUYVRQ1lBwITfXHEs7wa7pxR41CmZt61ilP9vq-jyhhHXJNvRBrUuUSEaD1p-QnmG52k3FwnazL86lwIsy4VBXDC3CQK001dETZHGQMj9XBlAMVi3yhLB42glOwHATcSrJM5hmWXFm2GqsXt4lrawQgFVmjYgNJ0J0tTyUbwtSjimVAUpBpV7jwn7ZD6H77f_WkYZqiEvqcwGtWhnm14goHvF70xeCstjqIh4ztW0zdi5aQUUUTxKvdGmUR0QP2-Be0YXbiaelYJ-UBukP10BIxdTyCvGTchyPdC_k__V8hPusmJfLSAGwJYl1xxDJmrRZoujzf6hBAMN4pYY5CBOyuDosEIjmxY7P6G5_25SCg4Aul1cV5sLr9vcTtZqYaiVI63IZeY4WU2dIetBiAw"
	ticker := Ticket{}
	err := ticker.Decode([]byte(token))
	if err != nil {
		t.Errorf("err=%+v", err)
	}
	t.Logf("ticker=%+v", ticker)
}
