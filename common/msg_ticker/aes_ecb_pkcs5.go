package msg_ticker

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"strings"

	log "xim/baselib/logger"
)

type Ecb struct {
	Block        cipher.Block
	EcbBlockSize int
}

func NewECB(block cipher.Block) *Ecb {
	return &Ecb{
		Block:        block,
		EcbBlockSize: block.BlockSize(),
	}
}

type EcbEncrypter Ecb

// NewEcbEncrypter returns a BlockMode which encrypts in electronic code book
// mode, using the given Block.
func NewEcbEncrypter(block cipher.Block) cipher.BlockMode {
	return (*EcbEncrypter)(NewECB(block))
}

// BlockSize 继承实现BlockSize
func (encrypter *EcbEncrypter) BlockSize() int {
	return encrypter.EcbBlockSize
}

// CryptBlocks 继承实现CryptBlocks
func (encrypter *EcbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%encrypter.EcbBlockSize != 0 {
		log.Errorf("crypto/cipher: input not full blocks")
		return
	}
	if len(dst) < len(src) {
		log.Errorf("crypto/cipher: output smaller than input")
		return
	}
	for len(src) > 0 {
		encrypter.Block.Encrypt(dst, src[:encrypter.EcbBlockSize])
		src = src[encrypter.EcbBlockSize:]
		dst = dst[encrypter.EcbBlockSize:]
	}
}

// AesEncrypt AES/ECB/PKCS5,并返回Base64UrlSafeEncode
func AesEncrypt(src, key string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		log.Errorf("NewCipher error, %v", err)
		return "", err
	}
	if src == "" {
		log.Errorf("src content empty!")
		return "", err
	}
	ecb := NewEcbEncrypter(block)
	content := []byte(src)
	content = PKCS5Padding(content, block.BlockSize())
	crypted := make([]byte, len(content))
	ecb.CryptBlocks(crypted, content)
	base64Crypted := Base64UrlSafeEncode(crypted)
	return base64Crypted, nil
}

type EcbDecrypter Ecb

// NewEcbDecrypter returns a BlockMode which decrypts in electronic code book
// mode, using the given Block.
func NewEcbDecrypter(block cipher.Block) cipher.BlockMode {
	return (*EcbDecrypter)(NewECB(block))
}

// BlockSize 继承实现BlockSize
func (decrypter *EcbDecrypter) BlockSize() int {
	return decrypter.EcbBlockSize
}

// CryptBlocks 继承实现CryptBlocks
func (decrypter *EcbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%decrypter.EcbBlockSize != 0 {
		log.Errorf("crypto/cipher: input not full blocks")
		return
	}
	if len(dst) < len(src) {
		log.Errorf("crypto/cipher: output smaller than input")
		return
	}
	for len(src) > 0 {
		decrypter.Block.Decrypt(dst, src[:decrypter.EcbBlockSize])
		src = src[decrypter.EcbBlockSize:]
		dst = dst[decrypter.EcbBlockSize:]
	}
}

// AesDecrypt 编码
func AesDecrypt(crypted, key string) string {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		log.Errorf("AesDecrypt failed, error: %v:", err)
	}
	blockMode := NewEcbDecrypter(block)
	urlDecodeCrypted := Base64URLSafeDecode(crypted)
	origData := make([]byte, len(urlDecodeCrypted))
	blockMode.CryptBlocks(origData, urlDecodeCrypted)
	origData = PKCS5UnPadding(origData)
	return string(origData)
}

// PKCS5Padding PKCS5填充
func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS5UnPadding PKCS5解码
func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	// 去掉最后一个字节 unpadding 次
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// Base64UrlSafeEncode 转为base64
func Base64UrlSafeEncode(source []byte) string {
	// Base64 Url Safe is the same as Base64 but does not contain '/' and '+' (replaced by '_' and '-') and trailing '=' are removed.
	byteArr := base64.StdEncoding.EncodeToString(source)
	safeUrl := strings.Replace(string(byteArr), "/", "_", -1)
	safeUrl = strings.Replace(safeUrl, "+", "-", -1)
	safeUrl = strings.Replace(safeUrl, "=", "", -1)
	return safeUrl
}

// Base64URLSafeDecode base64解码
func Base64URLSafeDecode(encodeData string) []byte {
	var missing = (4 - len(encodeData)%4) % 4
	encodeData += strings.Repeat("=", missing)
	encodeData = strings.Replace(encodeData, "_", "/", -1)
	encodeData = strings.Replace(encodeData, "-", "+", -1)
	decodeData, err := base64.StdEncoding.DecodeString(encodeData)
	if err != nil {
		log.Errorf("Decode failed, encodeData: %s, error: %v", encodeData, err)
		return nil
	}
	return decodeData
}
