package header

import (
	"fmt"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"net"
	"net/url"
	"strconv"
	"strings"
)

type Header struct {
	//request_uri string
	//version         string
	//server          string
	//method          string
	//opaque          string
	//username   string
	//password   string
	//password_set bool
	//host            string
	//path            string
	//raw_path         string
	//force_query      string
	//raw_query        string
	//fragment        string
	//remote_addr      string
	//is_form_http bool
	ctx context.Context

	headers map[string]string
	//trailers map[string]string
	cookies map[string]string
	md      metadata.MD
}

// 注意：所有的key最终会被转换为小写
// 示例
/*
import "github.com/jilieryuyi/grpc-gateway/tools/header"
//这里的ctx来源于serverimp.go的接口上下文ctx context.Context
h := header.NewHeader(ctx)
appid := h.GetCookie("appid")
*/

func NewHeader(ctx context.Context) *Header {
	header := &Header{
		ctx:     ctx,
		headers: make(map[string]string),
		cookies: make(map[string]string),
		md:      make(metadata.MD),
	}
	header.parse()
	return header
}

func (h *Header) parse() {
	md, ok := metadata.FromIncomingContext(h.ctx)
	if ok {
		for key, value := range md {
			if len(value) > 0 {
				h.headers[key] = value[0]
			} else {
				h.headers[key] = ""
			}
		}
	}

	cookies, ok := md["cookie"]
	if ok {
		if len(cookies) > 0 {
			c := strings.Split(cookies[0], ";")
			for _, iv := range c {
				iv = strings.Trim(iv, " ")
				pos := strings.Index(iv, "=")
				if pos > 0 {
					k := strings.ToLower(strings.Trim(iv[:pos], " "))
					v := strings.Trim(iv[pos+1:], " ")
					h.cookies[k] = v
				}
			}
		}
	}

	md, ok = metadata.FromOutgoingContext(h.ctx)
	if ok {
		for key, value := range md {
			if len(value) > 0 {
				h.headers[key] = value[0]
			} else {
				h.headers[key] = ""
			}
		}
	}

}

// 如果返回空字符串，说明key不存在
func (h *Header) GetHeader(key string) string {
	v, ok := h.headers[strings.ToLower(key)]
	if ok {
		return v
	}
	return ""
}

// 所有的key都是小写
func (h *Header) GetHeaders() map[string]string {
	return h.headers
}

// 如果返回空字符串，说明cookie不存在
func (h *Header) GetCookie(key string) string {
	v, ok := h.cookies[strings.ToLower(key)]
	if ok {
		return v
	}
	return ""
}

// 所有的key都是小写
func (h *Header) GetCookies() map[string]string {
	return h.cookies
}

func (h *Header) GetCookieInt64(key string) int64 {
	v, ok := h.cookies[strings.ToLower(key)]
	if ok {
		d, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return 0
		}
		return d
	}
	return 0
}

func (h *Header) GetHeaderInt64(key string) int64 {
	v, ok := h.headers[strings.ToLower(key)]
	if ok {
		d, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return 0
		}
		return d
	}
	return 0
}

// key、value 只能是数字或者字符串
func (h *Header) Set(key interface{}, value ...interface{}) *Header {
	//grpc的header的key必须为小写
	skey := strings.ToLower(fmt.Sprintf("%v", key))
	for _, v := range value {
		h.md[skey] = append(h.md[skey], fmt.Sprintf("%v", v))
	}
	return h
}

func (h *Header) SetCookie(key, value string) {
	key = strings.ToLower(key)
	c, ok := h.md["cookie"]
	if ok && len(c) > 0 {
		h.md["cookie"] = []string{c[0] + "; " + key + "=" + value}
	} else {
		h.md["cookie"] = []string{key + "=" + value}
	}
}

//server端发送header
func (h *Header) Send() {
	grpc.SendHeader(h.ctx, h.md)
}

// client端是通过context发送header的
func (h *Header) ClientContext() context.Context {
	return metadata.NewOutgoingContext(h.ctx, h.md)
}

func (h *Header) ServerContext() context.Context {
	return metadata.NewIncomingContext(h.ctx, h.md)
}

func (h *Header) GetGuid() string {
	if guid := h.GetCookie("guid"); guid != "" {
		return guid
	}
	if XLA_CI := h.GetCookie("XLA_CI"); XLA_CI != "" {
		return XLA_CI
	}
	if STATIS_CI := h.GetCookie("STATIS_CI"); STATIS_CI != "" {
		return STATIS_CI
	}
	return ""
}

// 获取用户的登录态access_token
func (h *Header) GetAccessToken() string {
	au := h.GetHeader("Authorization")
	temps := strings.SplitN(au, " ", 2)
	if len(temps) != 2 || temps[0] != "Bearer" {
		return ""
	}
	token := temps[1]
	return token
}

// 获取用户的数字型id
func (h *Header) GetUserid() uint64 {
	userid := h.GetCookie("userid")
	uid, err := strconv.ParseUint(userid, 10, 64)
	if err != nil {
		return 0
	}
	return uid
}

// 获取渠道包类型
func (h *Header) GetChannel() string {
	return h.GetCookie("channel")
}

// 获取用户的设备id
func (h *Header) GetDeviceid() string {
	if deviceid := h.GetHeader("x-device-id"); deviceid != "" {
		return deviceid
	}
	if deviceid := h.GetCookie("deviceid"); deviceid != "" {
		return deviceid
	}
	return ""
}

func (h *Header) GetAppid() int32 {
	appid := h.GetHeader("appid")
	aid, err := strconv.ParseInt(appid, 10, 32)
	if err != nil {
		return 0
	}
	return int32(aid)
}

// 获取用户的客户端ip
func (h *Header) GetClientIP() string {
	xForwardedFor := h.GetHeader("x-forwarded-for")
	ip := strings.TrimSpace(strings.Split(xForwardedFor, ",")[0])
	if ip != "" {
		return ip
	}

	ip = strings.TrimSpace(h.GetHeader("x-real-ip"))
	if ip != "" {
		return ip
	}

	pr, ok := peer.FromContext(h.ctx)
	if !ok {
		return ip
	}
	if ip, _, err := net.SplitHostPort(strings.TrimSpace(pr.Addr.String())); err == nil {
		return ip
	}

	return ""
}

// 获取referer-host
func (h *Header) GetRefererHost() string {
	referer := h.GetHeader("referer")
	if referer == "" {
		return ""
	}
	u, err := url.Parse(referer)
	if err != nil {
		return ""
	}
	return u.Host
}

func (h *Header) GetLoginData() map[string]interface{} {
	loginData := make(map[string]interface{}, 0)
	loginData["sessionid"] = h.GetCookie("sessionid")
	loginData["deviceid"] = h.GetCookie("deviceid")
	loginData["userid"] = h.GetCookie("userid")
	loginData["appid"] = "-1" //这里特殊化，由于迅雷直播平台需要传appid，可默认填-1
	loginData["token"] = h.GetCookie("token")
	loginData["mid"] = h.GetCookie("h_m")
	return loginData
}
