package signature

import (
	"crypto/md5"
	"encoding/hex"
	"sort"
	"strings"
	"encoding/json"
	"fmt"
)

//interface转成map
func InterfaceParamsToMap(params interface{}) (map[string]string, error) {
	paramsByte, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	paramsMap := make(map[string]interface{}, 0)
	err = json.Unmarshal(paramsByte, &paramsMap)
	if err != nil {
		return nil, err
	}
	newParamsMap := make(map[string]string, 0)
	for k, val := range paramsMap {
		if valFloat, ok := val.(float64); ok {
			newParamsMap[k] = fmt.Sprintf("%d", uint64(valFloat))
		} else {
			newParamsMap[k] = fmt.Sprintf("%v", val)
		}
	}
	return newParamsMap, nil
}

//获取签名
func GetTwoSignture(signKey string, params map[string]string) string {
	var sortKeys []string
	for key, _ := range params {
		if key != "sign" {
			item := fmt.Sprintf("%s=%v", key, params[key])
			sortKeys = append(sortKeys, item)
		}
	}
	sort.Strings(sortKeys)

	h := md5.New()
	h.Write([]byte(strings.Join(sortKeys, "&") + "&key=" + signKey))
	x := h.Sum(nil)
	return hex.EncodeToString(x)
}