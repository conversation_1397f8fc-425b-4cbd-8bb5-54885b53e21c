package server

import (
	"time"

	log "xim/baselib/logger"
	"xim/basemsgcallin/config"
	"xim/basemsgcallin/controller"
	"xim/basemsgcallin/stat"
	pb_basemsgcallin "xim/proto/api/basemsgcallin"

	"golang.org/x/net/context"
)

// BasemsgcallinServer 服务接入层
// 负责实现proto协议中定义的grpc接口，检查请求参数，拦截非法请求，记录访问日志
type BasemsgcallinServer struct {
	controller *controller.BasemsgcallinController
}

// NewBasemsgcallinServer 创建basemsgcallin服务
// 初始化controller和业务自定义模块
func NewBasemsgcallinServer(conf *config.Config) (*BasemsgcallinServer, error) {
	s := &BasemsgcallinServer{}

	var err error
	s.controller, err = controller.NewBasemsgcallinController(conf)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Test 实现Test接口
func (s *BasemsgcallinServer) Test(ctx context.Context, req *pb_basemsgcallin.TestReq) (*pb_basemsgcallin.TestRsp, error) {
	var (
		err       error
		res       *pb_basemsgcallin.TestRsp
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("Test req:%+v res:%v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.Test(ctx, req)
	return res, nil
}

// 获取长连接所需的校验token，以及连接地址端口
func (s *BasemsgcallinServer) GetMsgConnToken(ctx context.Context, req *pb_basemsgcallin.GetMsgConnTokenReq) (*pb_basemsgcallin.GetMsgConnTokenRsp, error) {
	var (
		err       error
		res       = &pb_basemsgcallin.GetMsgConnTokenRsp{}
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("GetMsgConnToken req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.GetMsgConnToken(ctx, req)
	return res, nil
}

// 发布下行消息到长连接客户端
func (s *BasemsgcallinServer) PublishMsg(ctx context.Context, req *pb_basemsgcallin.PublishMsgReq) (*pb_basemsgcallin.PublishMsgRsp, error) {
	var (
		err       error
		res       = &pb_basemsgcallin.PublishMsgRsp{}
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("PublishMsg req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.PublishMsg(ctx, req)
	return res, nil
}

// 平台用户订阅topic
func (s *BasemsgcallinServer) SubscribeTopic(ctx context.Context, req *pb_basemsgcallin.SubscribeTopicReq) (*pb_basemsgcallin.SubscribeTopicRsp, error) {
	var (
		err       error
		res       = &pb_basemsgcallin.SubscribeTopicRsp{}
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("SubscribeTopic req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.SubscribeTopic(ctx, req)
	return res, nil
}

// 平台用户取消订阅topic
func (s *BasemsgcallinServer) UnSubscribeTopic(ctx context.Context, req *pb_basemsgcallin.UnSubscribeTopicReq) (*pb_basemsgcallin.UnSubscribeTopicRsp, error) {
	var (
		err       error
		res       = &pb_basemsgcallin.UnSubscribeTopicRsp{}
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("UnSubscribeTopic req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.UnSubscribeTopic(ctx, req)
	return res, nil
}

// 批量查询平台用户的订阅topic信息
func (s *BasemsgcallinServer) UsersTopics(ctx context.Context, req *pb_basemsgcallin.UsersTopicsReq) (*pb_basemsgcallin.UsersTopicsRsp, error) {
	var (
		err       error
		res       = &pb_basemsgcallin.UsersTopicsRsp{}
		beginTime = time.Now()
	)

	defer func() {
		log.Infof("UsersTopics req:%+v res:%+v err:%v deal time:%v", req, res, err, stat.Latency(beginTime))
	}()

	res, err = s.controller.UsersTopics(ctx, req)
	return res, nil
}
