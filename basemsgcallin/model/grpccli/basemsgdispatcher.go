package grpccli

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"xim/baselib/grpccli"
	log "xim/baselib/logger"
	"xim/proto/api/basemsgdispatcher"
)

var basemsgdispatcherClient basemsgdispatcher.SClient

func NewBasemsgdispatcher() error {
	var err error
	var conn *grpc.ClientConn
	conn, err = grpccli.NewLocalConn()
	if err != nil {
		return err
	}
	basemsgdispatcherClient = basemsgdispatcher.NewSClient(conn)
	return nil
}

func PublishMsg(ctx context.Context, payload string, tType int32, platformId uint64, userId, deviceId string, excludeDeviceIds []string,
	topic string, includeUserIds, excludeUserIds []string, broadcastRate uint32, limitList []*basemsgdispatcher.ClientLimit) error {
	req := &basemsgdispatcher.PublishMsgReq{
		Payload:          payload,
		Type:             basemsgdispatcher.PublishTargetType(tType),
		PlatformId:       platformId,
		UserId:           userId,
		DeviceId:         deviceId,
		ExcludeDeviceIds: excludeDeviceIds,
		Topic:            topic,
		IncludeUserIds:   includeUserIds,
		ExcludeUserIds:   excludeUserIds,
		BroadcastRate:    broadcastRate,
		LimitList:        limitList,
	}
	rsp, err := basemsgdispatcherClient.PublishMsg(ctx, req)
	defer func() {
		if err == nil {
			log.Infof("PublishMsg req %+v rsp %v", req, rsp)
		} else {
			log.Errorf("PublishMsg req %+v error %v", req, err)
		}
	}()
	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	}
	return err
}
