package grpccli

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"sync"
	"xim/baselib/grpccli"
	log "xim/baselib/logger"
	"xim/proto/api/basemsgcallin"
	"xim/proto/api/basemsgtopic"
)

var basemsgtopicClient basemsgtopic.SClient

func NewBasemsgtopicClinet() error {
	var err error
	var conn *grpc.ClientConn
	conn, err = grpccli.NewLocalConn()
	if err != nil {
		return err
	}
	basemsgtopicClient = basemsgtopic.NewSClient(conn)
	return nil
}

func Subscribe(ctx context.Context, platformId uint64, userId, deviceId string, topics []string) error {
	req := &basemsgtopic.SubscribeReq{
		PlatformId: platformId,
		UserId:     userId,
		DeviceId:   deviceId,
		Topics:     topics,
	}
	rsp, err := basemsgtopicClient.Subscribe(ctx, req)
	defer func() {
		if err == nil {
			log.Infof("Subscribe req %+v rsp %v ", req, rsp)
		} else {
			log.Errorf("Subscribe req %+v error %v", req, err)
		}
	}()
	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	}
	return err
}

func UnSubscribe(ctx context.Context, platformId uint64, userId, deviceId string, topics []string) error {
	req := &basemsgtopic.UnSubscribeReq{
		PlatformId: platformId,
		UserId:     userId,
		DeviceId:   deviceId,
		Topics:     topics,
	}
	rsp, err := basemsgtopicClient.UnSubscribe(ctx, req)
	defer func() {
		if err == nil {
			log.Infof("UnSubscribe req %+v rsp %v", req, rsp)
		} else {
			log.Errorf("UnSubscribe req %+v error %v", req, err)
		}
	}()
	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	}
	return err
}

func UserTopics(ctx context.Context, platformId uint64, userId, deviceId string) (*basemsgtopic.UserTopicsData, error) {
	req := &basemsgtopic.UserTopicsReq{
		PlatformId: platformId,
		UserId:     userId,
		DeviceId:   deviceId,
	}
	rsp, err := basemsgtopicClient.UserTopics(ctx, req)
	defer func() {
		if err == nil {
			log.Infof("UserTopics req %+v rsp %+v", req, rsp)
		} else {
			log.Errorf("UserTopics req %+v error %v", req, err)
		}
	}()
	var data *basemsgtopic.UserTopicsData
	if err != nil {
		err = fmt.Errorf("error %v", err)
	} else if rsp == nil {
		err = fmt.Errorf("rsp nil")
	} else if rsp.Result != 0 {
		err = fmt.Errorf("failmsg %s", rsp.Message)
	} else {
		data = rsp.Data
	}
	return data, err
}

func UsersTopics(platformId uint64, userDevices []*basemsgcallin.UserDevice) (map[string]*basemsgtopic.UserTopicsData, error) {
	userDeviceMap := make(map[string]*basemsgcallin.UserDevice, 0)
	for _, userDevice := range userDevices {
		userDeviceId := fmt.Sprintf("%s@%s", userDevice.UserId, userDevice.DeviceId)
		userDeviceMap[userDeviceId] = userDevice
	}

	wg := sync.WaitGroup{}
	syncUserTopicMap := sync.Map{}
	for userDeviceId, userDevice := range userDeviceMap {
		wg.Add(1)
		go func(_platformId uint64, _userId, _deviceId, _userDeviceId string) {
			defer wg.Done()
			topicsData, _ := UserTopics(context.Background(), _platformId, _userId, _deviceId)
			if topicsData != nil && len(topicsData.List) > 0 {
				syncUserTopicMap.Store(_userDeviceId, topicsData)
			}
		}(platformId, userDevice.UserId, userDevice.DeviceId, userDeviceId)
	}
	wg.Wait()

	userTopicMap := make(map[string]*basemsgtopic.UserTopicsData, 0)
	syncUserTopicMap.Range(func(k, v interface{}) bool {
		userDeviceId := k.(string)
		topicsData := v.(*basemsgtopic.UserTopicsData)
		userDevice := userDeviceMap[userDeviceId]
		if userTopicMap[userDevice.UserId] == nil {
			userTopicMap[userDevice.UserId] = topicsData
		} else {
			userTopicMap[userDevice.UserId].List = append(userTopicMap[userDevice.UserId].List, topicsData.List...)
		}
		return true
	})
	return userTopicMap, nil
}
