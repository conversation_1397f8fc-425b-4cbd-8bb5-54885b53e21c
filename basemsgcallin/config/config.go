package config

import (
	"flag"
	"os"

	log "xim/baselib/logger"

	"github.com/jinzhu/configor"
)

type MsgConnAddrConfig struct {
	MasterHost   string `yaml:"master_host"`
	MasterWshost string `yaml:"master_wshost"`
	MasterPort   int32  `yaml:"master_port"`
	MasterWsport int32  `yaml:"master_wsport"`
	SlaveHost    string `yaml:"slave_host"`
	SlaveWshost  string `yaml:"slave_wshost"`
	SlavePort    int32  `yaml:"slave_port"`
	SlaveWsport  int32  `yaml:"slave_wsport"`
}

type JaegerConfig struct {
	ServiceName string `yaml:"service_name"`
	Endpoint    string `yaml:"endpoint"`
	Enable      bool   `default:"false" yaml:"enable"`
	Environment string `yaml:"environment"`
}

// Config 服务配置
type Config struct {
	Addr   string `default:"0.0.0.0:8080"`
	Logger string `default:"deploy/logger.xml"`
	Env    string
	//todo 平台的签名密钥先写死在配置文件，后续改成读数据库之类的
	PlatformKey map[uint64]string `yaml:"platform_key"`
	//todo 长连接连接地址，后续改到分平台返回地址，每个平台不同域名
	MsgconnAddr map[uint64]MsgConnAddrConfig `yaml:"msgconn_addr"`
	Jaeger      JaegerConfig
}

// NewConfig 读取配置
func NewConfig() Config {
	configPath := flag.String("basemsg_config", "deploy/config.yml", "configuration file")
	flag.Parse()

	var conf Config
	if err := configor.Load(&conf, *configPath); err != nil {
		log.Panicf("load config error: %v", err)
		os.Exit(1)
	}

	return conf
}
