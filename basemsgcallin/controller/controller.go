package controller

import (
	"context"

	"xim/baselib/logger"
	"xim/proto/api/basemsgdispatcher"
	"xim/proto/api/common"

	"time"

	"xim/basemsgcallin/config"
	"xim/basemsgcallin/model"
	"xim/basemsgcallin/model/grpccli"
	"xim/basemsgcallin/util"
	"xim/common/msg_ticker"
	"xim/common/signature"
	pb_basemsgcallin "xim/proto/api/basemsgcallin"
)

// BasemsgcallinController 服务控制层
// 用于控制业务流程、处理事件并作出响应。“事件”包括 gRPC 接口请求和数据 Model 上的改变（例如nsq事件）
// 按业务将 Controller 分割成不同的文件以利维护
type BasemsgcallinController struct {
	conf *config.Config
}

// NewBasemsgcallinController 创建basemsgcallin controller
func NewBasemsgcallinController(conf *config.Config) (*BasemsgcallinController, error) {
	c := &BasemsgcallinController{conf: conf}

	err := model.NewBasemsgcallinModel(conf)
	if err != nil {
		return nil, err
	}

	return c, nil
}

// Test 实现Test接口
func (c *BasemsgcallinController) Test(ctx context.Context, req *pb_basemsgcallin.TestReq) (*pb_basemsgcallin.TestRsp, error) {
	message := "basemsgcallin"
	return &pb_basemsgcallin.TestRsp{
		Base: &common.SvcBaseResp{
			Code: util.RetOK,
			Msg:  message,
		},
	}, nil
}

func (c *BasemsgcallinController) GetMsgConnToken(ctx context.Context, req *pb_basemsgcallin.GetMsgConnTokenReq) (*pb_basemsgcallin.GetMsgConnTokenRsp, error) {
	res := &pb_basemsgcallin.GetMsgConnTokenRsp{
		Base: &common.SvcBaseResp{
			Code: util.RetOK,
			Msg:  "success",
		},
	}

	//检查参数
	platformId, err := util.ParseUint64(req.PlatformId)
	if err != nil || platformId <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId wrong",
		}
		return res, nil
	}
	if req.UserId == "" || req.DeviceId == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "userid or deviceid empty",
		}
		return res, nil
	}
	//解析extendData
	extendData := &msg_ticker.TicketExtendData{}
	util.Json.UnmarshalFromString(req.ExtendData, extendData)
	extendDataJson := ""
	extendDataJson, _ = util.Json.MarshalToString(extendData)
	logger.Debugf("extendData  %v", extendDataJson)

	//todo 开放调试平台
	//检查签名
	//if platformId != 1 {
	platformSecretKey := c.conf.PlatformKey[platformId]
	if platformSecretKey == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId not exist",
		}
		return res, nil
	}
	signTool := signature.NewSignature(platformSecretKey, 2*time.Minute)
	signTool.SetUserReflect(true)
	isValid, err := signTool.Verify(req)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  err.Error(),
		}
		return res, err
	}
	if !isValid {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  "sign verify fail",
		}
		return res, nil
	}
	//}

	//生成长连接token，设置连接地址端口
	ticket := &msg_ticker.Ticket{
		UserId:         req.UserId,
		PlatformId:     platformId,
		DeviceId:       req.DeviceId,
		ExtendDataJson: extendDataJson,
	}
	err = ticket.EncodeToToken() //计算token并设置到ticket中
	if err != nil {
		logger.Errorf("ticket.EncodeToToken fail,err:%v", err)
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "token encode fail",
		}
		return res, err
	}
	mHost := c.conf.MsgconnAddr[platformId].MasterHost
	mPort := c.conf.MsgconnAddr[platformId].MasterPort
	sHost := c.conf.MsgconnAddr[platformId].SlaveHost
	sPort := c.conf.MsgconnAddr[platformId].SlavePort
	if extendData.Scheme == util.MsgConnScheme_Websocket {
		mHost = c.conf.MsgconnAddr[platformId].MasterWshost
		mPort = c.conf.MsgconnAddr[platformId].MasterWsport
		sHost = c.conf.MsgconnAddr[platformId].SlaveWshost
		sPort = c.conf.MsgconnAddr[platformId].SlaveWsport
	}
	res.Data = &pb_basemsgcallin.MsgTokenData{
		Token: ticket.Token,
		MasterAddr: &pb_basemsgcallin.MsgAddrData{
			Host: mHost,
			Port: mPort,
		},
		SlaveAddr: &pb_basemsgcallin.MsgAddrData{
			Host: sHost,
			Port: sPort,
		},
		GeneralTopic: "upstream_msg",   //todo 写死了上行消息通用topic
		KickTopic:    "sessioninvalid", //todo 写死了被踢下行消息的topic，该topic消息由topic服务下发
	}
	logger.Debugf("res %v", res)
	return res, nil
}

func (c *BasemsgcallinController) PublishMsg(ctx context.Context, req *pb_basemsgcallin.PublishMsgReq) (*pb_basemsgcallin.PublishMsgRsp, error) {
	res := &pb_basemsgcallin.PublishMsgRsp{
		Base: &common.SvcBaseResp{
			Code: util.RetOK,
			Msg:  "success",
		},
	}

	//检查参数
	platformId, err := util.ParseUint64(req.PlatformId)
	if err != nil || platformId <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId wrong",
		}
		return res, nil
	}
	if len(req.Payload) <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "Payload empty",
		}
		return res, nil
	}
	tType, err := util.ParseInt32(req.Type)
	if err != nil || tType <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "Type wrong",
		}
		return res, nil
	}
	targetInfo := &pb_basemsgcallin.PublishTargetInfo{}
	err = util.Json.Unmarshal([]byte(req.TargetJson), targetInfo)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "target info wrong json format",
		}
		return res, err
	}
	var limitList []*basemsgdispatcher.ClientLimit
	if len(targetInfo.LimitList) > 0 {
		limitListJson, err := util.Json.MarshalToString(targetInfo.LimitList)
		if err == nil && limitListJson != "" {
			limitList = make([]*basemsgdispatcher.ClientLimit, 0)
			err = util.Json.UnmarshalFromString(limitListJson, &limitList)
		}
		if err != nil {
			res.Base = &common.SvcBaseResp{
				Code: util.RetInvalidParam,
				Msg:  "target client limit wrong",
			}
			return res, err
		}
	}

	//todo 开放调试平台
	//检查签名
	//if platformId != 1 {
	platformSecretKey := c.conf.PlatformKey[platformId]
	if platformSecretKey == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId not exist",
		}
		return res, nil
	}
	signTool := signature.NewSignature(platformSecretKey, 2*time.Minute)
	signTool.SetUserReflect(true)
	isValid, err := signTool.Verify(req)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  err.Error(),
		}
		return res, err
	}
	if !isValid {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  "sign verify fail",
		}
		return res, nil
	}
	//}

	//下发消息
	err = grpccli.PublishMsg(context.Background(), req.Payload, tType, platformId, targetInfo.UserId, targetInfo.DeviceId, targetInfo.ExcludeDeviceIds,
		targetInfo.Topic, targetInfo.IncludeUserIds, targetInfo.ExcludeUserIds, targetInfo.BroadcastRate, limitList)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetPublishError,
			Msg:  err.Error(),
		}
		return res, err
	}
	return res, nil
}

func (c *BasemsgcallinController) SubscribeTopic(ctx context.Context, req *pb_basemsgcallin.SubscribeTopicReq) (*pb_basemsgcallin.SubscribeTopicRsp, error) {
	res := &pb_basemsgcallin.SubscribeTopicRsp{
		Base: &common.SvcBaseResp{
			Code: util.RetOK,
			Msg:  "success",
		},
	}

	//检查参数
	platformId, err := util.ParseUint64(req.PlatformId)
	if err != nil || platformId <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId wrong",
		}
		return res, nil
	}
	if req.UserId == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "UserId empty",
		}
		return res, nil
	}
	topics := make([]string, 0)
	err = util.Json.UnmarshalFromString(req.Topics, &topics)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "Topics wrong",
		}
		return res, err
	}
	if len(topics) <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "Topics empty",
		}
		return res, nil
	}

	//todo 开放调试平台
	//检查签名
	//if platformId != 1 {
	platformSecretKey := c.conf.PlatformKey[platformId]
	if platformSecretKey == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId not exist",
		}
		return res, nil
	}
	signTool := signature.NewSignature(platformSecretKey, 2*time.Minute)
	signTool.SetUserReflect(true)
	isValid, err := signTool.Verify(req)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  err.Error(),
		}
		return res, err
	}
	if !isValid {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  "sign verify fail",
		}
		return res, nil
	}
	//}

	//订阅topic
	err = grpccli.Subscribe(ctx, platformId, req.UserId, req.DeviceId, topics)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSubscribeError,
			Msg:  err.Error(),
		}
		return res, err
	}
	return res, nil
}

func (c *BasemsgcallinController) UnSubscribeTopic(ctx context.Context, req *pb_basemsgcallin.UnSubscribeTopicReq) (*pb_basemsgcallin.UnSubscribeTopicRsp, error) {
	res := &pb_basemsgcallin.UnSubscribeTopicRsp{
		Base: &common.SvcBaseResp{
			Code: util.RetOK,
			Msg:  "success",
		},
	}

	//检查参数
	platformId, err := util.ParseUint64(req.PlatformId)
	if err != nil || platformId <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId wrong",
		}
		return res, nil
	}
	if req.UserId == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "UserId empty",
		}
		return res, nil
	}
	topics := make([]string, 0)
	err = util.Json.UnmarshalFromString(req.Topics, &topics)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "Topics wrong",
		}
		return res, err
	}
	if len(topics) <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "Topics empty",
		}
		return res, nil
	}

	//todo 开放调试平台
	//检查签名
	//if platformId != 1 {
	platformSecretKey := c.conf.PlatformKey[platformId]
	if platformSecretKey == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId not exist",
		}
		return res, nil
	}
	signTool := signature.NewSignature(platformSecretKey, 2*time.Minute)
	signTool.SetUserReflect(true)
	isValid, err := signTool.Verify(req)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  err.Error(),
		}
		return res, err
	}
	if !isValid {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  "sign verify fail",
		}
		return res, nil
	}
	//}

	//取消订阅topic
	err = grpccli.UnSubscribe(ctx, platformId, req.UserId, req.DeviceId, topics)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetUnsubscribeError,
			Msg:  err.Error(),
		}
		return res, err
	}
	return res, nil
}

func (c *BasemsgcallinController) UsersTopics(ctx context.Context, req *pb_basemsgcallin.UsersTopicsReq) (*pb_basemsgcallin.UsersTopicsRsp, error) {
	res := &pb_basemsgcallin.UsersTopicsRsp{
		Base: &common.SvcBaseResp{
			Code: util.RetOK,
			Msg:  "success",
		},
	}

	//检查参数
	platformId, err := util.ParseUint64(req.PlatformId)
	if err != nil || platformId <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId wrong",
		}
		return res, nil
	}
	userDevices := make([]*pb_basemsgcallin.UserDevice, 0)
	err = util.Json.UnmarshalFromString(req.UserDevices, &userDevices)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "UserDevices wrong",
		}
		return res, err
	}
	tempUserDevices := make([]*pb_basemsgcallin.UserDevice, 0)
	for _, userDevice := range userDevices {
		if userDevice != nil && userDevice.UserId != "" {
			tempUserDevices = append(tempUserDevices, userDevice)
		}
	}
	userDevices = tempUserDevices
	if len(userDevices) <= 0 {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "UserDevices empty",
		}
		return res, nil
	}

	//todo 开放调试平台
	//检查签名
	//if platformId != 1 {
	platformSecretKey := c.conf.PlatformKey[platformId]
	if platformSecretKey == "" {
		res.Base = &common.SvcBaseResp{
			Code: util.RetInvalidParam,
			Msg:  "PlatformId not exist",
		}
		return res, nil
	}
	signTool := signature.NewSignature(platformSecretKey, 2*time.Minute)
	signTool.SetUserReflect(true)
	isValid, err := signTool.Verify(req)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  err.Error(),
		}
		return res, err
	}
	if !isValid {
		res.Base = &common.SvcBaseResp{
			Code: util.RetSignError,
			Msg:  "sign verify fail",
		}
		return res, nil
	}
	//}

	//批量查询用户订阅的topic信息
	userTopicMap, err := grpccli.UsersTopics(platformId, userDevices)
	if err != nil {
		res.Base = &common.SvcBaseResp{
			Code: util.RetUsersTopicsError,
			Msg:  err.Error(),
		}
		return res, err
	}
	res.Data = &pb_basemsgcallin.UsersTopicsData{UserTopics: userTopicMap}
	return res, nil
}
