# 服务监听地址
addr: 0.0.0.0:8080

# 日志配置文件
logger: deploy/logger.xml

# 环境，分测试和线上
env: pro

# 数据库配置
db:
  #实例列表
  instances:
    master:
      driver: mysql
      url: dev:tJipDi4shJaj@tcp(rm-uf6u858f8go98ji8y.mysql.rds.aliyuncs.com:3306)/xllivemp_base_conf?timeout=3s&readTimeout=3s&writeTimeout=3s&charset=utf8mb4

#平台密钥配置
platform_key:
  4: B8n37v4Xq95cfs   #melon项目
  3: fHa23SDFNA13Ss$1 #皮皮直播项目
  5: fsnq7vFSF9xffs2d #小米直播项目
  1: ORETY94GR9TCC8HG #迅雷直播项目
  7: dnx9DaxM1#x0*xbz #语音国内社交项目

#长连接地址
msgconn_addr:
  4:
    master_host: basemsg-mozhi.xunleizhichui.com
    master_wshost: basemsg-mozhi.xunleizhichui.com
    master_port: 8080 #mqtt端口
    master_wsport: 8081 #ws端口
    slave_host: basemsg-mozhi.xunleizhichui.com
    slave_wshost: basemsg-mozhi.xunleizhichui.com
    slave_port: 8080
    slave_wsport: 8081
  5:
    master_host: basemsg-live.baijinshow.com
    master_wshost: basemsg-live.baijinshow.com
    master_port: 8080 #mqtt端口
    master_wsport: 443 #ws端口
    slave_host: basemsg-live.baijinshow.com
    slave_wshost: basemsg-live.baijinshow.com
    slave_port: 8080
    slave_wsport: 443
  1:
    master_host: basemsg-live.xunlei.com
    master_wshost: basemsg-live.xunlei.com
    master_port: 8080 #mqtt端口
    master_wsport: 8081 #ws端口
    slave_host: basemsg-live.xunlei.com
    slave_wshost: basemsg-live.xunlei.com
    slave_port: 8080
    slave_wsport: 8081
  7:
    master_host: basemsg-live.yinbanwl.com
    master_wshost: basemsg-live.yinbanwl.com
    master_port: 8080 #mqtt端口
    master_wsport: 8081 #ws端口
    slave_host: basemsg-live.yinbanwl.com
    slave_wshost: basemsg-live.yinbanwl.com
    slave_port: 8080
    slave_wsport: 8081
