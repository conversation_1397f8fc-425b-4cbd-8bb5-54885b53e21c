apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: xllivemp-basemsgcallin-s-mesh
  namespace: xllivemp
  labels:
    xlmesh-app: xllivemp-basemsgcallin-s
spec:
  replicas: ${replicas}
  template:
    metadata:
      labels:
        xlmesh-app: xllivemp-basemsgcallin-s
      annotations:
        actmonitor_enable: "true"
        actmetrics_path: "/stats/prometheus"
        actmetrics_port: "9003"
        grpc_actmonitor_enable: "true"
        grpc_actmetrics_path: "/grpc_metric"
        grpc_actmetrics_port: "9100"
    spec:
      initContainers:
      - name: init-data
        image: backends/basemsg/basemsgcallin/basemsgcallin-initdata:${version}
        imagePullPolicy: Always
        command: ['sh', '-c', 'cp /initdata/* /sharedfiles/']
        volumeMounts:
        - name: initdata
          mountPath: /sharedfiles/
      containers:
      - name: envoy
        image: registry.xunlei.cn/xllive/localenvoy:v1.10.0.5
        #command: ["sh", "-c", "envsubst < /etc/envoy/envoy.yaml.tpl > /etc/envoy/envoy.yaml && envoy -c /etc/envoy/envoy.yaml --log-level info --base-id 12345678"]
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
        resources:
          limits:
            cpu: 200m
            memory: 256M
          requests:
            cpu: 100m
            memory: 128Mi
        ports:
        - name: ingress
          containerPort: 9001
        - name: egress
          containerPort: 9002
        - name: admin
          containerPort: 9003
        livenessProbe:
          tcpSocket:
            port: ingress
          initialDelaySeconds: 30
          timeoutSeconds: 2
        volumeMounts:
        - name: initdata
          mountPath: /initdata/
        env:
        - name: XLMESH_ENVOY_SERVICE_NODE
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: XLMESH_ENVOY_SERVICE_CLUSTER # 该参数值为serviceName将"-"和"."转换为"_"
          value: xllivemp_basemsgcallin_s
        - name: XLMESH_ENVOY_SERVICE_NAME
          value: xllivemp.basemsgcallin.s
        - name: XLMESH_ENVOY_PB_FILE
          value: /initdata/proto_descriptor.pb
        - name: XLMESH_KDS_HOST
          value: xllivemp-basekds-mesh
        - name: XLMESH_KDS_PORT
          value: "8018"
      - name: xllivemp-basemsgcallin-s
        image: backends/basemsg/basemsgcallin:${version}
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        env:
        - name: XLSOA_KUBESERVICE_CONFIG_FILE
          value: /opt/xllivemp/basemsgcallin/deploy/soa.yml
        - name: MODULES_XLSOA_SERVER_CONTEXT_OAUTH_SECURE_SWITCH
          value: "off"
        volumeMounts:
        - name: xllivempconf
          mountPath: /opt/xllivemp/basemsgcallin/deploy/
      volumes:
      - name: xllivempconf
        configMap:
          name: ${name}
      - name: initdata
        emptyDir: {}
      imagePullSecrets:
      - name: registry-xunlei-cn

