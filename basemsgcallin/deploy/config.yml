# 服务监听地址
addr: 0.0.0.0:8080

# 日志配置文件
logger: deploy/logger.xml

# 环境，分测试和线上
env: dev

#平台密钥配置
platform_key:
  4: B8n37v4Xq95cfs    #为melon项目测试平台

#长连接地址
msgconn_addr:
  4:
    master_host: *********** #内网ip为*************
    master_wshost: ***********
    master_port: 8080 #mqtt端口
    master_wsport: 8081 #ws端口
    slave_host: *********** #内网ip为*************
    slave_wshost: ***********
    slave_port: 8080
    slave_wsport: 8081
jaeger:
  enable: true
  endpoint: jaeger:4317
  environment: dev
  service_name: vc.basemsgcallin.s