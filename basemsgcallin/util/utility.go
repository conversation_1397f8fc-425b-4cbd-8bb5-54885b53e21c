package util

/**
通用方法定义
*/

import (
	"strconv"
	"time"

	"xim/common/header"

	jsoniter "github.com/json-iterator/go"
	"github.com/json-iterator/go/extra"
	"golang.org/x/net/context"
)

var Json = jsoniter.ConfigCompatibleWithStandardLibrary

func init() {
	extra.RegisterFuzzyDecoders()
}

// 字符串时间（格式 YYYY-MM-DD HH:mm:ss）转 Unix 时间戳
func ParseUnixTimestamp(srcTime string) int64 {
	if srcTime == "0000-00-00 00:00:00" || srcTime == "0" {
		return int64(0)
	}
	timeLayout := "2006-01-02 15:04:05"                          //转化所需模板
	loc, _ := time.LoadLocation("Local")                         //重要：获取时区
	theTime, _ := time.ParseInLocation(timeLayout, srcTime, loc) //使用模板在对应时区转化为time.time类型
	return int64(theTime.Unix())
}

// Unix 时间戳转为字符串（格式 YYYY-MM-DD HH:mm:ss）
func FormatUnixTimestamp(ts int64) string {
	timeLayout := "2006-01-02 15:04:05"
	t := time.Unix(ts, 0)
	return t.Format(timeLayout)
}

// 字符串和整形相互转换
func ParseInt64(s string) (int64, error) {
	value, err := strconv.ParseInt(s, 10, 64)
	return value, err
}
func ParseUint64(s string) (uint64, error) {
	value, err := strconv.ParseUint(s, 10, 64)
	return uint64(value), err
}
func ParseInt32(s string) (int32, error) {
	value, err := strconv.ParseInt(s, 10, 32)
	return int32(value), err
}
func ParseUint32(s string) (uint32, error) {
	value, err := strconv.ParseUint(s, 10, 32)
	return uint32(value), err
}
func ParseInt(s string) (int, error) {
	value, err := strconv.ParseUint(s, 10, 32)
	return int(value), err
}

func FormatInt64(i int64) string {
	value := strconv.FormatInt(i, 10)
	return value
}
func FormatUint64(i uint64) string {
	value := strconv.FormatUint(i, 10)
	return value
}
func FormatInt32(i int32) string {
	value := strconv.FormatInt(int64(i), 10)
	return value
}
func FormatUint32(i uint32) string {
	value := strconv.FormatUint(uint64(i), 10)
	return value
}

// 获取key的cookie值
func GetCookieValue(ctx context.Context, cookleKey string) string {
	headerData := header.NewHeader(ctx)
	return headerData.GetCookie(cookleKey)
}
